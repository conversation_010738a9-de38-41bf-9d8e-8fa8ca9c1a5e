#!/usr/bin/env python3
"""
System Environment Detector for PROJECT-ALPHA
Implements system capability detection and adaptive processing strategies
based on available resources for optimal Excel import performance.
"""

import os
import sys
import platform
import logging
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import json

logger = logging.getLogger('system_environment_detector')

class SystemEnvironmentDetector:
    """Detects system capabilities and recommends optimal processing strategies."""
    
    def __init__(self):
        self.system_profile = {}
        self.performance_profile = {}
        self.compatibility_profile = {}
        self.processing_recommendations = {}
        
        self._detect_system_environment()
        self._benchmark_system_performance()
        self._analyze_compatibility()
        self._generate_recommendations()
    
    def _detect_system_environment(self):
        """Detect comprehensive system environment information."""
        logger.info("Detecting system environment...")
        
        # Basic system information
        self.system_profile = {
            'platform': platform.platform(),
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'python_version': sys.version,
            'python_executable': sys.executable,
            'cpu_count': os.cpu_count() or 1,
            'architecture': platform.architecture(),
            'hostname': platform.node()
        }
        
        # Memory information
        self.system_profile.update(self._detect_memory_info())
        
        # Storage information
        self.system_profile.update(self._detect_storage_info())
        
        # Windows-specific information
        if platform.system() == 'Windows':
            self.system_profile.update(self._detect_windows_info())
        
        # Environment variables
        self.system_profile['environment'] = self._get_relevant_env_vars()
        
        logger.info(f"System profile detected: {self.system_profile['system']} {self.system_profile['release']}")
    
    def _detect_memory_info(self) -> Dict[str, Any]:
        """Detect memory information."""
        memory_info = {'memory_detection_method': 'unknown'}
        
        try:
            # Try psutil first
            import psutil
            memory = psutil.virtual_memory()
            memory_info.update({
                'total_memory_gb': memory.total / (1024**3),
                'available_memory_gb': memory.available / (1024**3),
                'memory_percent_used': memory.percent,
                'memory_detection_method': 'psutil'
            })
        except ImportError:
            # Fallback to system-specific methods
            if platform.system() == 'Windows':
                memory_info.update(self._detect_windows_memory())
            else:
                # Conservative estimates for unknown systems
                memory_info.update({
                    'total_memory_gb': 4.0,
                    'available_memory_gb': 2.0,
                    'memory_percent_used': 50.0,
                    'memory_detection_method': 'estimate'
                })
        
        return memory_info
    
    def _detect_windows_memory(self) -> Dict[str, Any]:
        """Detect Windows memory using ctypes."""
        try:
            import ctypes
            kernel32 = ctypes.windll.kernel32
            c_ulong = ctypes.c_ulong
            
            class MEMORYSTATUSEX(ctypes.Structure):
                _fields_ = [
                    ('dwLength', c_ulong),
                    ('dwMemoryLoad', c_ulong),
                    ('ullTotalPhys', ctypes.c_ulonglong),
                    ('ullAvailPhys', ctypes.c_ulonglong),
                    ('ullTotalPageFile', ctypes.c_ulonglong),
                    ('ullAvailPageFile', ctypes.c_ulonglong),
                    ('ullTotalVirtual', ctypes.c_ulonglong),
                    ('ullAvailVirtual', ctypes.c_ulonglong),
                    ('sullAvailExtendedVirtual', ctypes.c_ulonglong),
                ]
            
            memory_status = MEMORYSTATUSEX()
            memory_status.dwLength = ctypes.sizeof(MEMORYSTATUSEX)
            kernel32.GlobalMemoryStatusEx(ctypes.byref(memory_status))
            
            return {
                'total_memory_gb': memory_status.ullTotalPhys / (1024**3),
                'available_memory_gb': memory_status.ullAvailPhys / (1024**3),
                'memory_percent_used': memory_status.dwMemoryLoad,
                'memory_detection_method': 'windows_api'
            }
        except Exception as e:
            logger.warning(f"Windows memory detection failed: {e}")
            return {
                'total_memory_gb': 4.0,
                'available_memory_gb': 2.0,
                'memory_percent_used': 50.0,
                'memory_detection_method': 'windows_fallback'
            }
    
    def _detect_storage_info(self) -> Dict[str, Any]:
        """Detect storage information."""
        storage_info = {}
        
        try:
            # Get current working directory space
            cwd = Path.cwd()
            if hasattr(os, 'statvfs'):  # Unix-like systems
                stat = os.statvfs(cwd)
                total_space = stat.f_frsize * stat.f_blocks
                free_space = stat.f_frsize * stat.f_bavail
            else:  # Windows
                import shutil
                total_space, used_space, free_space = shutil.disk_usage(cwd)
            
            storage_info.update({
                'total_storage_gb': total_space / (1024**3),
                'free_storage_gb': free_space / (1024**3),
                'storage_percent_free': (free_space / total_space) * 100
            })
        except Exception as e:
            logger.warning(f"Storage detection failed: {e}")
            storage_info.update({
                'total_storage_gb': 100.0,
                'free_storage_gb': 50.0,
                'storage_percent_free': 50.0
            })
        
        return storage_info
    
    def _detect_windows_info(self) -> Dict[str, Any]:
        """Detect Windows-specific information."""
        windows_info = {}
        
        try:
            # Windows version details
            import sys
            if hasattr(sys, 'getwindowsversion'):
                win_ver = sys.getwindowsversion()
                windows_info.update({
                    'windows_major': win_ver.major,
                    'windows_minor': win_ver.minor,
                    'windows_build': win_ver.build,
                    'windows_platform': win_ver.platform,
                    'windows_service_pack': win_ver.service_pack
                })
            
            # Try to get Windows edition
            try:
                result = subprocess.run(['wmic', 'os', 'get', 'Caption', '/value'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if 'Caption=' in line:
                            windows_info['windows_edition'] = line.split('=', 1)[1].strip()
                            break
            except Exception:
                pass
            
        except Exception as e:
            logger.warning(f"Windows info detection failed: {e}")
        
        return windows_info
    
    def _get_relevant_env_vars(self) -> Dict[str, str]:
        """Get relevant environment variables."""
        relevant_vars = [
            'PATH', 'PYTHONPATH', 'TEMP', 'TMP', 'USERPROFILE', 'APPDATA',
            'LOCALAPPDATA', 'PROGRAMFILES', 'SYSTEMROOT', 'PROCESSOR_ARCHITECTURE',
            'NUMBER_OF_PROCESSORS', 'COMPUTERNAME', 'USERNAME'
        ]
        
        env_vars = {}
        for var in relevant_vars:
            value = os.environ.get(var)
            if value:
                env_vars[var] = value
        
        return env_vars
    
    def _benchmark_system_performance(self):
        """Benchmark system performance for processing recommendations."""
        logger.info("Benchmarking system performance...")
        
        # CPU performance test
        cpu_score = self._benchmark_cpu()
        
        # Memory performance test
        memory_score = self._benchmark_memory()
        
        # I/O performance test
        io_score = self._benchmark_io()
        
        self.performance_profile = {
            'cpu_score': cpu_score,
            'memory_score': memory_score,
            'io_score': io_score,
            'overall_score': (cpu_score + memory_score + io_score) / 3,
            'benchmark_timestamp': time.time()
        }
        
        logger.info(f"Performance benchmark completed: overall score {self.performance_profile['overall_score']:.1f}")
    
    def _benchmark_cpu(self) -> float:
        """Benchmark CPU performance."""
        try:
            start_time = time.time()
            
            # Simple CPU-intensive task
            result = 0
            for i in range(100000):
                result += i * i
            
            elapsed = time.time() - start_time
            
            # Score based on time (lower is better, so invert)
            # Baseline: 0.1 seconds = 100 points
            score = max(10, min(100, 10 / elapsed))
            
            return score
        except Exception:
            return 50.0  # Default score
    
    def _benchmark_memory(self) -> float:
        """Benchmark memory performance."""
        try:
            start_time = time.time()
            
            # Memory allocation test
            data = []
            for i in range(10000):
                data.append([j for j in range(100)])
            
            # Memory access test
            total = sum(sum(row) for row in data)
            
            elapsed = time.time() - start_time
            del data  # Cleanup
            
            # Score based on time and available memory
            time_score = max(10, min(100, 5 / elapsed))
            memory_gb = self.system_profile.get('total_memory_gb', 4.0)
            memory_score = min(100, memory_gb * 20)  # 20 points per GB
            
            return (time_score + memory_score) / 2
        except Exception:
            return 50.0  # Default score
    
    def _benchmark_io(self) -> float:
        """Benchmark I/O performance."""
        try:
            import tempfile
            
            start_time = time.time()
            
            # Write test
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
                temp_file = f.name
                for i in range(1000):
                    f.write(f"Test line {i}\n")
            
            # Read test
            with open(temp_file, 'r') as f:
                lines = f.readlines()
            
            # Cleanup
            os.unlink(temp_file)
            
            elapsed = time.time() - start_time
            
            # Score based on time
            score = max(10, min(100, 2 / elapsed))
            
            return score
        except Exception:
            return 50.0  # Default score
    
    def _analyze_compatibility(self):
        """Analyze system compatibility for Excel import operations."""
        logger.info("Analyzing system compatibility...")
        
        # Check Python version compatibility
        python_version = sys.version_info
        python_compatible = python_version >= (3, 7)
        
        # Check library availability
        libraries = self._check_library_availability()
        
        # Check system resources
        memory_adequate = self.system_profile.get('total_memory_gb', 0) >= 2.0
        storage_adequate = self.system_profile.get('free_storage_gb', 0) >= 1.0
        
        # Check Windows version (if applicable)
        windows_compatible = True
        if platform.system() == 'Windows':
            windows_major = self.system_profile.get('windows_major', 10)
            windows_compatible = windows_major >= 10
        
        self.compatibility_profile = {
            'python_compatible': python_compatible,
            'python_version': f"{python_version.major}.{python_version.minor}.{python_version.micro}",
            'libraries_available': libraries,
            'memory_adequate': memory_adequate,
            'storage_adequate': storage_adequate,
            'windows_compatible': windows_compatible,
            'overall_compatible': all([
                python_compatible,
                libraries['pandas'],
                libraries['openpyxl'] or libraries['xlrd'],
                memory_adequate,
                storage_adequate,
                windows_compatible
            ])
        }
        
        logger.info(f"Compatibility analysis: {'COMPATIBLE' if self.compatibility_profile['overall_compatible'] else 'ISSUES DETECTED'}")
    
    def _check_library_availability(self) -> Dict[str, bool]:
        """Check availability of required libraries."""
        libraries = {}
        
        required_libs = ['pandas', 'openpyxl', 'xlrd', 'psutil', 'dateutil']
        
        for lib in required_libs:
            try:
                __import__(lib)
                libraries[lib] = True
            except ImportError:
                libraries[lib] = False
        
        return libraries
    
    def _generate_recommendations(self):
        """Generate processing strategy recommendations."""
        logger.info("Generating processing recommendations...")
        
        memory_gb = self.system_profile.get('total_memory_gb', 4.0)
        cpu_count = self.system_profile.get('cpu_count', 1)
        overall_score = self.performance_profile.get('overall_score', 50.0)
        
        # Determine processing strategy
        if memory_gb >= 8 and cpu_count >= 4 and overall_score >= 70:
            strategy = 'high_performance'
            chunk_size = 2000
            max_memory_mb = 1024
        elif memory_gb >= 4 and cpu_count >= 2 and overall_score >= 50:
            strategy = 'standard'
            chunk_size = 1000
            max_memory_mb = 512
        else:
            strategy = 'low_resource'
            chunk_size = 500
            max_memory_mb = 256
        
        # Excel processing recommendations
        excel_strategy = 'robust'
        if not self.compatibility_profile.get('libraries_available', {}).get('openpyxl', False):
            excel_strategy = 'fallback'
        elif memory_gb < 2:
            excel_strategy = 'memory_safe'
        
        self.processing_recommendations = {
            'strategy': strategy,
            'chunk_size': chunk_size,
            'max_memory_mb': max_memory_mb,
            'excel_strategy': excel_strategy,
            'parallel_processing': cpu_count > 1 and memory_gb >= 4,
            'memory_monitoring': memory_gb < 6,
            'aggressive_cleanup': memory_gb < 4,
            'temp_file_cleanup': True,
            'progress_reporting': True
        }
        
        logger.info(f"Recommended strategy: {strategy} (chunk_size: {chunk_size}, max_memory: {max_memory_mb}MB)")
    
    def get_system_profile(self) -> Dict[str, Any]:
        """Get complete system profile."""
        return {
            'system': self.system_profile,
            'performance': self.performance_profile,
            'compatibility': self.compatibility_profile,
            'recommendations': self.processing_recommendations,
            'detection_timestamp': time.time()
        }
    
    def get_processing_strategy(self) -> str:
        """Get recommended processing strategy."""
        return self.processing_recommendations.get('strategy', 'standard')
    
    def get_excel_import_config(self) -> Dict[str, Any]:
        """Get optimized configuration for Excel import."""
        return {
            'strategy': self.processing_recommendations.get('excel_strategy', 'robust'),
            'chunk_size': self.processing_recommendations.get('chunk_size', 1000),
            'max_memory_mb': self.processing_recommendations.get('max_memory_mb', 512),
            'enable_monitoring': self.processing_recommendations.get('memory_monitoring', True),
            'aggressive_cleanup': self.processing_recommendations.get('aggressive_cleanup', False),
            'parallel_processing': self.processing_recommendations.get('parallel_processing', False)
        }
    
    def is_system_compatible(self) -> bool:
        """Check if system is compatible for Excel import operations."""
        return self.compatibility_profile.get('overall_compatible', False)
    
    def get_compatibility_issues(self) -> List[str]:
        """Get list of compatibility issues."""
        issues = []
        
        if not self.compatibility_profile.get('python_compatible', True):
            issues.append("Python version too old (requires 3.7+)")
        
        if not self.compatibility_profile.get('memory_adequate', True):
            issues.append("Insufficient memory (requires 2GB+)")
        
        if not self.compatibility_profile.get('storage_adequate', True):
            issues.append("Insufficient storage space (requires 1GB+)")
        
        libraries = self.compatibility_profile.get('libraries_available', {})
        if not libraries.get('pandas', False):
            issues.append("pandas library not available")
        
        if not (libraries.get('openpyxl', False) or libraries.get('xlrd', False)):
            issues.append("No Excel reading library available (openpyxl or xlrd)")
        
        if not self.compatibility_profile.get('windows_compatible', True):
            issues.append("Windows version not supported")
        
        return issues

# Global detector instance
_global_detector = None

def get_system_detector() -> SystemEnvironmentDetector:
    """Get global system environment detector."""
    global _global_detector
    if _global_detector is None:
        _global_detector = SystemEnvironmentDetector()
    return _global_detector

def get_system_profile() -> Dict[str, Any]:
    """Get system profile."""
    return get_system_detector().get_system_profile()

def get_processing_strategy() -> str:
    """Get recommended processing strategy."""
    return get_system_detector().get_processing_strategy()

def get_excel_import_config() -> Dict[str, Any]:
    """Get Excel import configuration."""
    return get_system_detector().get_excel_import_config()

def is_system_compatible() -> bool:
    """Check system compatibility."""
    return get_system_detector().is_system_compatible()

def get_compatibility_issues() -> List[str]:
    """Get compatibility issues."""
    return get_system_detector().get_compatibility_issues()

if __name__ == "__main__":
    # System environment detection test
    print("=" * 60)
    print("PROJECT-ALPHA SYSTEM ENVIRONMENT DETECTION")
    print("=" * 60)
    
    detector = SystemEnvironmentDetector()
    profile = detector.get_system_profile()
    
    print(f"System: {profile['system']['system']} {profile['system']['release']}")
    print(f"Memory: {profile['system']['total_memory_gb']:.1f} GB")
    print(f"CPU Cores: {profile['system']['cpu_count']}")
    print(f"Performance Score: {profile['performance']['overall_score']:.1f}")
    print(f"Compatible: {'YES' if profile['compatibility']['overall_compatible'] else 'NO'}")
    print(f"Recommended Strategy: {profile['recommendations']['strategy']}")
    
    if not profile['compatibility']['overall_compatible']:
        print("\nCompatibility Issues:")
        for issue in detector.get_compatibility_issues():
            print(f"- {issue}")
    
    print(f"\nExcel Import Config:")
    config = detector.get_excel_import_config()
    for key, value in config.items():
        print(f"- {key}: {value}")
