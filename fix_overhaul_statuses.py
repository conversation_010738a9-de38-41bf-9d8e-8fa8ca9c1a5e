"""
Manual fix script for overhaul statuses
Run this to fix existing overhaul statuses and identify data issues
"""
import sys
import os
import logging

# Add project root to Python's import path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_overhaul_statuses():
    """Fix all overhaul statuses and report issues"""
    print("🔧 Fixing Overhaul Statuses")
    print("=" * 40)
    
    try:
        import overhaul_service
        from models import Overhaul
        
        # Get all overhauls
        all_overhauls = Overhaul.get_all()
        print(f"Found {len(all_overhauls)} total overhaul records")
        
        # Count statuses before fix
        status_counts_before = {}
        for oh in all_overhauls:
            status = oh.get('status', 'None')
            status_counts_before[status] = status_counts_before.get(status, 0) + 1
        
        print("\n📊 Status counts BEFORE fix:")
        for status, count in sorted(status_counts_before.items()):
            print(f"  {status}: {count}")
        
        # Run the fix
        print(f"\n🔄 Running status update...")
        updated_count = overhaul_service.update_overhaul_statuses()
        print(f"✅ Update completed: {updated_count} statuses changed")
        
        # Count statuses after fix
        all_overhauls_after = Overhaul.get_all()
        status_counts_after = {}
        for oh in all_overhauls_after:
            status = oh.get('status', 'None')
            status_counts_after[status] = status_counts_after.get(status, 0) + 1
        
        print("\n📊 Status counts AFTER fix:")
        for status, count in sorted(status_counts_after.items()):
            print(f"  {status}: {count}")
        
        # Report remaining issues
        unknown_after = status_counts_after.get('unknown', 0)
        if unknown_after > 0:
            print(f"\n⚠️  {unknown_after} records still have 'unknown' status")
            print("This might be due to:")
            print("- Missing commission dates")
            print("- Invalid date formats (like 'UNDER MLOH')")
            print("- Missing equipment records")
            
        print(f"\n✅ Status fix completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    fix_overhaul_statuses() 