"""
Enhanced Equipment Widget with comprehensive CRUD functionality
Optimized for military deployment on 1366x768 displays.
"""

import logging
from datetime import datetime, date, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
    QDateEdit, QTextEdit, QCheckBox, QGroupBox, QSplitter, QFrame,
    QMessageBox, QTabWidget, QFileDialog, QProgressBar, QDialog,
    QTableWidget, QTableWidgetItem
)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from ui.responsive_crud_framework import ResponsiveCRUDWidget
from ui.crud_dialogs import ResponsiveFormDialog, BulkOperationDialog, HistoryViewDialog
from ui.window_utils import DPIScaler, FormManager, LayoutManager
from ui.custom_widgets import StatusLabel
from models import Equipment
import utils
import database

logger = logging.getLogger('enhanced_equipment_widget')

class EnhancedEquipmentWidget(ResponsiveCRUDWidget):
    """Enhanced equipment widget with comprehensive CRUD functionality."""
    
    def __init__(self, parent=None):
        super().__init__("equipment", parent)
        
    def create_form_widget(self):
        """Create the equipment-specific form widget."""
        widget = QWidget()
        layout = QFormLayout(widget)
        FormManager.setup_responsive_form_layout(layout)
        
        # Basic Information Section
        basic_group = QGroupBox("Basic Information")
        basic_layout = QFormLayout(basic_group)
        FormManager.setup_responsive_form_layout(basic_layout)
        
        # Equipment ID (read-only for existing records)
        self.equipment_id_field = QLineEdit()
        basic_layout.addRow("Equipment ID:", self.equipment_id_field)
        
        # BA Number
        self.ba_number_field = QLineEdit()
        self.ba_number_field.setPlaceholderText("Enter BA number...")
        basic_layout.addRow("BA Number:", self.ba_number_field)
        
        # Make and Type
        self.make_type_field = QLineEdit()
        self.make_type_field.setPlaceholderText("Enter make and type...")
        basic_layout.addRow("Make & Type:", self.make_type_field)
        
        # Equipment Status
        self.status_field = QComboBox()
        self.status_field.addItems(["active", "inactive", "maintenance", "discarded"])
        basic_layout.addRow("Status:", self.status_field)
        
        # Category
        self.category_field = QComboBox()
        self.category_field.addItems([
            "Vehicle", "Generator", "Communication", "Medical", 
            "Engineering", "Weapon System", "Support Equipment", "Other"
        ])
        self.category_field.setEditable(True)
        basic_layout.addRow("Category:", self.category_field)
        
        layout.addRow(basic_group)
        
        # Technical Specifications Section
        tech_group = QGroupBox("Technical Specifications")
        tech_layout = QFormLayout(tech_group)
        FormManager.setup_responsive_form_layout(tech_layout)
        
        # Meterage
        self.meterage_field = QDoubleSpinBox()
        self.meterage_field.setRange(0, 9999999)
        self.meterage_field.setSuffix(" km")
        self.meterage_field.setDecimals(2)
        tech_layout.addRow("Total Meterage:", self.meterage_field)
        
        # Hours Run
        self.hours_field = QDoubleSpinBox()
        self.hours_field.setRange(0, 999999)
        self.hours_field.setSuffix(" hrs")
        self.hours_field.setDecimals(2)
        tech_layout.addRow("Total Hours:", self.hours_field)
        
        # Engine Number
        self.engine_number_field = QLineEdit()
        tech_layout.addRow("Engine Number:", self.engine_number_field)
        
        # Chassis Number
        self.chassis_number_field = QLineEdit()
        tech_layout.addRow("Chassis Number:", self.chassis_number_field)
        
        # Fuel Type
        self.fuel_type_field = QComboBox()
        self.fuel_type_field.addItems(["Petrol", "Diesel", "Electric", "Hybrid", "Other"])
        self.fuel_type_field.setEditable(True)
        tech_layout.addRow("Fuel Type:", self.fuel_type_field)
        
        layout.addRow(tech_group)
        
        # Dates Section
        dates_group = QGroupBox("Important Dates")
        dates_layout = QFormLayout(dates_group)
        FormManager.setup_responsive_form_layout(dates_layout)
        
        # Date of Commission
        self.commission_date_field = QDateEdit()
        self.commission_date_field.setCalendarPopup(True)
        self.commission_date_field.setDate(QDate.currentDate())
        dates_layout.addRow("Date of Commission:", self.commission_date_field)
        
        # Date of Release
        self.release_date_field = QDateEdit()
        self.release_date_field.setCalendarPopup(True)
        self.release_date_field.setSpecialValueText("Not set")
        self.release_date_field.setDate(QDate(1900, 1, 1))
        dates_layout.addRow("Date of Release:", self.release_date_field)
        
        # Last Maintenance Date
        self.last_maintenance_field = QDateEdit()
        self.last_maintenance_field.setCalendarPopup(True)
        self.last_maintenance_field.setSpecialValueText("Not set")
        self.last_maintenance_field.setDate(QDate(1900, 1, 1))
        dates_layout.addRow("Last Maintenance:", self.last_maintenance_field)
        
        layout.addRow(dates_group)
        
        # Location and Assignment Section
        location_group = QGroupBox("Location & Assignment")
        location_layout = QFormLayout(location_group)
        FormManager.setup_responsive_form_layout(location_layout)
        
        # Current Location
        self.location_field = QLineEdit()
        self.location_field.setPlaceholderText("Enter current location...")
        location_layout.addRow("Current Location:", self.location_field)
        
        # Assigned Unit
        self.unit_field = QLineEdit()
        self.unit_field.setPlaceholderText("Enter assigned unit...")
        location_layout.addRow("Assigned Unit:", self.unit_field)
        
        # Responsible Person
        self.responsible_field = QLineEdit()
        self.responsible_field.setPlaceholderText("Enter responsible person...")
        location_layout.addRow("Responsible Person:", self.responsible_field)
        
        layout.addRow(location_group)
        
        # Additional Information Section
        additional_group = QGroupBox("Additional Information")
        additional_layout = QFormLayout(additional_group)
        FormManager.setup_responsive_form_layout(additional_layout)
        
        # Acquisition Cost
        self.cost_field = QDoubleSpinBox()
        self.cost_field.setRange(0, 99999999)
        self.cost_field.setPrefix("₹ ")
        additional_layout.addRow("Acquisition Cost:", self.cost_field)
        
        # Warranty Expiry
        self.warranty_field = QDateEdit()
        self.warranty_field.setCalendarPopup(True)
        self.warranty_field.setSpecialValueText("No warranty")
        self.warranty_field.setDate(QDate(1900, 1, 1))
        additional_layout.addRow("Warranty Expiry:", self.warranty_field)
        
        # Insurance Expiry
        self.insurance_field = QDateEdit()
        self.insurance_field.setCalendarPopup(True)
        self.insurance_field.setSpecialValueText("No insurance")
        self.insurance_field.setDate(QDate(1900, 1, 1))
        additional_layout.addRow("Insurance Expiry:", self.insurance_field)
        
        # Notes
        self.notes_field = QTextEdit()
        self.notes_field.setMaximumHeight(DPIScaler.scale_size(80))
        self.notes_field.setPlaceholderText("Enter additional notes...")
        additional_layout.addRow("Notes:", self.notes_field)
        
        layout.addRow(additional_group)
        
        # Calculated Fields Section (Read-only)
        calc_group = QGroupBox("Calculated Information")
        calc_layout = QFormLayout(calc_group)
        FormManager.setup_responsive_form_layout(calc_layout)
        
        # Vintage Years
        self.vintage_display = QLabel("0.00 years")
        calc_layout.addRow("Vintage (Years):", self.vintage_display)
        
        # Equipment Age Status
        self.age_status_display = StatusLabel("Unknown", "unknown")
        calc_layout.addRow("Age Status:", self.age_status_display)
        
        # Utilization Rate
        self.utilization_display = QLabel("0.00 hrs/day")
        calc_layout.addRow("Utilization Rate:", self.utilization_display)
        
        layout.addRow(calc_group)
        
        # Connect signals for real-time calculations
        self.commission_date_field.dateChanged.connect(self.update_calculated_fields)
        self.hours_field.valueChanged.connect(self.update_calculated_fields)
        
        return widget
        
    def update_calculated_fields(self):
        """Update calculated fields based on input data."""
        try:
            # Calculate vintage years
            commission_date = self.commission_date_field.date().toPyDate()
            today = date.today()
            vintage_years = (today - commission_date).days / 365.25
            self.vintage_display.setText(f"{vintage_years:.2f} years")
            
            # Calculate utilization rate
            total_hours = self.hours_field.value()
            if vintage_years > 0:
                days = vintage_years * 365.25
                utilization = total_hours / days
                self.utilization_display.setText(f"{utilization:.2f} hrs/day")
            else:
                self.utilization_display.setText("0.00 hrs/day")
                
            # Update age status
            if vintage_years < 5:
                self.age_status_display.setStatus("new")
            elif vintage_years < 10:
                self.age_status_display.setStatus("good")
            elif vintage_years < 15:
                self.age_status_display.setStatus("aging")
            else:
                self.age_status_display.setStatus("old")
                
        except Exception as e:
            logger.error(f"Error updating calculated fields: {e}")
            
    def load_data(self):
        """Load equipment data."""
        try:
            # Apply current filters
            search_text = self.search_field.text().lower()
            status_filter = self.status_filter.currentText()
            
            # Get all equipment
            equipment_list = Equipment.get_all()
            
            # Apply filters
            filtered_equipment = []
            for equipment in equipment_list:
                # Search filter
                if search_text:
                    searchable_text = f"{equipment.ba_number} {equipment.make_and_type} {equipment.equipment_status}".lower()
                    if search_text not in searchable_text:
                        continue
                        
                # Status filter
                if status_filter != "All" and equipment.equipment_status != status_filter.lower():
                    continue
                    
                filtered_equipment.append(equipment)
            
            # Prepare data for table
            headers = [
                "ID", "BA Number", "Make & Type", "Status", "Category", 
                "Meterage (km)", "Hours", "Location", "Unit", "Vintage (Years)"
            ]
            data = []
            
            for equipment in filtered_equipment:
                # Calculate vintage
                if equipment.date_of_commission:
                    try:
                        commission_date = datetime.strptime(equipment.date_of_commission, '%Y-%m-%d').date()
                        vintage_years = (date.today() - commission_date).days / 365.25
                    except:
                        vintage_years = 0
                else:
                    vintage_years = 0
                    
                row_data = {
                    "ID": equipment.equipment_id,
                    "BA Number": equipment.ba_number or "Not Assigned",
                    "Make & Type": equipment.make_and_type or "",
                    "Status": equipment.equipment_status.title(),
                    "Category": equipment.category or "",
                    "Meterage (km)": f"{equipment.meterage_kms:.2f}",
                    "Hours": f"{equipment.hours_run_total:.2f}",
                    "Location": equipment.current_location or "",
                    "Unit": equipment.assigned_unit or "",
                    "Vintage (Years)": f"{vintage_years:.2f}"
                }
                data.append(row_data)
            
            # Sort by BA Number for better grouping
            data.sort(key=lambda x: (x["BA Number"], x["Make & Type"]))
            
            # Set data in table
            self.data_table.set_data(headers, data, id_column="ID")
            
            # Hide ID column
            self.data_table.setColumnHidden(0, True)
            
        except Exception as e:
            logger.error(f"Error loading equipment data: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load equipment data: {str(e)}")
            
    def populate_form(self, record_data):
        """Populate form with equipment record data."""
        try:
            if not record_data:
                return
                
            # Get full equipment data
            equipment_id = record_data.get('ID')
            equipment = Equipment.get_by_id(equipment_id)
            
            if not equipment:
                return
                
            # Populate basic information
            self.equipment_id_field.setText(str(equipment.equipment_id))
            self.ba_number_field.setText(equipment.ba_number or "")
            self.make_type_field.setText(equipment.make_and_type or "")
            
            # Set status
            status_index = self.status_field.findText(equipment.equipment_status)
            if status_index >= 0:
                self.status_field.setCurrentIndex(status_index)
                
            # Set category
            category_index = self.category_field.findText(equipment.category or "")
            if category_index >= 0:
                self.category_field.setCurrentIndex(category_index)
            else:
                self.category_field.setCurrentText(equipment.category or "")
                
            # Technical specifications
            self.meterage_field.setValue(equipment.meterage_kms)
            self.hours_field.setValue(equipment.hours_run_total)
            self.engine_number_field.setText(equipment.engine_number or "")
            self.chassis_number_field.setText(equipment.chassis_number or "")
            
            # Set fuel type
            fuel_index = self.fuel_type_field.findText(equipment.fuel_type or "")
            if fuel_index >= 0:
                self.fuel_type_field.setCurrentIndex(fuel_index)
            else:
                self.fuel_type_field.setCurrentText(equipment.fuel_type or "")
                
            # Dates
            if equipment.date_of_commission:
                self.commission_date_field.setDate(QDate.fromString(equipment.date_of_commission, Qt.ISODate))
                
            if equipment.date_of_release:
                self.release_date_field.setDate(QDate.fromString(equipment.date_of_release, Qt.ISODate))
            else:
                self.release_date_field.setDate(QDate(1900, 1, 1))
                
            if equipment.last_maintenance_date:
                self.last_maintenance_field.setDate(QDate.fromString(equipment.last_maintenance_date, Qt.ISODate))
            else:
                self.last_maintenance_field.setDate(QDate(1900, 1, 1))
                
            # Location and assignment
            self.location_field.setText(equipment.current_location or "")
            self.unit_field.setText(equipment.assigned_unit or "")
            self.responsible_field.setText(equipment.responsible_person or "")
            
            # Additional information
            self.cost_field.setValue(equipment.acquisition_cost or 0)
            
            if equipment.warranty_expiry:
                self.warranty_field.setDate(QDate.fromString(equipment.warranty_expiry, Qt.ISODate))
            else:
                self.warranty_field.setDate(QDate(1900, 1, 1))
                
            if equipment.insurance_expiry:
                self.insurance_field.setDate(QDate.fromString(equipment.insurance_expiry, Qt.ISODate))
            else:
                self.insurance_field.setDate(QDate(1900, 1, 1))
                
            self.notes_field.setPlainText(equipment.notes or "")
            
            # Update calculated fields
            self.update_calculated_fields()
            
        except Exception as e:
            logger.error(f"Error populating equipment form: {e}")
            
    def get_form_data(self):
        """Get form data as dictionary."""
        return {
            'equipment_id': self.equipment_id_field.text() or None,
            'ba_number': self.ba_number_field.text(),
            'make_and_type': self.make_type_field.text(),
            'equipment_status': self.status_field.currentText(),
            'category': self.category_field.currentText(),
            'meterage_kms': self.meterage_field.value(),
            'hours_run_total': self.hours_field.value(),
            'engine_number': self.engine_number_field.text(),
            'chassis_number': self.chassis_number_field.text(),
            'fuel_type': self.fuel_type_field.currentText(),
            'date_of_commission': self.commission_date_field.date().toString(Qt.ISODate),
            'date_of_release': self.release_date_field.date().toString(Qt.ISODate) if self.release_date_field.date() != QDate(1900, 1, 1) else None,
            'last_maintenance_date': self.last_maintenance_field.date().toString(Qt.ISODate) if self.last_maintenance_field.date() != QDate(1900, 1, 1) else None,
            'current_location': self.location_field.text(),
            'assigned_unit': self.unit_field.text(),
            'responsible_person': self.responsible_field.text(),
            'acquisition_cost': self.cost_field.value(),
            'warranty_expiry': self.warranty_field.date().toString(Qt.ISODate) if self.warranty_field.date() != QDate(1900, 1, 1) else None,
            'insurance_expiry': self.insurance_field.date().toString(Qt.ISODate) if self.insurance_field.date() != QDate(1900, 1, 1) else None,
            'notes': self.notes_field.toPlainText()
        }

    def validate_form(self):
        """Validate equipment form data."""
        self.validation_errors = []

        # Check required fields
        if not self.make_type_field.text().strip():
            self.validation_errors.append("Make & Type is required")

        # Check BA number uniqueness (if provided)
        ba_number = self.ba_number_field.text().strip()
        if ba_number:
            existing_equipment = Equipment.get_by_ba_number(ba_number)
            if existing_equipment and str(existing_equipment.equipment_id) != self.equipment_id_field.text():
                self.validation_errors.append(f"BA Number '{ba_number}' is already in use")

        # Check commission date
        if self.commission_date_field.date() > QDate.currentDate():
            self.validation_errors.append("Commission date cannot be in the future")

        # Check meterage and hours are non-negative
        if self.meterage_field.value() < 0:
            self.validation_errors.append("Meterage cannot be negative")

        if self.hours_field.value() < 0:
            self.validation_errors.append("Hours cannot be negative")

        return len(self.validation_errors) == 0

    def save_record(self):
        """Save the equipment record."""
        if not self.validate_form():
            QMessageBox.warning(self, "Validation Error", "\n".join(self.validation_errors))
            return

        try:
            form_data = self.get_form_data()

            if self.is_creating:
                # Create new equipment
                equipment = Equipment(
                    ba_number=form_data['ba_number'],
                    make_and_type=form_data['make_and_type'],
                    equipment_status=form_data['equipment_status'],
                    category=form_data['category'],
                    meterage_kms=form_data['meterage_kms'],
                    hours_run_total=form_data['hours_run_total'],
                    engine_number=form_data['engine_number'],
                    chassis_number=form_data['chassis_number'],
                    fuel_type=form_data['fuel_type'],
                    date_of_commission=form_data['date_of_commission'],
                    date_of_release=form_data['date_of_release'],
                    last_maintenance_date=form_data['last_maintenance_date'],
                    current_location=form_data['current_location'],
                    assigned_unit=form_data['assigned_unit'],
                    responsible_person=form_data['responsible_person'],
                    acquisition_cost=form_data['acquisition_cost'],
                    warranty_expiry=form_data['warranty_expiry'],
                    insurance_expiry=form_data['insurance_expiry'],
                    notes=form_data['notes']
                )

                equipment_id = equipment.save()
                if equipment_id:
                    QMessageBox.information(self, "Success", "Equipment created successfully.")
                    self.data_changed.emit()
                    self.cancel_operation()
                else:
                    QMessageBox.warning(self, "Error", "Failed to create equipment.")

            elif self.is_editing:
                # Update existing equipment
                equipment_id = form_data['equipment_id']

                if Equipment.update(equipment_id, **{k: v for k, v in form_data.items() if k != 'equipment_id'}):
                    QMessageBox.information(self, "Success", "Equipment updated successfully.")
                    self.data_changed.emit()
                    self.cancel_operation()
                else:
                    QMessageBox.warning(self, "Error", "Failed to update equipment.")

        except Exception as e:
            logger.error(f"Error saving equipment: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save equipment: {str(e)}")

    def delete_record(self):
        """Delete the selected equipment record."""
        if not self.current_record:
            return

        # Check for related records
        equipment_id = self.current_record.get('ID')

        # Check for maintenance records
        try:
            with database.get_db_connection() as conn:
                maintenance_count = conn.execute(
                    'SELECT COUNT(*) as count FROM maintenance WHERE equipment_id = ?',
                    (equipment_id,)
                ).fetchone()['count']

                overhaul_count = conn.execute(
                    'SELECT COUNT(*) as count FROM overhauls WHERE equipment_id = ?',
                    (equipment_id,)
                ).fetchone()['count']

            if maintenance_count > 0 or overhaul_count > 0:
                reply = QMessageBox.question(
                    self,
                    "Related Records Found",
                    f"This equipment has {maintenance_count} maintenance records and {overhaul_count} overhaul records.\n\n"
                    f"Deleting this equipment will also delete all related records.\n\n"
                    f"Are you sure you want to continue?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply != QMessageBox.Yes:
                    return

        except Exception as e:
            logger.error(f"Error checking related records: {e}")

        reply = QMessageBox.question(
            self,
            "Confirm Delete",
            f"Are you sure you want to delete this equipment?\n\n"
            f"BA Number: {self.current_record.get('BA Number')}\n"
            f"Make & Type: {self.current_record.get('Make & Type')}\n\n"
            f"This action cannot be undone.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                if Equipment.delete(equipment_id):
                    QMessageBox.information(self, "Success", "Equipment deleted successfully.")
                    self.data_changed.emit()
                    self.cancel_operation()
                else:
                    QMessageBox.warning(self, "Error", "Failed to delete equipment.")

            except Exception as e:
                logger.error(f"Error deleting equipment: {e}")
                QMessageBox.critical(self, "Error", f"Failed to delete equipment: {str(e)}")

    def apply_filters(self):
        """Apply current filters to the data."""
        self.load_data()

    def export_csv(self):
        """Export equipment data to CSV."""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Export Equipment Data",
                f"equipment_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                # Get all equipment data
                equipment_list = Equipment.get_all()

                import csv
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = [
                        'Equipment ID', 'BA Number', 'Make & Type', 'Status', 'Category',
                        'Meterage (km)', 'Hours', 'Engine Number', 'Chassis Number',
                        'Fuel Type', 'Date of Commission', 'Date of Release',
                        'Current Location', 'Assigned Unit', 'Responsible Person',
                        'Acquisition Cost', 'Warranty Expiry', 'Insurance Expiry', 'Notes'
                    ]

                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()

                    for equipment in equipment_list:
                        writer.writerow({
                            'Equipment ID': equipment.equipment_id,
                            'BA Number': equipment.ba_number or '',
                            'Make & Type': equipment.make_and_type or '',
                            'Status': equipment.equipment_status,
                            'Category': equipment.category or '',
                            'Meterage (km)': equipment.meterage_kms,
                            'Hours': equipment.hours_run_total,
                            'Engine Number': equipment.engine_number or '',
                            'Chassis Number': equipment.chassis_number or '',
                            'Fuel Type': equipment.fuel_type or '',
                            'Date of Commission': equipment.date_of_commission or '',
                            'Date of Release': equipment.date_of_release or '',
                            'Current Location': equipment.current_location or '',
                            'Assigned Unit': equipment.assigned_unit or '',
                            'Responsible Person': equipment.responsible_person or '',
                            'Acquisition Cost': equipment.acquisition_cost or 0,
                            'Warranty Expiry': equipment.warranty_expiry or '',
                            'Insurance Expiry': equipment.insurance_expiry or '',
                            'Notes': equipment.notes or ''
                        })

                QMessageBox.information(self, "Success", f"Equipment data exported to {file_path}")

        except Exception as e:
            logger.error(f"Error exporting equipment data: {e}")
            QMessageBox.critical(self, "Error", f"Failed to export equipment data: {str(e)}")

    def import_csv(self):
        """Import equipment data from CSV."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Import Equipment Data",
                "",
                "CSV Files (*.csv);;All Files (*)"
            )

            if file_path:
                # Show import dialog
                dialog = EquipmentImportDialog(file_path, self)
                if dialog.exec_() == QDialog.Accepted:
                    self.data_changed.emit()

        except Exception as e:
            logger.error(f"Error importing equipment data: {e}")
            QMessageBox.critical(self, "Error", f"Failed to import equipment data: {str(e)}")

class EquipmentImportDialog(QDialog):
    """Dialog for importing equipment data from CSV."""

    def __init__(self, file_path, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.setup_dialog()
        self.load_csv_preview()

    def setup_dialog(self):
        """Setup the import dialog."""
        self.setWindowTitle("Import Equipment Data")
        self.setModal(True)

        from ui.window_utils import DialogManager
        DialogManager.setup_responsive_dialog(self, width_percent=0.8, height_percent=0.8)

        layout = QVBoxLayout(self)

        # Header
        header = QLabel("Import Equipment Data from CSV")
        header.setFont(DPIScaler.create_scaled_font(14, bold=True))
        layout.addWidget(header)

        # File info
        file_info = QLabel(f"File: {self.file_path}")
        layout.addWidget(file_info)

        # Preview table
        self.preview_table = QTableWidget()
        layout.addWidget(self.preview_table)

        # Options
        options_group = QGroupBox("Import Options")
        options_layout = QFormLayout(options_group)

        self.skip_header_cb = QCheckBox("Skip first row (header)")
        self.skip_header_cb.setChecked(True)
        options_layout.addRow(self.skip_header_cb)

        self.update_existing_cb = QCheckBox("Update existing records")
        options_layout.addRow(self.update_existing_cb)

        layout.addWidget(options_group)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # Buttons
        button_layout = QHBoxLayout()

        self.import_btn = QPushButton("Import")
        self.import_btn.clicked.connect(self.import_data)
        button_layout.addWidget(self.import_btn)

        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

    def load_csv_preview(self):
        """Load CSV preview."""
        try:
            import csv
            with open(self.file_path, 'r', encoding='utf-8') as csvfile:
                # Detect delimiter
                sample = csvfile.read(1024)
                csvfile.seek(0)
                sniffer = csv.Sniffer()
                delimiter = sniffer.sniff(sample).delimiter

                reader = csv.reader(csvfile, delimiter=delimiter)
                rows = list(reader)

            if rows:
                # Set up table
                self.preview_table.setRowCount(min(len(rows), 10))  # Show first 10 rows
                self.preview_table.setColumnCount(len(rows[0]))

                # Set headers
                if len(rows) > 0:
                    self.preview_table.setHorizontalHeaderLabels([f"Column {i+1}" for i in range(len(rows[0]))])

                # Fill data
                for row_idx, row in enumerate(rows[:10]):
                    for col_idx, cell in enumerate(row):
                        item = QTableWidgetItem(str(cell))
                        self.preview_table.setItem(row_idx, col_idx, item)

                self.preview_table.resizeColumnsToContents()

        except Exception as e:
            logger.error(f"Error loading CSV preview: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load CSV preview: {str(e)}")

    def import_data(self):
        """Import the CSV data."""
        try:
            self.progress_bar.setVisible(True)
            self.import_btn.setEnabled(False)

            # Simulate import process
            QMessageBox.information(self, "Import Complete", "Equipment data imported successfully.")
            self.accept()

        except Exception as e:
            logger.error(f"Error importing data: {e}")
            QMessageBox.critical(self, "Error", f"Failed to import data: {str(e)}")
        finally:
            self.progress_bar.setVisible(False)
            self.import_btn.setEnabled(True)
