"""Main window for the equipment inventory application."""
from PyQt5.QtWidgets import (QMainWindow, QTabWidget, QAction, QMenuBar, QStatusBar,
                            QMessageBox, QFileDialog, QSplitter, QVBoxLayout, QWidget)
from ui.excel_format_converter import ExcelFormatConverterDialog
from PyQt5.QtGui import QIcon, QKeySequence
from PyQt5.QtCore import Qt, QSize

import config
from ui.dashboard_widget import DashboardWidget
from ui.equipment_widget import EquipmentWidget
from ui.fluids_widget import FluidsWidget
from ui.maintenance_widget import MaintenanceWidget
from ui.repairs_widget import OverhaulWidget
from ui.medium_reset_widget import MediumResetWidget
from ui.discrepancies_widget import DiscrepanciesWidget
from ui.discard_criteria_widget import DiscardCriteriaWidget
from ui.tyre_maintenance_widget import TyreMaintenanceWidget
from ui.demand_forecast_widget import DemandForecastWidget

class MainWindow(QMainWindow):
    """Main window of the application."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Equipment Inventory Management")
        self.resize(config.MAIN_WINDOW_WIDTH, config.MAIN_WINDOW_HEIGHT)
        self.setMinimumSize(config.MAIN_WINDOW_MIN_WIDTH, config.MAIN_WINDOW_MIN_HEIGHT)
        
        # Enable window state saving/restoring
        self.setObjectName("MainWindow")
        
        # Create central widget with tab interface
        self.tab_widget = QTabWidget()
        self.setCentralWidget(self.tab_widget)
        
        # Initialize UI
        self.init_ui()
        
        # Set status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Application ready", 3000)
    
    def init_ui(self):
        """Initialize the user interface."""
        # Create menus
        self.create_menus()
        
        # Add tabs
        self.add_dashboard_tab()
        self.add_equipment_tab()
        self.add_fluids_tab()
        self.add_maintenance_tab()
        self.add_overhaul_tab()
        self.add_medium_reset_tab()
        self.add_discrepancies_tab()
        self.add_discard_criteria_tab()
        self.add_tyre_maintenance_tab()
        self.add_demand_forecast_tab()
    
    def create_menus(self):
        """Create application menus."""
        # File menu
        file_menu = self.menuBar().addMenu("&File")
        
        # Export action
        export_action = QAction("&Export Data...", self)
        export_action.setShortcut(QKeySequence.Save)
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)
        
        # Convert Excel for Import action
        convert_action = QAction("Convert Excel for Import...", self)
        convert_action.setShortcut(QKeySequence("Ctrl+Shift+C"))
        convert_action.triggered.connect(self.open_excel_format_converter)
        file_menu.addAction(convert_action)

        # Import action
        import_action = QAction("&Import Data...", self)
        import_action.setShortcut(QKeySequence.Open)
        import_action.triggered.connect(self.import_data)
        file_menu.addAction(import_action)
        
        file_menu.addSeparator()
        
        # Exit action
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Help menu
        help_menu = self.menuBar().addMenu("&Help")
        
        # About action
        about_action = QAction("&About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def add_dashboard_tab(self):
        """Add dashboard tab to the interface."""
        dashboard = DashboardWidget()
        self.tab_widget.addTab(dashboard, "Dashboard")
    
    def add_equipment_tab(self):
        """Add equipment tab to the interface."""
        equipment = EquipmentWidget()
        self.tab_widget.addTab(equipment, "Equipment")
    
    def add_fluids_tab(self):
        """Add fluids tab to the interface."""
        fluids = FluidsWidget()
        self.tab_widget.addTab(fluids, "Fluids")
    
    def add_maintenance_tab(self):
        """Add maintenance tab to the interface."""
        maintenance = MaintenanceWidget()
        self.tab_widget.addTab(maintenance, "Maintenance")
    
    def add_overhaul_tab(self):
        """Add overhaul tab to the interface."""
        overhaul = OverhaulWidget()
        self.tab_widget.addTab(overhaul, "Overhaul")
    
    def add_medium_reset_tab(self):
        """Add medium reset tab to the interface."""
        medium_reset = MediumResetWidget()
        self.tab_widget.addTab(medium_reset, "Medium Reset")
    
    def add_discrepancies_tab(self):
        """Add discrepancies tab to the interface."""
        discrepancies = DiscrepanciesWidget()
        self.tab_widget.addTab(discrepancies, "Discrepancies")
    
    def add_discard_criteria_tab(self):
        """Add discard criteria tab to the interface."""
        discard_criteria = DiscardCriteriaWidget()
        self.tab_widget.addTab(discard_criteria, "Discard Criteria")
    
    def add_tyre_maintenance_tab(self):
        """Add tyre maintenance tab to the interface."""
        tyre_maintenance = TyreMaintenanceWidget()
        self.tab_widget.addTab(tyre_maintenance, "Tyre Maintenance")
    
    def add_demand_forecast_tab(self):
        """Add demand forecast tab to the interface."""
        demand_forecast = DemandForecastWidget()
        self.tab_widget.addTab(demand_forecast, "Demand Forecast")
    
    def open_excel_format_converter(self):
        dialog = ExcelFormatConverterDialog(self)
        dialog.exec_()

    def export_data(self):
        """Export data to CSV files."""
        export_dir = QFileDialog.getExistingDirectory(
            self, "Select Export Directory", "",
            QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
        )
        
        if not export_dir:
            return
        
        try:
            # Call export methods on each widget
            # This would need to be implemented in each widget class
            for i in range(self.tab_widget.count()):
                widget = self.tab_widget.widget(i)
                if hasattr(widget, 'export_data'):
                    widget.export_data(export_dir)
            
            QMessageBox.information(self, "Export Successful", 
                                  f"Data exported successfully to {export_dir}")
            
        except Exception as e:
            QMessageBox.critical(self, "Export Error", 
                                f"Error exporting data: {str(e)}")
    
    def import_data(self):
        """Import data from CSV files."""
        import_dir = QFileDialog.getExistingDirectory(
            self, "Select Import Directory", "",
            QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
        )
        
        if not import_dir:
            return
        
        # Confirm import
        confirm = QMessageBox.question(
            self, "Confirm Import", 
            "Importing data may overwrite existing records. Continue?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if confirm != QMessageBox.Yes:
            return
        
        try:
            # Call import methods on each widget
            # This would need to be implemented in each widget class
            for i in range(self.tab_widget.count()):
                widget = self.tab_widget.widget(i)
                if hasattr(widget, 'import_data'):
                    widget.import_data(import_dir)
            
            QMessageBox.information(self, "Import Successful", 
                                  "Data imported successfully")
            
        except Exception as e:
            QMessageBox.critical(self, "Import Error", 
                                f"Error importing data: {str(e)}")
    
    def show_about(self):
        """Show about dialog."""
        QMessageBox.about(
            self, 
            "About " + config.APP_NAME,
            f"<h2>{config.APP_NAME} v{config.APP_VERSION}</h2>"
            f"<p>A comprehensive equipment inventory management system</p>"
            f"<p>Developed by {config.ORGANIZATION}</p>"
            f"<p>© 2023 All rights reserved</p>"
        )
    
    def closeEvent(self, event):
        """Handle window close event."""
        confirm = QMessageBox.question(
            self, "Confirm Exit", 
            "Are you sure you want to exit the application?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if confirm == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()

    def show_status_message(self, message, timeout=3000):
        """Show a message in the status bar."""
        self.status_bar.showMessage(message, timeout)
