"""
Responsive CRUD Framework for PROJECT-ALPHA
Provides standardized, responsive UI components for Create, Read, Update, Delete operations
optimized for military deployment on 1366x768 displays.
"""

import logging
from datetime import datetime, date
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
    QDateEdit, QTextEdit, QCheckBox, QGroupBox, QSplitter, QFrame,
    QMessageBox, QDialog, QDialogButtonBox, QScrollArea, QTabWidget,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
    QProgressBar, QToolButton, QMenu, QAction, QFileDialog
)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPixmap, QPalette, QColor

from ui.window_utils import DPIScaler, FormManager, LayoutManager
from ui.custom_widgets import StatusLabel
import utils
import database

logger = logging.getLogger('responsive_crud_framework')

class ResponsiveCRUDWidget(QWidget):
    """
    Base class for responsive CRUD widgets optimized for military deployment.
    Provides standardized layout, styling, and functionality patterns.
    """
    
    # Signals for data operations
    data_changed = pyqtSignal()
    record_selected = pyqtSignal(dict)
    operation_completed = pyqtSignal(str, bool)  # operation_type, success
    
    def __init__(self, table_name, parent=None):
        super().__init__(parent)
        self.table_name = table_name
        self.current_record = None
        self.is_editing = False
        self.is_creating = False
        
        # Cache for performance optimization
        self.data_cache = {}
        self.last_refresh = None
        
        self.setup_responsive_ui()
        self.setup_styling()
        self.connect_signals()
        
    def setup_responsive_ui(self):
        """Setup the main responsive UI layout."""
        # Create main layout with DPI-aware spacing
        main_layout = QHBoxLayout(self)
        LayoutManager.setup_responsive_layout(main_layout, margins=(5, 5, 5, 5), spacing=8)
        
        # Create responsive splitter
        self.splitter = QSplitter(Qt.Horizontal)
        
        # Left panel: Data list and controls
        self.left_panel = self.create_left_panel()
        
        # Right panel: Form and details
        self.right_panel = self.create_right_panel()
        
        # Add panels to splitter with responsive sizing
        self.splitter.addWidget(self.left_panel)
        self.splitter.addWidget(self.right_panel)
        
        # Set responsive splitter sizes based on screen resolution
        screen_info = DPIScaler.get_screen_info()
        if screen_info['width'] <= 1366:
            # For low-resolution displays, give more space to the list
            self.splitter.setSizes([450, 350])
        else:
            # For higher resolutions, balanced layout
            self.splitter.setSizes([500, 400])
        
        main_layout.addWidget(self.splitter)
        
    def create_left_panel(self):
        """Create the left panel with data list and controls."""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout(panel)
        LayoutManager.setup_responsive_layout(layout, margins=(8, 8, 8, 8), spacing=6)
        
        # Header with title and controls
        header_layout = self.create_header_layout()
        layout.addLayout(header_layout)
        
        # Filter and search controls
        filter_layout = self.create_filter_layout()
        layout.addLayout(filter_layout)
        
        # Data table with pagination
        self.data_table = self.create_data_table()
        layout.addWidget(self.data_table)
        
        # Action buttons
        action_layout = self.create_action_buttons()
        layout.addLayout(action_layout)
        
        return panel
        
    def create_right_panel(self):
        """Create the right panel with form and details."""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout(panel)
        LayoutManager.setup_responsive_layout(layout, margins=(8, 8, 8, 8), spacing=6)
        
        # Form header
        self.form_header = QLabel("Record Details")
        self.form_header.setFont(DPIScaler.create_scaled_font(12, bold=True))
        self.form_header.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.form_header)
        
        # Scrollable form area for low-resolution displays
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # Form widget
        self.form_widget = self.create_form_widget()
        scroll_area.setWidget(self.form_widget)
        layout.addWidget(scroll_area)
        
        # Form action buttons
        form_actions = self.create_form_actions()
        layout.addLayout(form_actions)
        
        return panel
        
    def create_header_layout(self):
        """Create the header layout with title and bulk operations."""
        layout = QHBoxLayout()
        
        # Title
        title = QLabel(f"{self.table_name.title()} Management")
        title.setFont(DPIScaler.create_scaled_font(14, bold=True))
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Bulk operations dropdown
        self.bulk_menu = QToolButton()
        self.bulk_menu.setText("Bulk Operations")
        self.bulk_menu.setPopupMode(QToolButton.InstantPopup)
        
        bulk_actions = QMenu()
        bulk_actions.addAction("Bulk Create", self.bulk_create)
        bulk_actions.addAction("Bulk Edit", self.bulk_edit)
        bulk_actions.addAction("Bulk Delete", self.bulk_delete)
        bulk_actions.addSeparator()
        bulk_actions.addAction("Export CSV", self.export_csv)
        bulk_actions.addAction("Import CSV", self.import_csv)
        
        self.bulk_menu.setMenu(bulk_actions)
        layout.addWidget(self.bulk_menu)
        
        # Refresh button
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.clicked.connect(self.refresh_data)
        layout.addWidget(self.refresh_btn)
        
        return layout
        
    def create_filter_layout(self):
        """Create filter and search controls."""
        layout = QGridLayout()
        
        # Search field
        layout.addWidget(QLabel("Search:"), 0, 0)
        self.search_field = QLineEdit()
        self.search_field.setPlaceholderText("Search records...")
        self.search_field.textChanged.connect(self.apply_filters)
        layout.addWidget(self.search_field, 0, 1)
        
        # Status filter
        layout.addWidget(QLabel("Status:"), 0, 2)
        self.status_filter = QComboBox()
        self.status_filter.addItems(["All", "Active", "Inactive", "Completed", "Pending"])
        self.status_filter.currentTextChanged.connect(self.apply_filters)
        layout.addWidget(self.status_filter, 0, 3)
        
        # Date range filter
        layout.addWidget(QLabel("From:"), 1, 0)
        self.date_from = QDateEdit()
        self.date_from.setCalendarPopup(True)
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        layout.addWidget(self.date_from, 1, 1)
        
        layout.addWidget(QLabel("To:"), 1, 2)
        self.date_to = QDateEdit()
        self.date_to.setCalendarPopup(True)
        self.date_to.setDate(QDate.currentDate())
        layout.addWidget(self.date_to, 1, 3)
        
        return layout
        
    def create_data_table(self):
        """Create the main data table with pagination."""
        from ui.paginated_table_widget import PaginatedTableWidget
        
        # Responsive page size based on screen resolution
        screen_info = DPIScaler.get_screen_info()
        if screen_info['height'] <= 768:
            page_size = 50  # Smaller page size for low-resolution displays
        else:
            page_size = 100
            
        table = PaginatedTableWidget(
            page_size=page_size,
            max_total_rows=50000,
            enable_ba_filter=True,
            enable_ba_grouping=True
        )
        
        table.row_selected.connect(self.on_record_selected)
        return table
        
    def create_action_buttons(self):
        """Create action buttons for CRUD operations."""
        layout = QHBoxLayout()
        
        # Create button
        self.create_btn = QPushButton("➕ Create")
        self.create_btn.clicked.connect(self.create_record)
        self.create_btn.setStyleSheet(self.get_button_style("create"))
        layout.addWidget(self.create_btn)
        
        # Edit button
        self.edit_btn = QPushButton("✏️ Edit")
        self.edit_btn.clicked.connect(self.edit_record)
        self.edit_btn.setEnabled(False)
        self.edit_btn.setStyleSheet(self.get_button_style("edit"))
        layout.addWidget(self.edit_btn)
        
        # Delete button
        self.delete_btn = QPushButton("🗑️ Delete")
        self.delete_btn.clicked.connect(self.delete_record)
        self.delete_btn.setEnabled(False)
        self.delete_btn.setStyleSheet(self.get_button_style("delete"))
        layout.addWidget(self.delete_btn)
        
        # View details button
        self.view_btn = QPushButton("👁️ View")
        self.view_btn.clicked.connect(self.view_record)
        self.view_btn.setEnabled(False)
        layout.addWidget(self.view_btn)
        
        return layout
        
    def create_form_widget(self):
        """Create the form widget - to be overridden by subclasses."""
        widget = QWidget()
        layout = QFormLayout(widget)
        FormManager.setup_responsive_form_layout(layout)
        
        # Placeholder form
        self.id_field = QLineEdit()
        self.id_field.setReadOnly(True)
        layout.addRow("ID:", self.id_field)
        
        self.name_field = QLineEdit()
        layout.addRow("Name:", self.name_field)
        
        self.status_field = QComboBox()
        self.status_field.addItems(["Active", "Inactive"])
        layout.addRow("Status:", self.status_field)
        
        return widget
        
    def create_form_actions(self):
        """Create form action buttons."""
        layout = QHBoxLayout()
        
        # Save button
        self.save_btn = QPushButton("💾 Save")
        self.save_btn.clicked.connect(self.save_record)
        self.save_btn.setEnabled(False)
        self.save_btn.setStyleSheet(self.get_button_style("save"))
        layout.addWidget(self.save_btn)
        
        # Cancel button
        self.cancel_btn = QPushButton("❌ Cancel")
        self.cancel_btn.clicked.connect(self.cancel_operation)
        self.cancel_btn.setEnabled(False)
        layout.addWidget(self.cancel_btn)
        
        # History button
        self.history_btn = QPushButton("📋 History")
        self.history_btn.clicked.connect(self.view_history)
        self.history_btn.setEnabled(False)
        layout.addWidget(self.history_btn)
        
        layout.addStretch()
        
        return layout
        
    def setup_styling(self):
        """Setup responsive styling for military deployment."""
        # Main widget styling
        self.setStyleSheet(f"""
            QWidget {{
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: {DPIScaler.scale_font_size(10)}px;
            }}
            
            QGroupBox {{
                font-weight: bold;
                border: {DPIScaler.scale_size(2)}px solid #cccccc;
                border-radius: {DPIScaler.scale_size(5)}px;
                margin-top: {DPIScaler.scale_size(10)}px;
                padding-top: {DPIScaler.scale_size(5)}px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: {DPIScaler.scale_size(10)}px;
                padding: 0 {DPIScaler.scale_size(5)}px 0 {DPIScaler.scale_size(5)}px;
            }}
            
            QFrame {{
                border: {DPIScaler.scale_size(1)}px solid #d0d0d0;
                border-radius: {DPIScaler.scale_size(4)}px;
                background-color: #fafafa;
            }}
        """)
        
    def get_button_style(self, button_type):
        """Get responsive button styling based on type."""
        base_style = f"""
            QPushButton {{
                border: none;
                padding: {DPIScaler.scale_size(8)}px {DPIScaler.scale_size(16)}px;
                border-radius: {DPIScaler.scale_size(4)}px;
                font-weight: bold;
                font-size: {DPIScaler.scale_font_size(10)}px;
                min-height: {DPIScaler.scale_size(32)}px;
            }}
            QPushButton:hover {{
                opacity: 0.8;
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
                color: #666666;
            }}
        """
        
        colors = {
            "create": "background-color: #4CAF50; color: white;",
            "edit": "background-color: #2196F3; color: white;",
            "delete": "background-color: #f44336; color: white;",
            "save": "background-color: #4CAF50; color: white;",
            "view": "background-color: #9E9E9E; color: white;"
        }
        
        return base_style + colors.get(button_type, "background-color: #e0e0e0; color: black;")
        
    def connect_signals(self):
        """Connect internal signals."""
        self.data_changed.connect(self.refresh_data)
        
    # CRUD Operation Methods (to be overridden by subclasses)
    
    def load_data(self):
        """Load data into the table - to be overridden."""
        pass
        
    def create_record(self):
        """Start creating a new record."""
        self.is_creating = True
        self.is_editing = False
        self.current_record = None
        self.clear_form()
        self.set_form_mode("create")
        
    def edit_record(self):
        """Start editing the selected record."""
        if not self.current_record:
            return
        self.is_editing = True
        self.is_creating = False
        self.populate_form(self.current_record)
        self.set_form_mode("edit")
        
    def save_record(self):
        """Save the current record - to be overridden."""
        pass
        
    def delete_record(self):
        """Delete the selected record - to be overridden."""
        pass
        
    def view_record(self):
        """View record details."""
        if not self.current_record:
            return
        self.populate_form(self.current_record)
        self.set_form_mode("view")
        
    def cancel_operation(self):
        """Cancel current operation."""
        self.is_creating = False
        self.is_editing = False
        self.clear_form()
        self.set_form_mode("view")
        
    def refresh_data(self):
        """Refresh the data table."""
        self.load_data()
        self.last_refresh = datetime.now()
        
    def apply_filters(self):
        """Apply current filters to the data."""
        # To be implemented by subclasses
        pass
        
    def on_record_selected(self, record_data):
        """Handle record selection."""
        self.current_record = record_data
        self.record_selected.emit(record_data)
        self.update_button_states()
        
        if not self.is_editing and not self.is_creating:
            self.view_record()
            
    def update_button_states(self):
        """Update button enabled states based on current selection."""
        has_selection = self.current_record is not None
        
        self.edit_btn.setEnabled(has_selection and not self.is_editing and not self.is_creating)
        self.delete_btn.setEnabled(has_selection and not self.is_editing and not self.is_creating)
        self.view_btn.setEnabled(has_selection)
        self.history_btn.setEnabled(has_selection)
        
        self.save_btn.setEnabled(self.is_editing or self.is_creating)
        self.cancel_btn.setEnabled(self.is_editing or self.is_creating)
        
    def set_form_mode(self, mode):
        """Set form mode: 'view', 'edit', or 'create'."""
        is_readonly = mode == "view"
        
        # Enable/disable form fields based on mode
        for widget in self.form_widget.findChildren((QLineEdit, QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox, QTextEdit)):
            if hasattr(widget, 'setReadOnly'):
                widget.setReadOnly(is_readonly)
            else:
                widget.setEnabled(not is_readonly)
                
        # Update header
        mode_text = {
            "view": "View Record",
            "edit": "Edit Record", 
            "create": "Create New Record"
        }
        self.form_header.setText(mode_text.get(mode, "Record Details"))
        
        self.update_button_states()
        
    def clear_form(self):
        """Clear all form fields."""
        for widget in self.form_widget.findChildren((QLineEdit, QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox, QTextEdit)):
            if isinstance(widget, QLineEdit):
                widget.clear()
            elif isinstance(widget, QComboBox):
                widget.setCurrentIndex(0)
            elif isinstance(widget, QDateEdit):
                widget.setDate(QDate.currentDate())
            elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
                widget.setValue(0)
            elif isinstance(widget, QTextEdit):
                widget.clear()
                
    def populate_form(self, record_data):
        """Populate form with record data - to be overridden."""
        pass
        
    # Bulk Operations
    
    def bulk_create(self):
        """Bulk create operation."""
        QMessageBox.information(self, "Bulk Create", "Bulk create functionality to be implemented.")
        
    def bulk_edit(self):
        """Bulk edit operation."""
        QMessageBox.information(self, "Bulk Edit", "Bulk edit functionality to be implemented.")
        
    def bulk_delete(self):
        """Bulk delete operation."""
        QMessageBox.information(self, "Bulk Delete", "Bulk delete functionality to be implemented.")
        
    def export_csv(self):
        """Export data to CSV."""
        QMessageBox.information(self, "Export CSV", "CSV export functionality to be implemented.")
        
    def import_csv(self):
        """Import data from CSV."""
        QMessageBox.information(self, "Import CSV", "CSV import functionality to be implemented.")
        
    def view_history(self):
        """View record history."""
        QMessageBox.information(self, "View History", "History view functionality to be implemented.")
