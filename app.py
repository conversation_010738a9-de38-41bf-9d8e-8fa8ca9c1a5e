"""
Briefcase entry point for PROJECT-ALPHA Equipment Inventory Management System
"""
import sys
import os
import logging

# Setup path for both development and packaged environments
def setup_application_path():
    """Setup application path for proper module loading."""
    if getattr(sys, 'frozen', False):
        # Running as compiled executable (Briefcase/PyInstaller)
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller
            application_path = sys._MEIPASS  # type: ignore
        else:
            # Briefcase
            application_path = os.path.dirname(sys.executable)
    else:
        # Running in development
        application_path = os.path.dirname(os.path.abspath(__file__))
    
    # Add to Python path if not already there
    if application_path not in sys.path:
        sys.path.insert(0, application_path)
    
    return application_path

def setup_error_handling():
    """Setup error handling for packaged application."""
    try:
        # Create logs directory in user data folder
        import tempfile
        import pathlib
        
        log_dir = pathlib.Path(tempfile.gettempdir()) / "InventoryTracker" / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = log_dir / "application.log"
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()  # Also log to console
            ]
        )
        
        logger = logging.getLogger('inventorytracker')
        logger.info(f"Application starting, logs at: {log_file}")
        
        return logger
    except Exception as e:
        print(f"Failed to setup logging: {e}")
        return logging.getLogger('inventorytracker')

def main():
    """Main entry point for the Briefcase application."""
    logger = None
    try:
        # Setup application environment
        app_path = setup_application_path()
        logger = setup_error_handling()
        
        logger.info("=" * 50)
        logger.info("PROJECT-ALPHA Equipment Inventory Management System")
        logger.info("=" * 50)
        logger.info(f"Application path: {app_path}")
        logger.info(f"Python version: {sys.version}")
        logger.info(f"Platform: {sys.platform}")
        
        # Import and run the main application
        logger.info("Importing main application module...")
        from main import main as app_main
        
        logger.info("Starting main application...")
        return app_main()
        
    except ImportError as e:
        error_msg = f"Failed to import main application: {e}"
        if logger:
            logger.error(error_msg)
        print(error_msg)
        
        # Show user-friendly error dialog if possible
        try:
            from PyQt5.QtWidgets import QApplication, QMessageBox
            app = QApplication(sys.argv) if not QApplication.instance() else QApplication.instance()
            QMessageBox.critical(
                None, 
                "Application Error", 
                f"Failed to start the application.\n\n{error_msg}\n\nPlease contact support.",
                QMessageBox.StandardButton.Ok
            )
        except:
            pass
        
        return 1
        
    except Exception as e:
        error_msg = f"Application startup error: {e}"
        if logger:
            logger.error(error_msg)
            logger.error("Full traceback:", exc_info=True)
        print(error_msg)
        
        # Show user-friendly error dialog
        try:
            import traceback
            from PyQt5.QtWidgets import QApplication, QMessageBox
            app = QApplication(sys.argv) if not QApplication.instance() else QApplication.instance()
            
            detailed_error = f"{error_msg}\n\nTechnical details:\n{traceback.format_exc()}"
            QMessageBox.critical(
                None, 
                "Unexpected Error", 
                f"An unexpected error occurred during startup.\n\n{error_msg}\n\nPlease contact support with the log files.",
                QMessageBox.StandardButton.Ok
            )
        except:
            print("Failed to show error dialog")
            import traceback
            traceback.print_exc()
        
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code) 