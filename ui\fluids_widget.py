"""Fluids management widget for the equipment inventory application."""
import logging
from datetime import datetime, date
from datetime import datetime as _dt

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, 
                           QLabel, QPushButton, QLineEdit, QComboBox, 
                           QSpinBox, QDoubleSpinBox, QDateEdit, QCheckBox,
                           QGroupBox, QFormLayout, QMessageBox, QSplitter,
                           QTextEdit)
from PyQt5.QtCore import Qt, QDate

import database
import config
import utils
from models import Equipment, Fluid
from ui.custom_widgets import ReadOnlyTableWidget, StatusLabel

# Configure logger
logger = logging.getLogger('fluids_widget')

class FluidsWidget(QWidget):
    """Widget for managing fluids."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.equipment_cache = {}  # Cache equipment data
        self.setup_ui()

    def update_computed_top_up(self):
        """Update the computed top-up value based on percent and capacity."""
        capacity = self.capacity_field.value()
        percent = self.top_up_percent_field.value()
        computed = round(capacity * percent / 100, 3)
        self.top_up_field.setValue(computed)
        
    def setup_ui(self):
        """Set up the fluids widget UI."""
        # Create main layout
        main_layout = QHBoxLayout(self)
        
        # Create splitter for resizable sections
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Create left panel (fluids list)
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # Create paginated table for fluids list with enhanced BA filtering and grouping
        from ui.paginated_table_widget import PaginatedTableWidget
        self.fluids_table = PaginatedTableWidget(
            page_size=100,
            max_total_rows=50000,
            enable_ba_filter=True,
            enable_ba_grouping=True,
            show_vintage_button=False  # Disable vintage age button for fluids table
        )
        self.fluids_table.row_selected.connect(self.fluid_selected)
        left_layout.addWidget(self.fluids_table)
        
        # Create buttons for fluid actions
        button_layout = QHBoxLayout()
        self.add_button = QPushButton("Add New")
        self.edit_button = QPushButton("Edit")
        self.delete_button = QPushButton("Delete")
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        
        self.add_button.clicked.connect(self.add_fluid)
        self.edit_button.clicked.connect(self.edit_fluid)
        self.delete_button.clicked.connect(self.delete_fluid)
        
        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.edit_button)
        button_layout.addWidget(self.delete_button)
        left_layout.addLayout(button_layout)
        
        # Create right panel (fluid details)
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Add fluid details form
        self.create_fluid_form(right_layout)
        
        # Add panels to splitter
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        
        # Set initial sizes
        splitter.setSizes([400, 600])
        
        # Add splitter to main layout
        main_layout.addWidget(splitter)
    
    def create_fluid_form(self, parent_layout):
        """Create the fluid details form."""
        # Create form group
        form_group = QGroupBox("Fluid Details")
        form_layout = QFormLayout()
        
        # Create form fields
        self.id_field = QLineEdit()
        self.id_field.setReadOnly(True)
        
        self.equipment_field = QComboBox()
        
        self.assembly_field = QLineEdit()
        
        self.accounting_unit_field = QComboBox()
        self.accounting_unit_field.addItems(["Ltr", "Kg", "Gal", "Qt"])
        
        self.capacity_field = QDoubleSpinBox()
        self.capacity_field.setMinimum(0)
        self.capacity_field.setMaximum(1000)
        self.capacity_field.setDecimals(2)
        self.capacity_field.setSuffix(" units")
        
        self.top_up_percent_field = QDoubleSpinBox()
        self.top_up_percent_field.setMinimum(0)
        self.top_up_percent_field.setMaximum(100)
        self.top_up_percent_field.setDecimals(2)
        self.top_up_percent_field.setSuffix(" %")
        self.top_up_percent_field.setValue(config.DEFAULT_TOP_UP_PERCENT)
        self.top_up_percent_field.valueChanged.connect(self.update_computed_top_up)

        self.top_up_field = QDoubleSpinBox()
        self.top_up_field.setMinimum(0)
        self.top_up_field.setMaximum(1000)
        self.top_up_field.setDecimals(2)
        self.top_up_field.setSuffix(" units")
        self.top_up_field.setReadOnly(True)

        self.grade_field = QLineEdit()
        
        self.date_of_change_field = QDateEdit()
        self.date_of_change_field.setCalendarPopup(True)
        self.date_of_change_field.setSpecialValueText("Not Set")
        self.date_of_change_field.setDate(QDate.currentDate())
        
        # Add fields to form
        form_layout.addRow("ID:", self.id_field)
        self.equipment_field.setMinimumWidth(350)
        self.equipment_field.setMaximumWidth(500)
        self.equipment_field.setMaxVisibleItems(10)  # Limit visible items to prevent huge dropdown
        form_layout.addRow("Make & Type:", self.equipment_field)
        form_layout.addRow("Assembly:", self.assembly_field)
        form_layout.addRow("Accounting Unit:", self.accounting_unit_field)
        form_layout.addRow("Capacity:", self.capacity_field)
        form_layout.addRow("Top-up Percent:", self.top_up_percent_field)
        form_layout.addRow("Computed Top-up:", self.top_up_field)
        form_layout.addRow("Grade:", self.grade_field)
        form_layout.addRow("Date of Change:", self.date_of_change_field)
        
        form_group.setLayout(form_layout)
        
        # Create buttons for form actions
        button_layout = QHBoxLayout()
        self.save_button = QPushButton("Save")
        self.cancel_button = QPushButton("Cancel")
        
        self.save_button.clicked.connect(self.save_fluid)
        self.cancel_button.clicked.connect(self.cancel_edit)
        
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        
        # Add form and buttons to parent layout
        parent_layout.addWidget(form_group)
        parent_layout.addLayout(button_layout)
        
        # Apply combobox hover fix to equipment dropdown
        from ui.common_styles import apply_combobox_hover_fix
        apply_combobox_hover_fix(self.equipment_field)
        apply_combobox_hover_fix(self.accounting_unit_field)
        
        # Initially disable all fields
        self.set_form_enabled(False)
    
    def load_data(self):
        """Load fluids data."""
        logger.info("Loading fluids data")
        try:
            # Get all fluids
            self.fluids_list = Fluid.get_all() or []
            # Load equipment data for form dropdown
            self.load_equipment_data()
            # Prepare data for enhanced table with built-in filtering
            self.update_fluids_table()
            logger.info("Fluids data loaded successfully")
        except Exception as e:
            logger.error(f"Error loading fluids data: {e}")



    def update_fluids_table(self):
        """Update the fluids table with all data (filtering handled by enhanced table)."""
        try:
            # Add BA Number to headers and keep ID for functionality but will hide it
            headers = ["ID", "BA Number", "Make & Type", "Assembly", "Capacity", "Unit", "Grade", "Date of Change"]
            data = []

            # Prepare all data (no filtering here - let the enhanced table handle it)
            for fluid in self.fluids_list:
                row_data = {
                    "ID": fluid['fluid_id'],
                    "BA Number": fluid.get('ba_number') or "",
                    "Make & Type": fluid.get('make_and_type', ''),
                    "Assembly": fluid['fluid_type'] or "",
                    "Capacity": f"{float(fluid['capacity_ltrs_kg']):.2f}" if fluid['capacity_ltrs_kg'] is not None else "0.00",
                    "Unit": fluid['accounting_unit'] or "Ltr",
                    "Grade": fluid.get('grade') or "",
                    "Date of Change": utils.format_date_for_display(fluid.get('date_of_change')) if fluid.get('date_of_change') else ""
                }
                data.append(row_data)

            # Sort by BA Number, then Make & Type
            data.sort(key=lambda x: (x["BA Number"], x["Make & Type"]))

            # Set data in enhanced table (with built-in filtering)
            self.fluids_table.set_data(headers, data, id_column=0)

            # Hide the ID column
            self.fluids_table.setColumnHidden(0, True)

        except Exception as e:
            logger.error(f"Error updating fluids table: {e}")
            QMessageBox.critical(self, "Table Error", f"An error occurred while updating the fluids table:\n{str(e)}", QMessageBox.StandardButton.Ok)

    
    def load_equipment_data(self):
        """Load equipment data for dropdown."""
        try:
            # Get active equipment
            equipment_list = Equipment.get_active()
            
            # Clear dropdown
            self.equipment_field.clear()
            
            # Add equipment to dropdown and cache
            if equipment_list:
                self.equipment_cache = {}
                for equipment in equipment_list:
                    equipment_id = equipment['equipment_id']
                    # Use proper formatting function to show BA numbers correctly
                    from utils import format_equipment_for_dropdown
                    equipment_name = format_equipment_for_dropdown(equipment)
                    self.equipment_field.addItem(equipment_name, equipment_id)
                    self.equipment_cache[equipment_id] = equipment
        except Exception as e:
            logger.error(f"Error loading equipment data: {e}")
    


    
    def fluid_selected(self, row_data):
        """Handle fluid selection from paginated table."""
        try:
            if row_data and 'ID' in row_data:
                fluid_id = row_data['ID']

                # Enable edit and delete buttons
                self.edit_button.setEnabled(True)
                self.delete_button.setEnabled(True)

                # Load fluid details
                self.load_fluid_details(fluid_id)
            else:
                # Disable edit and delete buttons
                self.edit_button.setEnabled(False)
                self.delete_button.setEnabled(False)

                # Clear form
                self.clear_form()
        except Exception as e:
            logger.error(f"Error in fluid selection: {e}")
    
    def load_fluid_details(self, fluid_id):
        """Load fluid details into the form."""
        # Get fluid data
        fluid = Fluid.get_by_id(fluid_id)
        
        if fluid:
            # Set form values
            self.id_field.setText(str(fluid['fluid_id']))
            
            # Set equipment dropdown
            equipment_id = fluid['equipment_id']
            index = self.equipment_field.findData(equipment_id)
            if index >= 0:
                self.equipment_field.setCurrentIndex(index)
            
            self.assembly_field.setText(fluid['fluid_type'] or "")
            
            # Set accounting unit dropdown
            index = self.accounting_unit_field.findText(fluid['accounting_unit'])
            if index >= 0:
                self.accounting_unit_field.setCurrentIndex(index)
            
            self.capacity_field.setValue(fluid['capacity_ltrs_kg'] or 0)
            # Set percent and computed top-up
            percent = fluid.get('top_up_percent')
            if percent is not None:
                self.top_up_percent_field.setValue(percent)
            else:
                # Fallback to legacy or default
                legacy = fluid.get('addl_10_percent_top_up')
                cap = fluid.get('capacity_ltrs_kg') or 0
                if legacy and cap:
                    self.top_up_percent_field.setValue(round((legacy / cap) * 100, 2))
                else:
                    self.top_up_percent_field.setValue(config.DEFAULT_TOP_UP_PERCENT)
            self.update_computed_top_up()
            self.grade_field.setText(fluid['grade'] or "")
            
            # Set date of change
            if fluid.get('date_of_change'):
                try:
                    if isinstance(fluid['date_of_change'], str):
                        # Parse ISO format date
                        date_obj = datetime.fromisoformat(fluid['date_of_change']).date()
                    else:
                        date_obj = fluid['date_of_change']
                    self.date_of_change_field.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))
                except Exception as e:
                    logger.warning(f"Error parsing date of change: {e}")
                    self.date_of_change_field.clear()
            else:
                self.date_of_change_field.clear()
            
            # Set form fields to read-only
            self.set_form_enabled(False)
    
    def clear_form(self):
        """Clear the fluid form."""
        self.id_field.clear()
        self.equipment_field.setCurrentIndex(-1)
        self.assembly_field.clear()
        self.accounting_unit_field.setCurrentIndex(0)  # Default to Ltr
        self.capacity_field.setValue(0)
        self.top_up_percent_field.setValue(config.DEFAULT_TOP_UP_PERCENT)
        self.top_up_field.setValue(0)
        self.grade_field.clear()
        self.date_of_change_field.clear()
    
    def set_form_enabled(self, enabled):
        """Enable or disable form fields."""
        self.equipment_field.setEnabled(enabled)
        self.assembly_field.setReadOnly(not enabled)
        self.accounting_unit_field.setEnabled(enabled)
        self.capacity_field.setReadOnly(not enabled)
        self.top_up_percent_field.setReadOnly(not enabled)
        self.grade_field.setReadOnly(not enabled)
        self.date_of_change_field.setReadOnly(not enabled)
        
        self.save_button.setEnabled(enabled)
        self.cancel_button.setEnabled(enabled)
    
    def add_fluid(self):
        """Add new fluid."""
        # Clear form
        self.clear_form()
        
        # Enable form fields
        self.set_form_enabled(True)
        
        # Focus on first field
        self.assembly_field.setFocus()
        self.update_computed_top_up()
    
    def edit_fluid(self):
        """Edit selected fluid."""
        # Enable form fields
        self.set_form_enabled(True)
        
        # Focus on first editable field
        self.assembly_field.setFocus()
        self.update_computed_top_up()
    
    def delete_fluid(self):
        """Delete selected fluid."""
        fluid_id = self.id_field.text()
        
        if not fluid_id:
            return
        
        # Confirm deletion
        confirm = QMessageBox.question(
            self,
            "Confirm Deletion",
            "Are you sure you want to delete this fluid record?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if confirm == QMessageBox.StandardButton.Yes:
            try:
                # Delete fluid
                result = Fluid.delete(int(fluid_id))
                
                if result:
                    # Show success message
                    QMessageBox.information(
                        self,
                        "Success",
                        "Fluid deleted successfully.",
                        QMessageBox.StandardButton.Ok
                    )
                    
                    # Reload data
                    self.load_data()
                    
                    # Clear form
                    self.clear_form()
                    
                    # Disable edit and delete buttons
                    self.edit_button.setEnabled(False)
                    self.delete_button.setEnabled(False)
                else:
                    # Show error message
                    QMessageBox.critical(
                        self,
                        "Error",
                        "Failed to delete fluid. Please try again.",
                        QMessageBox.StandardButton.Ok
                    )
            except Exception as e:
                logger.error(f"Error deleting fluid: {e}")
                QMessageBox.critical(
                    self,
                    "Error",
                    f"An error occurred: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
    
    def save_fluid(self):
        """Save fluid data."""
        # Get form values
        fluid_id = self.id_field.text()
        equipment_id = self.equipment_field.currentData()
        assembly = self.assembly_field.text()
        accounting_unit = self.accounting_unit_field.currentText()
        capacity_ltrs_kg = self.capacity_field.value()
        top_up_percent = self.top_up_percent_field.value()
        computed_top_up = round(capacity_ltrs_kg * top_up_percent / 100, 3)
        grade = self.grade_field.text()
        
        # Get date of change
        date_of_change = None
        if not self.date_of_change_field.date().isNull():
            date_obj = self.date_of_change_field.date().toPyDate()
            date_of_change = date_obj.isoformat()
        
        # Validate form
        if not equipment_id or not assembly:
            QMessageBox.warning(
                self,
                "Validation Error",
                "Equipment and Assembly are required fields.",
                QMessageBox.StandardButton.Ok
            )
            return
        
        try:
            # Audit log
            logger.info(f"Saving fluid: id={fluid_id}, eq_id={equipment_id}, assembly={assembly}, unit={accounting_unit}, capacity={capacity_ltrs_kg}, top_up_percent={top_up_percent}, computed_top_up={computed_top_up}, grade={grade}")
            # Create fluid object
            fluid = Fluid(
                fluid_id=int(fluid_id) if fluid_id else None,
                equipment_id=equipment_id,
                fluid_type=assembly,
                accounting_unit=accounting_unit,
                capacity_ltrs_kg=capacity_ltrs_kg,
                addl_10_percent_top_up=computed_top_up,  # For backward compatibility
                top_up_percent=top_up_percent,
                grade=grade,
                date_of_change=date_of_change
            )
            
            # Save to database
            result = fluid.save()
            
            if result:
                QMessageBox.information(
                    self,
                    "Success",
                    "Fluid saved successfully.",
                    QMessageBox.StandardButton.Ok
                )
                self.load_data()
                self.load_fluid_details(result)
                self.set_form_enabled(False)
                self.edit_button.setEnabled(True)
                self.delete_button.setEnabled(True)
            else:
                QMessageBox.critical(
                    self,
                    "Error",
                    "Failed to save fluid. Please try again.",
                    QMessageBox.StandardButton.Ok
                )
        except Exception as e:
            logger.error(f"Error saving fluid: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"An error occurred: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
    
    def cancel_edit(self):
        """Cancel fluid edit."""
        # Get current fluid ID
        fluid_id = self.id_field.text()
        
        if fluid_id:
            # Reload fluid details
            self.load_fluid_details(int(fluid_id))
        else:
            # Clear form
            self.clear_form()
        
        # Disable form fields
        self.set_form_enabled(False)