from database import DB_PATH, dict_factory, init_db
import os
import sys
import sqlite3
from pathlib import Path
import traceback
import logging

# Ensure project root is in sys.path for both development and executable
if getattr(sys, 'frozen', False):
    # Running as compiled executable
    project_root = os.path.dirname(sys.executable)
else:
    # Running in development
    project_root = os.path.dirname(os.path.abspath(__file__))

if project_root not in sys.path:
    sys.path.insert(0, project_root)

def ensure_db_ready():
    """Ensures the database is ready. Returns (success, first_run, corruption_recovered, error_message)"""
    corruption_recovered = False
    error_message = None
    try:
        db_dir = Path(DB_PATH).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        first_run = not os.path.exists(DB_PATH)
        
        # Try a basic connection with better error handling
        try:
            conn = sqlite3.connect(DB_PATH, check_same_thread=False, timeout=30.0)
            conn.row_factory = dict_factory
            conn.execute("PRAGMA foreign_keys = ON")
            # Test the connection with a simple query
            conn.execute("SELECT 1").fetchone()
            conn.close()
        except sqlite3.DatabaseError as db_err:
            # Corruption detected
            corruption_recovered = True
            error_message = f"Database file was corrupted and has been backed up. Details: {db_err}"
            corrupt_path = DB_PATH + ".corrupt.bak"
            if os.path.exists(DB_PATH):
                try:
                    os.rename(DB_PATH, corrupt_path)
                except OSError:
                    os.remove(DB_PATH)  # If rename fails, just delete
            # Create new DB
            conn = sqlite3.connect(DB_PATH, check_same_thread=False, timeout=30.0)
            conn.row_factory = dict_factory
            conn.execute("PRAGMA foreign_keys = ON")
            conn.close()
        
        # Now run full init
        success, _ = init_db()
        if not success:
            error_message = "init_db() failed unexpectedly. See db_debug.log for details."
            
        # Initialize policy tables if policy_service is available
        policy_init_success = False
        try:
            # Handle both development and executable environments
            if getattr(sys, 'frozen', False):
                # In executable, modules are already loaded
                import policy_service
                policy_service.ensure_tables_exist()
                policy_init_success = True
            else:
                # In development, ensure the module path is correct
                current_dir = os.path.dirname(os.path.abspath(__file__))
                if current_dir not in sys.path:
                    sys.path.insert(0, current_dir)
                    
                # Now try importing the policy_service module
                import policy_service
                policy_service.ensure_tables_exist()
                policy_init_success = True
                
        except ImportError as e:
            logging.warning(f"Policy module not available during database initialization. Some features will be disabled. Error: {e}")
            # This is not a critical failure - the app can work without policy features
        except Exception as e:
            logging.error(f"Error initializing policy tables: {e}")
            # Also not critical - log but continue
            
        # If policy initialization failed but basic DB is working, still consider it a success
        return success, first_run, corruption_recovered, error_message
        
    except Exception as e:
        tb = traceback.format_exc()
        error_msg = f"Database initialization failed: {str(e)}\n{tb}"
        logging.error(error_msg)
        return False, False, False, error_msg
