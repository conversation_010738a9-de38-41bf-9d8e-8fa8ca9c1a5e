"""Custom widgets for the equipment inventory application."""
import logging
from datetime import datetime, date, timedelta
import io

from PyQt5.QtWidgets import (QLabel, QFrame, QPushButton, QVBoxLayout, 
                           QHBoxLayout, QWidget, QTableWidget, QTableWidgetItem,
                           QHeaderView, QScrollArea)
from PyQt5.QtGui import QColor, QPalette, QFont
from PyQt5.QtCore import Qt, pyqtSignal

import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.cm as cm

import utils

# Configure logger
logger = logging.getLogger('custom_widgets')

class StatusLabel(QLabel):
    """Label that changes appearance based on status."""
    
    def __init__(self, text="", status="normal", parent=None):
        """Initialize a StatusLabel instance.
        
        Args:
            text (str): The label text
            status (str): The status - one of "normal", "warning", "critical", "inactive", "unknown"
            parent: The parent widget
        """
        super().__init__(text, parent)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setMinimumWidth(100)
        self.setStatus(status)
    
    def setStatus(self, status):
        """Set the status and update styling."""
        color = utils.get_status_color(status)
        
        self.setStyleSheet(f"""
            QLabel {{
                background-color: {color};
                color: white;
                border-radius: 5px;
                padding: 3px;
                font-weight: bold;
            }}
        """)

class DashboardTile(QFrame):
    """Widget for displaying a single dashboard metric."""
    
    def __init__(self, title, count, status="normal", parent=None):
        """Initialize a DashboardTile instance.
        
        Args:
            title (str): The tile title
            count (int): The count to display
            status (str): The status - one of "normal", "warning", "critical", "inactive", "unknown"
            parent: The parent widget
        """
        super().__init__(parent)
        
        # Set frame style
        self.setFrameShape(QFrame.Shape.StyledPanel)
        self.setFrameShadow(QFrame.Shadow.Raised)
        self.setLineWidth(1)
        
        # Apply status color
        color = utils.get_status_color(status)
        
        self.setStyleSheet(f"""
            QFrame {{
                border: 1px solid #ccc;
                border-radius: 5px;
                background-color: white;
            }}
            QLabel#title {{
                color: {color};
                font-weight: bold;
                font-size: 14px;
            }}
            QLabel#count {{
                color: {color};
                font-weight: bold;
                font-size: 24px;
            }}
        """)
        
        # Create layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Add title label
        title_label = QLabel(title)
        title_label.setObjectName("title")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # Add count label
        count_label = QLabel(str(count))
        count_label.setObjectName("count")
        count_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(count_label)

class AlertTile(QFrame):
    """Widget for displaying an alert with action button."""
    
    clicked = pyqtSignal(str, int)  # Signal to emit when button clicked (type, id)
    
    def __init__(self, title, message, alert_type, item_id, status="critical", parent=None):
        """Initialize an AlertTile instance.
        
        Args:
            title (str): The alert title
            message (str): The alert message
            alert_type (str): The type of alert (used in signal)
            item_id (int): The ID of the related item
            status (str): The status - one of "normal", "warning", "critical", "inactive", "unknown"
            parent: The parent widget
        """
        super().__init__(parent)
        
        self.alert_type = alert_type
        self.item_id = item_id
        
        # Set frame style
        self.setFrameShape(QFrame.Shape.StyledPanel)
        self.setFrameShadow(QFrame.Shadow.Raised)
        self.setLineWidth(1)
        
        # Apply status color
        color = utils.get_status_color(status)
        
        self.setStyleSheet(f"""
            QFrame {{
                border: 1px solid {color};
                border-radius: 5px;
                background-color: white;
                margin-bottom: 5px;
            }}
            QLabel#title {{
                color: {color};
                font-weight: bold;
                font-size: 14px;
            }}
            QLabel#message {{
                color: #333;
                font-size: 12px;
            }}
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
            }}
            QPushButton:hover {{
                background-color: {color};
                opacity: 0.8;
            }}
        """)
        
        # Create layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Add title label
        title_label = QLabel(title)
        title_label.setObjectName("title")
        layout.addWidget(title_label)
        
        # Add message label
        message_label = QLabel(message)
        message_label.setObjectName("message")
        message_label.setWordWrap(True)
        layout.addWidget(message_label)
        
        # Add action button
        button_layout = QHBoxLayout()
        button_layout.setAlignment(Qt.AlignmentFlag.AlignRight)
        action_button = QPushButton("View Details")
        action_button.clicked.connect(self.on_action_clicked)
        button_layout.addWidget(action_button)
        layout.addLayout(button_layout)
    
    def on_action_clicked(self):
        """Emit signal when action button clicked."""
        self.clicked.emit(self.alert_type, self.item_id)

class PieChartWidget(QWidget):
    """Widget for displaying a pie chart with a scrollable legend."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._is_closed = False
        self._wedge_tooltips = []
        self._last_tooltip = None
        # Layouts
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        # Chart area
        chart_layout = QVBoxLayout()
        chart_layout.setContentsMargins(0, 0, 0, 0)
        # Create figure and canvas
        self.figure = Figure(figsize=(3, 3), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setFixedWidth(260)
        self.canvas.setFixedHeight(260)
        chart_layout.addWidget(self.canvas)
        # Create subplot
        self.ax = self.figure.add_subplot(111)
        # Connect motion event for tooltips
        self.canvas.mpl_connect('motion_notify_event', self._on_motion)
        # Legend area (scrollable)
        self.legend_scroll = QScrollArea()
        self.legend_scroll.setWidgetResizable(True)
        self.legend_scroll.setFixedWidth(180)
        self.legend_widget = QWidget()
        self.legend_layout = QVBoxLayout(self.legend_widget)
        self.legend_layout.setContentsMargins(2, 2, 2, 2)
        self.legend_scroll.setWidget(self.legend_widget)
        # Add to main layout
        main_layout.addLayout(chart_layout, 0)
        main_layout.addWidget(self.legend_scroll, 0)
        self.setFixedWidth(460)

    def closeEvent(self, event):
        self._is_closed = True
        super().closeEvent(event)
    
    def update_chart(self, title, labels, values):
        if getattr(self, '_is_closed', False):
            logger.warning('PieChartWidget is closed, skipping update_chart')
            return
        self.ax.clear()
        self._wedge_tooltips = []
        # Remove old legend items
        while self.legend_layout.count():
            item = self.legend_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
        if not labels or not values or len(labels) == 0 or len(values) == 0:
            self.ax.text(0.5, 0.5, "No data available", ha='center', va='center')
            self.canvas.draw()
            return
        # Pie chart
        wedges, texts, autotexts = self.ax.pie(
            values, 
            labels=None, 
            autopct='%1.1f%%',
            startangle=90,
            wedgeprops={'edgecolor': 'white'}
        )
        colors = cm.tab10(range(len(values)))
        for i, wedge in enumerate(wedges):
            wedge.set_facecolor(colors[i % len(colors)])
            percent = f"{(values[i] / sum(values) * 100):.1f}%"
            self._wedge_tooltips.append({
                'wedge': wedge,
                'label': labels[i],
                'value': values[i],
                'percent': percent
            })
            # Add legend entry (color box + label)
            legend_row = QWidget()
            row_layout = QHBoxLayout(legend_row)
            row_layout.setContentsMargins(2, 2, 2, 2)
            color_box = QLabel()
            color_box.setFixedSize(16, 16)
            color_box.setStyleSheet(f"background-color: {self._mpl_color_to_hex(colors[i % len(colors)])}; border: 1px solid #888;")
            row_layout.addWidget(color_box)
            label = QLabel(labels[i])
            label.setStyleSheet("font-size: 10pt;")
            row_layout.addWidget(label)
            row_layout.addStretch(1)
            self.legend_layout.addWidget(legend_row)
        self.ax.set_aspect('equal')
        self.ax.set_title(title)
        self.canvas.draw()

    def _mpl_color_to_hex(self, color):
        rgb = tuple(int(255 * c) for c in color[:3])
        return '#%02x%02x%02x' % rgb

    def _on_motion(self, event):
        if not event.inaxes:
            if self._last_tooltip:
                QWidget.setToolTip(self, "")
                self._last_tooltip = None
            return
        for wedge_info in self._wedge_tooltips:
            wedge = wedge_info['wedge']
            contains, _ = wedge.contains(event)
            if contains:
                tip = f"{wedge_info['label']}: {wedge_info['value']} ({wedge_info['percent']})"
                if self._last_tooltip != tip:
                    QWidget.setToolTip(self, tip)
                    self._last_tooltip = tip
                return
        if self._last_tooltip:
            QWidget.setToolTip(self, "")
            self._last_tooltip = None

class BarChartWidget(QWidget):
    """Widget for displaying a bar chart."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._is_closed = False
        self._bar_tooltips = []
        self._last_tooltip = None
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        self.figure = Figure(figsize=(5, 4), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        self.ax = self.figure.add_subplot(111)
        self.canvas.mpl_connect('motion_notify_event', self._on_motion)

    def closeEvent(self, event):
        self._is_closed = True
        super().closeEvent(event)
    
    def update_chart(self, title, labels, values, x_label="", y_label=""):
        if getattr(self, '_is_closed', False):
            logger.warning('BarChartWidget is closed, skipping update_chart')
            return
        self.ax.clear()
        self._bar_tooltips = []
        if not labels or not values or len(labels) == 0 or len(values) == 0:
            self.ax.text(0.5, 0.5, "No data available", ha='center', va='center')
            self.canvas.draw()
            return
        
        # Create bars with dynamic width based on number of items
        bar_width = max(0.4, min(0.8, 1.0 / len(labels))) if len(labels) > 1 else 0.6
        bars = self.ax.bar(
            labels, 
            values,
            width=bar_width
        )
        
        # Use color palette for better visual distinction
        colors = cm.tab10(range(len(values)))
        for i, bar in enumerate(bars):
            bar.set_color(colors[i % len(colors)])
            self._bar_tooltips.append({
                'rect': bar,
                'label': labels[i],
                'value': values[i]
            })
        
        self.ax.set_title(title)
        self.ax.set_xlabel(x_label)
        self.ax.set_ylabel(y_label)
        
        # Add value labels on bars
        for i, v in enumerate(values):
            self.ax.text(i, v + max(values) * 0.01, str(v), ha='center', va='bottom')
        
        # Rotate x-axis labels if there are many entries or long labels
        if len(labels) > 5 or any(len(str(label)) > 10 for label in labels):
            self.ax.tick_params(axis='x', rotation=45)
            # Adjust label alignment for rotated text
            for label in self.ax.get_xticklabels():
                label.set_ha('right')
        
        # Adjust layout to prevent label cutoff
        self.figure.tight_layout()
        
        self.canvas.draw()

    def _on_motion(self, event):
        if not event.inaxes:
            if self._last_tooltip:
                QWidget.setToolTip(self, "")
                self._last_tooltip = None
            return
        for bar_info in self._bar_tooltips:
            rect = bar_info['rect']
            contains, _ = rect.contains(event)
            if contains:
                tip = f"{bar_info['label']}: {bar_info['value']}"
                if self._last_tooltip != tip:
                    QWidget.setToolTip(self, tip)
                    self._last_tooltip = tip
                return
        if self._last_tooltip:
            QWidget.setToolTip(self, "")
            self._last_tooltip = None

class StatisticsWidget(QWidget):
    """Widget for displaying equipment and maintenance statistics."""
    
    def __init__(self, parent=None):
        """Initialize a StatisticsWidget instance."""
        super().__init__(parent)
        
        # Create layout
        layout = QHBoxLayout(self)
        
        # Create pie chart for equipment distribution
        self.equipment_chart = PieChartWidget()
        layout.addWidget(self.equipment_chart)
        
        # Create bar chart for fluid type distribution
        self.maintenance_chart = BarChartWidget()
        layout.addWidget(self.maintenance_chart)
    
    def update_charts(self, equipment_data, bar_data, bar_title="Upcoming Maintenance by Month", bar_xlabel="Month", bar_ylabel="Count"):
        """Update charts with current data and custom bar chart title/labels."""
        # Update equipment distribution chart
        if equipment_data and 'labels' in equipment_data and 'values' in equipment_data:
            self.equipment_chart.update_chart(
                "Equipment Distribution",
                equipment_data['labels'],
                equipment_data['values']
            )
        else:
            self.equipment_chart.update_chart("Equipment Distribution", [], [])
        
        # Update bar chart (fluid type distribution)
        if bar_data and 'labels' in bar_data and 'values' in bar_data:
            self.maintenance_chart.update_chart(
                bar_title,
                bar_data['labels'],
                bar_data['values'],
                bar_xlabel,
                bar_ylabel
            )
        else:
            self.maintenance_chart.update_chart(bar_title, [], [], bar_xlabel, bar_ylabel)

class ReadOnlyTableWidget(QTableWidget):
    """Table widget with DPI-aware scaling and improved styling."""
    
    row_clicked = pyqtSignal(int)  # Signal emitted when a row is clicked
    
    def __init__(self, parent=None):
        """Initialize a ReadOnlyTableWidget instance."""
        super().__init__(parent)
        
        # Set table properties
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.setAlternatingRowColors(True)
        self.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        # Apply DPI-aware sizing
        self._setup_dpi_aware_sizing()
        
        # Set up horizontal header
        self.horizontalHeader().setStretchLastSection(True)
        self.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        
        # Hide vertical header (row numbers)
        self.verticalHeader().setVisible(False)
        
        # Connect signals
        self.cellClicked.connect(self.on_cell_clicked)
        
        # Apply DPI-aware styling
        self._apply_dpi_aware_styling()
    
    def _setup_dpi_aware_sizing(self):
        """Setup DPI-aware sizing for the table."""
        from ui.window_utils import DPIScaler
        
        # Scale row height
        base_row_height = 30
        scaled_row_height = DPIScaler.scale_size(base_row_height, min_size=25, max_size=50)
        self.verticalHeader().setDefaultSectionSize(scaled_row_height)
        
        # Scale header height
        base_header_height = 35
        scaled_header_height = DPIScaler.scale_size(base_header_height, min_size=30, max_size=60)
        self.horizontalHeader().setMinimumSectionSize(scaled_header_height)
        self.horizontalHeader().setDefaultSectionSize(scaled_header_height)
        
        # Apply scaled font
        table_font = DPIScaler.create_scaled_font(10)
        self.setFont(table_font)
        
        # Apply scaled font to headers
        header_font = DPIScaler.create_scaled_font(10, bold=True)
        self.horizontalHeader().setFont(header_font)
    
    def _apply_dpi_aware_styling(self):
        """Apply DPI-aware styling to the table."""
        from ui.window_utils import DPIScaler
        
        # Scale padding and border
        padding = DPIScaler.scale_size(4, min_size=2, max_size=8)
        border_width = DPIScaler.scale_size(1, min_size=1, max_size=3)
        border_radius = DPIScaler.scale_size(2, min_size=1, max_size=4)
        
        self.setStyleSheet(f"""
            QTableWidget {{
                border: {border_width}px solid #d9d9d9;
                border-radius: {border_radius}px;
                selection-background-color: #e6f2ff;
                selection-color: #000;
                gridline-color: #e6e6e6;
            }}
            QHeaderView::section {{
                background-color: #f0f0f0;
                border: {border_width}px solid #d9d9d9;
                padding: {padding}px;
                font-weight: bold;
            }}
            QTableWidget::item {{
                padding: {padding}px;
                border: none;
            }}
            QTableWidget::item:selected {{
                background-color: #007bff;
                color: white;
            }}
        """)
    
    def on_cell_clicked(self, row, column):
        """Emit signal when a row is clicked."""
        self.row_clicked.emit(row)
    
    def set_data(self, headers, data, id_column=None):
        """Set table data with specified headers."""
        # Clear existing data
        self.clearContents()
        self.setRowCount(0)
        
        # Set headers
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        # Store ID column index
        self.id_column = id_column
        
        # Add data rows
        if data:
            self.setRowCount(len(data))
            
            for row_idx, row_data in enumerate(data):
                for col_idx, header in enumerate(headers):
                    # Create cell item
                    if 'Status' in header and header in row_data:
                        # For status columns, use status label
                        status_label = StatusLabel(row_data[header], row_data[header])
                        self.setCellWidget(row_idx, col_idx, status_label)
                    else:
                        # For other columns, use regular item
                        value_str = str(row_data.get(header, ""))
                        item = QTableWidgetItem(value_str)
                        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

                        # Add tooltip for long text to show full content on hover
                        if len(value_str) > 20:  # Show tooltip for longer text
                            item.setToolTip(value_str)

                        self.setItem(row_idx, col_idx, item)

                        # Store ID as item data if this is the ID column
                        if id_column is not None and col_idx == id_column:
                            item.setData(Qt.ItemDataRole.UserRole, row_data.get(header))
        
        # Adjust column widths with DPI awareness
        self._resize_columns_responsive()
    
    def _resize_columns_responsive(self):
        """Resize columns with DPI awareness."""
        self.resizeColumnsToContents()
        
        # Apply minimum column widths based on DPI
        from ui.window_utils import DPIScaler
        min_column_width = DPIScaler.scale_size(80, min_size=60, max_size=120)
        
        for col in range(self.columnCount()):
            current_width = self.columnWidth(col)
            if current_width < min_column_width:
                self.setColumnWidth(col, min_column_width)
    
    def set_responsive_column_widths(self, column_widths):
        """Set column widths with DPI scaling applied and intelligent limits for different content types."""
        from ui.window_utils import DPIScaler

        for col, base_width in enumerate(column_widths):
            if col < self.columnCount():
                # Get header text to determine content type
                header_text = self.horizontalHeaderItem(col).text().lower() if self.horizontalHeaderItem(col) else ""

                # Set different max widths based on column content
                if 'make' in header_text and 'type' in header_text:
                    # Make & Type columns need more space for full equipment names
                    max_width = 500
                elif 'equipment' in header_text or 'ba' in header_text:
                    # Equipment and BA Number columns
                    max_width = 400
                elif 'remarks' in header_text or 'notes' in header_text or 'description' in header_text:
                    # Text fields need more space
                    max_width = 600
                else:
                    # Other columns (dates, numbers, status)
                    max_width = 300

                scaled_width = DPIScaler.scale_size(base_width, min_size=80, max_size=max_width)
                self.setColumnWidth(col, scaled_width)
    
    def get_selected_id(self):
        """Get the ID of the selected row."""
        if self.id_column is None:
            return None
        
        selected_rows = self.selectedItems()
        if not selected_rows:
            return None
        
        # Get ID from the first selected row's ID column
        selected_row = selected_rows[0].row()
        id_item = self.item(selected_row, self.id_column)
        
        if id_item:
            return id_item.data(Qt.ItemDataRole.UserRole)
        
        return None
    
    def get_selected_row_data(self):
        """Get data from all cells in the selected row."""
        selected_rows = self.selectedItems()
        if not selected_rows:
            return None
        
        selected_row = selected_rows[0].row()
        row_data = {}
        
        for col in range(self.columnCount()):
            header = self.horizontalHeaderItem(col).text()
            item = self.item(selected_row, col)
            
            if item:
                row_data[header] = item.text()
            else:
                # For custom widgets in cells
                widget = self.cellWidget(selected_row, col)
                if isinstance(widget, StatusLabel):
                    row_data[header] = widget.text()
                else:
                    row_data[header] = ""
        
        return row_data