#!/usr/bin/env python3
"""
PROJECT-ALPHA PyInstaller Build Script
Comprehensive build system for creating standalone executable with full dependency management
"""

import os
import sys
import shutil
import subprocess
import platform
import logging
import time
import tempfile
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# Enhanced logging setup
class ColoredFormatter(logging.Formatter):
    """Colored logging formatter for better readability"""
    
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green  
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        log_color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        record.levelname = f"{log_color}{record.levelname}{self.COLORS['RESET']}"
        return super().format(record)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('build_pyinstaller.log')
    ]
)

# Apply colored formatter to console handler
console_handler = logging.getLogger().handlers[0]
console_handler.setFormatter(ColoredFormatter('%(asctime)s - %(levelname)s - %(message)s'))

logger = logging.getLogger(__name__)

class ProjectAlphaPyInstallerBuilder:
    """Advanced PyInstaller builder for PROJECT-ALPHA with comprehensive features"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.build_start_time = datetime.now()
        self.build_log = []
        
        # Application metadata
        self.app_name = "InventoryTracker"
        self.app_version = "1.0.0"
        self.app_description = "PROJECT-ALPHA Equipment Inventory Management System"
        
        # Build configuration
        self.spec_file = self.project_root / "InventoryTracker.spec"
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        
        logger.info(f"🚀 Initializing PyInstaller builder for {self.app_description}")
        logger.info(f"📁 Project root: {self.project_root}")
        logger.info(f"🖥️  Platform: {platform.platform()}")
        logger.info(f"🐍 Python: {sys.version}")
        
    def log(self, message: str, level: str = "INFO"):
        """Enhanced logging with timestamp and level"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        self.build_log.append(log_entry)
        
        if level == "ERROR":
            logger.error(message)
        elif level == "WARNING":
            logger.warning(message)
        elif level == "DEBUG":
            logger.debug(message)
        else:
            logger.info(message)
    
    def check_system_requirements(self) -> bool:
        """Check system requirements for PyInstaller build"""
        self.log("🔍 Checking system requirements...")
        
        # Check Python version
        if sys.version_info < (3, 9):
            self.log(f"❌ Python {sys.version} is too old. Minimum required: Python 3.9", "ERROR")
            return False
        self.log(f"✅ Python version {sys.version} is suitable")
        
        # Check available disk space (need at least 2GB)
        try:
            import shutil
            total, used, free = shutil.disk_usage(self.project_root)
            free_gb = free // (1024**3)
            if free_gb < 2:
                self.log(f"❌ Insufficient disk space. Available: {free_gb}GB, Required: 2GB", "ERROR")
                return False
            self.log(f"✅ Disk space available: {free_gb}GB")
        except Exception as e:
            self.log(f"⚠️  Could not check disk space: {e}", "WARNING")
        
        # Check memory
        try:
            import psutil
            memory = psutil.virtual_memory()
            memory_gb = memory.total // (1024**3)
            if memory_gb < 4:
                self.log(f"⚠️  Low memory detected: {memory_gb}GB. Build may be slow", "WARNING")
            else:
                self.log(f"✅ Memory available: {memory_gb}GB")
        except ImportError:
            self.log("⚠️  Could not check memory (psutil not available)", "WARNING")
        
        return True
    
    def check_dependencies(self) -> bool:
        """Comprehensive dependency checking with version validation"""
        self.log("📦 Checking dependencies...")
        
        # Core dependencies with version requirements
        required_modules = {
            'PyQt5': '5.15.0',
            'pandas': '1.3.0', 
            'numpy': '1.19.0',
            'openpyxl': '3.0.0',
            'xlrd': '2.0.0',
            'matplotlib': '3.3.0',
            'reportlab': '3.6.0',
            'psutil': '5.8.0',
            'pint': '0.20',
            'fuzzywuzzy': '0.18',
            'python-dateutil': '2.8.0',
            'Pillow': '8.0.0',
            'pyinstaller': '5.0.0'
        }
        
        missing_modules = []
        outdated_modules = []
        
        for module_name, min_version in required_modules.items():
            try:
                if module_name == 'python-dateutil':
                    import dateutil
                    module = dateutil
                    import_name = 'dateutil'
                elif module_name == 'Pillow':
                    import PIL
                    module = PIL
                    import_name = 'PIL'
                else:
                    module = __import__(module_name)
                    import_name = module_name
                
                # Check version if available
                version = getattr(module, '__version__', 'unknown')
                self.log(f"✅ {import_name} {version}")
                
                # Simple version comparison (works for most cases)
                if version != 'unknown' and version < min_version:
                    outdated_modules.append((import_name, version, min_version))
                    
            except ImportError:
                missing_modules.append(module_name)
                self.log(f"❌ {module_name} missing", "ERROR")
        
        # Report issues
        if missing_modules:
            self.log(f"❌ Missing modules: {', '.join(missing_modules)}", "ERROR")
            self.log("💡 Install missing dependencies:", "INFO")
            self.log("   pip install -r requirements.txt", "INFO")
            return False
        
        if outdated_modules:
            for module, current, required in outdated_modules:
                self.log(f"⚠️  {module} {current} is older than required {required}", "WARNING")
        
        self.log(f"✅ All {len(required_modules)} dependencies are available")
        return True
    
    def check_pyinstaller(self) -> bool:
        """Verify PyInstaller installation and version"""
        self.log("🔧 Checking PyInstaller...")
        
        try:
            result = subprocess.run([
                sys.executable, "-m", "PyInstaller", "--version"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                version = result.stdout.strip()
                self.log(f"✅ PyInstaller version: {version}")
                
                # Check if version is recent enough
                version_parts = version.split('.')
                if len(version_parts) >= 2:
                    major = int(version_parts[0])
                    if major >= 5:
                        return True
                    else:
                        self.log(f"⚠️  PyInstaller {version} may be too old (recommend 5.0+)", "WARNING")
                        return True
                return True
            else:
                self.log("❌ PyInstaller not working properly", "ERROR")
                self.log(f"Error: {result.stderr}", "ERROR")
                return False
                
        except subprocess.TimeoutExpired:
            self.log("❌ PyInstaller check timed out", "ERROR")
            return False
        except Exception as e:
            self.log(f"❌ Error checking PyInstaller: {e}", "ERROR")
            return False
    
    def clean_previous_builds(self):
        """Clean previous build artifacts thoroughly"""
        self.log("🧹 Cleaning previous build artifacts...")
        
        # Directories to clean
        cleanup_dirs = [
            self.build_dir,
            self.dist_dir,
            self.project_root / '__pycache__',
            self.project_root / '.pytest_cache'
        ]
        
        cleaned_count = 0
        for dir_path in cleanup_dirs:
            if dir_path.exists():
                try:
                    shutil.rmtree(dir_path)
                    self.log(f"🗑️  Removed {dir_path.name}/")
                    cleaned_count += 1
                except Exception as e:
                    self.log(f"⚠️  Could not remove {dir_path.name}/: {e}", "WARNING")
        
        # Clean Python cache files recursively
        cache_files_cleaned = 0
        for cache_file in self.project_root.rglob('*.pyc'):
            try:
                cache_file.unlink()
                cache_files_cleaned += 1
            except Exception:
                pass
        
        for cache_dir in self.project_root.rglob('__pycache__'):
            try:
                shutil.rmtree(cache_dir)
                cache_files_cleaned += 1
            except Exception:
                pass
        
        self.log(f"✅ Cleanup completed: {cleaned_count} directories, {cache_files_cleaned} cache files")
    
    def validate_spec_file(self) -> bool:
        """Validate the PyInstaller spec file"""
        self.log("📋 Validating spec file...")
        
        if not self.spec_file.exists():
            self.log(f"❌ Spec file not found: {self.spec_file}", "ERROR")
            return False
        
        try:
            with open(self.spec_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for required elements
            required_elements = ['Analysis', 'PYZ', 'EXE', 'main.py']
            missing_elements = []
            
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                self.log(f"❌ Spec file missing elements: {missing_elements}", "ERROR")
                return False
            
            # Check for icon file reference
            if 'app_icon.ico' in content:
                icon_path = self.project_root / 'resources' / 'app_icon.ico'
                if not icon_path.exists():
                    self.log(f"⚠️  Icon file not found: {icon_path}", "WARNING")
            
            self.log("✅ Spec file validation passed")
            return True
            
        except Exception as e:
            self.log(f"❌ Error validating spec file: {e}", "ERROR")
            return False
    
    def build_executable(self) -> bool:
        """Build the executable using PyInstaller with progress monitoring"""
        self.log("🔨 Starting PyInstaller build...")
        
        # Build command
        cmd = [
            sys.executable, "-m", "PyInstaller",
            str(self.spec_file),
            "--clean",
            "--noconfirm",
            "--log-level=INFO"
        ]
        
        self.log(f"🚀 Running: {' '.join(cmd)}")
        
        try:
            # Start build process
            start_time = time.time()
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
                cwd=self.project_root
            )
            
            # Monitor progress
            output_lines = []
            warning_count = 0
            error_count = 0
            
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                
                if output:
                    line = output.strip()
                    if line:
                        output_lines.append(line)
                        
                        # Categorize and display important messages
                        line_lower = line.lower()
                        if 'error' in line_lower or 'failed' in line_lower:
                            error_count += 1
                            print(f"❌ {line}")
                            self.log(f"PyInstaller Error: {line}", "ERROR")
                        elif 'warning' in line_lower:
                            warning_count += 1
                            print(f"⚠️  {line}")
                            self.log(f"PyInstaller Warning: {line}", "WARNING")
                        elif any(keyword in line_lower for keyword in ['analyzing', 'building', 'exe']):
                            print(f"🔄 {line}")
                        elif 'successfully' in line_lower or 'completed' in line_lower:
                            print(f"✅ {line}")
            
            process.wait()
            build_time = time.time() - start_time
            
            # Check results
            if process.returncode == 0:
                self.log(f"✅ PyInstaller build completed successfully in {build_time:.1f}s")
                if warning_count > 0:
                    self.log(f"⚠️  Build completed with {warning_count} warnings", "WARNING")
                return True
            else:
                self.log(f"❌ PyInstaller build failed (exit code: {process.returncode})", "ERROR")
                self.log(f"Build time: {build_time:.1f}s", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Build process error: {e}", "ERROR")
            return False
    
    def validate_executable(self) -> bool:
        """Comprehensive executable validation"""
        self.log("🔍 Validating generated executable...")
        
        exe_path = self.dist_dir / f"{self.app_name}.exe"
        
        if not exe_path.exists():
            self.log(f"❌ Executable not found: {exe_path}", "ERROR")
            return False
        
        # Check file size
        file_size_mb = exe_path.stat().st_size / (1024 * 1024)
        self.log(f"📏 Executable size: {file_size_mb:.1f} MB")
        
        if file_size_mb < 30:
            self.log("⚠️  Executable seems small - may be missing dependencies", "WARNING")
        elif file_size_mb > 300:
            self.log("⚠️  Executable is large - consider optimization", "WARNING")
        
        # Test executable startup
        self.log("🧪 Testing executable startup...")
        try:
            # Quick startup test (5 second timeout)
            test_proc = subprocess.Popen(
                [str(exe_path)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' else 0
            )
            
            # Wait briefly then terminate
            time.sleep(2)
            test_proc.terminate()
            test_proc.wait(timeout=3)
            
            self.log("✅ Executable startup test passed")
            return True
            
        except subprocess.TimeoutExpired:
            test_proc.kill()
            self.log("⚠️  Executable test timed out (may be normal for GUI apps)", "WARNING")
            return True
        except Exception as e:
            self.log(f"⚠️  Executable test failed: {e}", "WARNING")
            return True  # Don't fail build for test issues
    
    def create_distribution_package(self) -> bool:
        """Create a complete distribution package"""
        self.log("📦 Creating distribution package...")
        
        dist_package_dir = self.project_root / f"{self.app_name}_Distribution"
        
        try:
            # Remove old distribution
            if dist_package_dir.exists():
                shutil.rmtree(dist_package_dir)
            
            dist_package_dir.mkdir()
            
            # Copy executable
            exe_path = self.dist_dir / f"{self.app_name}.exe"
            if exe_path.exists():
                shutil.copy2(exe_path, dist_package_dir / f"{self.app_name}.exe")
                self.log("✅ Copied executable")
            
            # Create README
            readme_content = f"""# {self.app_description}

## Installation
1. Extract all files to a folder
2. Run {self.app_name}.exe
3. The application will create its database and configuration files automatically

## System Requirements
- Windows 10 or later
- 4GB RAM minimum (8GB recommended)
- 500MB free disk space

## Version Information
- Version: {self.app_version}
- Build Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- Platform: {platform.platform()}

## Support
For technical support, contact the development team.

## License
See LICENSE file for licensing information.
"""
            
            with open(dist_package_dir / "README.txt", 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            # Copy license files
            for license_file in ['LICENSE', 'LICENSE.txt']:
                license_path = self.project_root / license_file
                if license_path.exists():
                    shutil.copy2(license_path, dist_package_dir)
            
            # Create launcher batch file
            launcher_content = f"""@echo off
title {self.app_description}
echo Starting {self.app_description}...
echo.
echo If you see this window, the application is starting.
echo The main window will appear shortly.
echo.
start "" "{self.app_name}.exe"
"""
            
            with open(dist_package_dir / f"Start_{self.app_name}.bat", 'w') as f:
                f.write(launcher_content)
            
            self.log(f"✅ Distribution package created: {dist_package_dir}")
            return True
            
        except Exception as e:
            self.log(f"❌ Failed to create distribution package: {e}", "ERROR")
            return False
    
    def generate_build_report(self):
        """Generate comprehensive build report"""
        self.log("📊 Generating build report...")
        
        build_duration = datetime.now() - self.build_start_time
        exe_path = self.dist_dir / f"{self.app_name}.exe"
        
        report = {
            'build_info': {
                'app_name': self.app_name,
                'app_version': self.app_version,
                'build_date': self.build_start_time.isoformat(),
                'build_duration_seconds': build_duration.total_seconds(),
                'python_version': sys.version,
                'platform': platform.platform(),
                'project_root': str(self.project_root)
            },
            'executable_info': {
                'path': str(exe_path) if exe_path.exists() else None,
                'size_mb': exe_path.stat().st_size / (1024 * 1024) if exe_path.exists() else 0,
                'exists': exe_path.exists()
            },
            'build_log': self.build_log
        }
        
        # Save JSON report
        report_file = self.project_root / f"build_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Print summary
        print("\n" + "="*70)
        print(f"🎉 BUILD SUMMARY")
        print("="*70)
        print(f"Application: {self.app_name} v{self.app_version}")
        print(f"Build Time: {build_duration}")
        print(f"Executable: {exe_path if exe_path.exists() else 'NOT CREATED'}")
        if exe_path.exists():
            print(f"Size: {exe_path.stat().st_size / (1024 * 1024):.1f} MB")
        print(f"Report: {report_file}")
        print("="*70)
    
    def run_complete_build(self) -> bool:
        """Execute the complete build process"""
        self.log("🚀 Starting complete PyInstaller build process")
        self.log("="*70)
        
        try:
            # Step 1: System requirements
            if not self.check_system_requirements():
                self.log("❌ System requirements check failed", "ERROR")
                return False
            
            # Step 2: Dependencies
            if not self.check_dependencies():
                self.log("❌ Dependency check failed", "ERROR")
                return False
            
            # Step 3: PyInstaller
            if not self.check_pyinstaller():
                self.log("❌ PyInstaller check failed", "ERROR")
                return False
            
            # Step 4: Clean
            self.clean_previous_builds()
            
            # Step 5: Validate spec
            if not self.validate_spec_file():
                self.log("❌ Spec file validation failed", "ERROR")
                return False
            
            # Step 6: Build
            if not self.build_executable():
                self.log("❌ Executable build failed", "ERROR")
                return False
            
            # Step 7: Validate
            if not self.validate_executable():
                self.log("❌ Executable validation failed", "ERROR")
                return False
            
            # Step 8: Package
            if not self.create_distribution_package():
                self.log("⚠️  Distribution package creation failed", "WARNING")
            
            self.log("🎉 Build completed successfully!")
            return True
            
        except KeyboardInterrupt:
            self.log("❌ Build interrupted by user", "ERROR")
            return False
        except Exception as e:
            self.log(f"❌ Unexpected error: {e}", "ERROR")
            import traceback
            self.log(f"Traceback: {traceback.format_exc()}", "ERROR")
            return False
        finally:
            self.generate_build_report()

def main():
    """Main entry point"""
    print("🚀 PROJECT-ALPHA PyInstaller Build System")
    print("="*70)
    
    try:
        builder = ProjectAlphaPyInstallerBuilder()
        success = builder.run_complete_build()
        
        if success:
            print("\n✅ Build completed successfully!")
            print("🎯 Next steps:")
            print("   1. Test the executable in dist/")
            print("   2. Check the distribution package")
            print("   3. Test on a clean system")
            return 0
        else:
            print("\n❌ Build failed!")
            print("💡 Check the build log for details")
            return 1
            
    except Exception as e:
        print(f"\n💥 Fatal error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 