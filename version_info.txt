# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Military Equipment Management Division'),
        StringStruct(u'FileDescription', u'PROJECT-ALPHA Equipment Inventory Management System'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'InventoryTracker'),
        StringStruct(u'LegalCopyright', u'© 2024 Military Equipment Management Division'),
        StringStruct(u'OriginalFilename', u'InventoryTracker.exe'),
        StringStruct(u'ProductName', u'PROJECT-ALPHA Equipment Inventory Management System'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
) 