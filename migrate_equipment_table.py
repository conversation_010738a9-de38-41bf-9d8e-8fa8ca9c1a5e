import sqlite3

DB_PATH = "inventory.db"

# List of columns to add: (column_name, SQL type + default)
columns_to_add = [
    ("meterage_description", "TEXT"),
    ("hours_run_total", "REAL DEFAULT 0"),
    ("hours_run_previous_month", "REAL DEFAULT 0"),
    ("hours_run_current_month", "REAL DEFAULT 0"),
    ("equipment_status", "TEXT DEFAULT 'active'"),
    ("oh1_done_date", "TEXT"),
    ("oh1_due_date", "TEXT"),
    ("oh2_done_date", "TEXT"),
    ("oh2_due_date", "TEXT"),
    ("date_of_induction", "TEXT"),
]

conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()

for col, coltype in columns_to_add:
    try:
        cursor.execute(f"ALTER TABLE equipment ADD COLUMN {col} {coltype};")
        print(f"Added column: {col}")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print(f"Column already exists: {col}")
        else:
            print(f"Error adding column {col}: {e}")

conn.commit()
conn.close()
print("Migration complete.")
