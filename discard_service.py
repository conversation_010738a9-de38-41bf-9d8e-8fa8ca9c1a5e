"""
Discard Criteria Service Module
Handles automatic creation and management of discard criteria based on equipment specifications.
"""
import logging
from datetime import datetime, date, timedelta
from models import Equipment, DiscardCriteria
import config

# Check if policy module is available
try:
    # Add absolute path to ensure consistent imports
    import sys
    import os
    
    # Make sure project root is in path - use absolute path to be certain
    project_root = os.path.abspath(os.path.dirname(os.path.abspath(__file__)))
    
    # Add to sys.path if not already there
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    # Import policy modules
    import policy_service
    import policy_models
    
    # Ensure policy tables exist
    policy_service.ensure_tables_exist()
    
    POLICY_SUPPORT = True
    logging.info("Policy module successfully loaded in discard_service")
except ImportError as e:
    POLICY_SUPPORT = False
    logging.warning(f"Policy module not available. Some features will be disabled. Error: {e}")

logger = logging.getLogger('discard_service')


def match_equipment_to_criteria(make_and_type):
    """
    Match equipment make and type to discard criteria rules.
    
    Args:
        make_and_type (str): Equipment make and type string
        
    Returns:
        dict or None: Criteria rules {'years': int, 'kms': int, 'hours': int} or None if no match
    """
    if not make_and_type:
        return None
    
    # Normalize the input for comparison
    normalized_type = str(make_and_type).lower().strip()
    
    # Try to match using priority order (most specific patterns first)
    for pattern in config.DISCARD_CRITERIA_PRIORITY:
        if pattern in normalized_type:
            rules = config.DISCARD_CRITERIA_RULES.get(pattern)
            if rules:
                logger.debug(f"Matched equipment '{make_and_type}' to pattern '{pattern}' with rules: {rules}")
                return rules
    
    logger.debug(f"No discard criteria rules found for equipment: {make_and_type}")
    return None


def calculate_vintage_years(date_of_commission):
    """
    Calculate vintage years from commission date to current date.
    
    Args:
        date_of_commission (str or date): Commission date
        
    Returns:
        float: Years since commission
    """
    if not date_of_commission:
        return 0.0
    
    try:
        if isinstance(date_of_commission, str):
            commission_date = date.fromisoformat(date_of_commission.split(' ')[0])
        else:
            commission_date = date_of_commission
        
        today = date.today()
        days_diff = (today - commission_date).days
        years = days_diff / 365.25
        
        return max(0.0, years)
    
    except Exception as e:
        logger.error(f"Error calculating vintage years for date {date_of_commission}: {e}")
        return 0.0


def check_discard_criteria_met(equipment_data, criteria_rules):
    """
    Check if equipment meets discard criteria based on rules.
    
    Args:
        equipment_data (dict): Equipment data with vintage_years, meterage_kms, hours_run_total
        criteria_rules (dict): Criteria rules with years, kms, hours thresholds
        
    Returns:
        bool: True if equipment meets discard criteria
    """
    if not criteria_rules:
        return False
    
    current_vintage = float(equipment_data.get('vintage_years', 0))
    current_kms = float(equipment_data.get('meterage_kms', 0))
    current_hours = float(equipment_data.get('hours_run_total', 0))
    
    criteria_years = criteria_rules.get('years')
    criteria_kms = criteria_rules.get('kms')
    criteria_hours = criteria_rules.get('hours')
    
    # Check each criteria type
    meets_criteria = False
    
    # Age criteria
    if criteria_years and current_vintage >= criteria_years:
        meets_criteria = True
        logger.debug(f"Equipment meets age criteria: {current_vintage:.1f} >= {criteria_years} years")
    
    # KM criteria
    if criteria_kms and current_kms >= criteria_kms:
        meets_criteria = True
        logger.debug(f"Equipment meets KM criteria: {current_kms:.0f} >= {criteria_kms} km")
    
    # Hours criteria
    if criteria_hours and current_hours >= criteria_hours:
        meets_criteria = True
        logger.debug(f"Equipment meets hours criteria: {current_hours:.0f} >= {criteria_hours} hours")
    
    return meets_criteria


def create_automatic_discard_criteria(equipment_id, force_update=False):
    """
    Create automatic discard criteria for a single equipment based on make/type rules.
    
    Args:
        equipment_id (int): Equipment ID
        force_update (bool): Whether to update existing criteria
        
    Returns:
        bool: True if criteria was created/updated, False otherwise
    """
    try:
        # Get equipment data
        equipment = Equipment.get_by_id(equipment_id)
        if not equipment:
            logger.warning(f"Equipment not found: {equipment_id}")
            return False
        
        # Extract equipment data
        make_and_type = getattr(equipment, 'make_and_type', '') or equipment.get('make_and_type', '') if isinstance(equipment, dict) else getattr(equipment, 'make_and_type', '')
        
        # Match to criteria rules
        criteria_rules = match_equipment_to_criteria(make_and_type)
        if not criteria_rules:
            logger.debug(f"No automatic criteria rules for equipment {equipment_id}: {make_and_type}")
            return False
        
        # Check if criteria already exists
        existing_criteria = DiscardCriteria.get_by_equipment(equipment_id)
        if existing_criteria and not force_update:
            logger.debug(f"Discard criteria already exists for equipment {equipment_id}, skipping")
            return False
        
        # Calculate vintage years if needed
        if isinstance(equipment, dict):
            date_of_commission = equipment.get('date_of_commission')
            vintage_years = equipment.get('vintage_years', 0)
        else:
            date_of_commission = getattr(equipment, 'date_of_commission', None)
            vintage_years = getattr(equipment, 'vintage_years', 0)
        
        if not vintage_years and date_of_commission:
            vintage_years = calculate_vintage_years(date_of_commission)
        
        # Create criteria based on rules
        criteria_years = criteria_rules.get('years', 0)
        criteria_kms = criteria_rules.get('kms', 0)
        criteria_hours = criteria_rules.get('hours', 0)
        
        # For hours-based equipment, use hours instead of kms
        if criteria_hours and not criteria_kms:
            # Convert hours to a KM equivalent or use hours field
            # For now, we'll set kms to 0 and rely on the hours check in the UI
            final_criteria_kms = 0
        else:
            final_criteria_kms = criteria_kms or 0
        
        if existing_criteria:
            # Update existing criteria
            criteria = DiscardCriteria(
                discard_criteria_id=existing_criteria[0]['discard_criteria_id'],
                equipment_id=equipment_id,
                criteria_years=criteria_years,
                criteria_kms=final_criteria_kms,
                criteria_hours=criteria_hours
            )
            action = "Updated"
        else:
            # Create new criteria
            criteria = DiscardCriteria(
                equipment_id=equipment_id,
                criteria_years=criteria_years,
                criteria_kms=final_criteria_kms,
                criteria_hours=criteria_hours
            )
            action = "Created"
        
        # Save criteria
        result = criteria.save()
        if result:
            logger.info(f"{action} automatic discard criteria for equipment {equipment_id} ({make_and_type}): {criteria_years} years, {final_criteria_kms} kms, {criteria_hours} hours")
            return True
        else:
            logger.error(f"Failed to save discard criteria for equipment {equipment_id}")
            return False
    
    except Exception as e:
        logger.error(f"Error creating automatic discard criteria for equipment {equipment_id}: {e}")
        return False


def create_all_automatic_discard_criteria(force_update=False):
    """
    Create automatic discard criteria for all equipment based on make/type rules.
    
    Args:
        force_update (bool): Whether to update existing criteria
        
    Returns:
        dict: Statistics {'created': int, 'updated': int, 'skipped': int, 'errors': int}
    """
    try:
        logger.info("Starting automatic discard criteria creation for all equipment...")
        
        # Get all active equipment
        equipment_list = Equipment.get_active()
        
        stats = {
            'created': 0,
            'updated': 0,
            'skipped': 0,
            'errors': 0,
            'total_processed': 0
        }
        
        for equipment in equipment_list:
            equipment_id = equipment.get('equipment_id')
            if not equipment_id:
                continue
            
            stats['total_processed'] += 1
            
            try:
                # Check if criteria already exists
                existing_criteria = DiscardCriteria.get_by_equipment(equipment_id)
                
                if existing_criteria and not force_update:
                    stats['skipped'] += 1
                    continue
                
                # Try to create automatic criteria
                success = create_automatic_discard_criteria(equipment_id, force_update)
                
                if success:
                    if existing_criteria:
                        stats['updated'] += 1
                    else:
                        stats['created'] += 1
                else:
                    stats['skipped'] += 1
                    
            except Exception as e:
                logger.error(f"Error processing equipment {equipment_id}: {e}")
                stats['errors'] += 1
        
        logger.info(f"Automatic discard criteria creation complete. Stats: {stats}")
        return stats
    
    except Exception as e:
        logger.error(f"Error in create_all_automatic_discard_criteria: {e}")
        return {'created': 0, 'updated': 0, 'skipped': 0, 'errors': 1, 'total_processed': 0}


def get_equipment_discard_status(equipment_data):
    """
    Get comprehensive discard status for equipment including automatic criteria matching.
    
    Args:
        equipment_data (dict): Equipment data
        
    Returns:
        dict: Status information with criteria and current values
    """
    make_and_type = equipment_data.get('make_and_type', '')
    equipment_id = equipment_data.get('equipment_id')
    
    # Get automatic criteria rules
    auto_rules = match_equipment_to_criteria(make_and_type)
    
    # Get existing criteria from database
    existing_criteria = DiscardCriteria.get_by_equipment(equipment_id) if equipment_id else None
    
    # Calculate current values
    current_vintage = float(equipment_data.get('vintage_years', 0))
    current_kms = float(equipment_data.get('meterage_kms', 0))
    current_hours = float(equipment_data.get('hours_run_total', 0))
    
    status = {
        'equipment_id': equipment_id,
        'make_and_type': make_and_type,
        'has_auto_rules': auto_rules is not None,
        'auto_rules': auto_rules,
        'has_manual_criteria': existing_criteria is not None,
        'has_policy': False,
        'policy_status': None,
        'current_vintage': current_vintage,
        'current_kms': current_kms,
        'current_hours': current_hours,
        'meets_discard_criteria': False,
        'discard_reasons': []
    }
    
    # First check policy-based criteria if policy support is available
    if POLICY_SUPPORT and equipment_id:
        policy_status = get_equipment_policy_status(equipment_data)
        if policy_status:
            status['has_policy'] = True
            status['policy_status'] = policy_status
            
            # Check if meets discard criteria
            if policy_status.get('conditions', {}).get(PolicyCondition.DISCARD, {}).get('met', False):
                status['meets_discard_criteria'] = True
                status['discard_reasons'].append("Policy-based discard criteria met")
                
                # Get specific threshold reasons
                discard_condition = policy_status.get('conditions', {}).get(PolicyCondition.DISCARD, {})
                if discard_condition.get('meets_years'):
                    status['discard_reasons'].append(
                        f"Policy - Age: {current_vintage:.1f} >= {discard_condition.get('years_threshold')} years"
                    )
                if discard_condition.get('meets_kms'):
                    status['discard_reasons'].append(
                        f"Policy - Mileage: {current_kms:.0f} >= {discard_condition.get('km_threshold')} km"
                    )
                if discard_condition.get('meets_hours'):
                    status['discard_reasons'].append(
                        f"Policy - Hours: {current_hours:.0f} >= {discard_condition.get('hours_threshold')} hrs"
                    )
        
    # If no policy applies or policy doesn't trigger discard, check older methods
    if not status['meets_discard_criteria']:
        # Check against automatic rules if available
        if auto_rules:
            if auto_rules.get('years') and current_vintage >= auto_rules['years']:
                status['meets_discard_criteria'] = True
                status['discard_reasons'].append(f"Age: {current_vintage:.1f} >= {auto_rules['years']} years")
            
            if auto_rules.get('kms') and current_kms >= auto_rules['kms']:
                status['meets_discard_criteria'] = True
                status['discard_reasons'].append(f"Mileage: {current_kms:.0f} >= {auto_rules['kms']} km")
            
            if auto_rules.get('hours') and current_hours >= auto_rules['hours']:
                status['meets_discard_criteria'] = True
                status['discard_reasons'].append(f"Hours: {current_hours:.0f} >= {auto_rules['hours']} hrs")
        
        # Check against manual criteria if available
        if existing_criteria:
            criteria = existing_criteria[0] if isinstance(existing_criteria, list) else existing_criteria
            criteria_years = float(criteria.get('criteria_years', 0))
            criteria_kms = float(criteria.get('criteria_kms', 0))
            
            if criteria_years > 0 and current_vintage >= criteria_years:
                status['meets_discard_criteria'] = True
                status['discard_reasons'].append(f"Manual criteria - Age: {current_vintage:.1f} >= {criteria_years} years")
            
            if criteria_kms > 0 and current_kms >= criteria_kms:
                status['meets_discard_criteria'] = True
                status['discard_reasons'].append(f"Manual criteria - Mileage: {current_kms:.0f} >= {criteria_kms} km")
    
    return status 


def get_equipment_policy_status(equipment_data):
    """
    Get policy-based status evaluation for equipment.
    
    Args:
        equipment_data (dict): Equipment data
        
    Returns:
        dict: Policy evaluation results or None if no policy applies
    """
    if not POLICY_SUPPORT:
        return None
        
    try:
        equipment_id = equipment_data.get('equipment_id')
        if not equipment_id:
            return None
            
        # Get policy for equipment make and type
        policy_data = policy_service.get_policy_for_equipment(equipment_id)
        if not policy_data:
            return None
            
        # Evaluate equipment against policy
        return policy_service.evaluate_equipment_against_policy(
            equipment_data, 
            policy_data
        )
        
    except Exception as e:
        logger.error(f"Error evaluating equipment against policy: {e}")
        return None


def get_policy_based_status_for_equipment(equipment_id):
    """
    Get policy-based status evaluation for a specific equipment.
    
    Args:
        equipment_id (int): Equipment ID
        
    Returns:
        dict: Policy-based status or None if no policy applies
    """
    if not POLICY_SUPPORT:
        return None
        
    try:
        # Get equipment data
        equipment = Equipment.get_by_id(equipment_id)
        if not equipment:
            return None
            
        return get_equipment_policy_status(equipment)
    except Exception as e:
        logger.error(f"Error getting policy-based status: {e}")
        return None
        

def migrate_criteria_to_policies():
    """
    Migrate existing discard criteria to the policy system.
    
    Returns:
        dict: Statistics on migration
    """
    if not POLICY_SUPPORT:
        return {'error': 'Policy support not available'}
        
    try:
        stats = {
            'processed': 0,
            'created': 0,
            'updated': 0,
            'skipped': 0,
            'errors': 0
        }
        
        # Ensure policy tables exist
        policy_service.ensure_tables_exist()
        
        # Create default policies based on discard criteria
        created = policy_service.create_default_policies()
        stats['created'] = created
        
        return stats
        
    except Exception as e:
        logger.error(f"Error migrating criteria to policies: {e}")
        return {'error': str(e)}


def send_discard_notification(equipment_data, criteria_data, notification_type="WARNING"):
    """
    Send notification for equipment approaching or meeting discard criteria.
    
    Args:
        equipment_data (dict): Equipment information
        criteria_data (dict): Discard criteria information
        notification_type (str): Type of notification (WARNING, CRITICAL, DISCARD)
    """
    if not config.DISCARD_NOTIFICATION_ENABLED:
        return
    
    try:
        ba_number = equipment_data.get('ba_number', 'Unknown')
        make_type = equipment_data.get('make_and_type', 'Unknown')
        
        current_vintage = float(equipment_data.get('vintage_years', 0))
        current_kms = float(equipment_data.get('meterage_kms', 0))
        current_hours = float(equipment_data.get('hours_run_total', 0))
        
        criteria_years = criteria_data.get('criteria_years', 0)
        criteria_kms = criteria_data.get('criteria_kms', 0)
        criteria_hours = criteria_data.get('criteria_hours', 0)
        
        message = f"Equipment {ba_number} ({make_type}) - {notification_type}\n"
        message += f"Current Status: {current_vintage:.1f} years, {current_kms:,.0f} km, {current_hours:,.0f} hours\n"
        message += f"Discard Criteria: {criteria_years} years, {criteria_kms:,.0f} km, {criteria_hours:,.0f} hours\n"
        
        if notification_type == "WARNING":
            message += "⚠️ Equipment is approaching discard criteria"
        elif notification_type == "CRITICAL":
            message += "🚨 Equipment has exceeded discard criteria"
        elif notification_type == "DISCARD":
            message += "❌ Equipment should be discarded immediately"
        
        logger.warning(f"DISCARD NOTIFICATION: {message}")
        
        # Here you could add email/SMS integration
        # send_email_notification(message)
        # send_sms_notification(message)
        
    except Exception as e:
        logger.error(f"Error sending discard notification: {e}")


def check_discard_warnings():
    """
    Check all equipment for discard criteria warnings and send notifications.
    
    Returns:
        dict: Summary of notifications sent
    """
    try:
        all_criteria = DiscardCriteria.get_all()
        notifications_sent = {
            'warning': 0,
            'critical': 0,
            'discard': 0
        }
        
        for criteria in all_criteria:
            equipment_id = criteria.get('equipment_id')
            if not equipment_id:
                continue
            
            # Get current equipment status
            current_vintage = float(criteria.get('vintage_years', 0))
            current_kms = float(criteria.get('meterage_kms', 0))
            current_hours = float(criteria.get('hours_run_total', 0))
            
            criteria_years = float(criteria.get('criteria_years', 0))
            criteria_kms = float(criteria.get('criteria_kms', 0))
            criteria_hours = float(criteria.get('criteria_hours', 0))
            
            # Calculate percentage of criteria met
            year_percent = current_vintage / criteria_years if criteria_years > 0 else 0
            km_percent = current_kms / criteria_kms if criteria_kms > 0 else 0
            hour_percent = current_hours / criteria_hours if criteria_hours > 0 else 0
            
            max_percent = max(year_percent, km_percent, hour_percent)
            
            # Determine notification type
            notification_type = None
            if max_percent >= config.DISCARD_CRITICAL_THRESHOLD:
                notification_type = "DISCARD"
                notifications_sent['discard'] += 1
            elif max_percent >= config.DISCARD_WARNING_THRESHOLD:
                notification_type = "WARNING"
                notifications_sent['warning'] += 1
            
            # Send notification if needed
            if notification_type and not criteria.get('notification_sent'):
                send_discard_notification(criteria, criteria, notification_type)
                
                # Update notification flag
                # (You'd need to add this functionality to the DiscardCriteria model)
        
        return notifications_sent
        
    except Exception as e:
        logger.error(f"Error checking discard warnings: {e}")
        return {'error': str(e)}