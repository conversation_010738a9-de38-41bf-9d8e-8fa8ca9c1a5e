#!/usr/bin/env python3
"""
FINAL MAINTENANCE SCHEMA FIX
This script fixes the maintenance table schema and verifies the fix works.
"""

import os
import sys
import sqlite3
import logging
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import project modules
import config
import database

def get_database_path():
    """Get the actual database path used by the application."""
    db_path = getattr(config, 'DATABASE_PATH', 'inventory.db')
    if not os.path.isabs(db_path):
        # Make it relative to the project root
        project_root = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(project_root, db_path)
    return db_path

def check_maintenance_table_schema(db_path):
    """Check the current maintenance table schema."""
    print(f"🔍 Checking maintenance table schema in: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"❌ Database file does not exist: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if maintenance table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='maintenance'")
        if not cursor.fetchone():
            print("❌ Maintenance table does not exist!")
            conn.close()
            return False
        
        # Get table schema
        cursor.execute("PRAGMA table_info(maintenance)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"📋 Current maintenance table columns: {column_names}")
        
        # Check for required columns
        required_columns = ['maintenance_id', 'equipment_id', 'maintenance_type', 
                          'done_date', 'next_due_date', 'maintenance_category']
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            print(f"❌ Missing required columns: {missing_columns}")
            conn.close()
            return False
        
        # Check for old column that shouldn't exist
        if 'due_date' in column_names:
            print("⚠️ Found old 'due_date' column - needs migration!")
            conn.close()
            return False
        
        print("✅ Maintenance table schema is correct!")
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error checking schema: {e}")
        return False

def migrate_maintenance_table(db_path):
    """Migrate the maintenance table to correct schema."""
    print(f"🔧 Migrating maintenance table in: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Step 1: Check current schema
        cursor.execute("PRAGMA table_info(maintenance)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"📋 Current columns: {column_names}")
        
        # Step 2: Create backup
        print("📋 Creating backup...")
        cursor.execute("DROP TABLE IF EXISTS maintenance_backup")
        cursor.execute("CREATE TABLE maintenance_backup AS SELECT * FROM maintenance")
        
        # Step 3: Drop and recreate maintenance table
        print("🔧 Recreating maintenance table...")
        cursor.execute("DROP TABLE maintenance")
        
        cursor.execute("""
            CREATE TABLE maintenance (
                maintenance_id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                maintenance_type TEXT NOT NULL,
                done_date TEXT,
                next_due_date TEXT,
                vintage_years REAL DEFAULT 0,
                meterage_kms REAL DEFAULT 0,
                completion_notes TEXT,
                status TEXT DEFAULT 'scheduled',
                completed_by TEXT,
                actual_completion_date TEXT,
                completion_meterage REAL,
                maintenance_category TEXT DEFAULT 'TM-1'
            )
        """)
        
        # Step 4: Migrate data from backup
        print("📊 Migrating data...")
        backup_columns = [col[1] for col in cursor.execute("PRAGMA table_info(maintenance_backup)").fetchall()]
        
        # Create column mapping for data migration
        column_mapping = {
            'maintenance_id': 'maintenance_id',
            'equipment_id': 'equipment_id', 
            'maintenance_type': 'maintenance_type',
            'done_date': 'done_date',
            'next_due_date': 'due_date' if 'due_date' in backup_columns else 'next_due_date',
            'vintage_years': 'vintage_years',
            'meterage_kms': 'meterage_kms',
            'completion_notes': 'completion_notes' if 'completion_notes' in backup_columns else 'NULL',
            'status': 'status' if 'status' in backup_columns else "'scheduled'",
            'completed_by': 'completed_by' if 'completed_by' in backup_columns else 'NULL',
            'actual_completion_date': 'actual_completion_date' if 'actual_completion_date' in backup_columns else 'NULL',
            'completion_meterage': 'completion_meterage' if 'completion_meterage' in backup_columns else 'NULL',
            'maintenance_category': 'maintenance_category' if 'maintenance_category' in backup_columns else "'TM-1'"
        }
        
        # Build migration query
        source_columns = []
        for new_col, old_col in column_mapping.items():
            if old_col.startswith("'") or old_col == 'NULL':
                source_columns.append(f"{old_col} as {new_col}")
            else:
                source_columns.append(f"{old_col} as {new_col}")
        
        migration_query = f"""
            INSERT INTO maintenance ({', '.join(column_mapping.keys())})
            SELECT {', '.join(source_columns)}
            FROM maintenance_backup
        """
        
        print(f"🔄 Migration query: {migration_query}")
        cursor.execute(migration_query)
        
        # Step 5: Create indexes
        print("⚡ Creating indexes...")
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_maintenance_equipment_category_status 
            ON maintenance(equipment_id, maintenance_category, status)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_maintenance_next_due_date 
            ON maintenance(next_due_date)
        """)
        
        # Step 6: Verify migration
        cursor.execute("SELECT COUNT(*) FROM maintenance")
        new_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_backup")
        old_count = cursor.fetchone()[0]
        
        print(f"📊 Migration complete: {old_count} → {new_count} records")
        
        # Commit changes
        conn.commit()
        
        # Clean up backup table
        cursor.execute("DROP TABLE maintenance_backup")
        conn.commit()
        conn.close()
        
        print("✅ Migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def test_maintenance_queries(db_path):
    """Test that maintenance queries work correctly after migration."""
    print("🧪 Testing maintenance queries...")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test 1: Basic query with maintenance_category
        print("📝 Test 1: Basic maintenance query with category...")
        cursor.execute("""
            SELECT m.*, e.make_and_type, e.ba_number
            FROM maintenance m
            JOIN equipment e ON m.equipment_id = e.equipment_id
            WHERE m.maintenance_category = 'TM-1'
            LIMIT 5
        """)
        results = cursor.fetchall()
        print(f"✅ Found {len(results)} TM-1 maintenance records")
        
        # Test 2: Query by status
        print("📝 Test 2: Query by status...")
        cursor.execute("""
            SELECT COUNT(*) FROM maintenance 
            WHERE status = 'scheduled'
        """)
        scheduled_count = cursor.fetchone()[0]
        print(f"✅ Found {scheduled_count} scheduled maintenance records")
        
        # Test 3: Query with next_due_date
        print("📝 Test 3: Query with next_due_date...")
        cursor.execute("""
            SELECT COUNT(*) FROM maintenance 
            WHERE next_due_date IS NOT NULL
        """)
        due_date_count = cursor.fetchone()[0]
        print(f"✅ Found {due_date_count} maintenance records with due dates")
        
        conn.close()
        print("✅ All maintenance queries working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Query test failed: {e}")
        return False

def main():
    """Main function to fix maintenance schema."""
    print("🚀 FINAL MAINTENANCE SCHEMA FIX")
    print("=" * 50)
    
    # Get database path
    db_path = get_database_path()
    print(f"📂 Database path: {db_path}")
    
    # Check if database exists
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        print("💡 Try running the application first to create the database.")
        return False
    
    # Check current schema
    if check_maintenance_table_schema(db_path):
        print("✅ Schema is already correct!")
    else:
        # Migrate schema
        if not migrate_maintenance_table(db_path):
            print("❌ Migration failed!")
            return False
    
    # Test queries
    if not test_maintenance_queries(db_path):
        print("❌ Query tests failed!")
        return False
    
    print("\n🎉 SUCCESS! Maintenance schema is fixed and working!")
    print("🔄 You can now restart the application and import maintenance records.")
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1) 