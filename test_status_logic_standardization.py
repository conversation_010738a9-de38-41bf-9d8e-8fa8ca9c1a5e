#!/usr/bin/env python3
"""
Test script to verify status calculation logic is standardized 
across all maintenance types and components.
"""

import sys
import os
sys.path.append('.')

import utils
from datetime import date, datetime, timedelta

def test_status_calculation_consistency():
    """Test that status calculation is consistent for all maintenance types."""
    print('Testing Status Calculation Logic Standardization')
    print('=' * 60)
    
    # Test different maintenance scenarios
    test_scenarios = [
        {
            'name': 'TM-1 Overdue (completed 2024-01-01)',
            'category': 'TM-1',
            'done_date': '2024-01-01',
            'expected_next_due': '2024-07-01',  # 6 months later
            'expected_status': 'overdue'
        },
        {
            'name': 'TM-1 Warning (completed 2025-01-01)', 
            'category': 'TM-1',
            'done_date': '2025-01-01',
            'expected_next_due': '2025-07-01',  # 6 months later
            'expected_status': 'warning'
        },
        {
            'name': 'TM-2 Overdue (completed 2024-01-01)',
            'category': 'TM-2', 
            'done_date': '2024-01-01',
            'expected_next_due': '2025-01-01',  # 12 months later
            'expected_status': 'overdue'
        },
        {
            'name': 'TM-2 Scheduled (completed 2024-08-01)',
            'category': 'TM-2',
            'done_date': '2024-08-01', 
            'expected_next_due': '2025-08-01',  # 12 months later
            'expected_status': 'upcoming'
        },
        {
            'name': 'Yearly Scheduled (completed 2024-08-01)',
            'category': 'Yearly',
            'done_date': '2024-08-01',
            'expected_next_due': '2025-08-01',  # 12 months later
            'expected_status': 'upcoming'
        },
        {
            'name': 'Monthly Critical (completed 2025-05-25)',
            'category': 'Monthly', 
            'done_date': '2025-05-25',
            'expected_next_due': '2025-06-25',  # 1 month later
            'expected_status': 'critical'
        }
    ]
    
    print(f'Testing {len(test_scenarios)} scenarios...\n')
    
    all_passed = True
    
    for scenario in test_scenarios:
        print(f"Testing: {scenario['name']}")
        
        # Calculate next due date
        next_due_date = utils.calculate_next_due_date(scenario['done_date'], scenario['category'])
        
        # Create maintenance record for status calculation (same logic as both widgets)
        maintenance_for_status = {
            'status': 'scheduled',  # Reset for next cycle
            'due_date': next_due_date.isoformat() if next_due_date else None
        }
        
        # Calculate status
        calculated_status = utils.calculate_maintenance_status(maintenance_for_status)
        
        # Verify results
        next_due_str = next_due_date.isoformat() if next_due_date else 'None'
        expected_next_due = scenario['expected_next_due']
        expected_status = scenario['expected_status']
        
        next_due_match = next_due_str == expected_next_due
        status_match = calculated_status == expected_status
        
        print(f"  Next Due: {next_due_str} (expected: {expected_next_due}) {'✓' if next_due_match else '✗'}")
        print(f"  Status: {calculated_status} (expected: {expected_status}) {'✓' if status_match else '✗'}")
        
        if not (next_due_match and status_match):
            all_passed = False
            print(f"  ❌ FAILED")
        else:
            print(f"  ✅ PASSED")
        
        print()
    
    return all_passed

def test_status_categories():
    """Test all status categories work correctly."""
    print('Testing All Status Categories')
    print('=' * 40)
    
    today = date.today()
    
    test_cases = [
        # (due_date, expected_status)
        (today - timedelta(days=30), 'overdue'),
        (today - timedelta(days=1), 'overdue'), 
        (today, 'critical'),
        (today + timedelta(days=3), 'critical'),
        (today + timedelta(days=7), 'critical'),
        (today + timedelta(days=15), 'warning'),
        (today + timedelta(days=30), 'warning'),
        (today + timedelta(days=45), 'upcoming'),
        (today + timedelta(days=90), 'upcoming'),
        (today + timedelta(days=120), 'scheduled'),
    ]
    
    all_passed = True
    
    for due_date, expected_status in test_cases:
        maintenance = {
            'status': 'scheduled',
            'due_date': due_date.isoformat()
        }
        
        calculated_status = utils.calculate_maintenance_status(maintenance)
        
        days_diff = (due_date - today).days
        match = calculated_status == expected_status
        
        print(f"  Due in {days_diff:3d} days: {calculated_status:9s} (expected: {expected_status:9s}) {'✓' if match else '✗'}")
        
        if not match:
            all_passed = False
    
    return all_passed

def test_maintenance_intervals():
    """Test that maintenance intervals are calculated correctly."""
    print('\nTesting Maintenance Intervals')
    print('=' * 40)
    
    intervals = {
        'TM-1': 6,      # months
        'TM-2': 12,     # months  
        'Yearly': 12,   # months
        'Monthly': 1    # month
    }
    
    base_date = datetime.strptime('2024-01-01', '%Y-%m-%d').date()
    
    all_passed = True
    
    for category, expected_months in intervals.items():
        next_due = utils.calculate_next_due_date('2024-01-01', category)
        
        if next_due:
            # Calculate actual months difference
            months_diff = (next_due.year - base_date.year) * 12 + (next_due.month - base_date.month)
            
            match = months_diff == expected_months
            print(f"  {category:8s}: {months_diff:2d} months (expected: {expected_months:2d}) {'✓' if match else '✗'}")
            
            if not match:
                all_passed = False
        else:
            print(f"  {category:8s}: Failed to calculate next due date ✗")
            all_passed = False
    
    return all_passed

if __name__ == '__main__':
    try:
        print('Status Logic Standardization Test')
        print('=' * 60)
        
        test1_passed = test_status_calculation_consistency()
        test2_passed = test_status_categories()
        test3_passed = test_maintenance_intervals()
        
        print('\n' + '=' * 60)
        print('SUMMARY:')
        print(f'  Status Calculation Consistency: {"✅ PASSED" if test1_passed else "❌ FAILED"}')
        print(f'  Status Categories: {"✅ PASSED" if test2_passed else "❌ FAILED"}')
        print(f'  Maintenance Intervals: {"✅ PASSED" if test3_passed else "❌ FAILED"}')
        
        if test1_passed and test2_passed and test3_passed:
            print('\n🎉 ALL TESTS PASSED - Status logic is standardized!')
        else:
            print('\n⚠️  SOME TESTS FAILED - Status logic needs attention!')
            
    except Exception as e:
        print(f'Error during testing: {e}')
        import traceback
        traceback.print_exc()
