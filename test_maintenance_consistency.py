#!/usr/bin/env python3
"""
Test script to compare maintenance status calculations between 
maintenance widget and dashboard widget to identify discrepancies.
"""

import sys
import os
sys.path.append('.')

import database
import utils
from datetime import date, datetime

def get_maintenance_widget_data(category):
    """Get maintenance data using the same logic as maintenance widget."""
    print(f"  Getting maintenance widget data for {category}...")
    
    # Same query as maintenance widget uses
    query = """
        SELECT m.*, e.make_and_type, e.ba_number, e.vintage_years, e.meterage_kms
        FROM maintenance m
        JOIN equipment e ON m.equipment_id = e.equipment_id
        WHERE m.maintenance_category = %s
        AND e.is_active = 1
        AND (m.status != 'archived' OR m.status IS NULL)
        ORDER BY m.due_date
    """
    
    maintenance_list = database.execute_query(query, (category,))
    
    if not maintenance_list:
        return [], {}
    
    # Apply maintenance widget status calculation logic
    status_counts = {'overdue': 0, 'critical': 0, 'warning': 0, 'upcoming': 0, 'scheduled': 0, 'completed': 0, 'unknown': 0}
    processed_records = []
    
    for maintenance in maintenance_list:
        # Apply the MAINTENANCE WIDGET logic (from ui/maintenance_widget.py)
        done_date_val = maintenance.get('done_date')
        
        # Calculate next due date if maintenance was completed
        next_due_date = None
        if done_date_val:
            next_due_date = utils.calculate_next_due_date(done_date_val, category)
        
        # Determine which date to use for status calculation
        if done_date_val and next_due_date:
            # This maintenance was completed, so calculate status for the NEXT cycle
            maintenance_for_status = {
                'status': 'scheduled',  # Reset status for next cycle calculation
                'due_date': next_due_date.isoformat() if hasattr(next_due_date, 'isoformat') else str(next_due_date)
            }
        else:
            # This maintenance is not completed, use original due_date and status
            maintenance_for_status = {
                'status': maintenance.get('status', ''),
                'due_date': maintenance.get('due_date')
            }
        
        # Calculate status using centralized function
        calculated_status = utils.calculate_maintenance_status(maintenance_for_status)
        status_counts[calculated_status] = status_counts.get(calculated_status, 0) + 1
        
        processed_records.append({
            'id': maintenance.get('maintenance_id'),
            'ba_number': maintenance.get('ba_number'),
            'status': calculated_status,
            'done_date': done_date_val,
            'due_date': maintenance.get('due_date'),
            'next_due': next_due_date.isoformat() if next_due_date else None
        })
    
    return processed_records, status_counts

def get_dashboard_widget_data(category):
    """Get maintenance data using the same logic as dashboard widget."""
    print(f"  Getting dashboard widget data for {category}...")
    
    # Same query as dashboard widget uses
    query = """
        SELECT m.*, e.make_and_type, e.ba_number
        FROM maintenance m
        JOIN equipment e ON m.equipment_id = e.equipment_id
        WHERE m.maintenance_category = %s
        AND e.is_active = 1
        AND (m.status != 'archived' OR m.status IS NULL)
        ORDER BY m.due_date
    """
    
    maintenance_list = database.execute_query(query, (category,))
    
    if not maintenance_list:
        return [], {}
    
    # Apply dashboard widget status calculation logic
    status_counts = {'overdue': 0, 'critical': 0, 'warning': 0, 'upcoming': 0, 'scheduled': 0, 'completed': 0, 'unknown': 0}
    processed_records = []
    
    for maintenance in maintenance_list:
        # Apply the DASHBOARD WIDGET logic (from ui/dashboard_widget.py)
        done_date_val = maintenance.get('done_date')
        
        if done_date_val:
            # Calculate next due date using standardized logic
            next_due_date = utils.calculate_next_due_date(done_date_val, category)
            if next_due_date:
                # For completed maintenance, calculate status for the NEXT cycle
                maintenance_for_status = {
                    'status': 'scheduled',  # Reset status for next cycle calculation
                    'due_date': next_due_date.isoformat()
                }
            else:
                # Fallback to original due_date if calculation fails
                maintenance_for_status = {
                    'status': maintenance.get('status', ''),
                    'due_date': maintenance.get('due_date')
                }
        else:
            # No done date, use the maintenance record as-is
            maintenance_for_status = maintenance
        
        # Calculate status using centralized function
        calculated_status = utils.calculate_maintenance_status(maintenance_for_status)
        status_counts[calculated_status] = status_counts.get(calculated_status, 0) + 1
        
        processed_records.append({
            'id': maintenance.get('maintenance_id'),
            'ba_number': maintenance.get('ba_number'),
            'status': calculated_status,
            'done_date': done_date_val,
            'due_date': maintenance.get('due_date'),
            'next_due': next_due_date.isoformat() if done_date_val and next_due_date else None
        })
    
    return processed_records, status_counts

def compare_maintenance_data():
    """Compare maintenance data between widget and dashboard."""
    print('Comparing Maintenance Widget vs Dashboard Widget Data')
    print('=' * 70)
    
    categories = ['TM-1', 'TM-2', 'Yearly', 'Monthly']
    
    for category in categories:
        print(f'\nCategory: {category}')
        print('-' * 40)
        
        # Get data from both sources
        widget_records, widget_counts = get_maintenance_widget_data(category)
        dashboard_records, dashboard_counts = get_dashboard_widget_data(category)
        
        print(f"  Widget Records: {len(widget_records)}")
        print(f"  Dashboard Records: {len(dashboard_records)}")
        
        # Compare status counts
        print(f"  Status Count Comparison:")
        for status in ['overdue', 'critical', 'warning', 'upcoming', 'scheduled', 'completed', 'unknown']:
            widget_count = widget_counts.get(status, 0)
            dashboard_count = dashboard_counts.get(status, 0)
            match = "✓" if widget_count == dashboard_count else "✗"
            print(f"    {status.capitalize()}: Widget={widget_count}, Dashboard={dashboard_count} {match}")
        
        # Check for discrepancies in individual records
        if len(widget_records) != len(dashboard_records):
            print(f"  ⚠️  RECORD COUNT MISMATCH!")
        
        # Compare first few records for detailed analysis
        print(f"  Sample Record Comparison (first 3):")
        max_records = min(3, len(widget_records), len(dashboard_records))
        for i in range(max_records):
            w_rec = widget_records[i]
            d_rec = dashboard_records[i]
            
            if w_rec['status'] != d_rec['status']:
                print(f"    Record {i+1}: STATUS MISMATCH!")
                print(f"      Widget: {w_rec['status']} | Dashboard: {d_rec['status']}")
                print(f"      BA: {w_rec['ba_number']} | Done: {w_rec['done_date']} | Due: {w_rec['due_date']}")
            else:
                print(f"    Record {i+1}: Status match ({w_rec['status']})")

if __name__ == '__main__':
    try:
        compare_maintenance_data()
        print('\n' + '=' * 70)
        print('Maintenance consistency analysis completed!')
    except Exception as e:
        print(f'Error during analysis: {e}')
        import traceback
        traceback.print_exc()
