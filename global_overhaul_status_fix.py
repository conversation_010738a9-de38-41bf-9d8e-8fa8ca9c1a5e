#!/usr/bin/env python3
"""
Global Overhaul Status Recalculation Tool for PROJECT-ALPHA

This script provides comprehensive overhaul status recalculation functionality
that can be used to fix status calculation issues across the entire system.

Key Features:
- Recalculates ALL overhaul statuses using centralized logic
- Handles missing due dates by calculating them from commission dates
- Creates missing overhaul records for equipment without them
- Provides detailed logging and progress reporting
- Can be run independently or integrated with other systems

Usage:
- Run standalone: python global_overhaul_status_fix.py
- Import and use: from global_overhaul_status_fix import fix_all_overhaul_statuses
"""

import sys
import os
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_all_overhaul_statuses(force_recalculate: bool = False) -> Dict[str, Any]:
    """
    Comprehensive overhaul status fix for all equipment records.
    
    Args:
        force_recalculate: If True, recalculates all statuses even if they seem correct
        
    Returns:
        Dict with statistics about the fix operation
    """
    logger.info("🔧 Starting comprehensive overhaul status recalculation...")
    
    stats = {
        'total_equipment': 0,
        'overhauls_found': 0,
        'overhauls_created': 0,
        'statuses_updated': 0,
        'due_dates_calculated': 0,
        'errors': [],
        'equipment_processed': [],
        'unknown_statuses_fixed': 0
    }
    
    try:
        from models import Equipment, Overhaul
        import overhaul_service
        
        # Get all equipment records
        all_equipment = Equipment.get_all()
        stats['total_equipment'] = len(all_equipment)
        
        logger.info(f"Found {stats['total_equipment']} equipment records to process")
        
        for equipment in all_equipment:
            try:
                equipment_id = equipment.get('equipment_id')
                ba_number = equipment.get('ba_number') or equipment.get('BANumber')
                make_type = equipment.get('make_and_type') or equipment.get('MakeAndType')
                
                logger.debug(f"Processing equipment {equipment_id}: {ba_number} - {make_type}")
                
                # Ensure overhaul records exist for this equipment
                created_count = ensure_overhaul_records_exist(equipment_id, equipment)
                stats['overhauls_created'] += created_count
                
                # Get all overhauls for this equipment
                overhauls = Overhaul.get_by_equipment(equipment_id)
                stats['overhauls_found'] += len(overhauls)
                
                # Process each overhaul
                for overhaul in overhauls:
                    try:
                        overhaul_id = overhaul.get('overhaul_id')
                        overhaul_type = overhaul.get('overhaul_type')
                        current_status = overhaul.get('status')
                        due_date = overhaul.get('due_date')
                        done_date = overhaul.get('done_date')
                        
                        # Calculate missing due dates
                        if not due_date or due_date in ['', 'None', None]:
                            calculated_due_date = calculate_missing_due_date(equipment, overhaul_type, overhauls)
                            if calculated_due_date:
                                Overhaul.update(overhaul_id, due_date=calculated_due_date)
                                stats['due_dates_calculated'] += 1
                                logger.info(f"Calculated due date for {overhaul_type} (ID: {overhaul_id}): {calculated_due_date}")
                                # Update the overhaul dict for status calculation
                                overhaul['due_date'] = calculated_due_date
                        
                        # Get OH-I done date for OH-II calculations
                        oh1_done_date = None
                        if overhaul_type == 'OH-II':
                            oh1 = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-I'), None)
                            oh1_done_date = oh1.get('done_date') if oh1 else None
                        
                        # Calculate new status using centralized function
                        new_status = overhaul_service.get_overhaul_status(
                            overhaul_type,
                            overhaul.get('due_date'),
                            done_date,
                            date_of_commission=equipment.get('date_of_commission'),
                            oh1_done_date=oh1_done_date,
                            meterage_km=equipment.get('meterage_kms') or equipment.get('MeterageKMs')
                        )
                        
                        # Update status if changed or if forcing recalculation
                        if new_status != current_status or force_recalculate:
                            Overhaul.update(overhaul_id, status=new_status)
                            stats['statuses_updated'] += 1
                            
                            if current_status == 'unknown':
                                stats['unknown_statuses_fixed'] += 1
                            
                            logger.info(f"Updated {overhaul_type} status (ID: {overhaul_id}): '{current_status}' → '{new_status}'")
                        
                    except Exception as e:
                        error_msg = f"Error processing overhaul {overhaul.get('overhaul_id')}: {e}"
                        logger.error(error_msg)
                        stats['errors'].append(error_msg)
                
                stats['equipment_processed'].append({
                    'equipment_id': equipment_id,
                    'ba_number': ba_number,
                    'make_type': make_type
                })
                
            except Exception as e:
                error_msg = f"Error processing equipment {equipment.get('equipment_id')}: {e}"
                logger.error(error_msg)
                stats['errors'].append(error_msg)
        
        # Final summary
        logger.info("✅ Comprehensive overhaul status recalculation completed!")
        logger.info(f"📊 Summary:")
        logger.info(f"  - Equipment processed: {len(stats['equipment_processed'])}/{stats['total_equipment']}")
        logger.info(f"  - Overhauls found: {stats['overhauls_found']}")
        logger.info(f"  - Overhauls created: {stats['overhauls_created']}")
        logger.info(f"  - Due dates calculated: {stats['due_dates_calculated']}")
        logger.info(f"  - Statuses updated: {stats['statuses_updated']}")
        logger.info(f"  - Unknown statuses fixed: {stats['unknown_statuses_fixed']}")
        logger.info(f"  - Errors: {len(stats['errors'])}")
        
        return stats
        
    except Exception as e:
        error_msg = f"Critical error in overhaul status recalculation: {e}"
        logger.error(error_msg)
        stats['errors'].append(error_msg)
        return stats

def ensure_overhaul_records_exist(equipment_id: int, equipment: Dict[str, Any]) -> int:
    """
    Ensure that OH-I and OH-II overhaul records exist for equipment.
    Creates missing records with proper due dates.
    
    Returns:
        Number of overhaul records created
    """
    created_count = 0
    
    try:
        from models import Overhaul
        
        # Get existing overhauls
        existing_overhauls = Overhaul.get_by_equipment(equipment_id)
        existing_types = [oh.get('overhaul_type') for oh in existing_overhauls]
        
        # Create OH-I if missing
        if 'OH-I' not in existing_types:
            oh1_due_date = calculate_missing_due_date(equipment, 'OH-I', existing_overhauls)
            
            Overhaul.create(
                equipment_id=equipment_id,
                overhaul_type='OH-I',
                due_date=oh1_due_date,
                status='scheduled',
                notes='Auto-created during status recalculation'
            )
            created_count += 1
            logger.info(f"Created OH-I record for equipment {equipment_id} with due date: {oh1_due_date}")
        
        # Create OH-II if missing
        if 'OH-II' not in existing_types:
            oh2_due_date = calculate_missing_due_date(equipment, 'OH-II', existing_overhauls)
            
            Overhaul.create(
                equipment_id=equipment_id,
                overhaul_type='OH-II',
                due_date=oh2_due_date,
                status='scheduled',
                notes='Auto-created during status recalculation'
            )
            created_count += 1
            logger.info(f"Created OH-II record for equipment {equipment_id} with due date: {oh2_due_date}")
        
        return created_count
        
    except Exception as e:
        logger.error(f"Error ensuring overhaul records exist for equipment {equipment_id}: {e}")
        return 0

def calculate_missing_due_date(equipment: Dict[str, Any], overhaul_type: str, existing_overhauls: List[Dict[str, Any]]) -> Optional[str]:
    """
    Calculate missing due dates based on business rules.
    
    Args:
        equipment: Equipment record
        overhaul_type: 'OH-I' or 'OH-II'
        existing_overhauls: List of existing overhaul records for this equipment
        
    Returns:
        Due date string in ISO format or None if cannot be calculated
    """
    try:
        if overhaul_type == 'OH-I':
            # OH-I due 15 years after commission date
            commission_date = equipment.get('date_of_commission')
            if commission_date:
                if isinstance(commission_date, str):
                    try:
                        commission_date_obj = date.fromisoformat(commission_date.split(' ')[0])
                    except:
                        return None
                else:
                    commission_date_obj = commission_date
                
                due_date_obj = commission_date_obj + timedelta(days=365*15)
                return due_date_obj.isoformat()
        
        elif overhaul_type == 'OH-II':
            # OH-II due 10 years after OH-I completion
            oh1 = next((oh for oh in existing_overhauls if oh.get('overhaul_type') == 'OH-I'), None)
            if oh1 and oh1.get('done_date'):
                oh1_done_date = oh1.get('done_date')
                if isinstance(oh1_done_date, str):
                    try:
                        oh1_done_date_obj = date.fromisoformat(oh1_done_date.split(' ')[0])
                    except:
                        return None
                else:
                    oh1_done_date_obj = oh1_done_date
                
                due_date_obj = oh1_done_date_obj + timedelta(days=365*10)
                return due_date_obj.isoformat()
            else:
                # If OH-I not completed, calculate based on commission + 25 years (15 + 10)
                commission_date = equipment.get('date_of_commission')
                if commission_date:
                    if isinstance(commission_date, str):
                        try:
                            commission_date_obj = date.fromisoformat(commission_date.split(' ')[0])
                        except:
                            return None
                    else:
                        commission_date_obj = commission_date
                    
                    due_date_obj = commission_date_obj + timedelta(days=365*25)
                    return due_date_obj.isoformat()
        
        return None
        
    except Exception as e:
        logger.error(f"Error calculating due date for {overhaul_type}: {e}")
        return None

def run_status_fix_standalone():
    """Run the status fix as a standalone script."""
    logger.info("🚀 PROJECT-ALPHA Global Overhaul Status Fix")
    logger.info("=" * 60)
    
    try:
        # Run the comprehensive fix
        stats = fix_all_overhaul_statuses(force_recalculate=False)
        
        # Display results
        if stats['errors']:
            logger.warning(f"⚠️  {len(stats['errors'])} errors occurred during processing:")
            for error in stats['errors'][:5]:  # Show first 5 errors
                logger.warning(f"  - {error}")
            if len(stats['errors']) > 5:
                logger.warning(f"  ... and {len(stats['errors']) - 5} more errors")
        
        if stats['statuses_updated'] > 0 or stats['unknown_statuses_fixed'] > 0:
            logger.info("✅ Overhaul status fix completed successfully!")
            logger.info(f"🎯 Key Results:")
            logger.info(f"  - {stats['unknown_statuses_fixed']} 'unknown' statuses fixed")
            logger.info(f"  - {stats['statuses_updated']} total statuses updated")
            logger.info(f"  - {stats['due_dates_calculated']} due dates calculated")
            logger.info(f"  - {stats['overhauls_created']} overhaul records created")
        else:
            logger.info("ℹ️  All overhaul statuses are already correct - no updates needed")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Global overhaul status fix failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = run_status_fix_standalone()
    
    logger.info("\n" + "=" * 60)
    if success:
        logger.info("✅ GLOBAL OVERHAUL STATUS FIX COMPLETE")
        logger.info("All overhaul statuses have been recalculated and updated.")
    else:
        logger.error("❌ GLOBAL OVERHAUL STATUS FIX FAILED")
        logger.error("Please check the logs and address any issues.")
    
    sys.exit(0 if success else 1)
