from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem, 
    QHeaderView, QPushButton, QLineEdit, QLabel, QSplitter, QFrame, 
    QGroupBox, QFormLayout, QTextEdit, QComboBox, QDateEdit, QSpinBox,
    QMessageBox, QTabWidget
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QColor
from datetime import datetime, date, timedelta
import logging

import database
import config
from models import Equipment, MediumReset

# Configure logger
logger = logging.getLogger('medium_reset_widget')


class MediumResetSubWidget(QWidget):
    """Widget for a single Medium Reset category (e.g., MR 1, MR 2)."""
    def __init__(self, category, parent=None):
        super().__init__(parent)
        self.category = category
        self.current_equipment = None # Stores the currently selected equipment for form context
        self.current_medium_reset = None # Stores the current manual medium reset entry for form context
        self.current_equipment_id = None # Track equipment ID for potential deletion

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Set up the user interface for the sub-widget."""
        layout = QVBoxLayout(self)

        # Top section - Medium Reset table (full width)
        table_panel = self.create_table_panel()
        layout.addWidget(table_panel)
        
        # Bottom section - Medium Reset details form and summary
        bottom_splitter = QSplitter(Qt.Horizontal)

        details_panel = self.create_right_panel() # This is the form
        bottom_splitter.addWidget(details_panel)

        summary_panel = self.create_status_summary_panel() # Renamed
        bottom_splitter.addWidget(summary_panel)

        bottom_splitter.setSizes([int(self.width() * 0.7), int(self.width() * 0.3)]) # Adjust initial sizes

        layout.addWidget(bottom_splitter)
        
        self.setLayout(layout)
        
        # Apply styles (can be inherited or set specifically if needed)
        # self.setStyleSheet(config.UI_STYLE) # Already applied by parent
        
        self.connect_signals()
        self.update_status_summary()


    def create_table_panel(self):
        """Create the table panel with medium reset list."""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Search section
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("Search:"))
        self.search_field = QLineEdit()
        self.search_field.setPlaceholderText(f"Search {self.category} medium resets...")
        search_layout.addWidget(self.search_field)
        layout.addLayout(search_layout)
        
        # Medium Reset table
        self.medium_reset_table = QTableWidget()

        if self.category == "MR 1":
            self.medium_reset_table.setColumnCount(6)
            self.medium_reset_table.setHorizontalHeaderLabels([
                "ID", "Equipment", "Release Date", "Current KM",
                "MR1 Due", "MR1 Status"
            ])
        elif self.category == "MR 2":
            self.medium_reset_table.setColumnCount(6)
            self.medium_reset_table.setHorizontalHeaderLabels([
                "ID", "Equipment", "Release Date", "Current KM",
                "MR2 Due", "MR2 Status"
            ])
        # Add elif for "OH 1", "OH 2" if they become separate tabs with different column needs

        header = self.medium_reset_table.horizontalHeader()
        header.setStretchLastSection(True)
        # Generic 6-column resizing
        header.resizeSection(0, 50)   # ID
        header.resizeSection(1, 150)  # Equipment
        header.resizeSection(2, 100)  # Date of Release
        header.resizeSection(3, 100)  # Current KM
        header.resizeSection(4, 100)  # Due Date (MR1 Due or MR2 Due)
        header.resizeSection(5, 100)  # Status (MR1 Status or MR2 Status)
        
        self.medium_reset_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.medium_reset_table.setAlternatingRowColors(True)
        layout.addWidget(self.medium_reset_table)
        
        # Action buttons
        button_layout = QHBoxLayout()
        self.add_button = QPushButton("Add Manual Entry")
        self.edit_button = QPushButton("Edit Selected") # Clarified button text
        self.delete_button = QPushButton("Delete Selected") # Clarified button text
        self.refresh_button = QPushButton("Refresh Data")
        
        self.edit_button.setEnabled(False)
        self.edit_button.setToolTip("Select a manual entry (Manual-XX) to edit. Calculated equipment data cannot be edited.")
        
        self.delete_button.setEnabled(False)
        self.delete_button.setToolTip("Select any row to delete. Manual entries are deleted, equipment data is deactivated.")
        
        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.edit_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addWidget(self.refresh_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        widget.setLayout(layout)
        return widget

    def create_status_summary_panel(self):
        """Create status summary section for this category."""
        summary_group = QGroupBox(f"{self.category} Maintenance Summary")
        summary_layout = QVBoxLayout()
        
        self.overdue_label = QLabel("Overdue: 0")
        self.overdue_label.setStyleSheet('color: red; font-weight: bold; font-size: 14px;')
        
        self.due_soon_label = QLabel("Due Soon: 0")
        self.due_soon_label.setStyleSheet('color: orange; font-weight: bold; font-size: 14px;')
        
        self.ok_label = QLabel("OK: 0")
        self.ok_label.setStyleSheet('color: green; font-weight: bold; font-size: 14px;')
        
        summary_layout.addWidget(self.overdue_label)
        summary_layout.addWidget(self.due_soon_label)
        summary_layout.addWidget(self.ok_label)
        
        summary_group.setLayout(summary_layout)
        return summary_group

    def create_right_panel(self):
        """Create the details panel with medium reset form (for manual entries)."""
        widget = QWidget()
        layout = QVBoxLayout()
        
        form_group = QGroupBox(f"Manual {self.category} Entry Details")
        main_form_layout = QHBoxLayout()
        
        left_form_layout = QFormLayout()
        self.id_field = QLineEdit()
        self.id_field.setReadOnly(True)
        left_form_layout.addRow("ID (Manual Entry):", self.id_field)
        
        self.equipment_field = QComboBox()
        left_form_layout.addRow("Equipment:", self.equipment_field)
        
        self.reset_type_field = QComboBox()
        self.reset_type_field.addItems(["Minor Reset", "Major Reset", "Complete Reset", "Partial Reset"]) # Generic, might be category specific
        left_form_layout.addRow("Reset Type:", self.reset_type_field)
        
        self.done_date_field = QDateEdit()
        self.done_date_field.setDate(QDate.currentDate())
        self.done_date_field.setCalendarPopup(True)
        left_form_layout.addRow("Done Date:", self.done_date_field)
        
        self.due_date_field = QDateEdit() # For manual entries, due date is explicit
        self.due_date_field.setDate(QDate.currentDate().addYears(1))
        self.due_date_field.setCalendarPopup(True)
        left_form_layout.addRow("Due Date (Manual):", self.due_date_field)
        
        right_form_layout = QFormLayout()
        self.status_label = QLabel() # Status for the selected equipment's overall MR/OH
        self.status_label.setStyleSheet('font-weight: bold;')
        right_form_layout.addRow("Selected Equip. Status:", self.status_label)
        
        self.meter_reading_field = QSpinBox()
        self.meter_reading_field.setMaximum(999999)
        right_form_layout.addRow("Meter Reading (at time of entry):", self.meter_reading_field)
        
        self.hours_reading_field = QSpinBox()
        self.hours_reading_field.setMaximum(999999)
        right_form_layout.addRow("Hours Reading (at time of entry):", self.hours_reading_field)
        
        text_layout = QVBoxLayout()
        self.description_field = QTextEdit()
        self.description_field.setMaximumHeight(60)
        text_layout.addWidget(QLabel("Description/Work Done:"))
        text_layout.addWidget(self.description_field)
        
        self.notes_field = QTextEdit()
        self.notes_field.setMaximumHeight(60)
        text_layout.addWidget(QLabel("Notes:"))
        text_layout.addWidget(self.notes_field)
        
        main_form_layout.addLayout(left_form_layout)
        main_form_layout.addLayout(right_form_layout)
        main_form_layout.addLayout(text_layout)
        
        form_group.setLayout(main_form_layout)
        layout.addWidget(form_group)
        
        button_layout = QHBoxLayout()
        self.save_button = QPushButton("Save Manual Entry")
        self.cancel_button = QPushButton("Cancel")
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        self.set_form_enabled(False)
        widget.setLayout(layout)
        return widget
    
    def connect_signals(self):
        """Connect widget signals to their handlers."""
        self.search_field.textChanged.connect(self.on_search_changed)
        self.medium_reset_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.add_button.clicked.connect(self.add_medium_reset_entry) # Changed to add_medium_reset_entry for clarity
        self.edit_button.clicked.connect(self.edit_medium_reset)
        self.delete_button.clicked.connect(self.delete_medium_reset)
        self.save_button.clicked.connect(self.save_medium_reset)
        self.cancel_button.clicked.connect(self.cancel_edit)
        self.refresh_button.clicked.connect(self.refresh_data)

    def load_data(self):
        """Load medium reset data from database for this category."""
        logger.info(f"Loading data for {self.category}")
        try:
            # Clear the table first to ensure proper refresh
            self.medium_reset_table.setRowCount(0)
            self.medium_reset_table.clearContents()
            
            # Clear form and reset state
            self.clear_form()
            self.current_equipment = None
            self.current_medium_reset = None
            self.current_equipment_id = None
            
            # Load fresh data
            equipment_data = self.get_filtered_equipment() # This gets all relevant equipment
            if equipment_data:
                self.populate_medium_reset_table(equipment_data) # Populate based on category
            else:
                # If no equipment data, ensure table is empty
                self.medium_reset_table.setRowCount(0)
                logger.info(f"No equipment data found for {self.category}")
                
            self.load_equipment_data_for_form() # Load equipment for the manual entry form dropdown
            self.update_status_summary()
            
            logger.info(f"Data loaded successfully for {self.category}. Equipment count: {len(equipment_data) if equipment_data else 0}")
            
            # Force immediate UI update
            from PyQt5.QtWidgets import QApplication
            QApplication.processEvents()
            
        except Exception as e:
            logger.error(f"Error loading {self.category} medium reset data: {e}", exc_info=True)
            QMessageBox.critical(self, "Error", f"Failed to load {self.category} data: {str(e)}")

    def load_equipment_data_for_form(self):
        """Load equipment data for the manual entry form dropdown."""
        try:
            equipment_list = Equipment.get_active()
            self.equipment_field.clear()
            self.equipment_field.addItem("Select Equipment", None)
            # Updated to match our imported equipment types (BMP and AERV vehicles)
            for equipment in equipment_list:
                make_and_type = equipment['make_and_type']
                if 'BMP' in make_and_type or 'AERV' in make_and_type:
                    display_text = f"{make_and_type} - {equipment['ba_number'] or 'N/A'}"
                    self.equipment_field.addItem(display_text, equipment['equipment_id'])
        except Exception as e:
            logger.error(f"Error loading equipment data for form: {e}", exc_info=True)

    def get_filtered_equipment(self):
        """Get equipment data filtered for allowed vehicle types."""
        # Updated to match the actual equipment types imported from Excel
        # Filter for BMP and AERV vehicles using LIKE to match our imported naming convention
        query = """
            SELECT equipment_id, make_and_type, date_of_commission, meterage_kms, ba_number
            FROM equipment 
            WHERE (make_and_type LIKE '%BMP%' OR make_and_type LIKE '%AERV%')
            AND is_active = 1
            ORDER BY make_and_type, equipment_id
        """
        return database.execute_query(query)
    
    def get_manual_entries_for_category(self):
        """Get manual medium reset entries for this category."""
        try:
            # Updated to match the actual equipment types imported from Excel
            query = """
                SELECT mr.*, e.make_and_type, e.ba_number, e.meterage_kms
                FROM medium_resets mr
                JOIN equipment e ON mr.equipment_id = e.equipment_id
                WHERE (e.make_and_type LIKE '%BMP%' OR e.make_and_type LIKE '%AERV%')
                AND e.is_active = 1
                ORDER BY mr.medium_reset_id DESC
            """
            return database.execute_query(query)
        except Exception as e:
            logger.error(f"Error getting manual entries for {self.category}: {e}", exc_info=True)
            return []

    def get_date_of_release(self, date_of_rel_str):
        """Parse date of release from database string."""
        if not date_of_rel_str:
            return None
        try:
            for fmt in ['%Y-%m-%d', '%d-%m-%Y', '%m/%d/%Y', '%d/%m/%Y']: # Add more formats if necessary
                try:
                    return datetime.strptime(str(date_of_rel_str), fmt).date()
                except ValueError:
                    continue
            logger.warning(f"Could not parse date: {date_of_rel_str}")
            return None
        except Exception as e:
            logger.error(f"Error parsing date string {date_of_rel_str}: {e}")
            return None

    def calculate_maintenance_dates(self, release_date):
        """Calculate maintenance due dates based on release date."""
        if not release_date:
            return {}
        schedule = {
            'mr1': {'years': 10, 'km_limit': config.MR1_KM_LIMIT if hasattr(config, 'MR1_KM_LIMIT') else 2400},
            'oh1': {'years': 16, 'km_limit': config.OH1_KM_LIMIT if hasattr(config, 'OH1_KM_LIMIT') else 3700},
            'mr2': {'years': 23, 'km_limit': config.MR2_KM_LIMIT if hasattr(config, 'MR2_KM_LIMIT') else 5400},
            'oh2': {'years': 29, 'km_limit': config.OH2_KM_LIMIT if hasattr(config, 'OH2_KM_LIMIT') else 6700}
        }
        dates = {}
        for event, event_config in schedule.items():
            due_date = release_date + timedelta(days=event_config['years'] * 365.25)
            dates[f'{event}_due_date'] = due_date
            dates[f'{event}_km_limit'] = event_config['km_limit']
        return dates

    def calculate_status(self, due_date, km_limit, current_km):
        """Calculate status based on date and KM criteria."""
        if not due_date or km_limit is None: # Ensure km_limit is checked
            return "N/A"
        today = date.today()
        current_km = float(current_km or 0)
        
        # Overdue by date or KM
        if today > due_date or current_km >= km_limit: # Changed to >= for KM limit
            return "Overdue"
        
        # Due soon (e.g., within 90 days or 500 KM) - make these configurable if needed
        days_to_due = (due_date - today).days
        km_to_limit = km_limit - current_km
        
        if days_to_due <= (config.DUE_SOON_DAYS_THRESHOLD if hasattr(config, 'DUE_SOON_DAYS_THRESHOLD') else 90) or \
           (km_to_limit <= (config.DUE_SOON_KM_THRESHOLD if hasattr(config, 'DUE_SOON_KM_THRESHOLD') else 500) and current_km < km_limit) : # ensure not already overdue by KM
            return "Due Soon"
        return "OK"

    def get_status_color(self, status):
        """Get color for status."""
        colors = {
            "OK": QColor(config.STATUS_OK_COLOR if hasattr(config, 'STATUS_OK_COLOR') else "#A9D08E"),
            "Due Soon": QColor(config.STATUS_DUE_SOON_COLOR if hasattr(config, 'STATUS_DUE_SOON_COLOR') else "#FFD966"),
            "Overdue": QColor(config.STATUS_OVERDUE_COLOR if hasattr(config, 'STATUS_OVERDUE_COLOR') else "#F4B084"),
            "N/A": QColor("#E0E0E0")
        }
        return colors.get(status, QColor(255, 255, 255))

    def populate_medium_reset_table(self, equipment_data):
        """Populate the medium reset table with manual entries only for the specific category."""
        # Get manual entries for this category
        manual_entries = self.get_manual_entries_for_category()
        
        # Show only manual entries (no automatic calculation)
        total_rows = len(manual_entries)
        self.medium_reset_table.setRowCount(0) # Clear existing rows
        self.medium_reset_table.setRowCount(total_rows)

        row_idx = 0
        
        # Show only manual entries (removed automatic calculation section)
        # Add manual entries to the table
        for manual_entry in manual_entries:
            # Manual entry ID (use negative to distinguish from equipment ID)
            id_item = QTableWidgetItem(f"Manual-{manual_entry.get('medium_reset_id', '')}")
            id_item.setFlags(id_item.flags() & ~Qt.ItemIsEditable)
            id_item.setBackground(QColor(240, 248, 255))  # Light blue background for manual entries
            self.medium_reset_table.setItem(row_idx, 0, id_item)

            # Equipment name
            equipment_name = f"{manual_entry.get('make_and_type', '')} ({manual_entry.get('ba_number', 'N/A')}) [MANUAL]"
            equipment_item = QTableWidgetItem(equipment_name)
            equipment_item.setFlags(equipment_item.flags() & ~Qt.ItemIsEditable)
            equipment_item.setBackground(QColor(240, 248, 255))
            self.medium_reset_table.setItem(row_idx, 1, equipment_item)

            # Done date (instead of release date for manual entries)
            done_date = manual_entry.get('done_date', '')
            done_date_item = QTableWidgetItem(str(done_date) if done_date else 'N/A')
            done_date_item.setFlags(done_date_item.flags() & ~Qt.ItemIsEditable)
            done_date_item.setBackground(QColor(240, 248, 255))
            self.medium_reset_table.setItem(row_idx, 2, done_date_item)

            # Meter reading (instead of current KM)
            meter_reading = manual_entry.get('meter_reading', 0) or 0
            km_item = QTableWidgetItem(f"{meter_reading:.0f}")
            km_item.setFlags(km_item.flags() & ~Qt.ItemIsEditable)
            km_item.setBackground(QColor(240, 248, 255))
            self.medium_reset_table.setItem(row_idx, 3, km_item)

            # Due date
            due_date = manual_entry.get('due_date', '')
            due_date_item = QTableWidgetItem(str(due_date) if due_date else 'N/A')
            due_date_item.setFlags(due_date_item.flags() & ~Qt.ItemIsEditable)
            due_date_item.setBackground(QColor(240, 248, 255))
            self.medium_reset_table.setItem(row_idx, 4, due_date_item)

            # Manual entry status based on due date
            manual_status = self.get_medium_reset_status(due_date) if due_date else 'N/A'
            status_item = QTableWidgetItem(manual_status)
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
            status_item.setBackground(QColor(240, 248, 255))
            self.medium_reset_table.setItem(row_idx, 5, status_item)
            
            row_idx += 1

        self.update_status_summary()


    def on_search_changed(self):
        """Handle search text changes."""
        self.filter_medium_resets()
        # self.update_status_summary() # update_status_summary is called by filter_medium_resets

    def filter_medium_resets(self):
        """Filter medium resets based on search text within this category."""
        search_text = self.search_field.text().lower()

        # Define columns to search based on category
        # Common columns: ID (0), Equipment (1)
        # Status columns: Primary status (6), Secondary status (9) if applicable
        searchable_cols = [0, 1]
        if self.category == "MR 1":
            searchable_cols.extend([5]) # MR1 Status is now at index 5
        elif self.category == "MR 2":
            searchable_cols.extend([5]) # MR2 Status is now at index 5

        for row in range(self.medium_reset_table.rowCount()):
            show_row = False
            # Check defined searchable columns
            for col in searchable_cols:
                item = self.medium_reset_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.medium_reset_table.setRowHidden(row, not show_row)
        self.update_status_summary()

    def refresh_data(self):
        """Refresh the medium reset data for this category."""
        self.load_data()
        QMessageBox.information(self, "Refresh", f"{self.category} data has been refreshed.")

    def update_status_summary(self):
        """Update the status summary with current statistics for this category."""
        overdue_count = 0
        due_soon_count = 0
        ok_count = 0

        # For "MR 1" tab, primary status is MR1 Status (col 5).
        # For "MR 2" tab, primary status is MR2 Status (col 5).
        primary_status_col_idx = 5
        # secondary_status_col_idx is no longer needed here as OH statuses are calculated internally

        for row in range(self.medium_reset_table.rowCount()):
            if not self.medium_reset_table.isRowHidden(row):
                primary_item = self.medium_reset_table.item(row, primary_status_col_idx)
                if primary_item:
                    primary_status = primary_item.text()

                    if primary_status == "Overdue":
                        overdue_count +=1
                    elif primary_status == "Due Soon":
                        due_soon_count += 1
                    elif primary_status == "OK":
                        is_row_truly_ok = True
                        # Common logic for checking secondary event (OH1 for MR1, OH2 for MR2)
                        secondary_event_key_to_check = None
                        if self.category == "MR 1":
                            secondary_event_key_to_check = 'oh1'
                        elif self.category == "MR 2":
                            secondary_event_key_to_check = 'oh2'

                        if secondary_event_key_to_check:
                            release_date_str = self.medium_reset_table.item(row, 2).text()
                            current_km_str = self.medium_reset_table.item(row, 3).text()
                            release_date = self.get_date_of_release(release_date_str)
                            current_km = float(current_km_str if current_km_str else 0)
                            maintenance_dates = self.calculate_maintenance_dates(release_date)

                            sec_due_date = maintenance_dates.get(f'{secondary_event_key_to_check}_due_date')
                            sec_km_limit = maintenance_dates.get(f'{secondary_event_key_to_check}_km_limit', 0)
                            secondary_status = self.calculate_status(sec_due_date, sec_km_limit, current_km)

                            if secondary_status == "Overdue" or secondary_status == "Due Soon":
                                is_row_truly_ok = False
                                # Add to respective counts if this secondary status makes the row non-OK
                                if secondary_status == "Overdue": overdue_count +=1
                                elif secondary_status == "Due Soon": due_soon_count +=1

                        if is_row_truly_ok:
                            ok_count += 1

        self.overdue_label.setText(f"Overdue: {overdue_count}")
        self.due_soon_label.setText(f"Due Soon: {due_soon_count}")
        self.ok_label.setText(f"OK: {ok_count}")

    def on_selection_changed(self):
        """Handle table selection changes."""
        selected_rows = self.medium_reset_table.selectionModel().selectedRows()
        if selected_rows:
            row = selected_rows[0].row()
            id_item = self.medium_reset_table.item(row, 0) # ID is in column 0
            
            if id_item:
                id_text = id_item.text()
                
                if id_text.startswith("Manual-"):
                    # This is a manual entry - enable edit/delete and load it
                    self.edit_button.setEnabled(True)
                    self.delete_button.setEnabled(True)
                    
                    # Extract manual entry ID
                    manual_id = id_text.replace("Manual-", "")
                    try:
                        manual_id = int(manual_id)
                        # Load the manual entry and set current_medium_reset
                        manual_entry = MediumReset.get_by_id(manual_id)
                        if manual_entry:
                            self.current_medium_reset = manual_entry
                            self.load_medium_reset_details_for_form(manual_id)
                        else:
                            logger.error(f"Manual entry not found: {manual_id}")
                            self.clear_form()
                    except ValueError:
                        logger.error(f"Invalid manual entry ID: {manual_id}")
                        self.clear_form()
                else:
                    # This is an equipment entry - no edit but allow delete (will deactivate equipment)
                    self.edit_button.setEnabled(False)
                    self.delete_button.setEnabled(True)
                    
                    try:
                        equipment_id = int(id_text)
                        self.current_equipment_id = equipment_id  # Store for potential deletion
                        self.load_equipment_details_for_form(equipment_id)
                    except ValueError:
                        logger.error(f"Invalid equipment ID: {id_text}")
                        self.clear_form()
        else:
            self.edit_button.setEnabled(False)
            self.delete_button.setEnabled(False)
            self.clear_form()
            self.current_equipment = None
            self.current_medium_reset = None


    def load_equipment_details_for_form(self, equipment_id):
        """Load equipment details for display in the form area (context)."""
        try:
            equipment = Equipment.get_by_id(equipment_id)
            if equipment:
                self.current_equipment = equipment # Store for context
                
                # Update the status_label in the form with this equipment's overall status
                release_date = self.get_date_of_release(equipment.date_of_commission)
                maintenance_dates = self.calculate_maintenance_dates(release_date)
                self.update_maintenance_status_display(equipment, maintenance_dates)

                # If we are about to edit a manual entry, we'd load its details here.
                # For now, just selecting a row in the main table shows equipment context.
                # If a manual entry for this equipment + category exists, it would be loaded
                # by load_medium_reset_details_for_form.

                # Pre-fill equipment in form if adding new manual entry for selected equipment
                if not self.current_medium_reset: # Only if not already editing a specific manual entry
                    index = self.equipment_field.findData(int(equipment_id))
                    if index >= 0:
                        self.equipment_field.setCurrentIndex(index)
                    self.id_field.setText("") # Clear manual entry ID field
                    self.description_field.setPlainText(f"Context: {equipment.make_and_type} - {equipment.ba_number or 'N/A'}")

        except Exception as e:
            logger.error(f"Error loading equipment details for form: {e}", exc_info=True)
            QMessageBox.warning(self, "Error", f"Failed to load equipment context: {str(e)}")


    def load_medium_reset_details_for_form(self, medium_reset_id): # For loading a specific MANUAL entry
        """Load manual medium reset details into the form for editing."""
        try:
            # This assumes MediumReset.get_by_id refers to the manual entry ID
            entry = MediumReset.get_by_id(medium_reset_id)
            if entry:
                self.current_medium_reset = entry # Store the manual entry being edited
                self.id_field.setText(str(entry['medium_reset_id']))

                index = self.equipment_field.findData(entry['equipment_id'])
                if index >= 0: self.equipment_field.setCurrentIndex(index)

                index = self.reset_type_field.findText(entry['reset_type'] or "")
                if index >= 0: self.reset_type_field.setCurrentIndex(index)
                
                if entry['done_date']: self.done_date_field.setDate(QDate.fromString(str(entry['done_date']), "yyyy-MM-dd"))
                if entry['due_date']: self.due_date_field.setDate(QDate.fromString(str(entry['due_date']), "yyyy-MM-dd"))
                
                self.meter_reading_field.setValue(entry['meter_reading'] or 0)
                self.hours_reading_field.setValue(entry['hours_reading'] or 0)
                self.description_field.setPlainText(entry['description'] or "")
                self.notes_field.setPlainText(entry['notes'] or "")

                self.set_form_enabled(True) # Enable form for editing
            else:
                QMessageBox.warning(self, "Not Found", "Manual medium reset entry not found.")
                self.clear_form()
        except Exception as e:
            logger.error(f"Error loading manual medium reset details for form: {e}", exc_info=True)
            QMessageBox.critical(self, "Error", f"Failed to load manual entry: {str(e)}")


    def update_maintenance_status_display(self, equipment, maintenance_dates):
        """Update the status display in the form with selected equipment's maintenance info."""
        # This shows the OVERALL status of the selected equipment in the form area,
        # not the status of a manual entry.
        try:
            current_km = float(equipment.meterage_kms or 0)
            status_parts = []
            
            # Determine which event (mr1, oh1, mr2, oh2) is relevant for *this* sub_widget's category
            # This part is crucial and needs to be adapted based on self.category
            # For example, if self.category is "MR1", we only care about 'mr1' status.
            # For now, showing all as a placeholder:
            relevant_event_key = None
            if self.category == "MR 1": relevant_event_key = 'mr1'
            elif self.category == "OH 1": relevant_event_key = 'oh1'
            elif self.category == "MR 2": relevant_event_key = 'mr2'
            elif self.category == "OH 2": relevant_event_key = 'oh2'

            if relevant_event_key:
                due_date = maintenance_dates.get(f'{relevant_event_key}_due_date')
                km_limit = maintenance_dates.get(f'{relevant_event_key}_km_limit', 0)
                status = self.calculate_status(due_date, km_limit, current_km)
                status_parts.append(f"{relevant_event_key.upper()}: {status}")
            else: # Fallback if category doesn't match known keys
                 status_parts.append("Overall Status (All Types):")
                 for event in ['mr1', 'oh1', 'mr2', 'oh2']:
                    due_date = maintenance_dates.get(f'{event}_due_date')
                    km_limit = maintenance_dates.get(f'{event}_km_limit', 0)
                    status = self.calculate_status(due_date, km_limit, current_km)
                    status_parts.append(f"  {event.upper()}: {status}")

            final_status_text = "\n".join(status_parts)
            self.status_label.setText(final_status_text)
            
            # Determine overall color for the label (e.g. worst status shown)
            if "Overdue" in final_status_text: self.status_label.setStyleSheet('color: red; font-weight: bold;')
            elif "Due Soon" in final_status_text: self.status_label.setStyleSheet('color: orange; font-weight: bold;')
            else: self.status_label.setStyleSheet('color: green; font-weight: bold;')

        except Exception as e:
            logger.error(f"Error updating maintenance status display: {e}", exc_info=True)
            self.status_label.setText("❓ UNKNOWN")
            self.status_label.setStyleSheet('color: gray; font-weight: bold;')

    def clear_form(self):
        """Clear the manual medium reset form."""
        self.current_medium_reset = None # Clear current manual entry context
        self.current_equipment = None # Clear equipment context too during data refresh
        self.current_equipment_id = None # Clear equipment ID
        self.id_field.clear()
        self.equipment_field.setCurrentIndex(0) # Reset to "Select Equipment"
        self.reset_type_field.setCurrentIndex(0)
        self.done_date_field.setDate(QDate.currentDate())
        self.due_date_field.setDate(QDate.currentDate().addYears(1))
        self.status_label.setText("") # Clear status display
        self.meter_reading_field.setValue(0)
        self.hours_reading_field.setValue(0)
        self.description_field.clear()
        self.notes_field.clear()
        self.set_form_enabled(False)
        
        # Disable action buttons
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)


    def set_form_enabled(self, enabled):
        """Enable or disable manual entry form fields."""
        self.equipment_field.setEnabled(enabled)
        self.reset_type_field.setEnabled(enabled)
        self.done_date_field.setEnabled(enabled) # Use setEnabled for QDateEdit
        self.due_date_field.setEnabled(enabled)  # Use setEnabled for QDateEdit
        self.meter_reading_field.setEnabled(enabled)
        self.hours_reading_field.setEnabled(enabled)
        self.description_field.setReadOnly(not enabled)
        self.notes_field.setReadOnly(not enabled)
        self.save_button.setEnabled(enabled)
        self.cancel_button.setEnabled(enabled)

    def add_medium_reset_entry(self):
        """Prepare form for adding a new manual medium reset entry."""
        self.medium_reset_table.clearSelection() # Clear table selection
        self.current_medium_reset = None # No current manual entry being edited
        self.clear_form() # Clear form fields
        self.set_form_enabled(True) # Enable form for new entry
        self.id_field.setText("") # Ensure ID is blank for new entry

        # If an equipment was selected in the table, keep it in the dropdown
        if self.current_equipment:
            index = self.equipment_field.findData(self.current_equipment.equipment_id)
            if index >= 0:
                self.equipment_field.setCurrentIndex(index)
        else:
             self.equipment_field.setCurrentIndex(0) # "Select Equipment"

        self.equipment_field.setFocus()
        QMessageBox.information(self, f"Add Manual {self.category} Entry",
                               f"Fill the form to add a manual tracking entry for {self.category}.\n"
                               "The main table shows system-calculated data based on equipment vintage.")


    def edit_medium_reset(self):
        """Edit the selected manual medium reset entry."""
        # This should be triggered when a MANUAL entry is selected, perhaps from a separate list or table.
        # For now, if current_medium_reset is set (e.g. after selection from a future manual entry list), enable form.
        if self.current_medium_reset and self.current_medium_reset.get('medium_reset_id'):
            self.load_medium_reset_details_for_form(self.current_medium_reset['medium_reset_id']) # Reload to ensure form is populated
            self.set_form_enabled(True)
            self.equipment_field.setFocus()
        else:
            # If no manual entry selected, try to see if a row in the main table is selected
            # to load its context for potentially creating a new manual entry.
            selected_rows = self.medium_reset_table.selectionModel().selectedRows()
            if selected_rows:
                 QMessageBox.information(self, "Hint", "To edit a manual entry, you'd typically select it from a list of manual entries. This form is for creating or editing such entries. Select 'Add Manual Entry' to create a new one.")
                 # Or, if we decide that selecting a row in the main table implies editing a related manual entry:
                 # equipment_id = self.medium_reset_table.item(selected_rows[0].row(), 0).text()
                 # Find and load manual entry for this equipment_id and self.category
                 # For now, this button is more for "after a manual entry has been loaded into the form"
            else:
                QMessageBox.warning(self, "Edit Manual Entry", "Please select a manual medium reset entry to edit, or add a new one.")


    def delete_medium_reset(self):
        """Delete the selected manual entry or deactivate equipment."""
        # First try to get info from table selection
        selected_rows = self.medium_reset_table.selectionModel().selectedRows()
        manual_entry_id = None
        equipment_id = None
        equipment_name = "Unknown"
        
        if selected_rows:
            row = selected_rows[0].row()
            id_item = self.medium_reset_table.item(row, 0)
            equipment_item = self.medium_reset_table.item(row, 1)
            equipment_name = equipment_item.text() if equipment_item else "Unknown"
            
            if id_item and id_item.text().startswith("Manual-"):
                # This is a manual entry
                try:
                    manual_entry_id = int(id_item.text().replace("Manual-", ""))
                except ValueError:
                    QMessageBox.warning(self, "Delete Error", "Invalid manual entry selected.")
                    return
            else:
                # This is calculated equipment data
                try:
                    equipment_id = int(id_item.text())
                except ValueError:
                    QMessageBox.warning(self, "Delete Error", "Invalid equipment entry selected.")
                    return
        
        # Fallback to stored values if no table selection
        if not manual_entry_id and not equipment_id:
            if self.current_medium_reset and self.current_medium_reset.get('medium_reset_id'):
                manual_entry_id = self.current_medium_reset['medium_reset_id']
                equipment_name = self.equipment_field.currentText()
            elif hasattr(self, 'current_equipment_id'):
                equipment_id = self.current_equipment_id
                equipment_name = self.equipment_field.currentText()
        
        if not manual_entry_id and not equipment_id:
            QMessageBox.warning(self, "Delete Error", "No entry is selected to delete.")
            return

        # Handle manual entry deletion
        if manual_entry_id:
            reply = QMessageBox.question(
                self, "Confirm Deletion",
                f"Are you sure you want to delete manual {self.category} entry ID {manual_entry_id}?\n\nEquipment: {equipment_name}",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                try:
                    if MediumReset.delete(manual_entry_id):
                        QMessageBox.information(self, "Success", "Manual medium reset entry deleted successfully.")
                        self.clear_form()
                        self.set_form_enabled(False)
                        self.current_medium_reset = None
                        # Refresh the table to show the deletion
                        self.load_data()
                    else:
                        QMessageBox.critical(self, "Error", "Failed to delete manual medium reset entry.")
                except Exception as e:
                    logger.error(f"Error deleting manual medium reset entry: {e}", exc_info=True)
                    QMessageBox.critical(self, "Error", f"An error occurred: {str(e)}")
        
        # Handle equipment deactivation
        elif equipment_id:
            reply = QMessageBox.question(
                self, "Confirm Equipment Deactivation",
                f"Are you sure you want to deactivate this equipment?\n\n"
                f"Equipment: {equipment_name}\n"
                f"ID: {equipment_id}\n\n"
                f"This will remove it from all Medium Reset calculations.\n"
                f"The equipment will be marked as inactive but not permanently deleted.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                try:
                    # Deactivate equipment by setting is_active = 0
                    result = database.execute_query(
                        "UPDATE equipment SET is_active = 0 WHERE equipment_id = ?", 
                        (equipment_id,)
                    )
                    
                    if result is not None:  # Check if query executed successfully
                        QMessageBox.information(self, "Success", f"Equipment {equipment_name} has been deactivated successfully.")
                        self.clear_form()
                        self.set_form_enabled(False)
                        if hasattr(self, 'current_equipment_id'):
                            delattr(self, 'current_equipment_id')
                        # Refresh the table to show the change
                        self.load_data()
                    else:
                        QMessageBox.critical(self, "Error", "Failed to deactivate equipment.")
                except Exception as e:
                    logger.error(f"Error deactivating equipment: {e}", exc_info=True)
                    QMessageBox.critical(self, "Error", f"An error occurred: {str(e)}")

    def save_medium_reset(self):
        """Save manual medium reset data."""
        equipment_id = self.equipment_field.currentData()
        if not equipment_id:
            QMessageBox.warning(self, "Validation Error", "Equipment is a required field for a manual entry.")
            return

        manual_entry_id = int(self.id_field.text()) if self.id_field.text() else None
        
        done_qdate = self.done_date_field.date()
        done_date_obj = date(done_qdate.year(), done_qdate.month(), done_qdate.day())
        
        due_qdate = self.due_date_field.date()
        due_date_obj = date(due_qdate.year(), due_qdate.month(), due_qdate.day())

        entry_data = {
            'medium_reset_id': manual_entry_id,
            'equipment_id': equipment_id,
            'reset_type': self.reset_type_field.currentText(), # This might need to be self.category or related
            'done_date': done_date_obj,
            'due_date': due_date_obj, # Due date for this manual entry
            'meter_reading': self.meter_reading_field.value(),
            'hours_reading': self.hours_reading_field.value(),
            'description': self.description_field.toPlainText(),
            'notes': self.notes_field.toPlainText(),
            'category': self.category # Store the category with the manual entry
        }

        try:
            # Create or update MediumReset instance
            # The MediumReset model needs to handle the 'category' field if it's new
            manual_entry = MediumReset(**entry_data)
            saved_id = manual_entry.save() # save() should return the ID

            if saved_id:
                QMessageBox.information(self, "Success", f"Manual {self.category} entry saved successfully (ID: {saved_id}).")
                self.load_medium_reset_details_for_form(saved_id) # Reload the saved entry
                self.set_form_enabled(False)
                # Refresh the table to show the new manual entry
                self.load_data()
            else:
                QMessageBox.critical(self, "Error", f"Failed to save manual {self.category} entry.")
        except Exception as e:
            logger.error(f"Error saving manual {self.category} entry: {e}", exc_info=True)
            QMessageBox.critical(self, "Error", f"An error occurred: {str(e)}")


    def cancel_edit(self):
        """Cancel manual medium reset edit."""
        if self.current_medium_reset and self.current_medium_reset.get('medium_reset_id'):
            self.load_medium_reset_details_for_form(self.current_medium_reset['medium_reset_id'])
        else:
            self.clear_form() # Clears and disables form
            # If there was equipment selected in the main table, its context might still be relevant
            if self.current_equipment:
                 self.load_equipment_details_for_form(self.current_equipment.equipment_id) # Restore equipment context display
        self.set_form_enabled(False)


    def get_medium_reset_status(self, due_date_str): # For manual entry due dates
        """Return status string based on due_date proximity for a manual entry."""
        if not due_date_str: return ""
        try:
            due_date = datetime.strptime(str(due_date_str), "%Y-%m-%d").date()
        except ValueError:
            try: # try another common format
                due_date = datetime.strptime(str(due_date_str), "%d-%m-%Y").date()
            except Exception:
                logger.warning(f"Could not parse due date for manual entry: {due_date_str}")
                return "Invalid Date"

        today = date.today()
        days_diff = (due_date - today).days

        if days_diff < 0: return "Overdue"
        # These thresholds could also be configurable
        elif days_diff <= (config.MANUAL_ENTRY_CRITICAL_DAYS if hasattr(config, 'MANUAL_ENTRY_CRITICAL_DAYS') else 30) : return "Critical"
        elif days_diff <= (config.MANUAL_ENTRY_WARNING_DAYS if hasattr(config, 'MANUAL_ENTRY_WARNING_DAYS') else 90) : return "Warning"
        else: return "Normal"


class MediumResetWidget(QWidget):
    """Widget for managing medium reset operations."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data() # Load data for the initial tab

    def setup_ui(self):
        """Set up the user interface with tabs."""
        layout = QVBoxLayout(self) # Set layout directly on the widget

        self.tabs = QTabWidget()

        # Create and add MediumResetSubWidget instances for each category
        self.mr1_widget = MediumResetSubWidget(category="MR 1", parent=self)
        self.tabs.addTab(self.mr1_widget, "MR 1")

        self.mr2_widget = MediumResetSubWidget(category="MR 2", parent=self)
        self.tabs.addTab(self.mr2_widget, "MR 2")

        # Add other categories/tabs as needed, e.g., OH 1, OH 2
        # self.oh1_widget = MediumResetSubWidget(category="OH 1", parent=self)
        # self.tabs.addTab(self.oh1_widget, "OH 1")
        #
        # self.oh2_widget = MediumResetSubWidget(category="OH 2", parent=self)
        # self.tabs.addTab(self.oh2_widget, "OH 2")

        layout.addWidget(self.tabs)
        self.setLayout(layout)

        # Apply styles
        self.setStyleSheet(config.UI_STYLE)

        # Connect tab change signal
        self.tabs.currentChanged.connect(self.tab_changed)

        # Initial load for the first tab (if any sub-widgets were added)
        # self.load_data() # Moved to __init__ to ensure UI is ready

    def tab_changed(self, index):
        """Handle tab changes."""
        current_tab = self.tabs.widget(index)
        if current_tab and hasattr(current_tab, 'load_data'):
            # This will call load_data on the MediumResetSubWidget
            current_tab.load_data()
        # else:
        #     # Optionally, handle cases where a tab might not have load_data
        #     # For now, we assume all tabs are MediumResetSubWidget instances
        #     pass

    def create_table_panel(self):
        """Create the table panel with medium reset list."""
        # widget = QWidget()
        # layout = QVBoxLayout()

        # # Search section
        # search_layout = QHBoxLayout()
        # search_layout.addWidget(QLabel("Search:"))
        # self.search_field = QLineEdit()
        # self.search_field.setPlaceholderText("Search medium resets...")
        # search_layout.addWidget(self.search_field)
        # layout.addLayout(search_layout)

        # # Medium Reset table
        # self.medium_reset_table = QTableWidget()
        # self.medium_reset_table.setColumnCount(16)
        # self.medium_reset_table.setHorizontalHeaderLabels([
        #     "ID", "Equipment", "Date of Release", "Current KM",
        #     "MR1 Due Date", "MR1 KM Limit", "MR1 Status",
        #     "OH1 Due Date", "OH1 KM Limit", "OH1 Status",
        #     "MR2 Due Date", "MR2 KM Limit", "MR2 Status",
        #     "OH2 Due Date", "OH2 KM Limit", "OH2 Status"
        # ])

        # # Set column widths for full screen
        # header = self.medium_reset_table.horizontalHeader()
        # header.setStretchLastSection(True)
        # header.resizeSection(0, 60)   # ID
        # header.resizeSection(1, 150)  # Equipment
        # header.resizeSection(2, 120)  # Date of Release
        # header.resizeSection(3, 100)  # Current KM
        # header.resizeSection(4, 120)  # MR1 Due Date
        # header.resizeSection(5, 100)  # MR1 KM Limit
        # header.resizeSection(6, 100)  # MR1 Status
        # header.resizeSection(7, 120)  # OH1 Due Date
        # header.resizeSection(8, 100)  # OH1 KM Limit
        # header.resizeSection(9, 100)  # OH1 Status
        # header.resizeSection(10, 120) # MR2 Due Date
        # header.resizeSection(11, 100) # MR2 KM Limit
        # header.resizeSection(12, 100) # MR2 Status
        # header.resizeSection(13, 120) # OH2 Due Date
        # header.resizeSection(14, 100) # OH2 KM Limit
        # header.resizeSection(15, 100) # OH2 Status

        # self.medium_reset_table.setSelectionBehavior(QTableWidget.SelectRows)
        # self.medium_reset_table.setAlternatingRowColors(True)
        # layout.addWidget(self.medium_reset_table)

        # # Action buttons
        # button_layout = QHBoxLayout()
        # self.add_button = QPushButton("Add Manual Entry")
        # self.edit_button = QPushButton("Edit")
        # self.delete_button = QPushButton("Delete")
        # self.refresh_button = QPushButton("Refresh")

        # # Initially disable edit and delete buttons
        # self.edit_button.setEnabled(False)
        # self.delete_button.setEnabled(False)

        # button_layout.addWidget(self.add_button)
        # button_layout.addWidget(self.edit_button)
        # button_layout.addWidget(self.delete_button)
        # button_layout.addWidget(self.refresh_button)
        # button_layout.addStretch()
        # layout.addLayout(button_layout)

        # # Add dashboard summary
        # self.create_dashboard_summary(layout)

        # widget.setLayout(layout)
        # return widget
        pass

    def create_dashboard_summary(self, layout):
        """Create dashboard summary section."""
        # summary_group = QGroupBox("Maintenance Summary")
        # summary_layout = QVBoxLayout()

        # # Summary labels
        # self.overdue_label = QLabel("Overdue: 0")
        # self.overdue_label.setStyleSheet('color: red; font-weight: bold; font-size: 14px;')

        # self.due_soon_label = QLabel("Due Soon: 0")
        # self.due_soon_label.setStyleSheet('color: orange; font-weight: bold; font-size: 14px;')

        # self.ok_label = QLabel("OK: 0")
        # self.ok_label.setStyleSheet('color: green; font-weight: bold; font-size: 14px;')

        # summary_layout.addWidget(self.overdue_label)
        # summary_layout.addWidget(self.due_soon_label)
        # summary_layout.addWidget(self.ok_label)

        # summary_group.setLayout(summary_layout)
        # layout.addWidget(summary_group)
        pass

    def create_right_panel(self):
        """Create the details panel with medium reset form."""
        # widget = QWidget()
        # layout = QVBoxLayout()

        # # Details form
        # form_group = QGroupBox("Medium Reset Details")
        # main_form_layout = QHBoxLayout()

        # # Left column
        # left_form_layout = QFormLayout()

        # # Form fields - Left column
        # self.id_field = QLineEdit()
        # self.id_field.setReadOnly(True)
        # left_form_layout.addRow("ID:", self.id_field)

        # self.equipment_field = QComboBox()
        # left_form_layout.addRow("Equipment:", self.equipment_field)

        # self.reset_type_field = QComboBox()
        # self.reset_type_field.addItems(["Minor Reset", "Major Reset", "Complete Reset", "Partial Reset"])
        # left_form_layout.addRow("Reset Type:", self.reset_type_field)

        # self.done_date_field = QDateEdit()
        # self.done_date_field.setDate(QDate.currentDate())
        # self.done_date_field.setCalendarPopup(True)
        # left_form_layout.addRow("Done Date:", self.done_date_field)

        # self.due_date_field = QDateEdit()
        # self.due_date_field.setDate(QDate.currentDate().addYears(1))
        # self.due_date_field.setCalendarPopup(True)
        # left_form_layout.addRow("Due Date:", self.due_date_field)

        # # Right column
        # right_form_layout = QFormLayout()

        # self.status_label = QLabel()
        # self.status_label.setStyleSheet('font-weight: bold;')
        # right_form_layout.addRow("Status:", self.status_label)

        # self.meter_reading_field = QSpinBox()
        # self.meter_reading_field.setMaximum(999999)
        # right_form_layout.addRow("Meter Reading:", self.meter_reading_field)

        # self.hours_reading_field = QSpinBox()
        # self.hours_reading_field.setMaximum(999999)
        # right_form_layout.addRow("Hours Reading:", self.hours_reading_field)

        # # Text areas section
        # text_layout = QVBoxLayout()

        # self.description_field = QTextEdit()
        # self.description_field.setMaximumHeight(60)
        # text_layout.addWidget(QLabel("Description:"))
        # text_layout.addWidget(self.description_field)

        # self.notes_field = QTextEdit()
        # self.notes_field.setMaximumHeight(60)
        # text_layout.addWidget(QLabel("Notes:"))
        # text_layout.addWidget(self.notes_field)

        # # Add layouts to main form
        # main_form_layout.addLayout(left_form_layout)
        # main_form_layout.addLayout(right_form_layout)
        # main_form_layout.addLayout(text_layout)

        # form_group.setLayout(main_form_layout)
        # layout.addWidget(form_group)

        # # Form buttons
        # button_layout = QHBoxLayout()
        # self.save_button = QPushButton("Save")
        # self.cancel_button = QPushButton("Cancel")

        # button_layout.addWidget(self.save_button)
        # button_layout.addWidget(self.cancel_button)
        # button_layout.addStretch()
        # layout.addLayout(button_layout)

        # # Initially disable form
        # self.set_form_enabled(False)

        # widget.setLayout(layout)
        # return widget
        pass



    def connect_signals(self):
        """Connect widget signals to their handlers."""
        # self.search_field.textChanged.connect(self.on_search_changed)
        # self.medium_reset_table.itemSelectionChanged.connect(self.on_selection_changed)
        # self.add_button.clicked.connect(self.add_medium_reset)
        # self.edit_button.clicked.connect(self.edit_medium_reset)
        # self.delete_button.clicked.connect(self.delete_medium_reset)
        # self.save_button.clicked.connect(self.save_medium_reset)
        # self.cancel_button.clicked.connect(self.cancel_edit)
        # self.refresh_button.clicked.connect(self.refresh_data)
        pass

    def load_data(self):
        """Load data for all tabs to ensure complete refresh."""
        logger.info("Refreshing Medium Reset widget data")
        try:
            # Refresh all tabs, not just the current one
            for i in range(self.tabs.count()):
                tab_widget = self.tabs.widget(i)
                if tab_widget and hasattr(tab_widget, 'load_data'):
                    tab_name = self.tabs.tabText(i)
                    logger.info(f"Refreshing {tab_name} tab")
                    tab_widget.load_data()
            
            logger.info("All Medium Reset tabs refreshed successfully")
        except Exception as e:
            logger.error(f"Error refreshing Medium Reset data: {e}")
            QMessageBox.critical(
                self,
                "Error", 
                f"Failed to refresh Medium Reset data: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
    
    def force_refresh(self):
        """Force a complete refresh of all Medium Reset data."""
        logger.info("Force refreshing Medium Reset widget after equipment changes")
        self.load_data()
        
        # Force immediate UI update to ensure changes are visible
        from PyQt5.QtWidgets import QApplication
        QApplication.processEvents()
        
        logger.info("Force refresh completed with UI update")
        # try:
        #     # Load equipment data for allowed vehicle types
        #     equipment_data = self.get_filtered_equipment()

        #     if equipment_data:
        #         self.populate_medium_reset_table(equipment_data)

        #     # Load equipment for dropdown
        #     self.load_equipment_data()

        # except Exception as e:
        #     logger.error(f"Error loading medium reset data: {e}")
        #     QMessageBox.critical(
        #         self,
        #         "Error",
        #         f"Failed to load medium reset data: {str(e)}",
        #         QMessageBox.StandardButton.Ok
        #     )

    def load_equipment_data(self):
        """Load equipment data for dropdown."""
        # try:
        #     equipment_list = Equipment.get_active()
        #     self.equipment_field.clear()
        #     self.equipment_field.addItem("Select Equipment", None)

        #     # Filter equipment to only show specific make and types
        #     allowed_types = ['ICV BMP-I', 'ICV BMP-II', 'AERV']

        #     for equipment in equipment_list:
        #         if equipment['make_and_type'] in allowed_types:
        #             display_text = f"{equipment['make_and_type']} - {equipment['ba_number'] or 'N/A'}"
        #             self.equipment_field.addItem(display_text, equipment['equipment_id'])

        # except Exception as e:
        #     logger.error(f"Error loading equipment data: {e}")
        pass

    def get_filtered_equipment(self):
        """Get equipment data filtered for allowed vehicle types."""
        # query = """
        #     SELECT equipment_id, make_and_type, date_of_rel, meterage_kms, ba_number
        #     FROM equipment
        #     WHERE make_and_type IN ('ICV BMP-I', 'ICV BMP-II', 'AERV')
        #     AND is_active = 1
        #     ORDER BY make_and_type, equipment_id
        # """
        # return database.execute_query(query)
        pass

    def get_date_of_release(self, date_of_rel_str):
        """Parse date of release from database string."""
        # if not date_of_rel_str:
        #     return None
        # try:
        #     # Try different date formats
        #     for fmt in ['%Y-%m-%d', '%d-%m-%Y', '%m/%d/%Y', '%d/%m/%Y']:
        #         try:
        #             return datetime.strptime(date_of_rel_str, fmt).date()
        #         except ValueError:
        #             continue
        #     return None
        # except:
        #     return None
        pass

    def calculate_maintenance_dates(self, release_date):
        """Calculate maintenance due dates based on release date."""
        # if not release_date:
        #     return {}

        # # Maintenance schedule
        # schedule = {
        #     'mr1': {'years': 10, 'km_limit': 2400},
        #     'oh1': {'years': 16, 'km_limit': 3700},
        #     'mr2': {'years': 23, 'km_limit': 5400},
        #     'oh2': {'years': 29, 'km_limit': 6700}  # OH2 is discard criteria
        # }

        # dates = {}
        # for event, config in schedule.items():
        #     due_date = release_date + timedelta(days=config['years'] * 365.25)
        #     dates[f'{event}_due_date'] = due_date
        #     dates[f'{event}_km_limit'] = config['km_limit']

        # return dates
        pass

    def calculate_status(self, due_date, km_limit, current_km):
        """Calculate status based on date and KM criteria."""
        # if not due_date:
        #     return "N/A"

        # today = date.today()
        # current_km = float(current_km or 0)

        # # Check if overdue (either by date or KM)
        # if today > due_date or current_km > km_limit:
        #     return "Overdue"

        # # Check if due soon (within 90 days or 500 KM)
        # days_until_due = (due_date - today).days
        # km_until_due = km_limit - current_km

        # if days_until_due <= 90 or km_until_due <= 500:
        #     return "Due Soon"

        # return "OK"
        pass

    def get_status_color(self, status):
        """Get color for status."""
        # colors = {
        #     "OK": QColor(169, 208, 142),      # Green #A9D08E
        #     "Due Soon": QColor(255, 217, 102), # Yellow #FFD966
        #     "Overdue": QColor(244, 176, 132),  # Red #F4B084
        #     "N/A": QColor(240, 240, 240)       # Light gray
        # }
        # return colors.get(status, QColor(255, 255, 255))
        pass

    def populate_medium_reset_table(self, equipment_data):
        """Populate the medium reset table with calculated data."""
        # self.medium_reset_table.setRowCount(len(equipment_data))

        # for row, equipment in enumerate(equipment_data):
        #     # Calculate dates and statuses
        #     release_date = self.get_date_of_release(equipment.get('date_of_rel'))
        #     maintenance_dates = self.calculate_maintenance_dates(release_date)
        #     current_km = float(equipment.get('meterage_kms', 0))

        #     # ID
        #     id_item = QTableWidgetItem(str(equipment.get('equipment_id', '')))
        #     id_item.setFlags(id_item.flags() & ~Qt.ItemIsEditable)
        #     self.medium_reset_table.setItem(row, 0, id_item)

        #     # Equipment
        #     equipment_item = QTableWidgetItem(str(equipment.get('make_and_type', '')))
        #     equipment_item.setFlags(equipment_item.flags() & ~Qt.ItemIsEditable)
        #     self.medium_reset_table.setItem(row, 1, equipment_item)

        #     # Date of Release
        #     release_item = QTableWidgetItem(release_date.strftime('%Y-%m-%d') if release_date else 'N/A')
        #     release_item.setFlags(release_item.flags() & ~Qt.ItemIsEditable)
        #     self.medium_reset_table.setItem(row, 2, release_item)

        #     # Current KM
        #     km_item = QTableWidgetItem(f"{current_km:.0f}")
        #     km_item.setFlags(km_item.flags() & ~Qt.ItemIsEditable)
        #     self.medium_reset_table.setItem(row, 3, km_item)

        #     # Populate maintenance columns
        #     col_index = 4
        #     for event in ['mr1', 'oh1', 'mr2', 'oh2']:
        #         due_date = maintenance_dates.get(f'{event}_due_date')
        #         km_limit = maintenance_dates.get(f'{event}_km_limit', 0)
        #         status = self.calculate_status(due_date, km_limit, current_km)
                
        #         # Due Date
        #         date_item = QTableWidgetItem(due_date.strftime('%Y-%m-%d') if due_date else 'N/A')
        #         date_item.setFlags(date_item.flags() & ~Qt.ItemIsEditable)
        #         self.medium_reset_table.setItem(row, col_index, date_item)
                
        #         # KM Limit
        #         km_limit_item = QTableWidgetItem(str(km_limit))
        #         km_limit_item.setFlags(km_limit_item.flags() & ~Qt.ItemIsEditable)
        #         self.medium_reset_table.setItem(row, col_index + 1, km_limit_item)
                
        #         # Status with color
        #         status_item = QTableWidgetItem(status)
        #         status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
        #         status_item.setBackground(self.get_status_color(status))
        #         self.medium_reset_table.setItem(row, col_index + 2, status_item)

        #         # Check if row should be highlighted (any overdue status)
        #         if status == "Overdue":
        #             for col in range(self.medium_reset_table.columnCount()):
        #                 item = self.medium_reset_table.item(row, col)
        #                 if item:
        #                     current_color = item.background().color()
        #                     if current_color == QColor(255, 255, 255) or current_color == QColor():
        #                         item.setBackground(QColor(255, 240, 240))  # Light red highlight

        #         col_index += 3

        # # Update dashboard summary after populating table
        # self.update_dashboard_summary()
        pass
    
    def on_search_changed(self):
        """Handle search text changes."""
        # self.filter_medium_resets()
        # self.update_dashboard_summary()
        pass

    def filter_medium_resets(self):
        """Filter medium resets based on search text."""
        # search_text = self.search_field.text().lower()
        
        # for row in range(self.medium_reset_table.rowCount()):
        #     show_row = False

        #     # Check equipment name and ID columns for search text
        #     for col in [0, 1]:  # ID and Equipment columns
        #         item = self.medium_reset_table.item(row, col)
        #         if item and search_text in item.text().lower():
        #             show_row = True
        #             break

        #     # Also check status columns for filtering by status
        #     if not show_row:
        #         for col in [6, 9, 12, 15]:  # Status columns
        #             item = self.medium_reset_table.item(row, col)
        #             if item and search_text in item.text().lower():
        #                 show_row = True
        #                 break

        #     self.medium_reset_table.setRowHidden(row, not show_row)
        pass

    def refresh_data(self):
        """Refresh the medium reset data."""
        # self.load_data()
        # QMessageBox.information(self, "Refresh", "Medium reset data has been refreshed.")
        pass

    def update_dashboard_summary(self):
        """Update the dashboard summary with current statistics."""
        # try:
        #     overdue_count = 0
        #     due_soon_count = 0
        #     ok_count = 0

        #     # Count statuses from visible rows
        #     for row in range(self.medium_reset_table.rowCount()):
        #         if not self.medium_reset_table.isRowHidden(row):
        #             # Check status columns (6, 9, 12, 15)
        #             for col in [6, 9, 12, 15]:
        #                 item = self.medium_reset_table.item(row, col)
        #                 if item:
        #                     status = item.text()
        #                     if status == "Overdue":
        #                         overdue_count += 1
        #                     elif status == "Due Soon":
        #                         due_soon_count += 1
        #                     elif status == "OK":
        #                         ok_count += 1

        #     # Update labels
        #     self.overdue_label.setText(f"Overdue: {overdue_count}")
        #     self.due_soon_label.setText(f"Due Soon: {due_soon_count}")
        #     self.ok_label.setText(f"OK: {ok_count}")

        # except Exception as e:
        #     logger.error(f"Error updating dashboard summary: {e}")
        pass

    def on_selection_changed(self):
        """Handle table selection changes."""
        # selected_rows = self.medium_reset_table.selectionModel().selectedRows()
        
        # if selected_rows:
        #     self.edit_button.setEnabled(True)
        #     self.delete_button.setEnabled(True)

        #     # Load selected equipment data for display
        #     row = selected_rows[0].row()
        #     equipment_id = self.medium_reset_table.item(row, 0).text()

        #     if equipment_id:
        #         self.load_equipment_details(equipment_id)
        # else:
        #     self.edit_button.setEnabled(False)
        #     self.delete_button.setEnabled(False)
        #     self.clear_form()
        pass

    def load_equipment_details(self, equipment_id):
        """Load equipment details for display."""
        # try:
        #     equipment = Equipment.get_by_id(equipment_id)

        #     if equipment:
        #         self.current_equipment = equipment

        #         # Populate form fields with equipment data
        #         self.id_field.setText(str(equipment.equipment_id))

        #         # Set equipment dropdown
        #         equipment_text = f"{equipment.make_and_type} - {equipment.ba_number or 'N/A'}"
        #         index = self.equipment_field.findText(equipment_text)
        #         if index >= 0:
        #             self.equipment_field.setCurrentIndex(index)

        #         # Calculate and display maintenance information
        #         release_date = self.get_date_of_release(equipment.date_of_rel)
        #         maintenance_dates = self.calculate_maintenance_dates(release_date)

        #         # Update status display with calculated information
        #         self.update_maintenance_status_display(equipment, maintenance_dates)

        #         self.meter_reading_field.setValue(int(equipment.meterage_kms or 0))
        #         self.description_field.setPlainText(f"Equipment: {equipment.make_and_type}\nBA Number: {equipment.ba_number or 'N/A'}\nDate of Release: {equipment.date_of_rel or 'N/A'}")

        # except Exception as e:
        #     logger.error(f"Error loading equipment details: {e}")
        #     QMessageBox.warning(self, "Error", f"Failed to load equipment details: {str(e)}")
        pass

    def load_medium_reset_details(self, medium_reset_id):
        """Load medium reset details into the form."""
        # try:
        #     result = MediumReset.get_by_id(medium_reset_id)

        #     if result:
        #         self.id_field.setText(str(result['medium_reset_id']))

        #         # Set equipment
        #         for i in range(self.equipment_field.count()):
        #             if self.equipment_field.itemData(i) == result['equipment_id']:
        #                 self.equipment_field.setCurrentIndex(i)
        #                 break

        #         # Set reset type
        #         reset_type = result['reset_type'] or ''
        #         index = self.reset_type_field.findText(reset_type)
        #         if index >= 0:
        #             self.reset_type_field.setCurrentIndex(index)

        #         # Set dates
        #         if result['done_date']:
        #             done_date = datetime.strptime(result['done_date'], "%Y-%m-%d").date()
        #             self.done_date_field.setDate(QDate(done_date))

        #         if result['due_date']:
        #             due_date = datetime.strptime(result['due_date'], "%Y-%m-%d").date()
        #             self.due_date_field.setDate(QDate(due_date))



        #         # Set status
        #         status = self.get_medium_reset_status(result.get('due_date'))
        #         self.status_label.setText(status)
        #         if status == 'Critical':
        #             self.status_label.setStyleSheet('color: red; font-weight: bold;')
        #         elif status == 'Warning':
        #             self.status_label.setStyleSheet('color: orange; font-weight: bold;')
        #         else:
        #             self.status_label.setStyleSheet('color: green; font-weight: bold;')

        #         # Set readings
        #         self.meter_reading_field.setValue(result['meter_reading'] or 0)
        #         self.hours_reading_field.setValue(result['hours_reading'] or 0)

        #         # Set text fields
        #         self.description_field.setPlainText(result['description'] or '')
        #         self.notes_field.setPlainText(result['notes'] or '')

        # except Exception as e:
        #     logger.error(f"Error loading medium reset details: {e}")
        pass

    def update_maintenance_status_display(self, equipment, maintenance_dates):
        """Update the status display with maintenance information."""
        # try:
        #     current_km = float(equipment.meterage_kms or 0)
        #     status_text = "Maintenance Status:\n"

        #     # Check each maintenance event
        #     for event in ['mr1', 'oh1', 'mr2', 'oh2']:
        #         due_date = maintenance_dates.get(f'{event}_due_date')
        #         km_limit = maintenance_dates.get(f'{event}_km_limit', 0)
        #         status = self.calculate_status(due_date, km_limit, current_km)

        #         event_name = event.upper().replace('OH2', 'OH2 (Discard)')
        #         status_text += f"{event_name}: {status}\n"

        #     self.status_label.setText(status_text.strip())

        #     # Set overall color based on worst status
        #     if "Overdue" in status_text:
        #         self.status_label.setStyleSheet('color: red; font-weight: bold;')
        #     elif "Due Soon" in status_text:
        #         self.status_label.setStyleSheet('color: orange; font-weight: bold;')
        #     else:
        #         self.status_label.setStyleSheet('color: green; font-weight: bold;')

        # except Exception as e:
        #     logger.error(f"Error updating maintenance status display: {e}")
        #     self.status_label.setText("❓ UNKNOWN")
        #     self.status_label.setStyleSheet('color: gray; font-weight: bold;')
        pass

    def clear_form(self):
        """Clear the medium reset form."""
        # self.id_field.clear()
        # self.equipment_field.setCurrentIndex(0)
        # self.reset_type_field.setCurrentIndex(0)
        # self.done_date_field.setDate(QDate.currentDate())
        # self.due_date_field.setDate(QDate.currentDate().addYears(1))
        # self.status_label.setText("")
        # self.meter_reading_field.setValue(0)
        # self.hours_reading_field.setValue(0)
        # self.description_field.clear()
        # self.notes_field.clear()
        pass

    def set_form_enabled(self, enabled):
        """Enable or disable form fields."""
        # self.equipment_field.setEnabled(enabled)
        # self.reset_type_field.setEnabled(enabled)
        # self.done_date_field.setReadOnly(not enabled)
        # self.due_date_field.setReadOnly(not enabled)
        # self.meter_reading_field.setEnabled(enabled)
        # self.hours_reading_field.setEnabled(enabled)
        # self.description_field.setReadOnly(not enabled)
        # self.notes_field.setReadOnly(not enabled)
        # self.save_button.setEnabled(enabled)
        # self.cancel_button.setEnabled(enabled)
        pass

    def add_medium_reset(self):
        """Add a new medium reset entry (for manual tracking)."""
        # self.current_medium_reset = None
        # self.clear_form()
        # self.set_form_enabled(True)

        # # Set default values
        # self.done_date_field.setDate(QDate.currentDate())
        # self.due_date_field.setDate(QDate.currentDate().addYears(1))

        # QMessageBox.information(self, "Add Medium Reset",
        #                        "This form allows you to add manual medium reset entries.\n"
        #                        "The main table shows automated calculations based on equipment vintage.")
        # self.equipment_field.setFocus()
        pass

    def edit_medium_reset(self):
        """Edit the selected medium reset."""
        # self.set_form_enabled(True)
        # self.equipment_field.setFocus()
        pass

    def delete_medium_reset(self):
        """Delete the selected medium reset."""
        # selected_items = self.medium_reset_table.selectedItems()
        # if not selected_items:
        #     return

        # row = selected_items[0].row()
        # medium_reset_id = self.medium_reset_table.item(row, 0).text()

        # # Confirm deletion
        # reply = QMessageBox.question(
        #     self,
        #     "Confirm Deletion",
        #     f"Are you sure you want to delete medium reset ID {medium_reset_id}?",
        #     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        #     QMessageBox.StandardButton.No
        # )

        # if reply == QMessageBox.StandardButton.Yes:
        #     try:
        #         result = MediumReset.delete(medium_reset_id)

        #         if result:
        #             QMessageBox.information(
        #                 self,
        #                 "Success",
        #                 "Medium reset record deleted successfully.",
        #                 QMessageBox.StandardButton.Ok
        #             )
        #             # Reload data to refresh the table
        #             self.load_data()
        #             # Clear the form completely
        #             self.clear_form()
        #             # Disable form editing
        #             self.set_form_enabled(False)
        #             # Disable action buttons
        #             self.edit_button.setEnabled(False)
        #             self.delete_button.setEnabled(False)
        #             # Clear table selection to ensure no row is selected
        #             self.medium_reset_table.clearSelection()
        #         else:
        #             QMessageBox.critical(
        #                 self,
        #                 "Error",
        #                 "Failed to delete medium reset record. Please try again.",
        #                 QMessageBox.StandardButton.Ok
        #             )
        #     except Exception as e:
        #         logger.error(f"Exception occurred while deleting medium reset with ID {medium_reset_id}: {e}", exc_info=True)
        #         QMessageBox.critical(
        #             self,
        #             "Error",
        #             f"An error occurred: {str(e)}",
        #             QMessageBox.StandardButton.Ok
        #         )
        pass

    def save_medium_reset(self):
        """Save medium reset data."""
        # # Get form values
        # medium_reset_id = self.id_field.text()
        # equipment_id = self.equipment_field.currentData()
        # reset_type = self.reset_type_field.currentText()

        # # Get date as Python date object
        # done_date_qdate = self.done_date_field.date()
        # done_date = date(done_date_qdate.year(), done_date_qdate.month(), done_date_qdate.day())

        # due_date_qdate = self.due_date_field.date()
        # due_date = date(due_date_qdate.year(), due_date_qdate.month(), due_date_qdate.day())



        # meter_reading = self.meter_reading_field.value()
        # hours_reading = self.hours_reading_field.value()
        # description = self.description_field.toPlainText()
        # notes = self.notes_field.toPlainText()

        # # Validate form
        # if not equipment_id:
        #     QMessageBox.warning(
        #         self,
        #         "Validation Error",
        #         "Equipment is a required field.",
        #         QMessageBox.StandardButton.Ok
        #     )
        #     return

        # try:
        #     # Create medium reset object
        #     new_medium_reset = MediumReset(
        #         medium_reset_id=int(medium_reset_id) if medium_reset_id else None,
        #         equipment_id=equipment_id,
        #         reset_type=reset_type,
        #         done_date=done_date,
        #         due_date=due_date,
        #         meter_reading=meter_reading,
        #         hours_reading=hours_reading,
        #         description=description,
        #         notes=notes
        #     )

        #     # Save medium reset
        #     result = new_medium_reset.save()

        #     if result:
        #         QMessageBox.information(
        #             self,
        #             "Success",
        #             "Medium reset record saved successfully.",
        #             QMessageBox.StandardButton.Ok
        #         )

        #         # Reload data
        #         self.load_data()

        #         # Load the saved medium reset details
        #         self.load_medium_reset_details(result)

        #         # Disable form fields
        #         self.set_form_enabled(False)

        #         # Enable edit and delete buttons
        #         self.edit_button.setEnabled(True)
        #         self.delete_button.setEnabled(True)
        #     else:
        #         QMessageBox.critical(
        #             self,
        #             "Error",
        #             "Failed to save medium reset record. Please try again.",
        #             QMessageBox.StandardButton.Ok
        #         )
        # except Exception as e:
        #     logger.error(f"Error saving medium reset: {e}")
        #     QMessageBox.critical(
        #         self,
        #         "Error",
        #         f"An error occurred: {str(e)}",
        #         QMessageBox.StandardButton.Ok
        #     )
        pass

    def cancel_edit(self):
        """Cancel medium reset edit."""
        # # Get current medium reset ID
        # medium_reset_id = self.id_field.text()

        # if medium_reset_id:
        #     # Reload medium reset details
        #     self.load_medium_reset_details(int(medium_reset_id))
        # else:
        #     # Clear form
        #     self.clear_form()

        # # Disable form fields
        # self.set_form_enabled(False)
        pass
    
    def add_medium_reset_entry(self):
        """Handle adding a new medium reset entry."""
        # # Clear any existing selection
        # self.medium_reset_table.clearSelection()
        # # Clear and enable the form for new entry
        # self.clear_form()
        # self.set_form_enabled(True)
        # # Disable edit and delete buttons since this is a new entry
        # self.edit_button.setEnabled(False)
        # self.delete_button.setEnabled(False)
        # # Focus on the equipment field
        # self.equipment_field.setFocus()
        pass
    
    def get_medium_reset_status(self, due_date):
        """Return status string based on due_date proximity."""
        # if not due_date:
        #     return ""
        # if isinstance(due_date, str):
        #     try:
        #         due_date = datetime.strptime(due_date, "%Y-%m-%d").date()
        #     except Exception:
        #         return ""
        # today = date.today()
        # if due_date <= today or (due_date - today).days <= 365:
        #     return "Critical"
        # elif (due_date - today).days <= 2*365:
        #     return "Warning"
        # else:
        #     return "Normal"
        pass