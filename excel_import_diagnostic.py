import pandas as pd
import logging

def diagnose_excel_import(file_path):
    """Diagnose Excel import issues"""
    print(f"🔍 Diagnosing Excel file: {file_path}")
    
    try:
        # Read Excel file
        excel_file = pd.ExcelFile(file_path)
        print(f"📊 Found {len(excel_file.sheet_names)} sheets: {excel_file.sheet_names}")
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n📋 Sheet: {sheet_name}")
            
            # Try different header configurations
            for header_config in [[0], [0, 1], None]:
                try:
                    df = pd.read_excel(excel_file, sheet_name=sheet_name, header=header_config)
                    print(f"  ✅ Header config {header_config}: {len(df)} rows, {len(df.columns)} columns")
                    print(f"     Sample columns: {list(df.columns)[:5]}...")
                    
                    # Check for equipment columns
                    equipment_cols = []
                    for col in df.columns:
                        col_str = str(col).upper()
                        if any(term in col_str for term in ['SER NO', 'BA NO', 'MAKE', 'TYPE']):
                            equipment_cols.append(col)
                    print(f"     Equipment columns found: {equipment_cols}")
                    
                    # Check for fluid columns
                    fluid_cols = []
                    fluid_types = ['ENG OIL', 'HYDRAULIC', 'COOLANT', 'GREASE', 'BRAKE', 'TRANSMISSION']
                    for col in df.columns:
                        col_str = str(col).upper()
                        if any(fluid_type in col_str for fluid_type in fluid_types):
                            fluid_cols.append(col)
                    print(f"     Fluid columns found: {len(fluid_cols)} - {fluid_cols[:3]}...")
                    
                    # Check for maintenance columns
                    maintenance_cols = []
                    for col in df.columns:
                        col_str = str(col).upper()
                        if any(term in col_str for term in ['TM-', 'MAINTENANCE', 'SERVICE', 'DUE', 'DONE']):
                            maintenance_cols.append(col)
                    print(f"     Maintenance columns found: {len(maintenance_cols)} - {maintenance_cols[:3]}...")
                    
                    # Check for overhaul columns
                    overhaul_cols = []
                    for col in df.columns:
                        col_str = str(col).upper()
                        if any(term in col_str for term in ['OH', 'OVERHAUL', 'RELEASE']):
                            overhaul_cols.append(col)
                    print(f"     Overhaul columns found: {len(overhaul_cols)} - {overhaul_cols[:3]}...")
                    
                    break  # Use first working configuration
                    
                except Exception as e:
                    print(f"  ❌ Header config {header_config} failed: {e}")
                    continue
    
    except Exception as e:
        print(f"❌ Error reading Excel file: {e}")

if __name__ == "__main__":
    # Replace with actual file path
    file_path = input("Enter Excel file path: ")
    diagnose_excel_import(file_path) 