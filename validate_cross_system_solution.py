#!/usr/bin/env python3
"""
Simple validation script for cross-system compatibility solution.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

def validate_components():
    """Validate all cross-system compatibility components."""
    print("=" * 60)
    print("PROJECT-ALPHA CROSS-SYSTEM COMPATIBILITY VALIDATION")
    print("=" * 60)
    
    results = []
    
    # Test 1: System Compatibility Layer
    try:
        from system_compatibility_layer import get_system_compatibility_status
        status = get_system_compatibility_status()
        print("✅ System Compatibility Layer: WORKING")
        print(f"   Compatible: {status['compatible']}")
        results.append(True)
    except Exception as e:
        print(f"❌ System Compatibility Layer: FAILED - {e}")
        results.append(False)
    
    # Test 2: Locale Compatibility Handler
    try:
        from locale_compatibility_handler import parse_date_locale_safe, parse_number_locale_safe
        date_result = parse_date_locale_safe('2023-12-25')
        number_result = parse_number_locale_safe('1,234.56')
        print("✅ Locale Compatibility Handler: WORKING")
        print(f"   Date parsing: {date_result}")
        print(f"   Number parsing: {number_result}")
        results.append(True)
    except Exception as e:
        print(f"❌ Locale Compatibility Handler: FAILED - {e}")
        results.append(False)
    
    # Test 3: Memory Resource Manager
    try:
        from memory_resource_manager import get_memory_status
        status = get_memory_status()
        print("✅ Memory Resource Manager: WORKING")
        print(f"   Available memory: {status['memory']['available_mb']:.1f}MB")
        results.append(True)
    except Exception as e:
        print(f"❌ Memory Resource Manager: FAILED - {e}")
        results.append(False)
    
    # Test 4: System Environment Detector
    try:
        from system_environment_detector import get_processing_strategy, get_system_profile
        strategy = get_processing_strategy()
        profile = get_system_profile()
        print("✅ System Environment Detector: WORKING")
        print(f"   Recommended strategy: {strategy}")
        print(f"   System: {profile['system']['system']} {profile['system']['release']}")
        print(f"   Memory: {profile['system']['total_memory_gb']:.1f}GB")
        results.append(True)
    except Exception as e:
        print(f"❌ System Environment Detector: FAILED - {e}")
        results.append(False)
    
    # Test 5: Cross-System Excel Importer
    try:
        from cross_system_excel_importer import CrossSystemExcelImporter
        importer = CrossSystemExcelImporter()
        print("✅ Cross-System Excel Importer: WORKING")
        print(f"   Processing strategy: {importer.processing_strategy}")
        results.append(True)
    except Exception as e:
        print(f"❌ Cross-System Excel Importer: FAILED - {e}")
        results.append(False)
    
    # Test 6: Deployment Validation Tools
    try:
        from deployment_validation_tools import validate_excel_import_readiness
        readiness = validate_excel_import_readiness()
        print("✅ Deployment Validation Tools: WORKING")
        print(f"   Excel import ready: {readiness}")
        results.append(True)
    except Exception as e:
        print(f"❌ Deployment Validation Tools: FAILED - {e}")
        results.append(False)
    
    # Summary
    print("\n" + "=" * 60)
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print("🎉 ALL COMPONENTS WORKING - READY FOR DEPLOYMENT")
        print("✅ Cross-system compatibility solution is fully functional")
        return True
    else:
        print(f"⚠️  {passed}/{total} COMPONENTS WORKING")
        print("❌ Some components need attention before deployment")
        return False

if __name__ == "__main__":
    success = validate_components()
    sys.exit(0 if success else 1)
