"""
Final maintenance logic fix script.
Updates any remaining due_date references in the codebase.
"""
import os
import re

def update_file_content(file_path, updates):
    """Update file content with multiple find/replace operations."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        updated = False
        for old_text, new_text in updates:
            if old_text in content:
                content = content.replace(old_text, new_text)
                updated = True
                print(f"✅ Updated in {file_path}: {old_text[:50]}...")
        
        if updated:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"💾 Saved {file_path}")
            return True
        return False
    except Exception as e:
        print(f"❌ Error updating {file_path}: {e}")
        return False

def fix_remaining_references():
    """Fix remaining due_date references in the codebase."""
    print("🔧 FINAL MAINTENANCE LOGIC FIXES")
    print("=" * 40)
    
    # Fix main.py
    main_updates = [
        ("due_date=due_date.isoformat()", "next_due_date=due_date.isoformat()"),
        ("f\"Due Date: {due_date", "f\"Next Due Date: {due_date")
    ]
    update_file_content("main.py", main_updates)
    
    # Fix models.py (remaining queries)
    models_updates = [
        ("WHERE m.due_date IS NOT NULL", "WHERE m.next_due_date IS NOT NULL"),
        ("date(m.due_date)", "date(m.next_due_date)"),
        ("ORDER BY m.due_date", "ORDER BY m.next_due_date"),
        ("m.due_date,", "m.next_due_date,"),
    ]
    update_file_content("models.py", models_updates)
    
    # Fix maintenance_widget.py
    widget_updates = [
        ("AND m.due_date IS NOT NULL", "AND m.next_due_date IS NOT NULL"),
        ("ORDER BY m.due_date", "ORDER BY m.next_due_date"),
        ("maintenance.get('due_date')", "maintenance.get('next_due_date') or maintenance.get('due_date')"),
    ]
    update_file_content("ui/maintenance_widget.py", widget_updates)
    
    # Fix utils.py
    utils_updates = [
        ("due_date_val = maintenance.get('due_date')", "due_date_val = maintenance.get('next_due_date') or maintenance.get('due_date')"),
    ]
    update_file_content("utils.py", utils_updates)
    
    print("\n🎯 FINAL FIXES COMPLETED!")
    print("✅ All due_date references should now be updated")
    print("✅ The application is ready to use with corrected maintenance logic")

if __name__ == "__main__":
    fix_remaining_references() 