"""
Code Quality and Stability Improvements for PROJECT-ALPHA
Addresses potential bugs, deprecated functions, and compatibility issues
"""

import logging
import sys
import traceback
from functools import wraps
from typing import Optional, Any, Dict, List

logger = logging.getLogger('code_quality')

class ErrorHandler:
    """Centralized error handling for the application."""
    
    @staticmethod
    def safe_execute(func, default_return=None, log_errors=True):
        """Safely execute a function with error handling."""
        try:
            return func()
        except Exception as e:
            if log_errors:
                logger.error(f"Error in {func.__name__ if hasattr(func, '__name__') else 'function'}: {e}")
                logger.debug(f"Traceback: {traceback.format_exc()}")
            return default_return
    
    @staticmethod
    def safe_database_operation(operation, default_return=None):
        """Safely execute database operations with proper error handling."""
        try:
            return operation()
        except Exception as e:
            logger.error(f"Database operation failed: {e}")
            logger.debug(f"Traceback: {traceback.format_exc()}")
            return default_return

def safe_method(default_return=None, log_errors=True):
    """Decorator for safe method execution."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if log_errors:
                    logger.error(f"Error in {func.__name__}: {e}")
                    logger.debug(f"Traceback: {traceback.format_exc()}")
                return default_return
        return wrapper
    return decorator

class DatabaseQueryValidator:
    """Validates and sanitizes database queries."""
    
    @staticmethod
    def validate_query_params(params):
        """Validate query parameters to prevent SQL injection."""
        if not isinstance(params, (tuple, list)):
            return ()
        
        validated_params = []
        for param in params:
            if param is None:
                validated_params.append(None)
            elif isinstance(param, (str, int, float, bool)):
                validated_params.append(param)
            else:
                # Convert to string for safety
                validated_params.append(str(param))
        
        return tuple(validated_params)
    
    @staticmethod
    def sanitize_query(query):
        """Basic query sanitization."""
        if not isinstance(query, str):
            raise ValueError("Query must be a string")
        
        # Remove potentially dangerous SQL commands
        dangerous_keywords = [
            'DROP', 'DELETE FROM', 'TRUNCATE', 'ALTER TABLE',
            'CREATE TABLE', 'INSERT INTO', 'UPDATE SET'
        ]
        
        query_upper = query.upper()
        for keyword in dangerous_keywords:
            if keyword in query_upper and 'SELECT' not in query_upper:
                logger.warning(f"Potentially dangerous query detected: {query[:100]}...")
        
        return query

class CompatibilityChecker:
    """Checks for compatibility issues and deprecated functions."""
    
    @staticmethod
    def check_python_version():
        """Check if Python version is compatible."""
        if sys.version_info < (3, 9):
            logger.warning(f"Python {sys.version} may not be fully compatible. Recommended: Python 3.9+")
            return False
        return True
    
    @staticmethod
    def check_pyqt5_compatibility():
        """Check PyQt5 compatibility and version."""
        try:
            from PyQt5.QtCore import QT_VERSION_STR
            logger.info(f"PyQt5 version: {QT_VERSION_STR}")
            return True
        except ImportError:
            logger.error("PyQt5 not available")
            return False
    
    @staticmethod
    def check_dependencies():
        """Check all required dependencies."""
        required_modules = [
            'PyQt5', 'pandas', 'openpyxl', 'matplotlib', 
            'reportlab', 'numpy', 'sqlite3'
        ]
        
        missing_modules = []
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
        
        if missing_modules:
            logger.error(f"Missing required modules: {missing_modules}")
            return False
        
        return True

class MemoryLeakPrevention:
    """Prevents common memory leaks in PyQt5 applications."""
    
    @staticmethod
    def cleanup_matplotlib():
        """Clean up matplotlib figures to prevent memory leaks."""
        try:
            import matplotlib.pyplot as plt
            plt.close('all')
            import gc
            gc.collect()
        except ImportError:
            pass
    
    @staticmethod
    def cleanup_pyqt_objects(widget):
        """Clean up PyQt objects properly."""
        try:
            if widget and hasattr(widget, 'deleteLater'):
                widget.deleteLater()
        except Exception as e:
            logger.debug(f"Error cleaning up PyQt object: {e}")
    
    @staticmethod
    def cleanup_database_connections():
        """Ensure database connections are properly closed."""
        try:
            import database
            # Force cleanup of any lingering connections
            import gc
            gc.collect()
        except Exception as e:
            logger.debug(f"Error cleaning up database connections: {e}")

class DataValidation:
    """Validates data to prevent crashes and corruption."""
    
    @staticmethod
    def validate_date_string(date_str):
        """Validate and normalize date strings."""
        if not date_str or date_str in ['', 'None', 'NULL', 'null']:
            return None
        
        try:
            from datetime import datetime
            # Try multiple date formats
            formats = ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d %H:%M:%S']
            
            for fmt in formats:
                try:
                    parsed_date = datetime.strptime(str(date_str).split(' ')[0], fmt)
                    return parsed_date.date().isoformat()
                except ValueError:
                    continue
            
            # If no format works, return None
            logger.warning(f"Could not parse date: {date_str}")
            return None
            
        except Exception as e:
            logger.error(f"Error validating date {date_str}: {e}")
            return None
    
    @staticmethod
    def validate_numeric_value(value, default=0):
        """Validate and convert numeric values."""
        if value is None or value == '':
            return default
        
        try:
            if isinstance(value, (int, float)):
                return float(value)
            
            # Handle string values
            value_str = str(value).strip()
            if value_str.lower() in ['none', 'null', '', 'n/a', 'na']:
                return default
            
            return float(value_str)
            
        except (ValueError, TypeError):
            logger.warning(f"Could not convert to numeric: {value}")
            return default
    
    @staticmethod
    def validate_equipment_data(equipment_dict):
        """Validate equipment data dictionary."""
        if not isinstance(equipment_dict, dict):
            return {}
        
        validated = {}
        
        # Required string fields
        string_fields = ['make_and_type', 'serial_number', 'ba_number', 'status']
        for field in string_fields:
            value = equipment_dict.get(field, '')
            validated[field] = str(value) if value is not None else ''
        
        # Numeric fields
        numeric_fields = ['units_held', 'vintage_years', 'meterage_kms', 'hours_run_total']
        for field in numeric_fields:
            validated[field] = DataValidation.validate_numeric_value(
                equipment_dict.get(field), default=0
            )
        
        # Date fields
        date_fields = ['last_service_date', 'next_service_date', 'date_of_commission']
        for field in date_fields:
            validated[field] = DataValidation.validate_date_string(
                equipment_dict.get(field)
            )
        
        # Boolean fields
        validated['is_active'] = bool(equipment_dict.get('is_active', True))
        
        return validated

class UIStabilityEnhancements:
    """Enhances UI stability and prevents crashes."""
    
    @staticmethod
    def safe_widget_update(widget, update_function, *args, **kwargs):
        """Safely update a widget with error handling."""
        try:
            if widget and hasattr(widget, 'isVisible') and widget.isVisible():
                return update_function(*args, **kwargs)
        except Exception as e:
            logger.error(f"Error updating widget: {e}")
            return None
    
    @staticmethod
    def safe_table_update(table, data, max_rows=1000):
        """Safely update table with large datasets."""
        try:
            if not table or not hasattr(table, 'setRowCount'):
                return False
            
            # Limit rows to prevent performance issues
            limited_data = data[:max_rows] if len(data) > max_rows else data
            
            if len(data) > max_rows:
                logger.warning(f"Table data truncated from {len(data)} to {max_rows} rows")
            
            table.setRowCount(len(limited_data))
            return True
            
        except Exception as e:
            logger.error(f"Error updating table: {e}")
            return False
    
    @staticmethod
    def safe_chart_update(chart_widget, data):
        """Safely update charts with error handling."""
        try:
            if not chart_widget or not hasattr(chart_widget, 'update_chart'):
                return False
            
            # Validate data before updating
            if not data or (isinstance(data, (list, dict)) and len(data) == 0):
                logger.debug("No data available for chart update")
                return False
            
            chart_widget.update_chart()
            return True
            
        except Exception as e:
            logger.error(f"Error updating chart: {e}")
            return False

class ConfigurationValidator:
    """Validates application configuration."""
    
    @staticmethod
    def validate_database_path(db_path):
        """Validate database path and permissions."""
        try:
            from pathlib import Path
            
            db_file = Path(db_path)
            db_dir = db_file.parent
            
            # Check if directory exists or can be created
            if not db_dir.exists():
                try:
                    db_dir.mkdir(parents=True, exist_ok=True)
                except PermissionError:
                    logger.error(f"No permission to create database directory: {db_dir}")
                    return False
            
            # Check write permissions
            if db_file.exists():
                if not os.access(db_file, os.W_OK):
                    logger.error(f"No write permission for database file: {db_file}")
                    return False
            else:
                if not os.access(db_dir, os.W_OK):
                    logger.error(f"No write permission for database directory: {db_dir}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating database path: {e}")
            return False
    
    @staticmethod
    def validate_ui_settings():
        """Validate UI settings and configurations."""
        try:
            # Check screen resolution
            from PyQt5.QtWidgets import QApplication
            from PyQt5.QtGui import QGuiApplication
            
            app = QApplication.instance() or QGuiApplication.instance()
            if app:
                screen = app.primaryScreen()
                if screen:
                    geometry = screen.geometry()
                    width, height = geometry.width(), geometry.height()
                    
                    if width < 1024 or height < 768:
                        logger.warning(f"Low screen resolution detected: {width}x{height}")
                        logger.info("Low-resolution optimizations will be applied")
                    
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error validating UI settings: {e}")
            return False

# Utility functions for easy integration
def initialize_quality_checks():
    """Initialize all quality checks."""
    logger.info("Initializing code quality checks...")
    
    # Check Python version
    if not CompatibilityChecker.check_python_version():
        logger.warning("Python version compatibility issues detected")
    
    # Check dependencies
    if not CompatibilityChecker.check_dependencies():
        logger.error("Dependency check failed")
        return False
    
    # Check PyQt5
    if not CompatibilityChecker.check_pyqt5_compatibility():
        logger.error("PyQt5 compatibility check failed")
        return False
    
    logger.info("Code quality checks completed successfully")
    return True

def cleanup_application_resources():
    """Clean up application resources to prevent memory leaks."""
    logger.info("Cleaning up application resources...")
    
    MemoryLeakPrevention.cleanup_matplotlib()
    MemoryLeakPrevention.cleanup_database_connections()
    
    # Force garbage collection
    import gc
    gc.collect()
    
    logger.info("Resource cleanup completed")

# Context manager for safe operations
class SafeOperation:
    """Context manager for safe operations with automatic cleanup."""
    
    def __init__(self, operation_name, cleanup_func=None):
        self.operation_name = operation_name
        self.cleanup_func = cleanup_func
    
    def __enter__(self):
        logger.debug(f"Starting safe operation: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            logger.error(f"Error in {self.operation_name}: {exc_val}")
            logger.debug(f"Traceback: {traceback.format_exc()}")
        
        if self.cleanup_func:
            try:
                self.cleanup_func()
            except Exception as cleanup_error:
                logger.error(f"Error during cleanup: {cleanup_error}")
        
        logger.debug(f"Completed safe operation: {self.operation_name}")
        return False  # Don't suppress exceptions
