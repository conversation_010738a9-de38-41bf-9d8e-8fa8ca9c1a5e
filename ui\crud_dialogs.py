"""
Enhanced CRUD dialogs for PROJECT-ALPHA
Provides responsive, military-optimized dialogs for Create, Read, Update, Delete operations.
"""

import logging
from datetime import datetime, date
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
    QDateEdit, QTextEdit, QCheckBox, QGroupBox, QScrollArea, QTabWidget,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
    QProgressBar, QMessageBox, QDialogButtonBox, QFileDialog, QFrame,
    QSplitter, QListWidget, QListWidgetItem
)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSignal, QThread, QObject
from PyQt5.QtGui import QFont, QIcon, QPixmap, QPalette, QColor

from ui.window_utils import DPIScaler, DialogManager, FormManager, LayoutManager
from ui.custom_widgets import StatusLabel
import utils
import database

logger = logging.getLogger('crud_dialogs')

class ResponsiveFormDialog(QDialog):
    """
    Base class for responsive form dialogs optimized for military deployment.
    Provides standardized layout, validation, and functionality.
    """
    
    # Signals
    data_saved = pyqtSignal(dict)
    operation_cancelled = pyqtSignal()
    
    def __init__(self, title="Form Dialog", mode="create", record_data=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.mode = mode  # 'create', 'edit', 'view'
        self.record_data = record_data or {}
        self.validation_errors = []
        
        self.setup_dialog()
        self.setup_form()
        self.setup_buttons()
        self.setup_styling()
        
        if self.record_data:
            self.populate_form()
            
        self.set_form_mode()
        
    def setup_dialog(self):
        """Setup the dialog with responsive sizing."""
        self.setWindowTitle(self.title)
        self.setModal(True)
        
        # Setup responsive dialog sizing
        DialogManager.setup_responsive_dialog(
            self, 
            width_percent=0.6, 
            height_percent=0.7,
            min_width=600,
            min_height=500
        )
        
        # Main layout
        self.main_layout = QVBoxLayout(self)
        LayoutManager.setup_responsive_layout(self.main_layout, margins=(10, 10, 10, 10), spacing=8)
        
    def setup_form(self):
        """Setup the form area with scrolling support."""
        # Create scroll area for form
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # Form widget
        self.form_widget = QWidget()
        self.form_layout = QFormLayout(self.form_widget)
        FormManager.setup_responsive_form_layout(self.form_layout)
        
        # Add form fields - to be overridden by subclasses
        self.create_form_fields()
        
        scroll_area.setWidget(self.form_widget)
        self.main_layout.addWidget(scroll_area)
        
    def create_form_fields(self):
        """Create form fields - to be overridden by subclasses."""
        # Example fields
        self.id_field = QLineEdit()
        self.id_field.setReadOnly(True)
        self.form_layout.addRow("ID:", self.id_field)
        
        self.name_field = QLineEdit()
        self.form_layout.addRow("Name:", self.name_field)
        
    def setup_buttons(self):
        """Setup dialog buttons."""
        button_layout = QHBoxLayout()
        
        # Validation status
        self.validation_label = QLabel("")
        self.validation_label.setStyleSheet("color: red; font-weight: bold;")
        button_layout.addWidget(self.validation_label)
        
        button_layout.addStretch()
        
        # Action buttons
        self.save_btn = QPushButton("💾 Save")
        self.save_btn.clicked.connect(self.save_data)
        self.save_btn.setStyleSheet(self.get_button_style("save"))
        button_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("❌ Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        if self.mode != "create":
            self.delete_btn = QPushButton("🗑️ Delete")
            self.delete_btn.clicked.connect(self.delete_record)
            self.delete_btn.setStyleSheet(self.get_button_style("delete"))
            button_layout.addWidget(self.delete_btn)
        
        self.main_layout.addLayout(button_layout)
        
    def setup_styling(self):
        """Setup responsive styling."""
        self.setStyleSheet(f"""
            QDialog {{
                background-color: #f5f5f5;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: {DPIScaler.scale_font_size(10)}px;
            }}
            
            QGroupBox {{
                font-weight: bold;
                border: {DPIScaler.scale_size(2)}px solid #cccccc;
                border-radius: {DPIScaler.scale_size(5)}px;
                margin-top: {DPIScaler.scale_size(10)}px;
                padding-top: {DPIScaler.scale_size(5)}px;
            }}
            
            QLineEdit, QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox {{
                border: {DPIScaler.scale_size(1)}px solid #cccccc;
                border-radius: {DPIScaler.scale_size(3)}px;
                padding: {DPIScaler.scale_size(5)}px;
                min-height: {DPIScaler.scale_size(25)}px;
            }}
            
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {{
                border: {DPIScaler.scale_size(2)}px solid #2196F3;
            }}
            
            QTextEdit {{
                border: {DPIScaler.scale_size(1)}px solid #cccccc;
                border-radius: {DPIScaler.scale_size(3)}px;
                padding: {DPIScaler.scale_size(5)}px;
            }}
        """)
        
    def get_button_style(self, button_type):
        """Get responsive button styling."""
        base_style = f"""
            QPushButton {{
                border: none;
                padding: {DPIScaler.scale_size(8)}px {DPIScaler.scale_size(16)}px;
                border-radius: {DPIScaler.scale_size(4)}px;
                font-weight: bold;
                font-size: {DPIScaler.scale_font_size(10)}px;
                min-height: {DPIScaler.scale_size(32)}px;
                min-width: {DPIScaler.scale_size(80)}px;
            }}
            QPushButton:hover {{
                opacity: 0.8;
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
                color: #666666;
            }}
        """
        
        colors = {
            "save": "background-color: #4CAF50; color: white;",
            "delete": "background-color: #f44336; color: white;",
            "cancel": "background-color: #9E9E9E; color: white;"
        }
        
        return base_style + colors.get(button_type, "background-color: #e0e0e0; color: black;")
        
    def set_form_mode(self):
        """Set form mode based on dialog mode."""
        is_readonly = self.mode == "view"
        
        # Enable/disable form fields
        for widget in self.form_widget.findChildren((QLineEdit, QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox, QTextEdit)):
            if hasattr(widget, 'setReadOnly'):
                widget.setReadOnly(is_readonly)
            else:
                widget.setEnabled(not is_readonly)
                
        # Update button visibility
        self.save_btn.setVisible(self.mode != "view")
        if hasattr(self, 'delete_btn'):
            self.delete_btn.setVisible(self.mode == "edit")
            
    def populate_form(self):
        """Populate form with record data - to be overridden."""
        pass
        
    def validate_form(self):
        """Validate form data - to be overridden."""
        self.validation_errors = []
        return True
        
    def get_form_data(self):
        """Get form data as dictionary - to be overridden."""
        return {}
        
    def save_data(self):
        """Save form data."""
        if not self.validate_form():
            self.show_validation_errors()
            return
            
        try:
            form_data = self.get_form_data()
            
            # Emit signal with data
            self.data_saved.emit(form_data)
            
            # Show success message
            QMessageBox.information(self, "Success", "Record saved successfully.")
            self.accept()
            
        except Exception as e:
            logger.error(f"Error saving data: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save record: {str(e)}")
            
    def delete_record(self):
        """Delete the current record."""
        reply = QMessageBox.question(
            self, 
            "Confirm Delete",
            "Are you sure you want to delete this record?\n\nThis action cannot be undone.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # Emit delete signal or handle deletion
                QMessageBox.information(self, "Success", "Record deleted successfully.")
                self.accept()
                
            except Exception as e:
                logger.error(f"Error deleting record: {e}")
                QMessageBox.critical(self, "Error", f"Failed to delete record: {str(e)}")
                
    def show_validation_errors(self):
        """Show validation errors to user."""
        if self.validation_errors:
            error_text = "\n".join(self.validation_errors)
            self.validation_label.setText(f"Validation Errors:\n{error_text}")
        else:
            self.validation_label.clear()

class BulkOperationDialog(QDialog):
    """Dialog for bulk operations (create, edit, delete)."""
    
    operation_completed = pyqtSignal(str, int, int)  # operation, success_count, total_count
    
    def __init__(self, operation_type, table_name, parent=None):
        super().__init__(parent)
        self.operation_type = operation_type  # 'create', 'edit', 'delete'
        self.table_name = table_name
        self.selected_records = []
        
        self.setup_dialog()
        self.setup_ui()
        
    def setup_dialog(self):
        """Setup the dialog."""
        self.setWindowTitle(f"Bulk {self.operation_type.title()} - {self.table_name.title()}")
        self.setModal(True)
        
        # Responsive sizing
        DialogManager.setup_responsive_dialog(
            self,
            width_percent=0.8,
            height_percent=0.8,
            min_width=800,
            min_height=600
        )
        
    def setup_ui(self):
        """Setup the UI based on operation type."""
        layout = QVBoxLayout(self)
        LayoutManager.setup_responsive_layout(layout)
        
        # Header
        header = QLabel(f"Bulk {self.operation_type.title()} Operation")
        header.setFont(DPIScaler.create_scaled_font(14, bold=True))
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)
        
        if self.operation_type == "create":
            self.setup_bulk_create_ui(layout)
        elif self.operation_type == "edit":
            self.setup_bulk_edit_ui(layout)
        elif self.operation_type == "delete":
            self.setup_bulk_delete_ui(layout)
            
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.execute_btn = QPushButton(f"Execute {self.operation_type.title()}")
        self.execute_btn.clicked.connect(self.execute_operation)
        self.execute_btn.setStyleSheet(self.get_button_style("execute"))
        button_layout.addWidget(self.execute_btn)
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
    def setup_bulk_create_ui(self, layout):
        """Setup UI for bulk create operation."""
        # CSV import option
        csv_group = QGroupBox("Import from CSV")
        csv_layout = QVBoxLayout(csv_group)
        
        csv_info = QLabel("Select a CSV file to import multiple records:")
        csv_layout.addWidget(csv_info)
        
        csv_file_layout = QHBoxLayout()
        self.csv_file_path = QLineEdit()
        self.csv_file_path.setReadOnly(True)
        csv_file_layout.addWidget(self.csv_file_path)
        
        browse_btn = QPushButton("Browse...")
        browse_btn.clicked.connect(self.browse_csv_file)
        csv_file_layout.addWidget(browse_btn)
        
        csv_layout.addLayout(csv_file_layout)
        layout.addWidget(csv_group)
        
        # Manual entry option
        manual_group = QGroupBox("Manual Entry")
        manual_layout = QVBoxLayout(manual_group)
        
        manual_info = QLabel("Enter multiple records manually (one per line):")
        manual_layout.addWidget(manual_info)
        
        self.manual_entry = QTextEdit()
        self.manual_entry.setPlaceholderText("Enter record data here...")
        manual_layout.addWidget(self.manual_entry)
        
        layout.addWidget(manual_group)
        
    def setup_bulk_edit_ui(self, layout):
        """Setup UI for bulk edit operation."""
        # Record selection
        selection_group = QGroupBox("Select Records to Edit")
        selection_layout = QVBoxLayout(selection_group)
        
        self.record_list = QListWidget()
        self.record_list.setSelectionMode(QAbstractItemView.MultiSelection)
        selection_layout.addWidget(self.record_list)
        
        layout.addWidget(selection_group)
        
        # Edit fields
        edit_group = QGroupBox("Fields to Update")
        edit_layout = QFormLayout(edit_group)
        FormManager.setup_responsive_form_layout(edit_layout)
        
        # Add common edit fields
        self.status_edit = QComboBox()
        self.status_edit.addItems(["", "Active", "Inactive", "Completed", "Pending"])
        edit_layout.addRow("Status:", self.status_edit)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(DPIScaler.scale_size(100))
        edit_layout.addRow("Notes:", self.notes_edit)
        
        layout.addWidget(edit_group)
        
    def setup_bulk_delete_ui(self, layout):
        """Setup UI for bulk delete operation."""
        # Warning
        warning = QLabel("⚠️ WARNING: This operation will permanently delete the selected records!")
        warning.setStyleSheet("color: red; font-weight: bold; font-size: 14px;")
        warning.setAlignment(Qt.AlignCenter)
        layout.addWidget(warning)
        
        # Record selection
        selection_group = QGroupBox("Select Records to Delete")
        selection_layout = QVBoxLayout(selection_group)
        
        self.record_list = QListWidget()
        self.record_list.setSelectionMode(QAbstractItemView.MultiSelection)
        selection_layout.addWidget(self.record_list)
        
        layout.addWidget(selection_group)
        
        # Confirmation
        confirm_group = QGroupBox("Confirmation")
        confirm_layout = QVBoxLayout(confirm_group)
        
        self.confirm_checkbox = QCheckBox("I understand that this action cannot be undone")
        confirm_layout.addWidget(self.confirm_checkbox)
        
        self.confirm_text = QLineEdit()
        self.confirm_text.setPlaceholderText("Type 'DELETE' to confirm")
        confirm_layout.addWidget(self.confirm_text)
        
        layout.addWidget(confirm_group)
        
    def get_button_style(self, button_type):
        """Get button styling."""
        base_style = f"""
            QPushButton {{
                border: none;
                padding: {DPIScaler.scale_size(10)}px {DPIScaler.scale_size(20)}px;
                border-radius: {DPIScaler.scale_size(4)}px;
                font-weight: bold;
                font-size: {DPIScaler.scale_font_size(11)}px;
                min-height: {DPIScaler.scale_size(35)}px;
            }}
            QPushButton:hover {{
                opacity: 0.8;
            }}
        """
        
        if button_type == "execute":
            if self.operation_type == "delete":
                return base_style + "background-color: #f44336; color: white;"
            else:
                return base_style + "background-color: #4CAF50; color: white;"
        else:
            return base_style + "background-color: #9E9E9E; color: white;"
            
    def browse_csv_file(self):
        """Browse for CSV file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select CSV File",
            "",
            "CSV Files (*.csv);;All Files (*)"
        )
        
        if file_path:
            self.csv_file_path.setText(file_path)
            
    def execute_operation(self):
        """Execute the bulk operation."""
        if self.operation_type == "delete":
            if not self.confirm_checkbox.isChecked() or self.confirm_text.text() != "DELETE":
                QMessageBox.warning(self, "Confirmation Required", 
                                  "Please confirm the deletion by checking the box and typing 'DELETE'.")
                return
                
        # Show progress
        self.progress_bar.setVisible(True)
        self.execute_btn.setEnabled(False)
        
        try:
            # Simulate operation progress
            total_operations = len(self.selected_records) if hasattr(self, 'selected_records') else 10
            success_count = 0
            
            for i in range(total_operations):
                # Simulate processing
                QTimer.singleShot(i * 100, lambda: self.progress_bar.setValue(int((i + 1) / total_operations * 100)))
                success_count += 1
                
            # Complete
            QTimer.singleShot(total_operations * 100 + 500, lambda: self.operation_complete(success_count, total_operations))
            
        except Exception as e:
            logger.error(f"Error in bulk operation: {e}")
            QMessageBox.critical(self, "Error", f"Bulk operation failed: {str(e)}")
            self.progress_bar.setVisible(False)
            self.execute_btn.setEnabled(True)
            
    def operation_complete(self, success_count, total_count):
        """Handle operation completion."""
        self.progress_bar.setVisible(False)
        self.execute_btn.setEnabled(True)
        
        self.operation_completed.emit(self.operation_type, success_count, total_count)
        
        QMessageBox.information(
            self, 
            "Operation Complete",
            f"Bulk {self.operation_type} completed.\n"
            f"Successful: {success_count}/{total_count}"
        )
        
        self.accept()

class HistoryViewDialog(QDialog):
    """Dialog for viewing record history and audit trails."""
    
    def __init__(self, record_id, table_name, parent=None):
        super().__init__(parent)
        self.record_id = record_id
        self.table_name = table_name
        
        self.setup_dialog()
        self.setup_ui()
        self.load_history()
        
    def setup_dialog(self):
        """Setup the dialog."""
        self.setWindowTitle(f"History - {self.table_name.title()} #{self.record_id}")
        self.setModal(True)
        
        DialogManager.setup_responsive_dialog(
            self,
            width_percent=0.7,
            height_percent=0.8,
            min_width=700,
            min_height=500
        )
        
    def setup_ui(self):
        """Setup the UI."""
        layout = QVBoxLayout(self)
        LayoutManager.setup_responsive_layout(layout)
        
        # Header
        header = QLabel(f"Change History for {self.table_name.title()} #{self.record_id}")
        header.setFont(DPIScaler.create_scaled_font(14, bold=True))
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)
        
        # History table
        self.history_table = QTableWidget()
        self.history_table.setAlternatingRowColors(True)
        self.history_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        layout.addWidget(self.history_table)
        
        # Close button
        close_btn = QPushButton("Close")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)
        
    def load_history(self):
        """Load history data."""
        # Placeholder implementation
        headers = ["Date", "User", "Action", "Field", "Old Value", "New Value", "Notes"]
        self.history_table.setColumnCount(len(headers))
        self.history_table.setHorizontalHeaderLabels(headers)
        
        # Sample data
        sample_data = [
            ["2024-01-15 10:30", "admin", "Created", "All", "", "Initial creation", "Record created"],
            ["2024-01-16 14:20", "user1", "Updated", "Status", "Pending", "Active", "Status changed"],
            ["2024-01-17 09:15", "user2", "Updated", "Notes", "", "Updated notes", "Added notes"]
        ]
        
        self.history_table.setRowCount(len(sample_data))
        
        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value))
                item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                self.history_table.setItem(row, col, item)
                
        # Resize columns
        self.history_table.resizeColumnsToContents()
        header = self.history_table.horizontalHeader()
        header.setStretchLastSection(True)
