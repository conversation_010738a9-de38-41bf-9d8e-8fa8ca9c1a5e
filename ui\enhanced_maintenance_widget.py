"""
Enhanced Maintenance Widget with comprehensive CRUD functionality
Optimized for military deployment on 1366x768 displays.
"""

import logging
from datetime import datetime, date, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
    QDateEdit, QTextEdit, QCheckBox, QGroupBox, QSplitter, QFrame,
    QMessageBox, QTabWidget, QFileDialog, QProgressBar, QDialog
)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

from ui.responsive_crud_framework import ResponsiveCRUDWidget
from ui.crud_dialogs import BulkOperationDialog, HistoryViewDialog
from ui.window_utils import DPIScaler, Form<PERSON>anager, LayoutManager
from ui.custom_widgets import StatusLabel
from models import Maintenance, Equipment
import utils
import config
import database

logger = logging.getLogger('enhanced_maintenance_widget')

class EnhancedMaintenanceWidget(QWidget):
    """Enhanced maintenance widget with comprehensive CRUD functionality."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the main UI with maintenance category tabs."""
        main_layout = QVBoxLayout(self)
        LayoutManager.setup_responsive_layout(main_layout, margins=(5, 5, 5, 5), spacing=8)
        
        # Header with title and global actions
        header_layout = self.create_header_layout()
        main_layout.addLayout(header_layout)
        
        # Tab widget for maintenance categories
        self.tab_widget = QTabWidget()
        
        # TM-1 tab
        self.tm1_widget = MaintenanceCRUDWidget("TM-1", parent=self)
        self.tab_widget.addTab(self.tm1_widget, "TM-1 Maintenance")
        
        # TM-2 tab
        self.tm2_widget = MaintenanceCRUDWidget("TM-2", parent=self)
        self.tab_widget.addTab(self.tm2_widget, "TM-2 Maintenance")
        
        # Yearly tab
        self.yearly_widget = MaintenanceCRUDWidget("Yearly", parent=self)
        self.tab_widget.addTab(self.yearly_widget, "Yearly Maintenance")
        
        # Monthly tab
        self.monthly_widget = MaintenanceCRUDWidget("Monthly", parent=self)
        self.tab_widget.addTab(self.monthly_widget, "Monthly Maintenance")
        
        # History tab
        self.history_widget = MaintenanceHistoryWidget(parent=self)
        self.tab_widget.addTab(self.history_widget, "Maintenance History")
        
        main_layout.addWidget(self.tab_widget)
        
        # Connect signals
        for widget in [self.tm1_widget, self.tm2_widget, self.yearly_widget, self.monthly_widget]:
            widget.data_changed.connect(self.on_data_changed)
            
    def create_header_layout(self):
        """Create the header layout with global actions."""
        layout = QHBoxLayout()
        
        # Title
        title = QLabel("🔧 Equipment Maintenance Management")
        title.setFont(DPIScaler.create_scaled_font(16, bold=True))
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Global actions
        self.global_export_btn = QPushButton("📊 Export All")
        self.global_export_btn.clicked.connect(self.export_all_maintenance)
        layout.addWidget(self.global_export_btn)
        
        self.global_refresh_btn = QPushButton("🔄 Refresh All")
        self.global_refresh_btn.clicked.connect(self.refresh_all_tabs)
        layout.addWidget(self.global_refresh_btn)
        
        return layout
        
    def on_data_changed(self):
        """Handle data changes from sub-widgets."""
        self.refresh_all_tabs()
        
    def refresh_all_tabs(self):
        """Refresh data in all tabs."""
        for widget in [self.tm1_widget, self.tm2_widget, self.yearly_widget, self.monthly_widget]:
            widget.refresh_data()
        if hasattr(self.history_widget, 'refresh_data'):
            self.history_widget.refresh_data()
            
    def export_all_maintenance(self):
        """Export all maintenance data to CSV."""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Export All Maintenance",
                f"all_maintenance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )
            
            if file_path:
                QMessageBox.information(self, "Success", f"All maintenance data exported to {file_path}")
                
        except Exception as e:
            logger.error(f"Error exporting maintenance data: {e}")
            QMessageBox.critical(self, "Error", f"Failed to export maintenance data: {str(e)}")
            
    def switch_to_category(self, category):
        """Switch to the specified maintenance category tab."""
        try:
            tab_mapping = {
                'TM-1': 0,
                'TM-2': 1,
                'Yearly': 2,
                'Monthly': 3
            }
            
            tab_index = tab_mapping.get(category, 0)
            self.tab_widget.setCurrentIndex(tab_index)
            
            # Load data for the current tab
            current_widget = self.tab_widget.currentWidget()
            if hasattr(current_widget, 'load_data'):
                current_widget.load_data()
                
        except Exception as e:
            logger.error(f"Error switching to maintenance category {category}: {e}")

class MaintenanceCRUDWidget(ResponsiveCRUDWidget):
    """CRUD widget for maintenance management with category-specific functionality."""
    
    def __init__(self, category, parent=None):
        self.category = category
        super().__init__(f"maintenance_{category.lower()}", parent)
        
    def create_form_widget(self):
        """Create the maintenance-specific form widget."""
        widget = QWidget()
        layout = QFormLayout(widget)
        FormManager.setup_responsive_form_layout(layout)
        
        # Basic Information Section
        basic_group = QGroupBox("Basic Information")
        basic_layout = QFormLayout(basic_group)
        FormManager.setup_responsive_form_layout(basic_layout)
        
        # Maintenance ID (read-only)
        self.id_field = QLineEdit()
        self.id_field.setReadOnly(True)
        basic_layout.addRow("Maintenance ID:", self.id_field)
        
        # Equipment selection
        self.equipment_field = QComboBox()
        self.equipment_field.setEditable(True)
        basic_layout.addRow("Equipment:", self.equipment_field)
        
        # Maintenance Category (read-only)
        self.category_field = QLineEdit(self.category)
        self.category_field.setReadOnly(True)
        basic_layout.addRow("Category:", self.category_field)
        
        # Maintenance Type
        self.type_field = QComboBox()
        maintenance_types = config.MAINTENANCE_TYPES.get(self.category, ["General Maintenance"])
        self.type_field.addItems(maintenance_types)
        self.type_field.setEditable(True)
        basic_layout.addRow("Maintenance Type:", self.type_field)
        
        layout.addRow(basic_group)
        
        # Scheduling Section
        schedule_group = QGroupBox("Scheduling")
        schedule_layout = QFormLayout(schedule_group)
        FormManager.setup_responsive_form_layout(schedule_layout)
        
        # Due Date
        self.due_date_field = QDateEdit()
        self.due_date_field.setCalendarPopup(True)
        self.due_date_field.setDate(QDate.currentDate().addDays(30))
        schedule_layout.addRow("Due Date:", self.due_date_field)
        
        # Scheduled Date
        self.scheduled_date_field = QDateEdit()
        self.scheduled_date_field.setCalendarPopup(True)
        self.scheduled_date_field.setSpecialValueText("Not scheduled")
        self.scheduled_date_field.setDate(QDate(1900, 1, 1))
        schedule_layout.addRow("Scheduled Date:", self.scheduled_date_field)
        
        # Completion Date
        self.completion_date_field = QDateEdit()
        self.completion_date_field.setCalendarPopup(True)
        self.completion_date_field.setSpecialValueText("Not completed")
        self.completion_date_field.setDate(QDate(1900, 1, 1))
        schedule_layout.addRow("Completion Date:", self.completion_date_field)
        
        layout.addRow(schedule_group)
        
        # Status and Priority Section
        status_group = QGroupBox("Status & Priority")
        status_layout = QFormLayout(status_group)
        FormManager.setup_responsive_form_layout(status_layout)
        
        # Status
        self.status_field = QComboBox()
        self.status_field.addItems(["pending", "scheduled", "in_progress", "completed", "cancelled", "waiting_decision"])
        status_layout.addRow("Status:", self.status_field)
        
        # Priority
        self.priority_field = QComboBox()
        self.priority_field.addItems(["low", "normal", "high", "critical"])
        self.priority_field.setCurrentText("normal")
        status_layout.addRow("Priority:", self.priority_field)
        
        # Calculated Status Display
        self.calculated_status_display = StatusLabel("Unknown", "unknown")
        status_layout.addRow("Calculated Status:", self.calculated_status_display)
        
        layout.addRow(status_group)
        
        # Technical Details Section
        technical_group = QGroupBox("Technical Details")
        technical_layout = QFormLayout(technical_group)
        FormManager.setup_responsive_form_layout(technical_layout)
        
        # Meterage at maintenance
        self.meterage_field = QDoubleSpinBox()
        self.meterage_field.setRange(0, 999999)
        self.meterage_field.setSuffix(" km")
        technical_layout.addRow("Meterage (km):", self.meterage_field)
        
        # Hours at maintenance
        self.hours_field = QDoubleSpinBox()
        self.hours_field.setRange(0, 999999)
        self.hours_field.setSuffix(" hrs")
        technical_layout.addRow("Hours:", self.hours_field)
        
        # Cost
        self.cost_field = QDoubleSpinBox()
        self.cost_field.setRange(0, 9999999)
        self.cost_field.setPrefix("₹ ")
        technical_layout.addRow("Cost:", self.cost_field)
        
        # Workshop/Vendor
        self.workshop_field = QLineEdit()
        technical_layout.addRow("Workshop/Vendor:", self.workshop_field)
        
        layout.addRow(technical_group)
        
        # Notes Section
        notes_group = QGroupBox("Notes & Comments")
        notes_layout = QFormLayout(notes_group)
        FormManager.setup_responsive_form_layout(notes_layout)
        
        # Work Description
        self.work_description_field = QTextEdit()
        self.work_description_field.setMaximumHeight(DPIScaler.scale_size(80))
        self.work_description_field.setPlaceholderText("Describe the maintenance work...")
        notes_layout.addRow("Work Description:", self.work_description_field)
        
        # Completion Notes
        self.completion_notes_field = QTextEdit()
        self.completion_notes_field.setMaximumHeight(DPIScaler.scale_size(80))
        self.completion_notes_field.setPlaceholderText("Enter completion notes...")
        notes_layout.addRow("Completion Notes:", self.completion_notes_field)
        
        # Issues Found
        self.issues_field = QTextEdit()
        self.issues_field.setMaximumHeight(DPIScaler.scale_size(60))
        self.issues_field.setPlaceholderText("Document any issues found...")
        notes_layout.addRow("Issues Found:", self.issues_field)
        
        layout.addRow(notes_group)
        
        # Load equipment data
        self.load_equipment_data()
        
        # Connect signals for real-time status calculation
        self.due_date_field.dateChanged.connect(self.update_calculated_status)
        self.completion_date_field.dateChanged.connect(self.update_calculated_status)
        
        return widget
        
    def load_equipment_data(self):
        """Load equipment data for the dropdown."""
        try:
            self.equipment_field.clear()
            equipment_list = Equipment.get_all()
            
            for equipment in equipment_list:
                if equipment.equipment_status != 'discarded':
                    display_text = f"{equipment.ba_number} - {equipment.make_and_type}"
                    self.equipment_field.addItem(display_text, equipment.equipment_id)
                    
        except Exception as e:
            logger.error(f"Error loading equipment data: {e}")
            
    def update_calculated_status(self):
        """Update calculated status based on due date."""
        try:
            due_date = self.due_date_field.date().toPyDate()
            completion_date = self.completion_date_field.date().toPyDate()
            
            # If completed, show completed status
            if completion_date != date(1900, 1, 1):
                self.calculated_status_display.setStatus("completed")
                return
                
            # Calculate status based on due date (for form display only)
            # Note: This is for UI form display, not database records
            maintenance_data = {'due_date': due_date, 'status': 'scheduled'}
            calculated_status = utils.calculate_maintenance_status(maintenance_data)
            self.calculated_status_display.setStatus(calculated_status)
            
        except Exception as e:
            logger.error(f"Error updating calculated status: {e}")
            
    def load_data(self):
        """Load maintenance data for this category."""
        try:
            # Get maintenance records by category
            maintenance_list = Maintenance.get_by_category(self.category)
            
            # Apply current filters
            search_text = self.search_field.text().lower()
            status_filter = self.status_filter.currentText()
            
            # Filter data
            filtered_maintenance = []
            for maintenance in maintenance_list:
                # Search filter
                if search_text:
                    equipment = Equipment.get_by_id(maintenance.get('equipment_id'))
                    searchable_text = f"{equipment.ba_number if equipment else ''} {equipment.make_and_type if equipment else ''} {maintenance.get('maintenance_type', '')}".lower()
                    if search_text not in searchable_text:
                        continue
                        
                # Status filter
                if status_filter != "All":
                    calculated_status = utils.calculate_maintenance_status(maintenance)
                    if status_filter.lower() != calculated_status:
                        continue
                        
                filtered_maintenance.append(maintenance)
            
            # Prepare data for table
            headers = [
                "ID", "BA Number", "Make & Type", "Type", "Due Date", 
                "Scheduled", "Completed", "Status", "Priority", "Cost"
            ]
            data = []
            
            for maintenance in filtered_maintenance:
                equipment = Equipment.get_by_id(maintenance.get('equipment_id'))
                if not equipment:
                    continue
                    
                # Calculate status using centralized logic that respects database status field
                calculated_status = utils.calculate_maintenance_status(maintenance)
                
                row_data = {
                    "ID": maintenance.get('maintenance_id'),
                    "BA Number": equipment.ba_number or "Not Assigned",
                    "Make & Type": equipment.make_and_type or "",
                    "Type": maintenance.get('maintenance_type', ''),
                    "Due Date": maintenance.get('due_date', ''),
                    "Scheduled": maintenance.get('scheduled_date', ''),
                    "Completed": maintenance.get('actual_completion_date', ''),
                    "Status": calculated_status.replace('_', ' ').title(),
                    "Priority": maintenance.get('priority', 'normal').title(),
                    "Cost": f"₹ {maintenance.get('cost', 0):,.2f}"
                }
                data.append(row_data)
            
            # Sort by due date and priority
            data.sort(key=lambda x: (x["Due Date"], x["Priority"]))
            
            # Set data in table
            self.data_table.set_data(headers, data, id_column="ID")
            
            # Hide ID column
            self.data_table.setColumnHidden(0, True)
            
        except Exception as e:
            logger.error(f"Error loading maintenance data: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load maintenance data: {str(e)}")

class MaintenanceHistoryWidget(QWidget):
    """Widget for viewing maintenance history and archived records."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the history widget UI."""
        layout = QVBoxLayout(self)
        LayoutManager.setup_responsive_layout(layout)
        
        # Header
        header = QLabel("📋 Maintenance History & Archive")
        header.setFont(DPIScaler.create_scaled_font(14, bold=True))
        layout.addWidget(header)
        
        # Filters
        filter_layout = QHBoxLayout()
        
        filter_layout.addWidget(QLabel("Equipment:"))
        self.equipment_filter = QComboBox()
        self.equipment_filter.addItem("All Equipment")
        filter_layout.addWidget(self.equipment_filter)
        
        filter_layout.addWidget(QLabel("Category:"))
        self.category_filter = QComboBox()
        self.category_filter.addItems(["All", "TM-1", "TM-2", "Yearly", "Monthly"])
        filter_layout.addWidget(self.category_filter)
        
        filter_layout.addWidget(QLabel("Date Range:"))
        self.date_from = QDateEdit()
        self.date_from.setCalendarPopup(True)
        self.date_from.setDate(QDate.currentDate().addMonths(-6))
        filter_layout.addWidget(self.date_from)
        
        self.date_to = QDateEdit()
        self.date_to.setCalendarPopup(True)
        self.date_to.setDate(QDate.currentDate())
        filter_layout.addWidget(self.date_to)
        
        filter_layout.addStretch()
        
        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self.load_history)
        filter_layout.addWidget(refresh_btn)
        
        layout.addLayout(filter_layout)
        
        # History table
        from ui.paginated_table_widget import PaginatedTableWidget
        self.history_table = PaginatedTableWidget(
            page_size=100,
            max_total_rows=50000,
            enable_ba_filter=True,
            enable_ba_grouping=False
        )
        layout.addWidget(self.history_table)
        
        # Load initial data
        self.load_history()
        
    def load_history(self):
        """Load maintenance history data."""
        try:
            # Get archived and completed maintenance records
            query = """
                SELECT m.*, e.ba_number, e.make_and_type
                FROM maintenance m
                JOIN equipment e ON m.equipment_id = e.equipment_id
                WHERE (m.status = 'completed' OR m.status = 'archived')
                AND m.actual_completion_date BETWEEN ? AND ?
                ORDER BY m.actual_completion_date DESC
            """
            
            date_from = self.date_from.date().toString(Qt.ISODate)
            date_to = self.date_to.date().toString(Qt.ISODate)
            
            history_records = database.execute_query(query, (date_from, date_to))
            
            # Prepare data for table
            headers = [
                "ID", "BA Number", "Make & Type", "Category", "Type", 
                "Due Date", "Completed Date", "Cost", "Workshop", "Notes"
            ]
            data = []
            
            for record in history_records:
                row_data = {
                    "ID": record.get('maintenance_id'),
                    "BA Number": record.get('ba_number', ''),
                    "Make & Type": record.get('make_and_type', ''),
                    "Category": record.get('maintenance_category', ''),
                    "Type": record.get('maintenance_type', ''),
                    "Due Date": record.get('due_date', ''),
                    "Completed Date": record.get('actual_completion_date', ''),
                    "Cost": f"₹ {record.get('cost', 0):,.2f}",
                    "Workshop": record.get('workshop', ''),
                    "Notes": (record.get('completion_notes', '') or '')[:50] + "..." if len(record.get('completion_notes', '') or '') > 50 else record.get('completion_notes', '')
                }
                data.append(row_data)
            
            # Set data in table
            self.history_table.set_data(headers, data, id_column="ID")
            
            # Hide ID column
            self.history_table.setColumnHidden(0, True)
            
        except Exception as e:
            logger.error(f"Error loading maintenance history: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load maintenance history: {str(e)}")
