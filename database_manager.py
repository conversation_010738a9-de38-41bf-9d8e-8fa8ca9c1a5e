"""
Database Export/Import Manager
Handles database backup, export, import, and merging operations.
"""
import os
import shutil
import sqlite3
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any

logger = logging.getLogger('database_manager')

class SimpleDatabaseManager:
    """Manages database export, import, and merging operations."""
    
    def __init__(self, db_path: str):
        """Initialize with database path."""
        self.db_path = db_path
    
    def _safe_file_copy(self, source_path: str, dest_path: str) -> None:
        """Safely copy a file with Windows-specific handling."""
        import time
        import platform
        
        # Normalize paths for Windows
        source_path = os.path.normpath(source_path)
        dest_path = os.path.normpath(dest_path)
        
        try:
            # On Windows, sometimes we need to ensure the destination is writable
            if os.path.exists(dest_path):
                if platform.system() == 'Windows':
                    # Remove read-only attribute if present
                    try:
                        import stat
                        os.chmod(dest_path, stat.S_IWRITE)
                    except:
                        pass
                
                # Small delay to ensure file is not locked
                time.sleep(0.1)
                
                # Remove destination file first
                os.remove(dest_path)
                
                # Another small delay after removal
                time.sleep(0.1)
            
            # Copy file
            shutil.copy2(source_path, dest_path)
            
            # Verify copy was successful
            if not os.path.exists(dest_path):
                raise Exception(f"File copy verification failed: {dest_path} was not created")
                
        except Exception as e:
            logger.error(f"Failed to copy {source_path} to {dest_path}: {e}")
            raise
    
    def export_database(self, target_path: str) -> Dict[str, Any]:
        """
        Export database to target location.
        
        Args:
            target_path: Destination path for exported database
            
        Returns:
            Dict with export results and statistics
        """
        try:
            logger.info(f"Starting database export to: {target_path}")
            
            # Ensure source database exists
            if not os.path.exists(self.db_path):
                raise FileNotFoundError(f"Source database not found: {self.db_path}")
            
            # Ensure target directory exists
            target_dir = Path(target_path).parent
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # ENHANCED: Get detailed source database statistics before export
            source_stats = self.get_database_stats(self.db_path)
            logger.info(f"Source database stats: {source_stats}")
            
            # ENHANCED: Validate source database before export
            if 'error' in source_stats:
                raise Exception(f"Source database is corrupted: {source_stats['error']}")
            
            if source_stats.get('equipment', 0) == 0:
                logger.warning("Source database has 0 equipment records!")
            
            logger.info(f"Pre-export: Source has {source_stats.get('equipment', 0)} equipment records")
            
            # Copy database file
            logger.info(f"Copying {self.db_path} to {target_path}")
            shutil.copy2(self.db_path, target_path)
            logger.info(f"Database file copied successfully")
            
            # Verify exported database
            if not os.path.exists(target_path):
                raise Exception("Export failed - target file not created")
            
            # ENHANCED: Detailed verification of exported database
            exported_stats = self.get_database_stats(target_path)
            logger.info(f"Exported database stats: {exported_stats}")
            
            if 'error' in exported_stats:
                raise Exception(f"Exported database is corrupted: {exported_stats['error']}")
            
            # ENHANCED: Check specific table counts
            equipment_diff = source_stats.get('equipment', 0) - exported_stats.get('equipment', 0)
            if equipment_diff != 0:
                logger.error(f"EQUIPMENT COUNT MISMATCH: Lost {equipment_diff} equipment records during export!")
                logger.error(f"Source equipment: {source_stats.get('equipment', 0)}")
                logger.error(f"Exported equipment: {exported_stats.get('equipment', 0)}")
            
            # Check if total record counts match
            total_diff = source_stats['total_records'] - exported_stats['total_records']
            if total_diff != 0:
                logger.warning(f"Total record count mismatch: lost {total_diff} records during export")
                logger.warning(f"Source total: {source_stats['total_records']}, Exported total: {exported_stats['total_records']}")
            else:
                logger.info(f"✅ Record counts match: {source_stats['total_records']} records exported successfully")
            
            file_size = os.path.getsize(target_path)
            
            result = {
                'success': True,
                'source_path': self.db_path,
                'target_path': target_path,
                'file_size': file_size,
                'export_date': datetime.now().isoformat(),
                'stats': exported_stats,
                'source_stats': source_stats,
                'message': 'Database exported successfully'
            }
            
            logger.info(f"Database export completed successfully. Size: {file_size} bytes")
            return result
            
        except Exception as e:
            logger.error(f"Database export failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'source_path': self.db_path,
                'target_path': target_path
            }
    
    def import_database(self, source_path: str, mode: str = 'replace', conflict_resolutions: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Import database from source location.
        
        Args:
            source_path: Path to source database file
            mode: Import mode ('replace' or 'merge')
            conflict_resolutions: Optional conflict resolutions for merge mode
            
        Returns:
            Dict with import results and statistics
        """
        try:
            logger.info(f"Starting database import from: {source_path}, mode: {mode}")
            
            # Validate source database
            if not os.path.exists(source_path):
                raise FileNotFoundError(f"Source database not found: {source_path}")
            
            # Verify source database integrity
            source_stats = self.get_database_stats(source_path)
            if 'error' in source_stats:
                raise Exception(f"Source database is corrupted: {source_stats['error']}")
            
            if mode == 'replace':
                return self._import_replace_mode(source_path, source_stats)
            elif mode == 'merge':
                return self._import_merge_mode(source_path, source_stats, conflict_resolutions)
            else:
                raise ValueError(f"Invalid import mode: {mode}")
                
        except Exception as e:
            logger.error(f"Database import failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'source_path': source_path,
                'mode': mode
            }
    
    def _import_replace_mode(self, source_path: str, source_stats: Dict[str, Any]) -> Dict[str, Any]:
        """Replace current database with imported database."""
        backup_path = None
        
        try:
            # Create backup of current database
            if os.path.exists(self.db_path):
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_path = f"{self.db_path}.backup_{timestamp}"
                self._safe_file_copy(self.db_path, backup_path)
                logger.info(f"Current database backed up to: {backup_path}")
            
            # Copy source database to current location using safe method
            self._safe_file_copy(source_path, self.db_path)
            logger.info(f"Database replaced successfully")
            
            # Verify imported database
            imported_stats = self.get_database_stats(self.db_path)
            if 'error' in imported_stats:
                # Restore backup if verification fails
                if backup_path and os.path.exists(backup_path):
                    self._safe_file_copy(backup_path, self.db_path)
                    logger.error("Import verification failed, backup restored")
                raise Exception(f"Imported database verification failed: {imported_stats['error']}")
            
            result = {
                'success': True,
                'source_path': source_path,
                'target_path': self.db_path,
                'backup_path': backup_path,
                'mode': 'replace',
                'import_date': datetime.now().isoformat(),
                'source_stats': source_stats,
                'imported_stats': imported_stats,
                'message': 'Database replaced successfully'
            }
            
            logger.info(f"Database import (replace mode) completed successfully")
            return result
            
        except Exception as e:
            # Restore backup on failure
            if backup_path and os.path.exists(backup_path):
                try:
                    self._safe_file_copy(backup_path, self.db_path)
                    logger.info("Backup restored after import failure")
                except Exception as restore_error:
                    logger.error(f"Failed to restore backup: {restore_error}")
            raise e
    
    def _import_merge_mode(self, source_path: str, source_stats: Dict[str, Any], conflict_resolutions: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """Merge source database with current database."""
        try:
            # Get current database stats
            current_stats = self.get_database_stats(self.db_path)
            
            # If no conflict resolutions provided, detect and return conflicts for user resolution
            if conflict_resolutions is None:
                conflicts = self.detect_conflicts(source_path, self.db_path)
                
                if conflicts:
                    # Return conflicts for user resolution
                    return {
                        'success': False,
                        'conflicts_detected': True,
                        'conflicts': conflicts,
                        'source_path': source_path,
                        'source_stats': source_stats,
                        'current_stats': current_stats,
                        'message': 'Conflicts detected - user resolution required'
                    }
            
            # Perform merge with conflict resolutions (if any)
            merge_result = self.merge_databases_with_conflicts(source_path, self.db_path, conflict_resolutions)
            
            # Get final stats
            final_stats = self.get_database_stats(self.db_path)
            
            result = {
                'success': True,
                'source_path': source_path,
                'target_path': self.db_path,
                'mode': 'merge',
                'import_date': datetime.now().isoformat(),
                'source_stats': source_stats,
                'current_stats': current_stats,
                'final_stats': final_stats,
                'merge_result': merge_result,
                'message': 'Database merged successfully'
            }
            
            logger.info(f"Database import (merge mode) completed successfully")
            return result
            
        except Exception as e:
            raise e
    
    def get_database_stats(self, db_path: str) -> Dict[str, Any]:
        """Get comprehensive database statistics."""
        try:
            if not os.path.exists(db_path):
                return {'error': 'Database file not found'}
            
            stats = {}
            
            with sqlite3.connect(db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                # Get table counts
                tables = [
                    'equipment', 'fluids', 'maintenance', 'repairs', 'overhauls',
                    'tyre_maintenance', 'conditioning', 'discard_criteria', 
                    'demand_forecast', 'tyre_forecast', 'battery_forecast', 'settings'
                ]
                
                for table in tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                        count = cursor.fetchone()['count']
                        stats[table] = count
                    except sqlite3.Error:
                        stats[table] = 0
                
                # Calculate totals
                stats['total_records'] = sum(v for k, v in stats.items() if k != 'settings')
                stats['database_size_bytes'] = os.path.getsize(db_path)
                stats['database_size_mb'] = round(stats['database_size_bytes'] / (1024 * 1024), 2)
                
                # Get database version info
                try:
                    cursor.execute("PRAGMA user_version")
                    stats['schema_version'] = cursor.fetchone()[0]
                except sqlite3.Error:
                    stats['schema_version'] = 0
                
                logger.debug(f"Database stats for {db_path}: {stats}")
                return stats
                
        except Exception as e:
            logger.error(f"Error getting database stats for {db_path}: {e}")
            return {'error': str(e)}
    
    def detect_conflicts(self, source_db: str, target_db: str) -> List[Dict[str, Any]]:
        """Detect potential conflicts between two databases."""
        conflicts = []
        
        try:
            with sqlite3.connect(source_db) as source_conn, sqlite3.connect(target_db) as target_conn:
                source_conn.row_factory = sqlite3.Row
                target_conn.row_factory = sqlite3.Row
                
                # Check for equipment conflicts (same BA number, different data)
                equipment_conflicts = self._detect_equipment_conflicts(source_conn, target_conn)
                conflicts.extend(equipment_conflicts)
                
                # Check for maintenance overlaps
                maintenance_conflicts = self._detect_maintenance_conflicts(source_conn, target_conn)
                conflicts.extend(maintenance_conflicts)
                
                logger.info(f"Detected {len(conflicts)} conflicts between databases")
                return conflicts
                
        except Exception as e:
            logger.error(f"Error detecting conflicts: {e}")
            return [{'type': 'detection_error', 'error': str(e)}]
    
    def _detect_equipment_conflicts(self, source_conn: sqlite3.Connection, target_conn: sqlite3.Connection) -> List[Dict[str, Any]]:
        """Detect equipment conflicts between databases."""
        conflicts = []
        
        try:
            # Get equipment from both databases
            source_cursor = source_conn.cursor()
            target_cursor = target_conn.cursor()
            
            source_cursor.execute("SELECT equipment_id, ba_number, make_and_type, meterage_kms FROM equipment")
            source_equipment = {row['ba_number']: dict(row) for row in source_cursor.fetchall() if row['ba_number']}
            
            target_cursor.execute("SELECT equipment_id, ba_number, make_and_type, meterage_kms FROM equipment")
            target_equipment = {row['ba_number']: dict(row) for row in target_cursor.fetchall() if row['ba_number']}
            
            # Find conflicts - ANY duplicate BA number is a conflict for user resolution
            for ba_number, source_eq in source_equipment.items():
                if ba_number in target_equipment:
                    target_eq = target_equipment[ba_number]
                    
                    # For database merging, treat ALL duplicate BA numbers as conflicts
                    # This allows user to choose whether to accept source data or keep target data
                    conflicts.append({
                        'type': 'equipment',
                        'ba_number': ba_number,
                        'source_data': source_eq,
                        'target_data': target_eq,
                        'description': f"Equipment {ba_number} exists in both databases - user resolution required"
                    })
            
            return conflicts
            
        except Exception as e:
            logger.error(f"Error detecting equipment conflicts: {e}")
            return []
    
    def _detect_maintenance_conflicts(self, source_conn: sqlite3.Connection, target_conn: sqlite3.Connection) -> List[Dict[str, Any]]:
        """Detect maintenance conflicts between databases."""
        conflicts = []
        
        try:
            # For now, return empty list - can be enhanced later
            # This would check for overlapping maintenance schedules, etc.
            return conflicts
            
        except Exception as e:
            logger.error(f"Error detecting maintenance conflicts: {e}")
            return []
    
    def merge_databases_with_conflicts(self, source_db: str, target_db: str, conflict_resolutions: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """Merge databases with conflict resolution support."""
        try:
            merge_stats = {
                'equipment_added': 0,
                'equipment_updated': 0,
                'equipment_skipped': 0,
                'fluids_added': 0,
                'maintenance_added': 0,
                'other_records_added': 0
            }
            
            if conflict_resolutions is None:
                conflict_resolutions = {}
            
            with sqlite3.connect(source_db) as source_conn, sqlite3.connect(target_db) as target_conn:
                source_conn.row_factory = sqlite3.Row
                target_conn.row_factory = sqlite3.Row
                
                # Get max IDs from target database
                max_ids = self._get_max_ids(target_conn)
                
                # Import equipment with ID remapping and conflict resolution
                equipment_result = self._import_equipment_with_remapping_detailed(
                    source_conn, target_conn, max_ids, conflict_resolutions
                )
                equipment_id_map = equipment_result['id_mapping']
                merge_stats.update(equipment_result['stats'])
                
                # Import related data with remapped equipment IDs
                if equipment_id_map:
                    merge_stats['fluids_added'] = self._import_fluids_with_remapping(
                        source_conn, target_conn, equipment_id_map, max_ids
                    )
                    merge_stats['maintenance_added'] = self._import_maintenance_with_remapping(
                        source_conn, target_conn, equipment_id_map, max_ids
                    )
                
                # Commit all changes
                target_conn.commit()
                
                logger.info(f"Database merge completed: {merge_stats}")
                return merge_stats
                
        except Exception as e:
            logger.error(f"Error merging databases: {e}")
            raise e
    
    def merge_databases_no_conflicts(self, source_db: str, target_db: str) -> Dict[str, Any]:
        """Merge databases by remapping IDs to avoid conflicts (deprecated - use merge_databases_with_conflicts)."""
        # For backward compatibility, call the new method with no conflict resolutions
        return self.merge_databases_with_conflicts(source_db, target_db, {})
    
    def _get_max_ids(self, conn: sqlite3.Connection) -> Dict[str, int]:
        """Get maximum IDs from all tables."""
        max_ids = {}
        cursor = conn.cursor()
        
        tables_with_ids = {
            'equipment': 'equipment_id',
            'fluids': 'fluid_id', 
            'maintenance': 'maintenance_id',
            'repairs': 'repair_id',
            'overhauls': 'overhaul_id'
        }
        
        for table, id_column in tables_with_ids.items():
            try:
                cursor.execute(f"SELECT MAX({id_column}) as max_id FROM {table}")
                result = cursor.fetchone()
                max_ids[table] = result['max_id'] if result['max_id'] is not None else 0
            except sqlite3.Error:
                max_ids[table] = 0
        
        return max_ids
    
    def _import_equipment_with_remapping_detailed(self, source_conn: sqlite3.Connection, 
                                                 target_conn: sqlite3.Connection, 
                                                 max_ids: Dict[str, int],
                                                 conflict_resolutions: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """Import equipment with ID remapping and conflict resolution. Returns detailed statistics and ID mapping."""
        id_mapping = {}
        stats = {
            'equipment_added': 0,
            'equipment_updated': 0,
            'equipment_skipped': 0
        }
        
        if conflict_resolutions is None:
            conflict_resolutions = {}
        
        try:
            source_cursor = source_conn.cursor()
            target_cursor = target_conn.cursor()
            
            # Get equipment from source
            source_cursor.execute("SELECT * FROM equipment")
            source_equipment = source_cursor.fetchall()
            
            # Get existing equipment with full details
            target_cursor.execute("SELECT equipment_id, ba_number, make_and_type, meterage_kms FROM equipment")
            existing_equipment = {}
            for row in target_cursor.fetchall():
                if row['ba_number']:
                    existing_equipment[row['ba_number']] = {
                        'equipment_id': row['equipment_id'],
                        'make_and_type': row['make_and_type'],
                        'meterage_kms': row['meterage_kms']
                    }
            
            new_equipment_id = max_ids['equipment'] + 1
            
            for equipment in source_equipment:
                equipment_dict = dict(equipment)
                old_id = equipment_dict['equipment_id']
                ba_number = equipment_dict.get('ba_number')
                
                # Check if BA number already exists
                if ba_number and ba_number in existing_equipment:
                    # Check user resolution for this conflict
                    resolution = conflict_resolutions.get(ba_number, 'skip')  # Default to skip if no resolution
                    
                    if resolution == 'accept':
                        # User chose to accept source data - update existing record
                        existing_id = existing_equipment[ba_number]['equipment_id']
                        id_mapping[old_id] = existing_id
                        
                        # Update with source data
                        update_dict = dict(equipment_dict)
                        update_dict['equipment_id'] = existing_id
                        
                        # Build update query
                        update_columns = [f"{col} = ?" for col in update_dict.keys() if col != 'equipment_id']
                        update_values = [update_dict[col] for col in update_dict.keys() if col != 'equipment_id']
                        update_values.append(existing_id)
                        
                        update_query = f"UPDATE equipment SET {', '.join(update_columns)} WHERE equipment_id = ?"
                        target_cursor.execute(update_query, update_values)
                        stats['equipment_updated'] += 1
                        logger.info(f"Updated equipment {ba_number} with source data per user choice")
                        continue
                    else:
                        # User chose to skip - keep existing data
                        stats['equipment_skipped'] += 1
                        logger.info(f"Skipped equipment {ba_number} per user choice (keeping existing data)")
                        continue
                
                # No conflict - add as new equipment
                equipment_dict['equipment_id'] = new_equipment_id
                id_mapping[old_id] = new_equipment_id
                
                # Insert into target database
                columns = ', '.join(equipment_dict.keys())
                placeholders = ', '.join('?' * len(equipment_dict))
                query = f"INSERT INTO equipment ({columns}) VALUES ({placeholders})"
                
                target_cursor.execute(query, list(equipment_dict.values()))
                stats['equipment_added'] += 1
                new_equipment_id += 1
            
            return {
                'id_mapping': id_mapping,
                'stats': stats
            }
            
        except Exception as e:
            logger.error(f"Error importing equipment: {e}")
            raise e
    
    def _import_fluids_with_remapping(self, source_conn: sqlite3.Connection,
                                    target_conn: sqlite3.Connection,
                                    equipment_id_map: Dict[int, int],
                                    max_ids: Dict[str, int]) -> int:
        """Import fluids with remapped equipment IDs and proper conflict handling."""
        try:
            source_cursor = source_conn.cursor()
            target_cursor = target_conn.cursor()
            
            # Get equipment IDs and their existing status in target database
            target_cursor.execute("SELECT equipment_id, ba_number FROM equipment")
            target_equipment = {row['equipment_id']: row['ba_number'] for row in target_cursor.fetchall()}
            
            source_cursor.execute("SELECT * FROM fluids")
            source_fluids = source_cursor.fetchall()
            
            new_fluid_id = max_ids['fluids'] + 1
            imported_count = 0
            
            # Track which target equipment IDs we've processed to avoid duplicates
            processed_target_equipment = set()
            
            for fluid in source_fluids:
                fluid_dict = dict(fluid)
                old_equipment_id = fluid_dict['equipment_id']
                
                # Only import fluids for equipment that was imported/updated
                if old_equipment_id in equipment_id_map:
                    new_equipment_id = equipment_id_map[old_equipment_id]
                    
                    # Check if this is an existing equipment that was updated (not newly added)
                    if new_equipment_id in target_equipment and new_equipment_id not in processed_target_equipment:
                        # This is an existing equipment that was updated with source data
                        # Remove existing fluid records to prevent duplicates
                        target_cursor.execute(
                            "DELETE FROM fluids WHERE equipment_id = ?", 
                            (new_equipment_id,)
                        )
                        logger.info(f"Removed existing fluid records for equipment ID {new_equipment_id} to prevent duplicates")
                        processed_target_equipment.add(new_equipment_id)
                    
                    fluid_dict['fluid_id'] = new_fluid_id
                    fluid_dict['equipment_id'] = new_equipment_id
                    
                    columns = ', '.join(fluid_dict.keys())
                    placeholders = ', '.join('?' * len(fluid_dict))
                    query = f"INSERT INTO fluids ({columns}) VALUES ({placeholders})"
                    
                    target_cursor.execute(query, list(fluid_dict.values()))
                    new_fluid_id += 1
                    imported_count += 1
            
            logger.info(f"Imported {imported_count} fluid records with duplicate prevention")
            return imported_count
            
        except Exception as e:
            logger.error(f"Error importing fluids: {e}")
            return 0
    
    def _import_maintenance_with_remapping(self, source_conn: sqlite3.Connection,
                                         target_conn: sqlite3.Connection,
                                         equipment_id_map: Dict[int, int],
                                         max_ids: Dict[str, int]) -> int:
        """Import maintenance records with remapped equipment IDs and proper conflict handling."""
        try:
            source_cursor = source_conn.cursor()
            target_cursor = target_conn.cursor()
            
            # Get equipment IDs and their existing status in target database
            target_cursor.execute("SELECT equipment_id, ba_number FROM equipment")
            target_equipment = {row['equipment_id']: row['ba_number'] for row in target_cursor.fetchall()}
            
            source_cursor.execute("SELECT * FROM maintenance")
            source_maintenance = source_cursor.fetchall()
            
            new_maintenance_id = max_ids['maintenance'] + 1
            imported_count = 0
            
            # Track which target equipment IDs we've processed to avoid duplicates
            processed_target_equipment = set()
            
            for maintenance in source_maintenance:
                maintenance_dict = dict(maintenance)
                old_equipment_id = maintenance_dict['equipment_id']
                
                # Only import maintenance for equipment that was imported/updated
                if old_equipment_id in equipment_id_map:
                    new_equipment_id = equipment_id_map[old_equipment_id]
                    
                    # Check if this is an existing equipment that was updated (not newly added)
                    if new_equipment_id in target_equipment and new_equipment_id not in processed_target_equipment:
                        # This is an existing equipment that was updated with source data
                        # Remove existing maintenance records to prevent duplicates
                        target_cursor.execute(
                            "DELETE FROM maintenance WHERE equipment_id = ?", 
                            (new_equipment_id,)
                        )
                        logger.info(f"Removed existing maintenance records for equipment ID {new_equipment_id} to prevent duplicates")
                        processed_target_equipment.add(new_equipment_id)
                    
                    maintenance_dict['maintenance_id'] = new_maintenance_id
                    maintenance_dict['equipment_id'] = new_equipment_id
                    
                    columns = ', '.join(maintenance_dict.keys())
                    placeholders = ', '.join('?' * len(maintenance_dict))
                    query = f"INSERT INTO maintenance ({columns}) VALUES ({placeholders})"
                    
                    target_cursor.execute(query, list(maintenance_dict.values()))
                    new_maintenance_id += 1
                    imported_count += 1
            
            logger.info(f"Imported {imported_count} maintenance records with duplicate prevention")
            return imported_count
            
        except Exception as e:
            logger.error(f"Error importing maintenance: {e}")
            return 0

# Convenience functions for external use
def export_database(db_path: str, target_path: str) -> Dict[str, Any]:
    """Export database to target path."""
    manager = SimpleDatabaseManager(db_path)
    return manager.export_database(target_path)

def import_database(db_path: str, source_path: str, mode: str = 'replace') -> Dict[str, Any]:
    """Import database from source path."""
    manager = SimpleDatabaseManager(db_path)
    return manager.import_database(source_path, mode)

def get_database_stats(db_path: str) -> Dict[str, Any]:
    """Get database statistics."""
    manager = SimpleDatabaseManager(db_path)
    return manager.get_database_stats(db_path) 