"""
Excel Importer Diagnostic Patch
Applies fixes to robust_excel_importer_working.py for column mapping issues

This patch can be applied to the existing importer to:
1. Add enhanced column mapping
2. Improve logging for debugging
3. Handle column name variations
4. Add diagnostic functions
"""

import logging
import re
import pandas as pd
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

def apply_enhanced_column_mapping_patch():
    """Apply enhanced column mapping to the existing RobustExcelImporter."""
    
    # Import the existing class
    try:
        from robust_excel_importer_working import RobustExcelImporter
    except ImportError:
        logger.error("Could not import RobustExcelImporter - ensure robust_excel_importer_working.py exists")
        return False
    
    # Monkey patch the _map_equipment_columns method
    def enhanced_map_equipment_columns(self, columns: list) -> Dict[str, str]:
        """Enhanced version of _map_equipment_columns with better column detection."""
        logger.info("=== ENHANCED COLUMN MAPPING DEBUG ===")
        logger.info(f"Total columns received: {len(columns)}")
        
        # Log all original columns with detailed info
        for i, col in enumerate(columns):
            col_str = str(col)
            logger.info(f"Column {i+1}: '{col_str}' (type: {type(col)}, len: {len(col_str)})")
            # Show first few characters and any special characters
            if col_str:
                special_chars = [c for c in col_str if not c.isalnum() and c != ' ']
                if special_chars:
                    logger.info(f"  Special chars: {special_chars}")
        
        # Enhanced column cleaning and normalization
        col_map = {}
        valid_columns = []
        filtered_count = 0
        
        for col in columns:
            col_str = str(col).strip()
            
            # Enhanced filtering - clean up line breaks and whitespace first
            col_str = re.sub(r'[\n\r\t]+', ' ', col_str)  # Replace line breaks with spaces
            col_str = re.sub(r'\s+', ' ', col_str)  # Normalize multiple spaces
            col_str = col_str.strip()
            
            # Skip unnamed columns with enhanced detection
            if (col_str.startswith('Unnamed') or 
                'Unnamed:' in col_str or
                col_str in ['', 'nan', 'NaN', 'None', 'null'] or
                len(col_str) < 2 or
                col_str.replace('.', '').replace('-', '').isdigit()):
                filtered_count += 1
                logger.debug(f"Filtered out problematic column: '{col_str}'")
                continue
            
            valid_columns.append(col)
        
        logger.info(f"Column filtering: {len(columns)} original -> {len(valid_columns)} valid ({filtered_count} filtered out)")
        logger.info(f"Valid columns to map: {[str(c) for c in valid_columns]}")
        
        # Enhanced primary field mappings with more variations
        primary_mappings = {
            'ba_number': [
                r'ba\s*no\.?', r'ba\s*number', r'ba\s*num\.?', r'book\s*no\.?',
                r'asset\s*no\.?', r'ba\s*#', r'ba[_\-]no', r'ba[_\-]number'
            ],
            'serial_number': [
                r'ser\s*no\.?', r'serial\s*no\.?', r'serial\s*number', r'ser\s*num\.?',
                r'equipment\s*no\.?', r'equip\s*no\.?', r'chassis\s*no\.?', r'engine\s*no\.?',
                r'ser[_\-]no', r'serial[_\-]no', r'serial[_\-]number'
            ],
            'make_and_type': [
                r'make\s*and\s*type', r'make\s*&\s*type', r'make\s*/\s*type', r'make[_\-]and[_\-]type',
                r'equipment\s*type', r'vehicle\s*type', r'nomenclature', r'type', r'make',
                r'model', r'description', r'equipment\s*desc'
            ],
            'meterage_kms': [
                r'km\s*run', r'kms\s*run', r'kilometers?\s*run', r'meterage',
                r'meterage\s*kms?', r'total\s*km', r'total\s*kms?', r'odometer',
                r'km[_\-]run', r'kms[_\-]run', r'meterage[_\-]kms?'
            ],
            'hours_run_total': [
                r'hrs?\s*run', r'hours?\s*run', r'total\s*hours?', r'running\s*hours?',
                r'engine\s*hours?', r'operating\s*hours?', r'total\s*hrs?',
                r'hrs?[_\-]run', r'hours?[_\-]run', r'hrs?[_\-]total'
            ],
            'vintage_years': [
                r'vintage', r'vintage\s*years?', r'age', r'age\s*years?',
                r'years?\s*old', r'service\s*years?', r'years?\s*in\s*service',
                r'vintage[_\-]years?', r'age[_\-]years?'
            ],
            'date_of_commission': [
                r'date\s*of\s*commission', r'commission\s*date', r'date\s*of\s*release',
                r'release\s*date', r'rel\s*date', r'date\s*of\s*induction',
                r'induction\s*date', r'commissioned\s*date',
                r'date[_\-]of[_\-]commission', r'date[_\-]of[_\-]release'
            ]
        }
        
        # Enhanced fluid field mappings with more variations
        enhanced_fluid_types = {
            'ENG OIL': {
                'aliases': ['ENGINE OIL', 'ENGOIL', 'ENG_OIL', 'ENGINE_OIL', 'MOTOR OIL', 'LUBE OIL'],
                'attributes': {
                    'CAPACITY': 'engine_oil_capacity',
                    'GRADE': 'engine_oil_grade',
                    'DT OF CHANGE': 'engine_oil_last_change_date',
                    'PERIODICITY': 'engine_oil_periodicity',
                    'ADDL 10% TOP UP': 'engine_oil_top_up'
                }
            },
            'HYDRAULIC FLUID': {
                'aliases': ['HYD OIL', 'HYD FLUID', 'HYDRAULIC OIL', 'HYD_OIL', 'HYD_FLUID', 'HYDR OIL'],
                'attributes': {
                    'CAPACITY': 'hydraulic_fluid_capacity',
                    'GRADE': 'hydraulic_fluid_grade',
                    'DT OF CHANGE': 'hydraulic_fluid_last_change_date',
                    'PERIODICITY': 'hydraulic_fluid_periodicity',
                    'ADDL 10% TOP UP': 'hydraulic_fluid_top_up'
                }
            },
            'COOLANT': {
                'aliases': ['COOLANT FLUID', 'COOLING FLUID', 'RADIATOR FLUID', 'ANTIFREEZE'],
                'attributes': {
                    'CAPACITY': 'coolant_capacity',
                    'GRADE': 'coolant_grade',
                    'DT OF CHANGE': 'coolant_last_change_date',
                    'PERIODICITY': 'coolant_periodicity',
                    'ADDL 10% TOP UP': 'coolant_top_up'
                }
            },
            'TRANSMISSION OIL': {
                'aliases': ['TRANS OIL', 'TXN OIL', 'TRANSMISSION_OIL', 'TRANS_OIL', 'TXN_OIL', 'GEAR OIL'],
                'attributes': {
                    'CAPACITY': 'transmission_oil_capacity',
                    'GRADE': 'transmission_oil_grade',
                    'DT OF CHANGE': 'transmission_oil_last_change_date',
                    'PERIODICITY': 'transmission_oil_periodicity',
                    'ADDL 10% TOP UP': 'transmission_oil_top_up'
                }
            }
        }
        
        # Enhanced separators for composite columns
        separators = [' -> ', ' : ', ' - ', ' >', ':', '>', '-', '.', ' ', '_', '|', '/', '\\', '~']
        
        # Normalize function for flexible matching
        def normalize_for_matching(text):
            if not text:
                return ''
            normalized = str(text).strip().upper()
            normalized = re.sub(r'[\n\r\t]+', ' ', normalized)
            normalized = re.sub(r'\s+', ' ', normalized)
            normalized = re.sub(r'[^\w\s]', ' ', normalized)
            normalized = re.sub(r'\s+', ' ', normalized)
            return normalized.strip()
        
        # First pass: Map primary equipment fields
        logger.info("=== PRIMARY FIELD MAPPING ===")
        for field, patterns in primary_mappings.items():
            best_match = None
            best_confidence = 0
            
            for col in valid_columns:
                col_normalized = normalize_for_matching(col)
                
                for pattern in patterns:
                    if re.search(pattern, col_normalized, re.IGNORECASE):
                        # Calculate confidence based on pattern specificity
                        confidence = len(pattern) / len(col_normalized) if col_normalized else 0
                        if confidence > best_confidence:
                            best_confidence = confidence
                            best_match = col
                
                # Also try exact word matching
                col_words = col_normalized.split()
                field_words = field.replace('_', ' ').split()
                
                if all(word in col_words for word in field_words):
                    confidence = len(field_words) / len(col_words) if col_words else 0
                    if confidence > best_confidence:
                        best_confidence = confidence
                        best_match = col
            
            if best_match and best_confidence > 0.3:  # Minimum confidence threshold
                col_map[field] = best_match
                logger.info(f"✓ Mapped primary field '{field}' to column: '{best_match}' (confidence: {best_confidence:.2f})")
            else:
                logger.warning(f"✗ No good match found for primary field '{field}'")
        
        # Second pass: Map fluid fields with enhanced detection
        logger.info("=== FLUID FIELD MAPPING ===")
        for col in valid_columns:
            col_str = str(col)
            col_normalized = normalize_for_matching(col_str)
            
            logger.debug(f"Processing fluid column: '{col_str}' -> normalized: '{col_normalized}'")
            
            # Try to identify fluid type and attribute
            fluid_type_found = None
            attribute_found = None
            separator_used = None
            
            # Method 1: Check for composite columns with separators
            for separator in separators:
                if separator in col_str:
                    parts = col_str.split(separator, 1)
                    if len(parts) == 2:
                        part1 = normalize_for_matching(parts[0])
                        part2 = normalize_for_matching(parts[1])
                        
                        logger.debug(f"  Split by '{separator}': '{part1}' | '{part2}'")
                        
                        # Check if first part matches any fluid type
                        for fluid_key, fluid_info in enhanced_fluid_types.items():
                            fluid_variations = [normalize_for_matching(fluid_key)] + [normalize_for_matching(alias) for alias in fluid_info['aliases']]
                            
                            for variation in fluid_variations:
                                if variation == part1 or (len(variation) > 3 and variation in part1):
                                    fluid_type_found = fluid_key
                                    separator_used = separator
                                    logger.debug(f"    Found fluid type: {fluid_key} via '{variation}'")
                                    break
                            
                            if fluid_type_found:
                                break
                        
                        # Check if second part matches any attribute
                        if fluid_type_found:
                            attribute_variations = {
                                'CAPACITY': ['CAPACITY', 'CAP', 'VOLUME', 'QTY', 'LTRS', 'KG', 'LTRS/KG', 'LTRS KG'],
                                'GRADE': ['GRADE', 'TYPE', 'SPEC', 'SPECIFICATION', 'BRAND'],
                                'DT OF CHANGE': ['DT OF CHANGE', 'DATE OF CHANGE', 'CHANGE DATE', 'LAST CHANGE', 'DT', 'DATE'],
                                'PERIODICITY': ['PERIODICITY', 'INTERVAL', 'PERIOD', 'FREQUENCY', 'HRS/MONTH', 'HRS MONTH'],
                                'ADDL 10% TOP UP': ['ADDL 10%', 'TOP UP', 'ADDITIONAL', 'EXTRA', '10%', 'TOPPING']
                            }
                            
                            for attr_key, attr_variations in attribute_variations.items():
                                for attr_var in attr_variations:
                                    attr_var_norm = normalize_for_matching(attr_var)
                                    if attr_var_norm == part2 or (len(attr_var_norm) > 2 and attr_var_norm in part2):
                                        attribute_found = attr_key
                                        logger.debug(f"    Found attribute: {attr_key} via '{attr_var}'")
                                        break
                                if attribute_found:
                                    break
                        
                        if fluid_type_found and attribute_found:
                            break
            
            # Method 2: Check for direct fluid type columns (without attributes)
            if not fluid_type_found:
                for fluid_key, fluid_info in enhanced_fluid_types.items():
                    fluid_variations = [normalize_for_matching(fluid_key)] + [normalize_for_matching(alias) for alias in fluid_info['aliases']]
                    
                    for variation in fluid_variations:
                        if variation == col_normalized or (len(variation) > 3 and variation in col_normalized):
                            fluid_type_found = fluid_key
                            attribute_found = 'CAPACITY'  # Default to capacity
                            logger.debug(f"  Found direct fluid type: {fluid_key} via '{variation}' (defaulting to capacity)")
                            break
                    
                    if fluid_type_found:
                        break
            
            # Map the field if we found both fluid type and attribute
            if fluid_type_found and attribute_found:
                if fluid_type_found in enhanced_fluid_types and attribute_found in enhanced_fluid_types[fluid_type_found]['attributes']:
                    field_name = enhanced_fluid_types[fluid_type_found]['attributes'][attribute_found]
                    col_map[field_name] = col
                    
                    separator_info = f" (via separator '{separator_used}')" if separator_used else " (direct match)"
                    logger.info(f"✓ Mapped fluid field '{field_name}' to column: '{col}'{separator_info}")
        
        logger.info(f"=== FINAL MAPPING RESULTS ===")
        logger.info(f"Total fields mapped: {len(col_map)}")
        for field, column in col_map.items():
            logger.info(f"  {field} -> '{column}'")
        
        return col_map
    
    # Apply the patch
    RobustExcelImporter._map_equipment_columns = enhanced_map_equipment_columns
    logger.info("✓ Applied enhanced column mapping patch to RobustExcelImporter")
    
    return True

def enhanced_extract_and_save_fluids_patch():
    """Patch the _extract_and_save_fluids method with enhanced logging."""
    
    try:
        from robust_excel_importer_working import RobustExcelImporter
    except ImportError:
        logger.error("Could not import RobustExcelImporter")
        return False
    
    # Get the original method
    original_extract_fluids = RobustExcelImporter._extract_and_save_fluids
    
    def enhanced_extract_and_save_fluids(self, df: pd.DataFrame, sheet_name: str) -> int:
        """Enhanced version with better logging and debugging."""
        logger.info(f"=== ENHANCED FLUID EXTRACTION for {sheet_name} ===")
        logger.info(f"DataFrame shape: {df.shape}")
        logger.info(f"DataFrame columns: {list(df.columns)}")
        
        fluids_count = 0
        
        # Enhanced fluid type detection
        enhanced_fluid_types = [
            'ENG OIL', 'ENGINE OIL', 'ENGOIL',
            'HYDRAULIC FLUID', 'HYD OIL', 'HYD FLUID', 'HYDRAULIC OIL',
            'COOLANT', 'COOLANT FLUID', 'COOLING FLUID',
            'TRANSMISSION OIL', 'TRANS OIL', 'TXN OIL',
            'BRAKE FLUID', 'BRAKE OIL',
            'GEAR BOX', 'GEARBOX', 'GEAR BOX OIL',
            'DIFFERENTIAL', 'DIFF', 'DIFFERENTIAL OIL',
            'CLUTCH', 'CLUTCH OIL', 'CLUTCH FLUID',
            'GREASE', 'LUBRICATING GREASE'
        ]
        
        # Map equipment data first
        equipment_col_map = self._map_equipment_columns(df.columns)
        logger.info(f"Equipment column mapping: {equipment_col_map}")
        
        # Pre-scan for fluid columns with enhanced detection
        fluid_columns_by_type = {}
        for fluid_type in enhanced_fluid_types:
            fluid_columns_by_type[fluid_type] = []
        
        # Enhanced column scanning
        separators = [' -> ', ' : ', ' - ', ' >', ':', '>', '-', '.', ' ', '_', '|', '/', '\\']
        
        for col in df.columns:
            col_str = str(col).strip()
            col_upper = col_str.upper()
            
            # Clean the column name
            col_clean = re.sub(r'[\n\r\t]+', ' ', col_upper)
            col_clean = re.sub(r'\s+', ' ', col_clean)
            
            logger.debug(f"Scanning column: '{col_str}' -> cleaned: '{col_clean}'")
            
            # Check each fluid type
            for fluid_type in enhanced_fluid_types:
                fluid_type_upper = fluid_type.upper()
                
                # Method 1: Exact match
                if col_clean == fluid_type_upper:
                    fluid_columns_by_type[fluid_type].append(col)
                    logger.debug(f"  Exact match: {fluid_type}")
                    continue
                
                # Method 2: Contains match
                if fluid_type_upper in col_clean:
                    fluid_columns_by_type[fluid_type].append(col)
                    logger.debug(f"  Contains match: {fluid_type}")
                    continue
                
                # Method 3: Separator-based match
                for separator in separators:
                    if separator in col_str:
                        parts = col_str.split(separator, 1)
                        if len(parts) == 2:
                            part1_clean = parts[0].strip().upper()
                            if part1_clean == fluid_type_upper or fluid_type_upper in part1_clean:
                                fluid_columns_by_type[fluid_type].append(col)
                                logger.debug(f"  Separator match: {fluid_type} via '{separator}'")
                                break
        
        # Log findings
        for fluid_type, columns in fluid_columns_by_type.items():
            if columns:
                logger.info(f"Found {len(columns)} columns for {fluid_type}: {columns}")
        
        # Process each row with enhanced logging
        for idx, row in df.iterrows():
            try:
                logger.debug(f"Processing row {idx+2}")
                
                equipment_data = self._extract_equipment_data(row, equipment_col_map, sheet_name)
                equipment_id = self._find_equipment_id(equipment_data)
                
                if not equipment_id:
                    logger.warning(f"Row {idx+2}: No equipment ID found - skipping fluid extraction")
                    logger.debug(f"Row {idx+2} equipment data: {equipment_data}")
                    continue
                
                logger.info(f"Row {idx+2}: Processing fluids for equipment ID {equipment_id} (BA: {equipment_data.get('ba_number', 'Unknown')})")
                
                # Process each fluid type that has columns
                row_fluid_count = 0
                for fluid_type in enhanced_fluid_types:
                    if not fluid_columns_by_type[fluid_type]:
                        continue
                    
                    logger.debug(f"Row {idx+2}: Checking {fluid_type}")
                    
                    # Check if row has meaningful data for this fluid type
                    has_meaningful_data = False
                    data_summary = {}
                    
                    for col in fluid_columns_by_type[fluid_type]:
                        value = row[col]
                        col_upper = str(col).upper()
                        
                        if pd.notna(value) and str(value).strip():
                            data_summary[col] = value
                            
                            # Check for capacity data (most important)
                            if any(term in col_upper for term in ['CAPACITY', 'LTRS', 'KG', 'VOL']):
                                numeric_val = self._parse_numeric(value)
                                if numeric_val > 0:
                                    has_meaningful_data = True
                                    logger.debug(f"Row {idx+2}: Found capacity {numeric_val} for {fluid_type} in {col}")
                    
                    if has_meaningful_data:
                        logger.info(f"Row {idx+2}: Processing {fluid_type} - data found: {data_summary}")
                        
                        # Use original extraction logic
                        fluid_data = self._extract_fluid_data(row, fluid_type, equipment_id)
                        
                        if fluid_data and fluid_data.get('capacity_ltrs_kg', 0) > 0:
                            if self._save_fluid_to_db(fluid_data):
                                fluids_count += 1
                                row_fluid_count += 1
                                logger.info(f"Row {idx+2}: ✓ Saved {fluid_type} (capacity: {fluid_data['capacity_ltrs_kg']}, grade: {fluid_data.get('grade', 'N/A')})")
                            else:
                                logger.warning(f"Row {idx+2}: ✗ Failed to save {fluid_type}")
                        else:
                            logger.debug(f"Row {idx+2}: No valid fluid data extracted for {fluid_type}")
                    else:
                        logger.debug(f"Row {idx+2}: No meaningful data for {fluid_type}")
                
                if row_fluid_count > 0:
                    logger.info(f"Row {idx+2}: Saved {row_fluid_count} fluid types")
                else:
                    logger.warning(f"Row {idx+2}: No fluids saved")
                
            except Exception as e:
                logger.error(f"Row {idx+2}: Error processing fluids: {e}")
                import traceback
                logger.error(f"Row {idx+2}: Traceback: {traceback.format_exc()}")
                continue
        
        logger.info(f"=== FLUID EXTRACTION COMPLETE ===")
        logger.info(f"Total fluids saved: {fluids_count}")
        return fluids_count
    
    # Apply the patch
    RobustExcelImporter._extract_and_save_fluids = enhanced_extract_and_save_fluids
    logger.info("✓ Applied enhanced fluid extraction patch to RobustExcelImporter")
    
    return True

def create_diagnostic_excel_file(filename: str = "diagnostic_test.xlsx"):
    """Create a diagnostic Excel file to test column mapping."""
    
    # Create data with various column name formats that might cause issues
    data = {
        # Standard formats
        'BA No': ['BA001', 'BA002', 'BA003'],
        'Serial Number': ['SER001', 'SER002', 'SER003'],
        'Make and Type': ['TATA 6X6 Truck', 'Maruti Gypsy', 'JCB Excavator'],
        
        # Format variations
        'BA NO ': ['BA004', 'BA005', 'BA006'],  # Extra space
        'Serial\nNumber': ['SER004', 'SER005', 'SER006'],  # Line break
        'Make & Type': ['Bulldozer D6', 'Crane 20T', 'Generator 10KVA'],  # Ampersand
        
        # Numeric fields
        'KM Run': [15000, 25000, 8000],
        'Hrs Run': [500, 800, 1200],
        'Vintage (Years)': [5, 8, 3],
        
        # Standard fluid columns
        'ENG OIL -> CAPACITY (LTRS/KG)': [15, 8, 20],
        'ENG OIL -> GRADE': ['15W40', '10W30', '20W50'],
        'ENG OIL -> DT OF CHANGE': ['2024-01-15', '2023-12-20', '2024-02-10'],
        
        # Variation fluid columns
        'ENGINE OIL : CAPACITY': [16, 9, 21],  # Different separator and name
        'ENGINE OIL : GRADE': ['20W40', '15W30', '25W50'],
        
        'HYD OIL|CAPACITY (LTRS/KG)': [50, 30, 80],  # Different separator
        'HYD OIL|GRADE': ['HLP 68', 'HLP 46', 'HLP 32'],
        
        'COOLANT.CAPACITY': [25, 15, 35],  # Dot separator
        'COOLANT.GRADE': ['Glycol', 'Ethylene', 'Propylene'],
        
        # Problem columns that should be filtered
        'Unnamed: 0': [1, 2, 3],
        '': ['', '', ''],
        '123': ['test1', 'test2', 'test3'],
        
        # Maintenance columns
        'OH I DONE DATE': ['2023-01-15', '2022-12-20', '2024-02-10'],
        'TM1 DUE': ['2024-06-15', '2024-07-20', '2024-08-10'],
        
        'Remarks': ['Good condition', 'Needs service', 'New equipment']
    }
    
    df = pd.DataFrame(data)
    
    # Add some rows with missing/empty data to test handling
    empty_row = {col: None for col in data.keys()}
    empty_row['BA No'] = 'BA999'
    empty_row['Make and Type'] = 'Test Equipment'
    
    df_empty = pd.DataFrame([empty_row])
    df = pd.concat([df, df_empty], ignore_index=True)
    
    df.to_excel(filename, index=False)
    
    logger.info(f"Created diagnostic Excel file: {filename}")
    logger.info(f"Columns ({len(data)}): {list(data.keys())}")
    logger.info(f"Rows: {len(df)}")
    
    return filename

def run_diagnostic_test():
    """Run a comprehensive diagnostic test of the enhanced Excel importer."""
    
    # Apply patches
    logger.info("=== APPLYING DIAGNOSTIC PATCHES ===")
    if not apply_enhanced_column_mapping_patch():
        logger.error("Failed to apply column mapping patch")
        return False
    
    if not enhanced_extract_and_save_fluids_patch():
        logger.error("Failed to apply fluid extraction patch")
        return False
    
    # Create test file
    logger.info("=== CREATING DIAGNOSTIC TEST FILE ===")
    test_file = create_diagnostic_excel_file()
    
    # Test the enhanced importer
    logger.info("=== TESTING ENHANCED IMPORTER ===")
    try:
        from robust_excel_importer_working import RobustExcelImporter
        
        importer = RobustExcelImporter()
        if not importer.initialize_staging():
            logger.error("Failed to initialize staging database")
            return False
        
        success, results = importer.process_excel_file(test_file)
        
        logger.info(f"=== TEST RESULTS ===")
        logger.info(f"Success: {success}")
        logger.info(f"Results: {results}")
        
        return success
        
    except Exception as e:
        logger.error(f"Error during diagnostic test: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    # Set up detailed logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the diagnostic test
    success = run_diagnostic_test()
    
    if success:
        logger.info("🎉 Diagnostic test completed successfully!")
    else:
        logger.error("❌ Diagnostic test failed - check logs for details") 