#!/usr/bin/env python3
"""
Excel Import Diagnostic Tool for PROJECT-ALPHA
Analyzes Excel import issues including column mapping problems, unnamed columns,
and cross-system compatibility issues. Provides detailed diagnostics and solutions.
"""

import os
import sys
import logging
import pandas as pd
import sqlite3
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('excel_diagnostic')

class ExcelImportDiagnosticTool:
    """Comprehensive diagnostic tool for Excel import issues."""
    
    def __init__(self):
        self.diagnostic_results = {
            'timestamp': datetime.now().isoformat(),
            'file_analysis': {},
            'column_analysis': {},
            'database_analysis': {},
            'system_analysis': {},
            'recommendations': [],
            'issues_found': []
        }
    
    def diagnose_excel_file(self, file_path: str) -> Dict[str, Any]:
        """Perform comprehensive diagnosis of Excel file and import issues."""
        logger.info(f"Starting Excel import diagnosis: {file_path}")
        
        try:
            # File-level analysis
            self._analyze_file_structure(file_path)
            
            # Column-level analysis
            self._analyze_column_mapping(file_path)
            
            # Database analysis
            self._analyze_database_state()
            
            # System analysis
            self._analyze_system_environment()
            
            # Generate recommendations
            self._generate_recommendations()
            
            logger.info("Excel import diagnosis completed")
            return self.diagnostic_results
            
        except Exception as e:
            logger.error(f"Diagnosis failed: {e}")
            self.diagnostic_results['diagnosis_error'] = str(e)
            return self.diagnostic_results
    
    def _analyze_file_structure(self, file_path: str):
        """Analyze Excel file structure and identify issues."""
        logger.info("Analyzing Excel file structure...")
        
        file_analysis = {
            'file_path': file_path,
            'file_exists': os.path.exists(file_path),
            'file_size_mb': 0,
            'sheets': {},
            'total_unnamed_columns': 0,
            'total_empty_columns': 0,
            'structure_issues': []
        }
        
        if not file_analysis['file_exists']:
            file_analysis['structure_issues'].append("File does not exist")
            self.diagnostic_results['file_analysis'] = file_analysis
            return
        
        try:
            # File size
            file_analysis['file_size_mb'] = os.path.getsize(file_path) / (1024 * 1024)
            
            # Read Excel file
            excel_file = pd.ExcelFile(file_path)
            file_analysis['sheet_names'] = excel_file.sheet_names
            file_analysis['sheet_count'] = len(excel_file.sheet_names)
            
            # Analyze each sheet
            for sheet_name in excel_file.sheet_names:
                sheet_analysis = self._analyze_sheet_detailed(excel_file, sheet_name)
                file_analysis['sheets'][sheet_name] = sheet_analysis
                
                # Accumulate totals
                file_analysis['total_unnamed_columns'] += sheet_analysis['unnamed_columns']
                file_analysis['total_empty_columns'] += sheet_analysis['empty_columns']
                
                # Collect issues
                if sheet_analysis['unnamed_columns'] > 0:
                    file_analysis['structure_issues'].append(
                        f"Sheet '{sheet_name}' has {sheet_analysis['unnamed_columns']} unnamed columns"
                    )
                
                if sheet_analysis['empty_columns'] > 0:
                    file_analysis['structure_issues'].append(
                        f"Sheet '{sheet_name}' has {sheet_analysis['empty_columns']} empty columns"
                    )
                
                if sheet_analysis['header_detection_issues']:
                    file_analysis['structure_issues'].extend(sheet_analysis['header_detection_issues'])
            
            logger.info(f"File structure analysis complete: {len(file_analysis['structure_issues'])} issues found")
            
        except Exception as e:
            file_analysis['structure_issues'].append(f"Error reading Excel file: {e}")
            logger.error(f"Error analyzing file structure: {e}")
        
        self.diagnostic_results['file_analysis'] = file_analysis
    
    def _analyze_sheet_detailed(self, excel_file: pd.ExcelFile, sheet_name: str) -> Dict[str, Any]:
        """Perform detailed analysis of individual sheet."""
        sheet_analysis = {
            'sheet_name': sheet_name,
            'total_columns': 0,
            'unnamed_columns': 0,
            'empty_columns': 0,
            'malformed_columns': 0,
            'data_rows': 0,
            'header_detection_issues': [],
            'column_details': [],
            'sample_data': {}
        }
        
        try:
            # Try different header configurations
            successful_reads = []
            
            for header_config in [None, [0], [0, 1], [0, 1, 2]]:
                try:
                    df = pd.read_excel(excel_file, sheet_name=sheet_name, header=header_config, nrows=10)
                    successful_reads.append({
                        'header_config': str(header_config),
                        'columns_found': len(df.columns),
                        'rows_found': len(df),
                        'column_sample': df.columns.tolist()[:5]
                    })
                except Exception as e:
                    sheet_analysis['header_detection_issues'].append(
                        f"Header config {header_config} failed: {e}"
                    )
            
            if not successful_reads:
                sheet_analysis['header_detection_issues'].append("No header configuration worked")
                return sheet_analysis
            
            # Use the most successful read (most columns found)
            best_read = max(successful_reads, key=lambda x: x['columns_found'])
            header_config = eval(best_read['header_config']) if best_read['header_config'] != 'None' else None
            
            # Read full sheet with best configuration
            df = pd.read_excel(excel_file, sheet_name=sheet_name, header=header_config)
            
            sheet_analysis['total_columns'] = len(df.columns)
            sheet_analysis['data_rows'] = len(df)
            sheet_analysis['successful_header_configs'] = successful_reads
            
            # Analyze columns in detail
            for i, col in enumerate(df.columns):
                col_str = str(col)
                
                col_detail = {
                    'index': i,
                    'name': col_str,
                    'is_unnamed': 'Unnamed:' in col_str or col_str.startswith('Unnamed'),
                    'is_empty': col_str.strip() == '' or col_str.lower() in ['nan', 'none', 'null'],
                    'is_numeric_only': col_str.replace('.', '').replace('-', '').isdigit(),
                    'length': len(col_str),
                    'has_data': not df[col].isna().all(),
                    'unique_values': df[col].nunique() if not df[col].isna().all() else 0
                }
                
                sheet_analysis['column_details'].append(col_detail)
                
                # Count issues
                if col_detail['is_unnamed']:
                    sheet_analysis['unnamed_columns'] += 1
                
                if col_detail['is_empty']:
                    sheet_analysis['empty_columns'] += 1
                
                if col_detail['is_numeric_only'] or len(col_str) < 2:
                    sheet_analysis['malformed_columns'] += 1
            
            # Sample data for debugging
            if len(df) > 0:
                sheet_analysis['sample_data'] = {
                    'first_row': df.iloc[0].to_dict(),
                    'column_types': df.dtypes.to_dict()
                }
            
        except Exception as e:
            sheet_analysis['header_detection_issues'].append(f"Sheet analysis failed: {e}")
            logger.error(f"Error analyzing sheet {sheet_name}: {e}")
        
        return sheet_analysis
    
    def _analyze_column_mapping(self, file_path: str):
        """Analyze column mapping effectiveness."""
        logger.info("Analyzing column mapping...")
        
        column_analysis = {
            'mapping_success': False,
            'primary_fields_mapped': {},
            'fluid_fields_mapped': {},
            'maintenance_fields_mapped': {},
            'mapping_confidence': 0.0,
            'mapping_issues': []
        }
        
        try:
            # Use enhanced column mapping
            from enhanced_column_mapping_importer import EnhancedColumnMappingImporter
            importer = EnhancedColumnMappingImporter()
            
            # Analyze structure
            structure_analysis = importer.analyze_excel_structure(file_path)
            column_analysis['structure_analysis'] = structure_analysis
            
            # Test column mapping on each sheet
            excel_file = pd.ExcelFile(file_path)
            
            for sheet_name in excel_file.sheet_names:
                try:
                    # Read sheet
                    df = pd.read_excel(excel_file, sheet_name=sheet_name, header=[0, 1])
                    
                    # Flatten columns if multi-level
                    if isinstance(df.columns, pd.MultiIndex):
                        new_columns = []
                        for col in df.columns:
                            parts = [str(c).strip() for c in col if str(c).strip() and str(c) != 'nan']
                            if len(parts) > 1:
                                new_columns.append(' -> '.join(parts))
                            elif len(parts) == 1:
                                new_columns.append(parts[0])
                            else:
                                new_columns.append(f"Unnamed_{len(new_columns)}")
                        df.columns = new_columns
                    
                    # Test mapping
                    mapping_result = importer.enhanced_column_mapping(df.columns.tolist())
                    
                    column_analysis[f'sheet_{sheet_name}_mapping'] = {
                        'primary_fields': mapping_result['primary_fields'],
                        'fluid_fields': mapping_result['fluid_fields'],
                        'maintenance_fields': mapping_result['maintenance_fields'],
                        'unnamed_columns': mapping_result['unnamed_columns'],
                        'ignored_columns': mapping_result['ignored_columns'],
                        'confidence': mapping_result['mapping_confidence']
                    }
                    
                    # Check for issues
                    if mapping_result['mapping_confidence'] < 50:
                        column_analysis['mapping_issues'].append(
                            f"Low mapping confidence for sheet {sheet_name}: {mapping_result['mapping_confidence']:.1f}%"
                        )
                    
                    if len(mapping_result['unnamed_columns']) > 5:
                        column_analysis['mapping_issues'].append(
                            f"Many unnamed columns in sheet {sheet_name}: {len(mapping_result['unnamed_columns'])}"
                        )
                    
                except Exception as e:
                    column_analysis['mapping_issues'].append(f"Mapping failed for sheet {sheet_name}: {e}")
            
            column_analysis['mapping_success'] = len(column_analysis['mapping_issues']) == 0
            
        except Exception as e:
            column_analysis['mapping_issues'].append(f"Column mapping analysis failed: {e}")
            logger.error(f"Error analyzing column mapping: {e}")
        
        self.diagnostic_results['column_analysis'] = column_analysis
    
    def _analyze_database_state(self):
        """Analyze current database state and import results."""
        logger.info("Analyzing database state...")
        
        database_analysis = {
            'database_accessible': False,
            'equipment_count': 0,
            'fluids_count': 0,
            'maintenance_count': 0,
            'recent_imports': [],
            'database_issues': []
        }
        
        try:
            import config
            
            if not os.path.exists(config.DB_PATH):
                database_analysis['database_issues'].append("Database file does not exist")
                self.diagnostic_results['database_analysis'] = database_analysis
                return
            
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            database_analysis['database_accessible'] = True
            
            # Count records
            cursor.execute("SELECT COUNT(*) FROM equipment WHERE is_active = 1")
            database_analysis['equipment_count'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM fluids")
            database_analysis['fluids_count'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM maintenance")
            database_analysis['maintenance_count'] = cursor.fetchone()[0]
            
            # Check for recent imports (if import log exists)
            try:
                cursor.execute("""
                    SELECT equipment_id, make_and_type, ba_number, remarks 
                    FROM equipment 
                    WHERE remarks LIKE '%Imported from%' 
                    ORDER BY equipment_id DESC 
                    LIMIT 10
                """)
                recent_equipment = cursor.fetchall()
                database_analysis['recent_imports'] = [
                    {'equipment_id': row[0], 'make_and_type': row[1], 'ba_number': row[2], 'remarks': row[3]}
                    for row in recent_equipment
                ]
            except Exception:
                pass
            
            # Check for alerts/issues
            cursor.execute("""
                SELECT COUNT(*) as overdue_count
                FROM overhauls o
                JOIN equipment e ON o.equipment_id = e.equipment_id
                WHERE o.status = 'Overdue' AND e.is_active = 1
            """)
            overdue_result = cursor.fetchone()
            database_analysis['overdue_overhauls'] = overdue_result[0] if overdue_result else 0
            
            conn.close()
            
        except Exception as e:
            database_analysis['database_issues'].append(f"Database analysis failed: {e}")
            logger.error(f"Error analyzing database: {e}")
        
        self.diagnostic_results['database_analysis'] = database_analysis
    
    def _analyze_system_environment(self):
        """Analyze system environment for compatibility issues."""
        logger.info("Analyzing system environment...")
        
        system_analysis = {
            'python_version': sys.version,
            'platform': sys.platform,
            'libraries_available': {},
            'memory_info': {},
            'user_environment': {},
            'compatibility_issues': []
        }
        
        try:
            # Check library availability
            required_libs = ['pandas', 'openpyxl', 'xlrd', 'sqlite3']
            for lib in required_libs:
                try:
                    __import__(lib)
                    system_analysis['libraries_available'][lib] = True
                except ImportError:
                    system_analysis['libraries_available'][lib] = False
                    system_analysis['compatibility_issues'].append(f"Missing library: {lib}")
            
            # Memory info
            try:
                import psutil
                memory = psutil.virtual_memory()
                system_analysis['memory_info'] = {
                    'total_gb': memory.total / (1024**3),
                    'available_gb': memory.available / (1024**3),
                    'percent_used': memory.percent
                }
            except ImportError:
                system_analysis['memory_info'] = {'status': 'psutil not available'}
            
            # User environment
            system_analysis['user_environment'] = {
                'username': os.environ.get('USERNAME', 'unknown'),
                'userprofile': os.environ.get('USERPROFILE', 'unknown'),
                'temp_dir': os.environ.get('TEMP', 'unknown'),
                'current_dir': os.getcwd()
            }
            
            # Check for cross-system issues
            if 'Swati Negi' in system_analysis['user_environment']['userprofile']:
                system_analysis['compatibility_issues'].append(
                    "Different user environment detected - may indicate cross-system deployment"
                )
            
        except Exception as e:
            system_analysis['compatibility_issues'].append(f"System analysis failed: {e}")
            logger.error(f"Error analyzing system environment: {e}")
        
        self.diagnostic_results['system_analysis'] = system_analysis
    
    def _generate_recommendations(self):
        """Generate recommendations based on analysis results."""
        recommendations = []
        issues_found = []
        
        # File structure recommendations
        file_analysis = self.diagnostic_results.get('file_analysis', {})
        if file_analysis.get('total_unnamed_columns', 0) > 0:
            issues_found.append(f"Found {file_analysis['total_unnamed_columns']} unnamed columns")
            recommendations.append("Clean Excel file by removing empty columns and fixing merged headers")
            recommendations.append("Use enhanced column mapping importer to handle unnamed columns")
        
        # Column mapping recommendations
        column_analysis = self.diagnostic_results.get('column_analysis', {})
        if not column_analysis.get('mapping_success', True):
            issues_found.append("Column mapping issues detected")
            recommendations.append("Use enhanced_column_mapping_importer.py for better column detection")
            recommendations.append("Check Excel file for proper header structure")
        
        # Database recommendations
        database_analysis = self.diagnostic_results.get('database_analysis', {})
        if database_analysis.get('equipment_count', 0) == 0:
            issues_found.append("No equipment found in database")
            recommendations.append("Verify Excel import completed successfully")
            recommendations.append("Check import logs for errors")
        
        # System recommendations
        system_analysis = self.diagnostic_results.get('system_analysis', {})
        if system_analysis.get('compatibility_issues'):
            issues_found.extend(system_analysis['compatibility_issues'])
            recommendations.append("Use cross_system_excel_importer.py for better compatibility")
            recommendations.append("Install missing libraries if any")
        
        self.diagnostic_results['recommendations'] = recommendations
        self.diagnostic_results['issues_found'] = issues_found
    
    def generate_diagnostic_report(self) -> str:
        """Generate human-readable diagnostic report."""
        report_lines = [
            "=" * 80,
            "PROJECT-ALPHA EXCEL IMPORT DIAGNOSTIC REPORT",
            "=" * 80,
            f"Generated: {self.diagnostic_results['timestamp']}",
            ""
        ]
        
        # Issues summary
        issues = self.diagnostic_results.get('issues_found', [])
        if issues:
            report_lines.extend([
                "🚨 ISSUES FOUND:",
                ""
            ])
            for issue in issues:
                report_lines.append(f"  ❌ {issue}")
            report_lines.append("")
        
        # File analysis
        file_analysis = self.diagnostic_results.get('file_analysis', {})
        if file_analysis:
            report_lines.extend([
                "📁 FILE ANALYSIS:",
                f"  File: {file_analysis.get('file_path', 'Unknown')}",
                f"  Size: {file_analysis.get('file_size_mb', 0):.1f} MB",
                f"  Sheets: {file_analysis.get('sheet_count', 0)}",
                f"  Unnamed columns: {file_analysis.get('total_unnamed_columns', 0)}",
                f"  Empty columns: {file_analysis.get('total_empty_columns', 0)}",
                ""
            ])
        
        # Database analysis
        database_analysis = self.diagnostic_results.get('database_analysis', {})
        if database_analysis:
            report_lines.extend([
                "🗄️  DATABASE ANALYSIS:",
                f"  Equipment count: {database_analysis.get('equipment_count', 0)}",
                f"  Fluids count: {database_analysis.get('fluids_count', 0)}",
                f"  Maintenance count: {database_analysis.get('maintenance_count', 0)}",
                f"  Overdue overhauls: {database_analysis.get('overdue_overhauls', 0)}",
                ""
            ])
        
        # Recommendations
        recommendations = self.diagnostic_results.get('recommendations', [])
        if recommendations:
            report_lines.extend([
                "💡 RECOMMENDATIONS:",
                ""
            ])
            for rec in recommendations:
                report_lines.append(f"  ✅ {rec}")
            report_lines.append("")
        
        return "\n".join(report_lines)

def diagnose_excel_import_issue(file_path: str, output_file: Optional[str] = None) -> Dict[str, Any]:
    """
    Diagnose Excel import issues and generate comprehensive report.
    
    Args:
        file_path (str): Path to Excel file to diagnose
        output_file (str, optional): Path to save diagnostic report
        
    Returns:
        dict: Diagnostic results
    """
    diagnostic_tool = ExcelImportDiagnosticTool()
    results = diagnostic_tool.diagnose_excel_file(file_path)
    
    # Generate and save report
    report = diagnostic_tool.generate_diagnostic_report()
    
    if output_file:
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"Diagnostic report saved: {output_file}")
        except Exception as e:
            logger.error(f"Failed to save report: {e}")
    
    print(report)
    return results

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='PROJECT-ALPHA Excel Import Diagnostic Tool')
    parser.add_argument('excel_file', help='Path to Excel file to diagnose')
    parser.add_argument('--output', '-o', help='Output file for diagnostic report')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.excel_file):
        print(f"❌ Excel file not found: {args.excel_file}")
        sys.exit(1)
    
    print("🔍 Starting Excel import diagnosis...")
    results = diagnose_excel_import_issue(args.excel_file, args.output)
    
    # Exit with appropriate code
    issues_count = len(results.get('issues_found', []))
    if issues_count == 0:
        print("\n✅ No major issues found")
        sys.exit(0)
    else:
        print(f"\n⚠️  {issues_count} issues found - see report for details")
        sys.exit(1)
