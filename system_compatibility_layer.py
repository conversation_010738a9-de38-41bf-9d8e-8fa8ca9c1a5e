#!/usr/bin/env python3
"""
System Compatibility Layer for PROJECT-ALPHA
Provides robust dependency management, error handling, and fallback mechanisms
for different system configurations and deployment environments.
"""

import os
import sys
import logging
import importlib
import subprocess
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable, Tuple
import traceback

logger = logging.getLogger('system_compatibility')

class DependencyManager:
    """Manages library dependencies with fallback mechanisms."""
    
    def __init__(self):
        self.required_libraries = {
            'pandas': {
                'min_version': '1.3.0',
                'max_version': '2.1.0',
                'fallback': None,
                'critical': True
            },
            'openpyxl': {
                'min_version': '3.0.0',
                'max_version': '3.2.0',
                'fallback': 'xlrd',
                'critical': True
            },
            'xlrd': {
                'min_version': '2.0.0',
                'max_version': '2.1.0',
                'fallback': None,
                'critical': False
            },
            'psutil': {
                'min_version': '5.8.0',
                'max_version': '5.10.0',
                'fallback': 'manual_memory_check',
                'critical': False
            },
            'dateutil': {
                'min_version': '2.8.0',
                'max_version': '2.9.0',
                'fallback': 'datetime',
                'critical': False
            }
        }
        
        self.available_libraries = {}
        self.fallback_implementations = {}
        self._check_all_dependencies()
    
    def _check_all_dependencies(self):
        """Check availability and versions of all dependencies."""
        for lib_name, lib_info in self.required_libraries.items():
            self.available_libraries[lib_name] = self._check_library(lib_name, lib_info)
    
    def _check_library(self, lib_name: str, lib_info: Dict[str, Any]) -> Dict[str, Any]:
        """Check individual library availability and version."""
        try:
            module = importlib.import_module(lib_name)
            version = getattr(module, '__version__', 'unknown')
            
            return {
                'available': True,
                'version': version,
                'compatible': self._check_version_compatibility(version, lib_info),
                'module': module
            }
        except ImportError:
            return {
                'available': False,
                'version': None,
                'compatible': False,
                'module': None
            }
    
    def _check_version_compatibility(self, version: str, lib_info: Dict[str, Any]) -> bool:
        """Check if library version is compatible."""
        if version == 'unknown':
            return True  # Assume compatible if version unknown
        
        try:
            from packaging import version as pkg_version
            v = pkg_version.parse(version)
            min_v = pkg_version.parse(lib_info['min_version'])
            max_v = pkg_version.parse(lib_info['max_version'])
            return min_v <= v < max_v
        except ImportError:
            # Fallback version check without packaging library
            return self._simple_version_check(version, lib_info)
    
    def _simple_version_check(self, version: str, lib_info: Dict[str, Any]) -> bool:
        """Simple version check without packaging library."""
        try:
            v_parts = [int(x) for x in version.split('.')]
            min_parts = [int(x) for x in lib_info['min_version'].split('.')]
            max_parts = [int(x) for x in lib_info['max_version'].split('.')]
            
            return min_parts <= v_parts < max_parts
        except (ValueError, IndexError):
            return True  # Assume compatible if parsing fails
    
    def get_dependency_status(self) -> Dict[str, Any]:
        """Get comprehensive dependency status report."""
        critical_missing = []
        warnings = []
        
        for lib_name, lib_info in self.required_libraries.items():
            status = self.available_libraries[lib_name]
            
            if not status['available']:
                if lib_info['critical']:
                    critical_missing.append(lib_name)
                else:
                    warnings.append(f"{lib_name} not available (using fallback)")
            elif not status['compatible']:
                warnings.append(f"{lib_name} version {status['version']} may be incompatible")
        
        return {
            'all_critical_available': len(critical_missing) == 0,
            'critical_missing': critical_missing,
            'warnings': warnings,
            'available_libraries': self.available_libraries
        }
    
    def get_safe_import(self, lib_name: str) -> Optional[Any]:
        """Get library with fallback if not available."""
        status = self.available_libraries.get(lib_name, {})
        
        if status.get('available'):
            return status['module']
        
        # Try fallback
        fallback = self.required_libraries[lib_name].get('fallback')
        if fallback and fallback in self.fallback_implementations:
            logger.warning(f"Using fallback for {lib_name}: {fallback}")
            return self.fallback_implementations[fallback]
        
        return None

class ErrorHandlingManager:
    """Manages error handling with context-aware recovery strategies."""
    
    def __init__(self):
        self.error_handlers = {}
        self.recovery_strategies = {}
        self.error_history = []
        
        # Register default error handlers
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """Register default error handlers for common issues."""
        
        # Memory errors
        self.register_error_handler(
            MemoryError,
            self._handle_memory_error,
            "Reduce memory usage and try chunked processing"
        )
        
        # File access errors
        self.register_error_handler(
            PermissionError,
            self._handle_permission_error,
            "Check file permissions and user access rights"
        )
        
        # Import errors
        self.register_error_handler(
            ImportError,
            self._handle_import_error,
            "Check library dependencies and installation"
        )
        
        # Excel file errors
        self.register_error_handler(
            Exception,  # Catch-all for Excel-specific errors
            self._handle_excel_error,
            "Try alternative Excel reading methods"
        )
    
    def register_error_handler(self, error_type: type, handler: Callable, description: str):
        """Register custom error handler."""
        self.error_handlers[error_type] = {
            'handler': handler,
            'description': description
        }
    
    def handle_error(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle error with appropriate recovery strategy."""
        error_type = type(error)
        error_info = {
            'error_type': error_type.__name__,
            'error_message': str(error),
            'context': context,
            'timestamp': str(datetime.now()),
            'traceback': traceback.format_exc()
        }
        
        self.error_history.append(error_info)
        logger.error(f"Handling error: {error_type.__name__}: {error}")
        
        # Find appropriate handler
        handler_info = None
        for registered_type, info in self.error_handlers.items():
            if isinstance(error, registered_type):
                handler_info = info
                break
        
        if handler_info:
            try:
                recovery_result = handler_info['handler'](error, context)
                return {
                    'handled': True,
                    'recovery_attempted': True,
                    'recovery_result': recovery_result,
                    'recommendation': handler_info['description']
                }
            except Exception as recovery_error:
                logger.error(f"Error handler failed: {recovery_error}")
                return {
                    'handled': False,
                    'recovery_attempted': True,
                    'recovery_error': str(recovery_error),
                    'recommendation': handler_info['description']
                }
        
        return {
            'handled': False,
            'recovery_attempted': False,
            'recommendation': "No specific handler available - check logs for details"
        }
    
    def _handle_memory_error(self, error: MemoryError, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle memory-related errors."""
        import gc
        gc.collect()  # Force garbage collection
        
        return {
            'action': 'memory_cleanup',
            'suggestion': 'switch_to_chunked_processing',
            'memory_freed': True
        }
    
    def _handle_permission_error(self, error: PermissionError, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle file permission errors."""
        file_path = context.get('file_path')
        
        if file_path:
            # Try copying to temp directory
            try:
                temp_dir = tempfile.gettempdir()
                temp_file = Path(temp_dir) / Path(file_path).name
                shutil.copy2(file_path, temp_file)
                
                return {
                    'action': 'copied_to_temp',
                    'temp_file': str(temp_file),
                    'success': True
                }
            except Exception as copy_error:
                return {
                    'action': 'copy_failed',
                    'error': str(copy_error),
                    'success': False
                }
        
        return {'action': 'no_recovery_possible', 'success': False}
    
    def _handle_import_error(self, error: ImportError, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle library import errors."""
        missing_module = str(error).split("'")[1] if "'" in str(error) else "unknown"
        
        return {
            'action': 'import_failed',
            'missing_module': missing_module,
            'suggestion': f'Install {missing_module} or use fallback implementation'
        }
    
    def _handle_excel_error(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle Excel file processing errors."""
        error_msg = str(error).lower()
        
        if 'corrupt' in error_msg or 'damaged' in error_msg:
            return {
                'action': 'file_corruption_detected',
                'suggestion': 'Try opening file in Excel and saving as new file'
            }
        elif 'password' in error_msg or 'encrypted' in error_msg:
            return {
                'action': 'password_protected',
                'suggestion': 'Remove password protection from Excel file'
            }
        elif 'format' in error_msg:
            return {
                'action': 'format_issue',
                'suggestion': 'Try converting file to .xlsx format'
            }
        
        return {
            'action': 'unknown_excel_error',
            'suggestion': 'Try alternative Excel reading method'
        }

class FallbackImplementations:
    """Provides fallback implementations for missing libraries."""
    
    @staticmethod
    def manual_memory_check() -> Dict[str, Any]:
        """Manual memory check when psutil is not available."""
        try:
            import ctypes
            if sys.platform == 'win32':
                # Windows memory check
                kernel32 = ctypes.windll.kernel32
                c_ulong = ctypes.c_ulong
                class MEMORYSTATUSEX(ctypes.Structure):
                    _fields_ = [
                        ('dwLength', c_ulong),
                        ('dwMemoryLoad', c_ulong),
                        ('ullTotalPhys', ctypes.c_ulonglong),
                        ('ullAvailPhys', ctypes.c_ulonglong),
                        ('ullTotalPageFile', ctypes.c_ulonglong),
                        ('ullAvailPageFile', ctypes.c_ulonglong),
                        ('ullTotalVirtual', ctypes.c_ulonglong),
                        ('ullAvailVirtual', ctypes.c_ulonglong),
                        ('sullAvailExtendedVirtual', ctypes.c_ulonglong),
                    ]
                
                memoryStatus = MEMORYSTATUSEX()
                memoryStatus.dwLength = ctypes.sizeof(MEMORYSTATUSEX)
                kernel32.GlobalMemoryStatusEx(ctypes.byref(memoryStatus))
                
                return {
                    'total_gb': memoryStatus.ullTotalPhys / (1024**3),
                    'available_gb': memoryStatus.ullAvailPhys / (1024**3),
                    'usage_percent': memoryStatus.dwMemoryLoad
                }
        except Exception:
            pass
        
        # Fallback estimate
        return {
            'total_gb': 4.0,  # Conservative estimate
            'available_gb': 2.0,
            'usage_percent': 50
        }
    
    @staticmethod
    def simple_date_parser(date_str: str) -> Optional[str]:
        """Simple date parser when dateutil is not available."""
        try:
            from datetime import datetime
            
            # Try common formats
            formats = [
                '%Y-%m-%d',
                '%d/%m/%Y',
                '%m/%d/%Y',
                '%d-%m-%Y',
                '%Y/%m/%d'
            ]
            
            for fmt in formats:
                try:
                    dt = datetime.strptime(str(date_str), fmt)
                    return dt.strftime('%Y-%m-%d')
                except ValueError:
                    continue
            
            return None
        except Exception:
            return None

class SystemCompatibilityLayer:
    """Main compatibility layer that coordinates all components."""
    
    def __init__(self):
        self.dependency_manager = DependencyManager()
        self.error_manager = ErrorHandlingManager()
        self.fallback_implementations = FallbackImplementations()
        
        # Register fallback implementations
        self.dependency_manager.fallback_implementations.update({
            'manual_memory_check': self.fallback_implementations.manual_memory_check,
            'simple_date_parser': self.fallback_implementations.simple_date_parser
        })
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system compatibility status."""
        dependency_status = self.dependency_manager.get_dependency_status()
        
        return {
            'compatible': dependency_status['all_critical_available'],
            'dependency_status': dependency_status,
            'error_history': len(self.error_manager.error_history),
            'recommendations': self._generate_system_recommendations(dependency_status)
        }
    
    def _generate_system_recommendations(self, dependency_status: Dict[str, Any]) -> List[str]:
        """Generate system-specific recommendations."""
        recommendations = []
        
        if dependency_status['critical_missing']:
            recommendations.append(
                f"Install critical libraries: {', '.join(dependency_status['critical_missing'])}"
            )
        
        if dependency_status['warnings']:
            recommendations.extend(dependency_status['warnings'])
        
        if not recommendations:
            recommendations.append("System appears compatible for Excel import operations")
        
        return recommendations
    
    def safe_import(self, lib_name: str) -> Optional[Any]:
        """Safely import library with fallback."""
        return self.dependency_manager.get_safe_import(lib_name)
    
    def handle_error(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle error with recovery strategies."""
        return self.error_manager.handle_error(error, context)
    
    def validate_excel_import_readiness(self) -> Dict[str, Any]:
        """Validate system readiness for Excel import operations."""
        status = self.get_system_status()
        
        # Check critical components
        pandas = self.safe_import('pandas')
        excel_reader = self.safe_import('openpyxl') or self.safe_import('xlrd')
        
        readiness = {
            'ready': pandas is not None and excel_reader is not None,
            'pandas_available': pandas is not None,
            'excel_reader_available': excel_reader is not None,
            'system_status': status,
            'recommended_strategy': self._recommend_import_strategy(status)
        }
        
        return readiness
    
    def _recommend_import_strategy(self, status: Dict[str, Any]) -> str:
        """Recommend import strategy based on system status."""
        if not status['compatible']:
            return 'fallback_only'
        
        dependency_status = status['dependency_status']
        
        if 'psutil' not in dependency_status['available_libraries']:
            return 'conservative_memory'
        
        return 'standard'

# Global compatibility layer instance
compatibility_layer = SystemCompatibilityLayer()

# Convenience functions for easy integration
def get_system_compatibility_status() -> Dict[str, Any]:
    """Get system compatibility status."""
    return compatibility_layer.get_system_status()

def safe_import_library(lib_name: str) -> Optional[Any]:
    """Safely import library with fallback mechanisms."""
    return compatibility_layer.safe_import(lib_name)

def handle_system_error(error: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
    """Handle system error with recovery strategies."""
    return compatibility_layer.handle_error(error, context or {})

def validate_excel_import_environment() -> Dict[str, Any]:
    """Validate environment readiness for Excel import."""
    return compatibility_layer.validate_excel_import_readiness()

if __name__ == "__main__":
    # System compatibility check
    print("=" * 60)
    print("PROJECT-ALPHA SYSTEM COMPATIBILITY CHECK")
    print("=" * 60)
    
    status = get_system_compatibility_status()
    print(f"System Compatible: {status['compatible']}")
    print(f"Error History: {status['error_history']} errors")
    
    print("\nRecommendations:")
    for rec in status['recommendations']:
        print(f"- {rec}")
    
    print("\nExcel Import Readiness:")
    readiness = validate_excel_import_environment()
    print(f"Ready: {readiness['ready']}")
    print(f"Recommended Strategy: {readiness['recommended_strategy']}")
