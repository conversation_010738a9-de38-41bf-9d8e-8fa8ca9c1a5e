import csv
import os
import re

# Input and output paths
input_csv = r"c:\Users\<USER>\OneDrive\Documents\New folder\combined.csv"
output_csv = r"c:\Users\<USER>\OneDrive\Documents\New folder\phasing_table.csv"

def simplify_column_name(col):
    """Auto-suggest a standard name by cleaning up and normalizing the column name."""
    # Remove units, extra info after |, and non-alphanum (keep underscores)
    col = col.replace('\n', ' ').replace('\r', ' ').strip()
    col = re.sub(r'\s+', ' ', col)
    # Take only the first part before | if present
    col = col.split('|')[0].strip()
    # Remove special characters
    col = re.sub(r'[^A-Za-z0-9 ]+', '', col)
    # Replace spaces with underscores
    col = col.replace(' ', '_')
    # Uppercase
    return col.upper()

def main():
    if not os.path.exists(input_csv):
        print(f"Input CSV not found: {input_csv}")
        return
    with open(input_csv, newline='', encoding='utf-8') as f:
        reader = csv.reader(f)
        headers = next(reader)
    
    unique_headers = []
    seen = set()
    for h in headers:
        if h not in seen:
            unique_headers.append(h)
            seen.add(h)

    with open(output_csv, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['Raw Column Name', 'Suggested Standard Name', 'Notes/Comments'])
        for raw in unique_headers:
            std = simplify_column_name(raw)
            writer.writerow([raw, std, ''])
    print(f"Phasing table written to: {output_csv}")

if __name__ == '__main__':
    main()
