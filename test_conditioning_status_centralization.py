#!/usr/bin/env python3
"""
Test script to validate the centralized conditioning status logic.

This script tests the new centralized conditioning status service to ensure
that all status calculations work correctly and consistently across components.
"""

import sys
import os
from datetime import datetime, date, timedelta

# Add project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from conditioning_status_service import conditioning_status_service
from conditioning_status_enums import (
    TyreRotationStatus, TyreConditionStatus, BatteryStatus,
    ConditioningThresholds, StatusColors
)


def test_tyre_rotation_status():
    """Test tyre rotation status calculations."""
    print("Testing Tyre Rotation Status Calculations...")
    
    test_cases = [
        # (current_meterage, rotation_interval, expected_status)
        (5000, 5000, TyreRotationStatus.DUE_FOR_ROTATION),  # Exactly at interval
        (6000, 5000, TyreRotationStatus.NORMAL),  # 1000km into next cycle (20% of next interval)
        (4500, 5000, TyreRotationStatus.ROTATION_SOON),  # 90% of interval
        (4000, 5000, TyreRotationStatus.ROTATION_SOON),  # 80% of interval
        (3000, 5000, TyreRotationStatus.NORMAL),  # 60% of interval
        (0, 5000, TyreRotationStatus.NORMAL),  # No meterage
        (5000, 0, TyreRotationStatus.UNKNOWN),  # No interval
        (11000, 5000, TyreRotationStatus.NORMAL),  # 1000km into 3rd cycle (20% of next interval)
        (10000, 5000, TyreRotationStatus.DUE_FOR_ROTATION),  # Exactly at 2nd rotation
        (14000, 5000, TyreRotationStatus.ROTATION_SOON),  # 4000km into 3rd cycle (80% of interval)
    ]
    
    passed = 0
    failed = 0
    
    for current_meterage, rotation_interval, expected_status in test_cases:
        result = conditioning_status_service.calculate_tyre_rotation_status(
            current_meterage, None, rotation_interval
        )
        
        if result == expected_status:
            print(f"✓ PASS: {current_meterage}km / {rotation_interval}km = {result}")
            passed += 1
        else:
            print(f"✗ FAIL: {current_meterage}km / {rotation_interval}km = {result}, expected {expected_status}")
            failed += 1
    
    print(f"Tyre Rotation Tests: {passed} passed, {failed} failed\n")
    return failed == 0


def test_tyre_condition_status():
    """Test tyre condition status calculations."""
    print("Testing Tyre Condition Status Calculations...")
    
    test_cases = [
        # (current_meterage, vintage_years, condition_kms, condition_years, expected_status)
        (50000, 3, 50000, 5, TyreConditionStatus.DUE_FOR_INSPECTION),  # KM threshold reached
        (40000, 5, 50000, 5, TyreConditionStatus.DUE_FOR_INSPECTION),  # Year threshold reached
        (45000, 4, 50000, 5, TyreConditionStatus.INSPECTION_SOON),  # 90% of KM threshold
        (40000, 4, 50000, 5, TyreConditionStatus.INSPECTION_SOON),  # 80% of year threshold
        (30000, 2, 50000, 5, TyreConditionStatus.NORMAL),  # Well within limits
        (0, 0, 0, 0, TyreConditionStatus.UNKNOWN),  # No criteria set
    ]
    
    passed = 0
    failed = 0
    
    for current_meterage, vintage_years, condition_kms, condition_years, expected_status in test_cases:
        result = conditioning_status_service.calculate_tyre_condition_status(
            current_meterage, vintage_years, condition_kms, condition_years
        )
        
        if result == expected_status:
            print(f"✓ PASS: {current_meterage}km/{vintage_years}yr vs {condition_kms}km/{condition_years}yr = {result}")
            passed += 1
        else:
            print(f"✗ FAIL: {current_meterage}km/{vintage_years}yr vs {condition_kms}km/{condition_years}yr = {result}, expected {expected_status}")
            failed += 1
    
    print(f"Tyre Condition Tests: {passed} passed, {failed} failed\n")
    return failed == 0


def test_battery_status():
    """Test battery status calculations."""
    print("Testing Battery Status Calculations...")
    
    today = datetime.now()
    
    test_cases = [
        # (done_date, life_months, expected_status)
        ((today - timedelta(days=750)).strftime('%Y-%m-%d'), 24, BatteryStatus.OVERDUE),  # 25 months old (overdue)
        ((today - timedelta(days=725)).strftime('%Y-%m-%d'), 24, BatteryStatus.DUE_FOR_REPLACEMENT),  # ~24 months old (≤7 days)
        ((today - timedelta(days=700)).strftime('%Y-%m-%d'), 24, BatteryStatus.REPLACEMENT_SOON),  # ~23 months old (≤30 days)
        ((today - timedelta(days=680)).strftime('%Y-%m-%d'), 24, BatteryStatus.NORMAL),  # ~22.5 months old (>30 days)
        ((today - timedelta(days=365)).strftime('%Y-%m-%d'), 24, BatteryStatus.NORMAL),  # 12 months old
        (None, 24, BatteryStatus.UNKNOWN),  # No done date
    ]
    
    passed = 0
    failed = 0
    
    for done_date, life_months, expected_status in test_cases:
        result = conditioning_status_service.calculate_battery_status(done_date, life_months)

        # Debug: calculate days until due for failed cases
        if result != expected_status and done_date:
            try:
                done_dt = datetime.strptime(done_date, '%Y-%m-%d')
                due_dt = done_dt + timedelta(days=life_months * 30.44)
                days_until_due = (due_dt - today).days
                print(f"✗ FAIL: {done_date} ({life_months}mo) = {result}, expected {expected_status} (days_until_due: {days_until_due})")
            except:
                print(f"✗ FAIL: {done_date} ({life_months}mo) = {result}, expected {expected_status}")
        elif result == expected_status:
            print(f"✓ PASS: {done_date} ({life_months}mo) = {result}")
            passed += 1
        else:
            print(f"✗ FAIL: {done_date} ({life_months}mo) = {result}, expected {expected_status}")

        if result != expected_status:
            failed += 1
        else:
            passed += 1
    
    print(f"Battery Status Tests: {passed} passed, {failed} failed\n")
    return failed == 0


def test_status_colors():
    """Test status color mappings."""
    print("Testing Status Color Mappings...")
    
    test_cases = [
        (TyreRotationStatus.NORMAL, StatusColors.NORMAL_COLOR),
        (TyreRotationStatus.ROTATION_SOON, StatusColors.WARNING_COLOR),
        (TyreRotationStatus.DUE_FOR_ROTATION, StatusColors.CRITICAL_COLOR),
        (TyreRotationStatus.OVERDUE, StatusColors.OVERDUE_COLOR),
        (TyreRotationStatus.UNKNOWN, StatusColors.UNKNOWN_COLOR),
        ("normal", StatusColors.NORMAL_COLOR),
        ("warning", StatusColors.WARNING_COLOR),
        ("critical", StatusColors.CRITICAL_COLOR),
    ]
    
    passed = 0
    failed = 0
    
    for status, expected_color in test_cases:
        result = conditioning_status_service.get_status_color(status)
        
        if result == expected_color:
            print(f"✓ PASS: {status} = {result}")
            passed += 1
        else:
            print(f"✗ FAIL: {status} = {result}, expected {expected_color}")
            failed += 1
    
    print(f"Status Color Tests: {passed} passed, {failed} failed\n")
    return failed == 0


def test_dashboard_alert_status():
    """Test dashboard alert status calculations."""
    print("Testing Dashboard Alert Status Calculations...")
    
    test_cases = [
        # (current_meterage, rotation_interval, expected_should_alert, expected_level)
        (5000, 5000, True, "critical"),  # Exactly at interval
        (6000, 5000, False, "normal"),  # 1000km into next cycle (20% of next interval)
        (4500, 5000, True, "warning"),  # 90% of interval (dashboard threshold)
        (4000, 5000, False, "normal"),  # 80% of interval (below dashboard threshold)
        (3000, 5000, False, "normal"),  # Well within interval
        (10000, 5000, True, "critical"),  # Exactly at 2nd rotation
        (14500, 5000, True, "warning"),  # 90% of 3rd interval
    ]
    
    passed = 0
    failed = 0
    
    for current_meterage, rotation_interval, expected_should_alert, expected_level in test_cases:
        should_alert, alert_level = conditioning_status_service.get_dashboard_alert_status(
            current_meterage, rotation_interval, use_dashboard_thresholds=True
        )
        
        if should_alert == expected_should_alert and alert_level == expected_level:
            print(f"✓ PASS: {current_meterage}km / {rotation_interval}km = alert:{should_alert}, level:{alert_level}")
            passed += 1
        else:
            print(f"✗ FAIL: {current_meterage}km / {rotation_interval}km = alert:{should_alert}, level:{alert_level}, expected alert:{expected_should_alert}, level:{expected_level}")
            failed += 1
    
    print(f"Dashboard Alert Tests: {passed} passed, {failed} failed\n")
    return failed == 0


def test_threshold_consistency():
    """Test that thresholds are consistent and properly configured."""
    print("Testing Threshold Consistency...")
    
    # Test that thresholds are properly ordered
    rotation_thresholds = ConditioningThresholds.get_tyre_rotation_thresholds()
    condition_thresholds = ConditioningThresholds.get_tyre_condition_thresholds()
    
    tests_passed = True
    
    # Check rotation thresholds are in ascending order
    if not (rotation_thresholds['warning'] < rotation_thresholds['critical'] < rotation_thresholds['overdue']):
        print("✗ FAIL: Rotation thresholds not in ascending order")
        tests_passed = False
    else:
        print("✓ PASS: Rotation thresholds properly ordered")
    
    # Check condition thresholds are in ascending order
    if not (condition_thresholds['warning'] < condition_thresholds['critical'] < condition_thresholds['overdue']):
        print("✗ FAIL: Condition thresholds not in ascending order")
        tests_passed = False
    else:
        print("✓ PASS: Condition thresholds properly ordered")
    
    # Check dashboard thresholds
    dashboard_thresholds = ConditioningThresholds.get_dashboard_thresholds()
    if dashboard_thresholds['warning'] != 0.90:
        print(f"✗ FAIL: Dashboard warning threshold is {dashboard_thresholds['warning']}, expected 0.90")
        tests_passed = False
    else:
        print("✓ PASS: Dashboard warning threshold is 90%")
    
    print(f"Threshold Consistency: {'PASS' if tests_passed else 'FAIL'}\n")
    return tests_passed


def main():
    """Run all tests."""
    print("=" * 60)
    print("CONDITIONING STATUS CENTRALIZATION VALIDATION")
    print("=" * 60)
    print()
    
    all_tests_passed = True
    
    # Run all test suites
    test_results = [
        test_tyre_rotation_status(),
        test_tyre_condition_status(),
        test_battery_status(),
        test_status_colors(),
        test_dashboard_alert_status(),
        test_threshold_consistency(),
    ]
    
    all_tests_passed = all(test_results)
    
    print("=" * 60)
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED! Centralized conditioning status logic is working correctly.")
        print("\n✅ Benefits achieved:")
        print("   - Eliminated duplicated status calculation logic")
        print("   - Standardized status thresholds (80% warning, 90% dashboard alerts)")
        print("   - Consistent status strings and color mappings")
        print("   - Centralized configuration for easy maintenance")
    else:
        print("⚠️  SOME TESTS FAILED! Please review the implementation.")
    print("=" * 60)
    
    return 0 if all_tests_passed else 1


if __name__ == "__main__":
    sys.exit(main())
