import pandas as pd
import sqlite3
import logging
from typing import Dict, Any, Optional
import re

class FlexibleExcelImporter:
    """More flexible Excel importer that handles various formats"""
    
    def __init__(self, db_path: str = "inventory.db"):
        self.db_path = db_path
        
    def import_excel_flexible(self, file_path: str) -> Dict[str, int]:
        """Import Excel with flexible column matching"""
        stats = {
            'equipment': 0,
            'fluids': 0,
            'maintenance': 0,
            'overhauls': 0,
            'conditioning': 0,
            'errors': []
        }
        
        try:
            excel_file = pd.ExcelFile(file_path)
            
            for sheet_name in excel_file.sheet_names:
                print(f"Processing sheet: {sheet_name}")
                
                # Try multiple header configurations
                df = self._read_sheet_flexible(excel_file, sheet_name)
                if df is None:
                    continue
                
                # Process each row
                for idx, row in df.iterrows():
                    try:
                        # 1. Extract and save equipment first
                        equipment_data = self._extract_equipment_flexible(row)
                        if equipment_data:
                            equipment_id = self._save_equipment(equipment_data)
                            if equipment_id:
                                stats['equipment'] += 1
                                
                                # 2. Extract fluids for this equipment
                                fluid_count = self._extract_fluids_flexible(row, equipment_id)
                                stats['fluids'] += fluid_count
                                
                                # 3. Extract maintenance for this equipment
                                maintenance_count = self._extract_maintenance_flexible(row, equipment_id)
                                stats['maintenance'] += maintenance_count
                                
                                # 4. Extract overhauls for this equipment
                                overhaul_count = self._extract_overhauls_flexible(row, equipment_id)
                                stats['overhauls'] += overhaul_count
                                
                    except Exception as e:
                        error_msg = f"Error processing row {idx}: {e}"
                        print(error_msg)
                        stats['errors'].append(error_msg)
                        
        except Exception as e:
            error_msg = f"Import failed: {e}"
            print(error_msg)
            stats['errors'].append(error_msg)
            
        return stats
    
    def _read_sheet_flexible(self, excel_file, sheet_name) -> Optional[pd.DataFrame]:
        """Try multiple methods to read the sheet"""
        # Try different header configurations
        for header_config in [[0, 1], [0], None]:
            try:
                df = pd.read_excel(excel_file, sheet_name=sheet_name, header=header_config)
                
                # Clean up column names
                if header_config == [0, 1]:  # Multi-level headers
                    new_columns = []
                    for col in df.columns:
                        if isinstance(col, tuple):
                            # Join non-null parts
                            parts = [str(part) for part in col if str(part) not in ['nan', 'Unnamed']]
                            new_columns.append(' -> '.join(parts))
                        else:
                            new_columns.append(str(col))
                    df.columns = new_columns
                
                return df
            except:
                continue
        return None
    
    def _extract_equipment_flexible(self, row) -> Optional[Dict[str, Any]]:
        """Extract equipment data with flexible column matching"""
        equipment_data = {}
        
        # Map common column patterns to data fields
        column_mappings = {
            'serial_number': ['SER NO', 'SERIAL', 'S NO', 'SL NO'],
            'ba_number': ['BA NO', 'BA NUMBER', 'BATTALION'],
            'make_and_type': ['MAKE & TYPE', 'MAKE AND TYPE', 'MAKE/TYPE', 'EQUIPMENT'],
            'vintage_years': ['VINTAGE', 'AGE', 'YEARS'],
            'meterage_kms': ['KM RUN', 'METERAGE', 'MILEAGE', 'KMS']
        }
        
        for field, patterns in column_mappings.items():
            for col in row.index:
                col_str = str(col).upper()
                if any(pattern in col_str for pattern in patterns):
                    value = row[col]
                    if pd.notna(value) and str(value).strip():
                        equipment_data[field] = str(value).strip()
                        break
        
        # Must have at least BA number or serial number
        if equipment_data.get('ba_number') or equipment_data.get('serial_number'):
            return equipment_data
        return None
    
    def _extract_fluids_flexible(self,
</rewritten_file> 