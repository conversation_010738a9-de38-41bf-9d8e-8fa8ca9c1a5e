@echo off
title PROJECT-ALPHA Equipment Inventory Management System

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    PROJECT-ALPHA                             ║
echo ║              Equipment Inventory Management                  ║
echo ║                       Version 1.0.0                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo Starting InventoryTracker.exe...
echo Please wait while the application loads (may take 30-60 seconds)
echo.

REM Run the executable
InventoryTracker.exe

REM If there's an error, show it
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                      ERROR OCCURRED                          ║
    echo ╚══════════════════════════════════════════════════════════════╝
    echo.
    echo The application failed to start. Common solutions:
    echo.
    echo 1. Run as Administrator (right-click this file and select "Run as administrator")
    echo 2. Check if Windows Defender is blocking the executable
    echo 3. Ensure you have write permissions in this folder
    echo 4. Try running InventoryTracker.exe directly
    echo.
    echo Error code: %ERRORLEVEL%
    echo.
    pause
) 