"""
Excel Importer Column Mapping Fix
Addresses the following issues:
1. Case and whitespace differences in column names
2. Line breaks (\n) and extra spaces in headers
3. Variations in naming (e.g., "ENG OIL", "ENGINE OIL", "ENG OIL ")
4. Enhanced logging for debugging
5. More flexible column detection

This module provides enhanced column mapping functions to replace the strict logic
in robust_excel_importer_working.py
"""

import re
import logging
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional

logger = logging.getLogger(__name__)

class EnhancedColumnMapper:
    """Enhanced column mapper with flexible matching."""
    
    def __init__(self):
        """Initialize the enhanced column mapper."""
        self.debug_mode = True  # Enable detailed logging
        self.column_variations = self._setup_column_variations()
        
    def _setup_column_variations(self) -> Dict[str, List[str]]:
        """Setup known column variations for flexible matching."""
        return {
            # Equipment basic fields
            'ba_number': [
                'ba no', 'ba number', 'ba num', 'ba_no', 'ba_number', 'ba_num',
                'book no', 'book number', 'book_no', 'book_number',
                'asset no', 'asset number', 'asset_no', 'asset_number'
            ],
            'serial_number': [
                'ser no', 'serial no', 'serial number', 'ser_no', 'serial_no', 'serial_number',
                'equipment no', 'equipment number', 'equip no', 'equip number',
                'chassis no', 'chassis number', 'engine no', 'engine number'
            ],
            'make_and_type': [
                'make and type', 'make & type', 'make type', 'make_and_type', 'make_type',
                'equipment type', 'equipment_type', 'type', 'make', 'model',
                'vehicle type', 'vehicle_type', 'nomenclature'
            ],
            'meterage_kms': [
                'km run', 'kms run', 'kilometers run', 'meterage', 'meterage kms',
                'km_run', 'kms_run', 'meterage_kms', 'total km', 'total kms',
                'odometer', 'distance', 'mileage'
            ],
            'hours_run_total': [
                'hrs run', 'hours run', 'total hours', 'hrs_run', 'hours_run',
                'hours_run_total', 'running hours', 'engine hours', 'operating hours',
                'total hrs', 'total_hrs', 'hrs total', 'hrs_total'
            ],
            'vintage_years': [
                'vintage', 'vintage years', 'vintage_years', 'age', 'age years',
                'years old', 'service years', 'years in service'
            ],
            'date_of_commission': [
                'date of commission', 'date_of_commission', 'commission date', 'commissioned date',
                'date of release', 'date_of_release', 'release date', 'rel date',
                'induction date', 'date of induction', 'date_of_induction'
            ],
            
            # Fluid types with all variations
            'engine_oil': [
                'eng oil', 'engine oil', 'engoil', 'eng_oil', 'engine_oil',
                'motor oil', 'lube oil', 'lubricating oil'
            ],
            'hydraulic_fluid': [
                'hydraulic fluid', 'hydraulic oil', 'hyd oil', 'hyd fluid',
                'hydraulic_fluid', 'hydraulic_oil', 'hyd_oil', 'hyd_fluid',
                'hydr oil', 'hydr fluid', 'hydrflu'
            ],
            'coolant': [
                'coolant', 'coolant fluid', 'cooling fluid', 'radiator fluid',
                'antifreeze', 'coolant_fluid', 'cooling_fluid'
            ],
            'transmission_oil': [
                'transmission oil', 'trans oil', 'txn oil', 'transmission_oil',
                'trans_oil', 'txn_oil', 'gear oil', 'gear_oil',
                'transmission fluid', 'trans fluid', 'txn fluid'
            ],
            'brake_fluid': [
                'brake fluid', 'brake oil', 'brake_fluid', 'brake_oil',
                'brk fluid', 'brk oil', 'brk_fluid', 'brk_oil'
            ],
            'gear_box': [
                'gear box', 'gearbox', 'gear_box', 'gear box oil',
                'gearbox oil', 'gear_box_oil', 'gb oil', 'gb_oil'
            ],
            'differential': [
                'differential', 'diff', 'differential oil', 'diff oil',
                'differential_oil', 'diff_oil', 'rear axle', 'front axle'
            ],
            'clutch': [
                'clutch', 'clutch oil', 'clutch fluid', 'clutch_oil', 'clutch_fluid'
            ],
            'grease': [
                'grease', 'lubricating grease', 'lube grease', 'lubrication'
            ],
            
            # Fluid attributes
            'capacity': [
                'capacity', 'cap', 'volume', 'qty', 'quantity', 'amount',
                'ltrs', 'liters', 'litres', 'kg', 'kgs', 'ltrs/kg', 'ltrs kg'
            ],
            'grade': [
                'grade', 'type', 'spec', 'specification', 'brand', 'quality'
            ],
            'date_of_change': [
                'date of change', 'dt of change', 'last change', 'change date',
                'date_of_change', 'dt_of_change', 'last_change', 'change_date',
                'replaced date', 'replacement date', 'service date'
            ],
            'periodicity': [
                'periodicity', 'interval', 'period', 'frequency', 'cycle',
                'service interval', 'maintenance interval', 'change interval',
                'hrs/month', 'hrs month', 'hrs_month', 'km/hrs', 'km hrs'
            ],
            'top_up': [
                'addl 10%', 'top up', 'additional', 'extra', '10%', 'topping',
                'addl_10%', 'top_up', 'addl 10% top up', 'addl_10%_top_up'
            ]
        }
    
    def normalize_column_name(self, col_name: str) -> str:
        """Normalize column name for flexible matching."""
        if not col_name or pd.isna(col_name):
            return ''
        
        # Convert to string and handle various formats
        normalized = str(col_name).strip()
        
        # Remove line breaks and multiple spaces
        normalized = re.sub(r'[\n\r\t]+', ' ', normalized)
        normalized = re.sub(r'\s+', ' ', normalized)
        
        # Convert to lowercase
        normalized = normalized.lower()
        
        # Remove special characters but keep essential ones
        normalized = re.sub(r'[^\w\s\-_&/()%]', '', normalized)
        
        # Normalize common separators
        normalized = re.sub(r'[\s\-_]+', ' ', normalized)
        
        return normalized.strip()
    
    def find_column_matches(self, columns: List[str], target_field: str) -> List[Tuple[str, float]]:
        """Find columns that match a target field with confidence scores."""
        matches = []
        
        if target_field not in self.column_variations:
            return matches
        
        target_variations = self.column_variations[target_field]
        
        for col in columns:
            normalized_col = self.normalize_column_name(col)
            
            if not normalized_col:
                continue
            
            # Calculate match confidence
            confidence = 0.0
            best_match = ""
            
            for variation in target_variations:
                normalized_variation = self.normalize_column_name(variation)
                
                # Exact match
                if normalized_col == normalized_variation:
                    confidence = 1.0
                    best_match = variation
                    break
                
                # Contains match
                elif normalized_variation in normalized_col or normalized_col in normalized_variation:
                    current_confidence = min(len(normalized_variation), len(normalized_col)) / max(len(normalized_variation), len(normalized_col))
                    if current_confidence > confidence:
                        confidence = current_confidence
                        best_match = variation
                
                # Partial word match
                elif any(word in normalized_col for word in normalized_variation.split()):
                    words_matched = sum(1 for word in normalized_variation.split() if word in normalized_col)
                    current_confidence = words_matched / len(normalized_variation.split()) * 0.8
                    if current_confidence > confidence:
                        confidence = current_confidence
                        best_match = variation
            
            if confidence > 0.3:  # Minimum threshold
                matches.append((col, confidence))
                if self.debug_mode:
                    logger.info(f"Column match: '{col}' -> '{target_field}' (confidence: {confidence:.2f}, via: '{best_match}')")
        
        # Sort by confidence (highest first)
        matches.sort(key=lambda x: x[1], reverse=True)
        return matches
    
    def enhanced_column_mapping(self, columns: List[str]) -> Dict[str, Any]:
        """Enhanced column mapping with flexible matching."""
        logger.info(f"=== ENHANCED COLUMN MAPPING DEBUG ===")
        logger.info(f"Total columns received: {len(columns)}")
        
        # Log all original columns
        for i, col in enumerate(columns):
            logger.info(f"Column {i+1}: '{col}' (type: {type(col)})")
        
        # Clean and normalize columns
        cleaned_columns = []
        filtered_columns = []
        
        for col in columns:
            col_str = str(col).strip()
            
            # Filter out problematic columns
            if (col_str.startswith('Unnamed') or 
                col_str in ['', 'nan', 'NaN', 'None', 'null'] or
                len(col_str) < 2 or
                col_str.replace('.', '').replace('-', '').isdigit()):
                filtered_columns.append(col_str)
                continue
            
            cleaned_columns.append(col)
        
        logger.info(f"After cleaning: {len(cleaned_columns)} valid columns, {len(filtered_columns)} filtered out")
        if filtered_columns:
            logger.info(f"Filtered columns: {filtered_columns}")
        
        # Map primary equipment fields
        primary_mapping = {}
        primary_fields = ['ba_number', 'serial_number', 'make_and_type', 'meterage_kms', 
                         'hours_run_total', 'vintage_years', 'date_of_commission']
        
        for field in primary_fields:
            matches = self.find_column_matches(cleaned_columns, field)
            if matches:
                best_match = matches[0][0]  # Highest confidence match
                primary_mapping[field] = best_match
                logger.info(f"✓ Mapped primary field '{field}' to column '{best_match}'")
            else:
                logger.warning(f"✗ No match found for primary field '{field}'")
        
        # Map fluid fields
        fluid_mapping = self._map_fluid_columns_enhanced(cleaned_columns)
        
        # Map maintenance fields
        maintenance_mapping = self._map_maintenance_columns_enhanced(cleaned_columns)
        
        # Compile results
        results = {
            'primary_fields': primary_mapping,
            'fluid_fields': fluid_mapping,
            'maintenance_fields': maintenance_mapping,
            'total_columns': len(columns),
            'valid_columns': len(cleaned_columns),
            'filtered_columns': len(filtered_columns),
            'mapping_confidence': self._calculate_mapping_confidence(primary_mapping, fluid_mapping, maintenance_mapping)
        }
        
        logger.info(f"=== MAPPING SUMMARY ===")
        logger.info(f"Primary fields mapped: {len(primary_mapping)}/{len(primary_fields)}")
        logger.info(f"Fluid fields mapped: {len(fluid_mapping)}")
        logger.info(f"Maintenance fields mapped: {len(maintenance_mapping)}")
        logger.info(f"Overall confidence: {results['mapping_confidence']:.2f}")
        
        return results
    
    def _map_fluid_columns_enhanced(self, columns: List[str]) -> Dict[str, str]:
        """Enhanced fluid column mapping with flexible detection."""
        fluid_mapping = {}
        
        # Fluid types to look for
        fluid_types = ['engine_oil', 'hydraulic_fluid', 'coolant', 'transmission_oil', 
                      'brake_fluid', 'gear_box', 'differential', 'clutch', 'grease']
        
        # Fluid attributes to look for
        fluid_attributes = ['capacity', 'grade', 'date_of_change', 'periodicity', 'top_up']
        
        logger.info(f"=== FLUID COLUMN MAPPING ===")
        
        # Enhanced separator detection
        separators = [' -> ', ' : ', ' - ', ' >', ':', '>', '-', '.', ' ', '_', '|', '/', '\\']
        
        for col in columns:
            col_normalized = self.normalize_column_name(col)
            
            # Log each column being processed
            logger.debug(f"Processing column: '{col}' -> normalized: '{col_normalized}'")
            
            # Try to identify fluid type and attribute combinations
            fluid_found = None
            attribute_found = None
            
            # Method 1: Check for composite columns (fluid -> attribute)
            for separator in separators:
                if separator in col:
                    parts = [p.strip() for p in col.split(separator, 1)]
                    if len(parts) == 2:
                        part1_norm = self.normalize_column_name(parts[0])
                        part2_norm = self.normalize_column_name(parts[1])
                        
                        # Check if first part is a fluid type
                        for fluid_type in fluid_types:
                            fluid_matches = self.find_column_matches([parts[0]], fluid_type)
                            if fluid_matches and fluid_matches[0][1] > 0.5:
                                fluid_found = fluid_type
                                break
                        
                        # Check if second part is an attribute
                        if fluid_found:
                            for attribute in fluid_attributes:
                                attr_matches = self.find_column_matches([parts[1]], attribute)
                                if attr_matches and attr_matches[0][1] > 0.5:
                                    attribute_found = attribute
                                    break
                        
                        if fluid_found and attribute_found:
                            field_name = f"{fluid_found}_{attribute_found}"
                            fluid_mapping[field_name] = col
                            logger.info(f"✓ Mapped composite fluid field '{field_name}' to column '{col}' (via separator '{separator}')")
                            break
            
            # Method 2: Check for direct fluid type columns (assume capacity)
            if not fluid_found:
                for fluid_type in fluid_types:
                    fluid_matches = self.find_column_matches([col], fluid_type)
                    if fluid_matches and fluid_matches[0][1] > 0.7:  # Higher threshold for direct match
                        field_name = f"{fluid_type}_capacity"
                        if field_name not in fluid_mapping:  # Avoid duplicates
                            fluid_mapping[field_name] = col
                            logger.info(f"✓ Mapped direct fluid field '{field_name}' to column '{col}' (direct fluid match)")
                        break
            
            # Method 3: Check for attribute-only columns (try to infer context)
            if not fluid_found and not attribute_found:
                for attribute in fluid_attributes:
                    attr_matches = self.find_column_matches([col], attribute)
                    if attr_matches and attr_matches[0][1] > 0.8:  # High threshold for context-less match
                        # Try to infer from surrounding context or use generic mapping
                        field_name = f"generic_{attribute}"
                        if field_name not in fluid_mapping:
                            fluid_mapping[field_name] = col
                            logger.info(f"✓ Mapped generic attribute field '{field_name}' to column '{col}'")
                        break
        
        return fluid_mapping
    
    def _map_maintenance_columns_enhanced(self, columns: List[str]) -> Dict[str, str]:
        """Enhanced maintenance column mapping."""
        maintenance_mapping = {}
        
        maintenance_patterns = {
            'overhaul_i_done': ['oh i done', 'oh1 done', 'overhaul 1 done', 'overhaul i done'],
            'overhaul_i_due': ['oh i due', 'oh1 due', 'overhaul 1 due', 'overhaul i due'],
            'overhaul_ii_done': ['oh ii done', 'oh2 done', 'overhaul 2 done', 'overhaul ii done'],
            'overhaul_ii_due': ['oh ii due', 'oh2 due', 'overhaul 2 due', 'overhaul ii due'],
            'tm1_done': ['tm1 done', 'tm 1 done', 'maintenance 1 done'],
            'tm1_due': ['tm1 due', 'tm 1 due', 'maintenance 1 due'],
            'tm2_done': ['tm2 done', 'tm 2 done', 'maintenance 2 done'],
            'tm2_due': ['tm2 due', 'tm 2 due', 'maintenance 2 due']
        }
        
        logger.info(f"=== MAINTENANCE COLUMN MAPPING ===")
        
        for field, patterns in maintenance_patterns.items():
            # Create temporary variations for this field
            temp_variations = {field: patterns}
            old_variations = self.column_variations
            self.column_variations.update(temp_variations)
            
            matches = self.find_column_matches(columns, field)
            if matches:
                best_match = matches[0][0]
                maintenance_mapping[field] = best_match
                logger.info(f"✓ Mapped maintenance field '{field}' to column '{best_match}'")
            
            # Restore original variations
            self.column_variations = old_variations
        
        return maintenance_mapping
    
    def _calculate_mapping_confidence(self, primary_mapping: Dict, fluid_mapping: Dict, maintenance_mapping: Dict) -> float:
        """Calculate overall mapping confidence score."""
        total_expected = 7 + 10 + 8  # Expected primary + fluid + maintenance fields
        total_mapped = len(primary_mapping) + len(fluid_mapping) + len(maintenance_mapping)
        
        if total_expected == 0:
            return 0.0
        
        return min(1.0, total_mapped / total_expected)

def create_test_excel_file(filename: str = "test_excel_minimal.xlsx"):
    """Create a minimal test Excel file with common column variations."""
    import pandas as pd
    
    # Sample data with various column name formats
    data = {
        'BA No': ['BA001', 'BA002', 'BA003'],
        'Serial Number': ['SER001', 'SER002', 'SER003'],
        'Make and Type': ['TATA 6X6 Truck', 'Maruti Gypsy', 'JCB Excavator'],
        'KM Run': [15000, 25000, 8000],
        'Hrs Run': [500, 800, 1200],
        'Vintage (Years)': [5, 8, 3],
        'Date of Commission': ['2019-01-15', '2016-05-20', '2021-03-10'],
        'ENG OIL -> CAPACITY (LTRS/KG)': [15, 8, 20],
        'ENG OIL -> GRADE': ['15W40', '10W30', '20W50'],
        'ENG OIL -> DT OF CHANGE': ['2024-01-15', '2023-12-20', '2024-02-10'],
        'HYDRAULIC FLUID -> CAPACITY (LTRS/KG)': [50, 30, 80],
        'HYDRAULIC FLUID -> GRADE': ['HLP 68', 'HLP 46', 'HLP 32'],
        'COOLANT -> CAPACITY (LTRS/KG)': [25, 15, 35],
        'COOLANT -> GRADE': ['Glycol', 'Ethylene', 'Propylene'],
        'Remarks': ['Good condition', 'Needs service', 'New equipment']
    }
    
    df = pd.DataFrame(data)
    df.to_excel(filename, index=False)
    
    logger.info(f"Created test Excel file: {filename}")
    logger.info(f"Columns: {list(data.keys())}")
    
    return filename

def test_enhanced_column_mapping():
    """Test the enhanced column mapping with various column formats."""
    # Test columns with various issues
    test_columns = [
        'BA No',  # Standard
        'BA NO ',  # Extra space
        'BA_Number',  # Underscore
        'ba number',  # Lowercase
        'Serial\nNumber',  # Line break
        'Make & Type',  # Ampersand
        'ENG OIL -> CAPACITY (LTRS/KG)',  # Standard composite
        'ENG OIL : GRADE',  # Different separator
        'ENGINE OIL - DT OF CHANGE',  # Variation in fluid name
        'HYD OIL|CAPACITY',  # Different separator
        'COOLANT.GRADE',  # Dot separator
        'Transmission Oil / Top Up',  # Slash separator
        'Unnamed: 0',  # Should be filtered
        '',  # Empty
        'OH I DONE DATE',  # Maintenance
        'TM1 DUE'  # Maintenance variation
    ]
    
    mapper = EnhancedColumnMapper()
    results = mapper.enhanced_column_mapping(test_columns)
    
    logger.info("=== TEST RESULTS ===")
    logger.info(f"Primary fields: {results['primary_fields']}")
    logger.info(f"Fluid fields: {results['fluid_fields']}")
    logger.info(f"Maintenance fields: {results['maintenance_fields']}")
    logger.info(f"Overall confidence: {results['mapping_confidence']:.2f}")

if __name__ == "__main__":
    # Set up logging for testing
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    # Test the enhanced mapping
    test_enhanced_column_mapping()
    
    # Create a test Excel file
    create_test_excel_file() 