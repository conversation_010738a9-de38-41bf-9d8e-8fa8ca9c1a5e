#!/usr/bin/env python3
"""
Test script to verify that overhaul overdue counts are synchronized 
between the dashboard and overhaul tab views.
"""

import sys
import os
sys.path.append('.')

import database
import utils
from models import Overhaul, Equipment
from datetime import date, datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_dashboard_overhaul_count():
    """Test the dashboard overhaul overdue count calculation."""
    print('Testing Dashboard Overhaul Overdue Count')
    print('=' * 50)
    
    try:
        overhauls = Overhaul.get_all()
        overdue_count = 0
        
        print(f"Total overhauls found: {len(overhauls) if overhauls else 0}")
        
        for oh in overhauls:
            equipment_id = oh.get('equipment_id')
            equipment = Equipment.get_by_id(equipment_id) if equipment_id else None
            
            # Calculate status using centralized logic (same as dashboard)
            status = utils.calculate_overhaul_status(
                oh.get('overhaul_type'),
                oh.get('due_date'),
                oh.get('done_date'),
                equipment.date_of_commission if equipment else None,
                None,  # oh1_done_date
                None,  # custom_intervals
                equipment.meterage_kms if equipment else None
            )
            
            print(f"Overhaul ID {oh.get('id')}: Type={oh.get('overhaul_type')}, "
                  f"Due={oh.get('due_date')}, Done={oh.get('done_date')}, Status={status}")
            
            # Count only overdue overhauls
            if status == "overdue":
                overdue_count += 1
                print(f"  -> COUNTED as overdue")
        
        print(f"\nDashboard Overhaul Overdue Count: {overdue_count}")
        return overdue_count
        
    except Exception as e:
        logger.error(f"Error testing dashboard overhaul count: {e}")
        return 0

def test_overhaul_tab_status_distribution():
    """Test the overhaul tab status distribution."""
    print('\nTesting Overhaul Tab Status Distribution')
    print('=' * 50)
    
    try:
        overhauls = Overhaul.get_all()
        status_counts = {
            'scheduled': 0,
            'warning': 0,
            'critical': 0,
            'overdue': 0,
            'completed': 0,
            'cancelled': 0,
            'discard': 0,
            'unknown': 0
        }
        
        for oh in overhauls:
            equipment_id = oh.get('equipment_id')
            equipment = Equipment.get_by_id(equipment_id) if equipment_id else None
            
            # Calculate status using centralized logic (same as overhaul tab)
            status = utils.calculate_overhaul_status(
                oh.get('overhaul_type'),
                oh.get('due_date'),
                oh.get('done_date'),
                equipment.date_of_commission if equipment else None,
                None,  # oh1_done_date
                None,  # custom_intervals
                equipment.meterage_kms if equipment else None
            )
            
            if status in status_counts:
                status_counts[status] += 1
            else:
                status_counts['unknown'] += 1
        
        print("Overhaul Status Distribution:")
        for status, count in status_counts.items():
            if count > 0:
                print(f"  {status.title()}: {count}")
        
        print(f"\nOverhaul Tab Overdue Count: {status_counts['overdue']}")
        return status_counts
        
    except Exception as e:
        logger.error(f"Error testing overhaul tab status distribution: {e}")
        return {}

def test_synchronization():
    """Test that dashboard and overhaul tab counts match."""
    print('\nTesting Dashboard-Overhaul Tab Synchronization')
    print('=' * 60)
    
    dashboard_count = test_dashboard_overhaul_count()
    tab_status_counts = test_overhaul_tab_status_distribution()
    tab_overdue_count = tab_status_counts.get('overdue', 0)
    
    print(f"\nSYNCHRONIZATION RESULTS:")
    print(f"  Dashboard Overdue Count: {dashboard_count}")
    print(f"  Overhaul Tab Overdue Count: {tab_overdue_count}")
    
    if dashboard_count == tab_overdue_count:
        print(f"  ✅ SYNCHRONIZED: Both views show {dashboard_count} overdue overhauls")
        return True
    else:
        print(f"  ❌ NOT SYNCHRONIZED: Dashboard shows {dashboard_count}, Tab shows {tab_overdue_count}")
        print(f"  Difference: {abs(dashboard_count - tab_overdue_count)}")
        return False

def test_meterage_based_overdue():
    """Test meterage-based overdue detection for OH-I overhauls."""
    print('\nTesting Meterage-Based Overdue Detection')
    print('=' * 50)
    
    try:
        overhauls = Overhaul.get_all()
        meterage_overdue_count = 0
        
        for oh in overhauls:
            if oh.get('overhaul_type') != 'OH-I':
                continue
                
            equipment_id = oh.get('equipment_id')
            equipment = Equipment.get_by_id(equipment_id) if equipment_id else None
            
            if equipment and equipment.meterage_kms:
                try:
                    km_val = float(equipment.meterage_kms)
                    if km_val >= 60000:
                        meterage_overdue_count += 1
                        print(f"OH-I Overhaul ID {oh.get('id')}: Equipment has {km_val} KM (≥60,000 = overdue)")
                except ValueError:
                    pass
        
        print(f"\nMeterage-Based Overdue OH-I Count: {meterage_overdue_count}")
        return meterage_overdue_count
        
    except Exception as e:
        logger.error(f"Error testing meterage-based overdue: {e}")
        return 0

if __name__ == "__main__":
    print("Overhaul Synchronization Test")
    print("=" * 70)
    
    # Test synchronization
    is_synchronized = test_synchronization()
    
    # Test meterage-based logic
    meterage_count = test_meterage_based_overdue()
    
    print(f"\nFINAL SUMMARY:")
    print(f"  Dashboard-Tab Synchronization: {'✅ PASS' if is_synchronized else '❌ FAIL'}")
    print(f"  Meterage-Based Overdue Detection: {'✅ ACTIVE' if meterage_count > 0 else 'ℹ️ NO CASES FOUND'}")
    
    if is_synchronized:
        print(f"\n🎉 SUCCESS: Dashboard and Overhaul Tab are properly synchronized!")
    else:
        print(f"\n⚠️ ISSUE: Dashboard and Overhaul Tab counts do not match.")
