{"build_info": {"app_name": "InventoryTracker", "app_version": "1.0.0", "build_date": "2025-07-03T21:43:59.870519", "build_duration_seconds": 0.093433, "python_version": "3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]", "platform": "Windows-10-10.0.26100-SP0", "project_root": "C:\\Users\\<USER>\\Downloads\\mt\\PROJECT-ALPHA"}, "executable_info": {"path": null, "size_mb": 0, "exists": false}, "build_log": ["[21:43:59] INFO: 🚀 Starting complete PyInstaller build process", "[21:43:59] INFO: ======================================================================", "[21:43:59] INFO: 🔍 Checking system requirements...", "[21:43:59] ERROR: ❌ Unexpected error: '<' not supported between instances of 'sys.version_info' and 'float'", "[21:43:59] ERROR: Traceback: Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Downloads\\mt\\PROJECT-ALPHA\\build_pyinstaller_executable.py\", line 549, in run_complete_build\n    if not self.check_system_requirements():\n  File \"C:\\Users\\<USER>\\Downloads\\mt\\PROJECT-ALPHA\\build_pyinstaller_executable.py\", line 97, in check_system_requirements\n    if sys.version_info < (3.9):\nTypeError: '<' not supported between instances of 'sys.version_info' and 'float'\n", "[21:43:59] INFO: 📊 Generating build report..."]}