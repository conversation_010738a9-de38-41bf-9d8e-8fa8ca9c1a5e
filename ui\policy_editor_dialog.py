"""
Policy Editor <PERSON><PERSON> Module
Handles the UI for creating and editing vehicle class policies.
"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTabWidget, QWidget, QFormLayout,
                            QComboBox, QLineEdit, QDoubleSpinBox, QCheckBox,
                            QGroupBox, QGridLayout, QMessageBox, QFrame,
                            QRadioButton, QButtonGroup, QSpinBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from policy_models import PolicyCondition
import policy_service


class PolicyEditorDialog(QDialog):
    """
    Dialog for creating and editing vehicle class policies.
    """
    def __init__(self, parent=None, policy_id=None, make_and_type=None):
        super().__init__(parent)
        self.parent = parent
        self.policy_id = policy_id
        self.make_and_type = make_and_type
        self.policy_data = None
        
        # If policy_id is provided, load the policy data
        if policy_id:
            self.policy_data = policy_service.get_policy_with_conditions(policy_id=policy_id)
            if self.policy_data:
                self.make_and_type = self.policy_data['policy'].get('make_and_type')
        
        self.condition_widgets = {}  # Store widgets for each condition type
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """Set up the policy editor dialog UI."""
        self.setWindowTitle("Vehicle Class Policy Editor")
        
        # Setup responsive sizing
        from ui.window_utils import DialogManager
        DialogManager.setup_responsive_dialog(self, width_percent=0.6, height_percent=0.7, 
                                            min_width=600, min_height=500)
        
        # Main layout
        main_layout = QVBoxLayout(self)
        
        # Policy details section
        details_group = QGroupBox("Policy Details")
        details_layout = QFormLayout()
        
        # Make and Type field
        self.make_type_combo = QComboBox()
        self.make_type_combo.setEditable(True)
        self.make_type_combo.setMinimumWidth(300)
        self.load_make_type_options()
        details_layout.addRow("Make and Type:", self.make_type_combo)
        
        # Add details to group
        details_group.setLayout(details_layout)
        main_layout.addWidget(details_group)
        
        # Discard criteria section (removed tab widget since we only need discard)
        # Create only the discard condition form
        self.discard_tab = self.create_condition_tab(PolicyCondition.DISCARD, "Discard")
        
        main_layout.addWidget(self.discard_tab)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.save_button = QPushButton("Save")
        self.cancel_button = QPushButton("Cancel")
        
        self.save_button.clicked.connect(self.save_policy)
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(button_layout)
    
    def create_condition_tab(self, condition_type, label):
        """Create a tab for a specific condition type."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Title
        title_label = QLabel(f"{label} Criteria")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title_label)
        
        # Description
        desc_label = QLabel("Specify the condition values for when this maintenance should be performed.")
        layout.addWidget(desc_label)
        
        # Form for condition values
        form_layout = QFormLayout()
        
        # Years threshold
        years_spin = QDoubleSpinBox()
        years_spin.setRange(0, 100)
        years_spin.setDecimals(1)
        years_spin.setSingleStep(0.5)
        years_spin.setSuffix(" years")
        form_layout.addRow("Vintage Years:", years_spin)
        
        # KMs threshold
        kms_spin = QDoubleSpinBox()
        kms_spin.setRange(0, 1000000)
        kms_spin.setDecimals(0)
        kms_spin.setSingleStep(1000)
        kms_spin.setSuffix(" kms")
        form_layout.addRow("Meterage (KMs):", kms_spin)
        
        # Hours threshold
        hours_spin = QDoubleSpinBox()
        hours_spin.setRange(0, 100000)
        hours_spin.setDecimals(0)
        hours_spin.setSingleStep(100)
        hours_spin.setSuffix(" hours")
        form_layout.addRow("Hours Run:", hours_spin)
        
        # Logic type
        logic_group = QGroupBox("Condition Logic")
        logic_layout = QVBoxLayout()
        
        earlier_radio = QRadioButton("Whichever Is Earlier (any threshold met)")
        later_radio = QRadioButton("All Must Be Met (all thresholds)")
        exact_radio = QRadioButton("Exact Match (first non-zero threshold)")
        
        logic_button_group = QButtonGroup()
        logic_button_group.addButton(earlier_radio, 0)
        logic_button_group.addButton(later_radio, 1)
        logic_button_group.addButton(exact_radio, 2)
        
        earlier_radio.setChecked(True)  # Default
        
        logic_layout.addWidget(earlier_radio)
        logic_layout.addWidget(later_radio)
        logic_layout.addWidget(exact_radio)
        
        logic_group.setLayout(logic_layout)
        
        layout.addLayout(form_layout)
        layout.addWidget(logic_group)
        layout.addStretch()
        
        # Store widgets for this condition
        self.condition_widgets[condition_type] = {
            'years': years_spin,
            'kms': kms_spin,
            'hours': hours_spin,
            'logic_group': logic_button_group
        }
        
        return tab
    
    def load_make_type_options(self):
        """Load make and type options from equipment database."""
        try:
            import database
            
            # Get unique make and type values
            with database.get_db_connection() as conn:
                cursor = conn.cursor()
                # Get all unique make and types from equipment table
                cursor.execute("""
                SELECT DISTINCT make_and_type
                FROM equipment
                WHERE make_and_type IS NOT NULL
                ORDER BY make_and_type
                """)
                make_types = [row.get('make_and_type') for row in cursor.fetchall()]
            
            # Clean up make types (remove newlines, normalize spaces)
            normalized_make_types = []
            for mt in make_types:
                if mt:
                    # Normalize make_and_type by removing newlines and extra spaces
                    cleaned = ' '.join(mt.splitlines()) if isinstance(mt, str) else str(mt)
                    normalized_make_types.append(cleaned)
            
            # Add to combo box
            self.make_type_combo.clear()
            self.make_type_combo.addItems(normalized_make_types)
            
            # Set current value if editing
            if self.make_and_type:
                # Normalize the make_and_type for consistent matching
                normalized_make_and_type = ' '.join(self.make_and_type.splitlines()) if isinstance(self.make_and_type, str) else str(self.make_and_type)
                
                # Try exact match first
                index = self.make_type_combo.findText(normalized_make_and_type)
                if index >= 0:
                    self.make_type_combo.setCurrentIndex(index)
                else:
                    # Try case-insensitive match
                    for i in range(self.make_type_combo.count()):
                        if self.make_type_combo.itemText(i).lower() == normalized_make_and_type.lower():
                            self.make_type_combo.setCurrentIndex(i)
                            break
                    else:
                        # No match found, set as custom text
                        self.make_type_combo.setEditText(normalized_make_and_type)
            
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to load make and type options: {str(e)}")
    
    def load_data(self):
        """Load policy data if editing an existing policy."""
        if not self.policy_data:
            return
        
        # Load conditions - only process discard conditions
        conditions = self.policy_data.get('conditions', [])
        
        for condition in conditions:
            condition_type = condition.get('condition_type')
            # Only load discard conditions since we removed other tabs
            if condition_type != PolicyCondition.DISCARD:
                continue
                
            if condition_type not in self.condition_widgets:
                continue
            
            widgets = self.condition_widgets[condition_type]
            
            # Set values
            years = condition.get('years_threshold')
            if years is not None:
                widgets['years'].setValue(float(years))
                
            kms = condition.get('km_threshold')
            if kms is not None:
                widgets['kms'].setValue(float(kms))
                
            hours = condition.get('hours_threshold')
            if hours is not None:
                widgets['hours'].setValue(float(hours))
            
            # Set logic type
            logic_type = condition.get('logic_type')
            if logic_type == PolicyCondition.EARLIER:
                widgets['logic_group'].button(0).setChecked(True)
            elif logic_type == PolicyCondition.LATER:
                widgets['logic_group'].button(1).setChecked(True)
            elif logic_type == PolicyCondition.EXACT:
                widgets['logic_group'].button(2).setChecked(True)
    
    def get_logic_type(self, button_group):
        """Get logic type from radio button group."""
        id = button_group.checkedId()
        if id == 0:
            return PolicyCondition.EARLIER
        elif id == 1:
            return PolicyCondition.LATER
        elif id == 2:
            return PolicyCondition.EXACT
        return PolicyCondition.EARLIER  # Default
    
    def save_policy(self):
        """Save the policy data."""
        try:
            # Get make and type and normalize it
            make_and_type = self.make_type_combo.currentText().strip() if self.make_type_combo.currentText() else ''
            if not make_and_type:
                QMessageBox.warning(self, "Validation Error", "Make and Type is required.")
                return
                
            # Make sure there are no leading/trailing whitespace or line breaks
            make_and_type = ' '.join(make_and_type.splitlines())
            
            # Collect conditions data - only process discard conditions
            conditions_data = []
            
            # Process only discard condition type since we removed other tabs
            for condition_type, widgets in self.condition_widgets.items():
                # Only save discard conditions
                if condition_type != PolicyCondition.DISCARD:
                    continue
                    
                years = widgets['years'].value()
                kms = widgets['kms'].value()
                hours = widgets['hours'].value()
                
                # Skip if all values are zero
                if years == 0 and kms == 0 and hours == 0:
                    continue
                
                logic_type = self.get_logic_type(widgets['logic_group'])
                
                condition = {
                    'condition_type': condition_type,
                    'years_threshold': years,
                    'km_threshold': kms,
                    'hours_threshold': hours,
                    'logic_type': logic_type
                }
                
                conditions_data.append(condition)
            
            # Save policy
            policy_id = policy_service.create_or_update_policy(
                make_and_type=make_and_type,
                conditions_data=conditions_data,
                policy_id=self.policy_id
            )
            
            if policy_id:
                QMessageBox.information(self, "Success", f"Policy for {make_and_type} saved successfully.")
                self.policy_id = policy_id
                self.accept()
            else:
                QMessageBox.warning(self, "Error", "Failed to save policy.")
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"An error occurred while saving policy: {str(e)}")
