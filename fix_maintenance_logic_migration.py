"""
Database migration script to fix maintenance logic.
Run this FIRST before any code changes.
"""
import sqlite3
import os
import shutil
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

def backup_database(db_path):
    """Create backup of database before migration."""
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(db_path, backup_path)
    print(f"✅ Database backed up to: {backup_path}")
    return backup_path

def migrate_maintenance_schema(db_path):
    """Migrate maintenance table to new schema."""
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 Starting maintenance table migration...")
        
        # Step 1: Check if migration already done
        cursor.execute("PRAGMA table_info(maintenance)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'next_due_date' in columns:
            print("✅ Migration already completed. Skipping schema changes.")
            conn.close()
            return True
        
        # Step 2: Create backup table
        print("📋 Creating backup table...")
        cursor.execute("""
            CREATE TABLE maintenance_backup AS 
            SELECT * FROM maintenance
        """)
        
        # Step 3: Create new maintenance table with correct schema
        print("🔧 Creating new maintenance table...")
        cursor.execute("DROP TABLE maintenance")
        
        cursor.execute("""
            CREATE TABLE maintenance (
                maintenance_id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER NOT NULL,
                maintenance_type TEXT,
                done_date TEXT,
                next_due_date TEXT,
                vintage_years REAL DEFAULT 0.0,
                meterage_kms REAL DEFAULT 0.0,
                completion_notes TEXT,
                status TEXT DEFAULT 'scheduled',
                completed_by TEXT,
                actual_completion_date TEXT,
                completion_meterage REAL,
                maintenance_category TEXT DEFAULT 'TM-1',
                FOREIGN KEY (equipment_id) REFERENCES equipment (equipment_id)
            )
        """)
        
        # Step 4: Migrate data from backup (rename due_date to next_due_date)
        print("📊 Migrating data...")
        cursor.execute("""
            INSERT INTO maintenance (
                maintenance_id, equipment_id, maintenance_type, done_date, 
                next_due_date, vintage_years, meterage_kms, completion_notes,
                status, completed_by, actual_completion_date, completion_meterage,
                maintenance_category
            )
            SELECT 
                maintenance_id, equipment_id, maintenance_type, done_date,
                due_date as next_due_date, vintage_years, meterage_kms, completion_notes,
                COALESCE(status, 'scheduled') as status, completed_by, 
                actual_completion_date, completion_meterage,
                COALESCE(maintenance_category, 'TM-1') as maintenance_category
            FROM maintenance_backup
        """)
        
        # Step 5: Create indexes for performance
        print("⚡ Creating indexes...")
        cursor.execute("""
            CREATE INDEX idx_maintenance_equipment_category_status 
            ON maintenance(equipment_id, maintenance_category, status)
        """)
        
        cursor.execute("""
            CREATE INDEX idx_maintenance_next_due_date 
            ON maintenance(next_due_date)
        """)
        
        # Step 6: Clean up data inconsistencies
        print("🧹 Cleaning up data...")
        
        # Fix NULL statuses
        cursor.execute("""
            UPDATE maintenance 
            SET status = 'scheduled' 
            WHERE status IS NULL OR status = ''
        """)
        
        # Fix impossible cases where done_date > next_due_date for scheduled items
        cursor.execute("""
            UPDATE maintenance 
            SET done_date = NULL 
            WHERE status = 'scheduled' 
            AND done_date IS NOT NULL 
            AND next_due_date IS NOT NULL
            AND date(done_date) > date(next_due_date)
        """)
        
        # Step 7: Validate migration
        cursor.execute("SELECT COUNT(*) FROM maintenance")
        new_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM maintenance_backup")
        old_count = cursor.fetchone()[0]
        
        if new_count != old_count:
            raise Exception(f"Data migration failed! Old count: {old_count}, New count: {new_count}")
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print(f"✅ Migration completed successfully!")
        print(f"📊 Migrated {new_count} maintenance records")
        print("🗂️  Backup table 'maintenance_backup' preserved for safety")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def validate_migration(db_path):
    """Validate the migration was successful."""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check schema
        cursor.execute("PRAGMA table_info(maintenance)")
        columns = [col[1] for col in cursor.fetchall()]
        
        required_columns = ['maintenance_id', 'equipment_id', 'done_date', 'next_due_date', 'status']
        missing_columns = [col for col in required_columns if col not in columns]
        
        if missing_columns:
            print(f"❌ Validation failed: Missing columns {missing_columns}")
            return False
        
        # Check for duplicate scheduled maintenance (should not exist after migration)
        cursor.execute("""
            SELECT equipment_id, maintenance_category, COUNT(*)
            FROM maintenance 
            WHERE status = 'scheduled'
            GROUP BY equipment_id, maintenance_category
            HAVING COUNT(*) > 1
        """)
        
        duplicates = cursor.fetchall()
        if duplicates:
            print(f"⚠️  Warning: Found {len(duplicates)} equipment with duplicate scheduled maintenance")
            for dup in duplicates[:5]:  # Show first 5
                print(f"   Equipment {dup[0]}, Category {dup[1]}: {dup[2]} records")
        
        conn.close()
        print("✅ Migration validation completed")
        return True
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        return False

def run_migration():
    """Main migration function."""
    # Try to find database file
    possible_paths = [
        "inventory.db",
        "PROJECT-ALPHA/inventory.db",
        os.path.join(os.path.expanduser("~"), "AppData", "Local", "InventoryTracker", "inventory.db")
    ]
    
    db_path = None
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("❌ Could not find database file. Please specify the path manually.")
        return False
    
    print(f"🎯 Found database: {db_path}")
    
    # Create backup
    backup_path = backup_database(db_path)
    
    # Run migration
    success = migrate_maintenance_schema(db_path)
    
    if success:
        # Validate migration
        validate_migration(db_path)
        print(f"\n🎉 MIGRATION COMPLETED SUCCESSFULLY!")
        print(f"📁 Original database: {db_path}")
        print(f"💾 Backup saved as: {backup_path}")
        print(f"\nYou can now update the code to use the new schema.")
    else:
        print(f"\n💥 MIGRATION FAILED!")
        print(f"🔄 You can restore from backup: {backup_path}")
    
    return success

if __name__ == "__main__":
    run_migration() 