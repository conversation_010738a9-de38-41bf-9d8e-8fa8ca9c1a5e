#!/usr/bin/env python3
"""
Cross-System Compatible Excel Importer for PROJECT-ALPHA
Ensures reliable Excel import functionality across all deployment environments:
- Different Windows versions (Windows 10/11 variations)
- Low-resource military systems (4GB RAM, older hardware)
- Systems with different Python/library versions
- Clean systems without development dependencies
- Systems with different regional settings/locales
"""

import os
import sys
import logging
import platform
import locale
import tempfile
import gc
import traceback
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

# Configure logging early
logger = logging.getLogger('cross_system_excel_importer')

class SystemCompatibilityManager:
    """Manages system compatibility and environment detection."""
    
    def __init__(self):
        self.system_info = self._detect_system_environment()
        self.compatibility_issues = []
        self.fallback_strategies = []
        
    def _detect_system_environment(self) -> Dict[str, Any]:
        """Detect comprehensive system environment information."""
        try:
            import psutil
            memory_gb = psutil.virtual_memory().total / (1024**3)
        except ImportError:
            memory_gb = 4.0  # Conservative estimate
        
        try:
            import ctypes
            # Get Windows version info
            version = sys.getwindowsversion()
            windows_version = f"{version.major}.{version.minor}.{version.build}"
        except (ImportError, AttributeError):
            windows_version = "Unknown"
        
        return {
            'platform': platform.platform(),
            'python_version': sys.version,
            'windows_version': windows_version,
            'memory_gb': memory_gb,
            'cpu_count': os.cpu_count() or 1,
            'locale': locale.getdefaultlocale(),
            'encoding': sys.getdefaultencoding(),
            'temp_dir': tempfile.gettempdir(),
            'user_writable': self._check_user_writable_access(),
            'available_libraries': self._check_library_availability()
        }
    
    def _check_user_writable_access(self) -> bool:
        """Check if user has write access to application directory."""
        try:
            test_file = Path(tempfile.gettempdir()) / "test_write_access.tmp"
            test_file.write_text("test")
            test_file.unlink()
            return True
        except Exception:
            return False
    
    def _check_library_availability(self) -> Dict[str, bool]:
        """Check availability of required libraries with version compatibility."""
        libraries = {}
        
        # Core Excel processing libraries
        try:
            import pandas
            libraries['pandas'] = True
            libraries['pandas_version'] = pandas.__version__
        except ImportError:
            libraries['pandas'] = False
        
        try:
            import openpyxl
            libraries['openpyxl'] = True
            libraries['openpyxl_version'] = openpyxl.__version__
        except ImportError:
            libraries['openpyxl'] = False
        
        try:
            import xlrd
            libraries['xlrd'] = True
            libraries['xlrd_version'] = xlrd.__version__
        except ImportError:
            libraries['xlrd'] = False
        
        # System monitoring
        try:
            import psutil
            libraries['psutil'] = True
            libraries['psutil_version'] = psutil.__version__
        except ImportError:
            libraries['psutil'] = False
        
        # Date handling
        try:
            import dateutil
            libraries['dateutil'] = True
        except ImportError:
            libraries['dateutil'] = False
        
        return libraries
    
    def get_optimal_processing_strategy(self) -> str:
        """Determine optimal processing strategy based on system capabilities."""
        memory_gb = self.system_info['memory_gb']
        cpu_count = self.system_info['cpu_count']
        
        if memory_gb >= 8 and cpu_count >= 4:
            return 'high_performance'
        elif memory_gb >= 4 and cpu_count >= 2:
            return 'standard'
        else:
            return 'low_resource'
    
    def get_compatibility_report(self) -> Dict[str, Any]:
        """Generate comprehensive compatibility report."""
        return {
            'system_info': self.system_info,
            'processing_strategy': self.get_optimal_processing_strategy(),
            'compatibility_issues': self.compatibility_issues,
            'recommendations': self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate system-specific recommendations."""
        recommendations = []
        
        if self.system_info['memory_gb'] < 4:
            recommendations.append("Low memory detected - using chunked processing")
        
        if not self.system_info['available_libraries']['pandas']:
            recommendations.append("pandas library missing - Excel import will fail")
        
        if not self.system_info['available_libraries']['openpyxl']:
            recommendations.append("openpyxl library missing - .xlsx files cannot be processed")
        
        if not self.system_info['user_writable']:
            recommendations.append("Limited write access - may affect temporary file operations")
        
        return recommendations

class CrossSystemExcelImporter:
    """Cross-system compatible Excel importer with robust error handling."""
    
    def __init__(self):
        self.compatibility_manager = SystemCompatibilityManager()
        self.processing_strategy = self.compatibility_manager.get_optimal_processing_strategy()
        self.setup_logging()
        self.setup_locale_handling()
        
    def setup_logging(self):
        """Setup robust logging that works across different systems."""
        try:
            # Use system temp directory for logs if main logging fails
            log_dir = Path(tempfile.gettempdir()) / "PROJECT_ALPHA_LOGS"
            log_dir.mkdir(exist_ok=True)
            
            log_file = log_dir / f"excel_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            
            # Configure file handler with fallback
            handler = logging.FileHandler(log_file, encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
            
        except Exception as e:
            # Fallback to console logging only
            logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
            logger.warning(f"Could not setup file logging: {e}")
    
    def setup_locale_handling(self):
        """Setup locale handling for different regional settings."""
        try:
            # Try to set a consistent locale for date parsing
            locale.setlocale(locale.LC_TIME, 'C')  # Use C locale for consistent date parsing
        except locale.Error:
            logger.warning("Could not set C locale, using system default")
        
        # Store original locale for restoration
        self.original_locale = locale.getlocale()
    
    def import_excel_cross_system(self, file_path: str) -> Dict[str, Any]:
        """
        Import Excel file with cross-system compatibility.
        
        Args:
            file_path (str): Path to Excel file
            
        Returns:
            dict: Import results with comprehensive error handling
        """
        logger.info(f"Starting cross-system Excel import: {file_path}")
        logger.info(f"System info: {self.compatibility_manager.system_info}")
        logger.info(f"Processing strategy: {self.processing_strategy}")
        
        # Validate file accessibility
        validation_result = self._validate_file_access(file_path)
        if not validation_result['success']:
            return {
                'success': False,
                'error': validation_result['error'],
                'system_info': self.compatibility_manager.get_compatibility_report()
            }
        
        # Check library dependencies
        dependency_check = self._check_dependencies()
        if not dependency_check['success']:
            return {
                'success': False,
                'error': dependency_check['error'],
                'missing_dependencies': dependency_check['missing'],
                'system_info': self.compatibility_manager.get_compatibility_report()
            }
        
        # Perform import with strategy-specific approach
        try:
            if self.processing_strategy == 'low_resource':
                return self._import_low_resource(file_path)
            elif self.processing_strategy == 'standard':
                return self._import_standard(file_path)
            else:
                return self._import_high_performance(file_path)
                
        except Exception as e:
            logger.error(f"Excel import failed: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            
            # Try fallback import method
            return self._import_fallback(file_path, str(e))
    
    def _validate_file_access(self, file_path: str) -> Dict[str, Any]:
        """Validate file accessibility and format."""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return {'success': False, 'error': f"File does not exist: {file_path}"}
            
            if not file_path.is_file():
                return {'success': False, 'error': f"Path is not a file: {file_path}"}
            
            # Check file size
            file_size_mb = file_path.stat().st_size / (1024 * 1024)
            if file_size_mb > 100:  # 100MB limit
                return {'success': False, 'error': f"File too large: {file_size_mb:.1f}MB (limit: 100MB)"}
            
            # Check file extension
            if file_path.suffix.lower() not in ['.xlsx', '.xls']:
                return {'success': False, 'error': f"Unsupported file format: {file_path.suffix}"}
            
            # Try to open file for reading
            with open(file_path, 'rb') as f:
                f.read(1024)  # Read first 1KB to test accessibility
            
            return {'success': True, 'file_size_mb': file_size_mb}
            
        except PermissionError:
            return {'success': False, 'error': "Permission denied accessing file"}
        except Exception as e:
            return {'success': False, 'error': f"File validation error: {e}"}
    
    def _check_dependencies(self) -> Dict[str, Any]:
        """Check required dependencies with version compatibility."""
        missing = []
        available = self.compatibility_manager.system_info['available_libraries']
        
        if not available.get('pandas'):
            missing.append('pandas')
        
        if not available.get('openpyxl'):
            missing.append('openpyxl')
        
        if missing:
            return {
                'success': False,
                'error': f"Missing required libraries: {', '.join(missing)}",
                'missing': missing
            }
        
        # Check version compatibility
        warnings = []
        try:
            import pandas as pd
            if hasattr(pd, '__version__'):
                version = pd.__version__
                major, minor = map(int, version.split('.')[:2])
                if major < 1 or (major == 1 and minor < 3):
                    warnings.append(f"pandas version {version} may have compatibility issues")
        except Exception:
            pass
        
        return {'success': True, 'warnings': warnings}
    
    def _import_low_resource(self, file_path: str) -> Dict[str, Any]:
        """Import strategy optimized for low-resource systems."""
        logger.info("Using low-resource import strategy")
        
        try:
            # Use memory-safe chunked processing
            from memory_safe_excel_importer import MemorySafeExcelImporter
            
            # Configure for very conservative memory usage
            importer = MemorySafeExcelImporter(
                file_path=file_path,
                chunk_size=500,  # Smaller chunks
                max_memory_mb=256  # Conservative memory limit
            )
            
            result = importer.import_all_data()
            
            # Force garbage collection
            gc.collect()
            
            return {
                'success': True,
                'strategy': 'low_resource',
                'result': result,
                'system_info': self.compatibility_manager.get_compatibility_report()
            }
            
        except Exception as e:
            logger.error(f"Low-resource import failed: {e}")
            return self._import_fallback(file_path, str(e))
    
    def _import_standard(self, file_path: str) -> Dict[str, Any]:
        """Standard import strategy for typical systems."""
        logger.info("Using standard import strategy")
        
        try:
            # Use enhanced Excel importer
            from enhanced_excel_importer import import_excel_enhanced
            
            result = import_excel_enhanced(file_path)
            
            return {
                'success': True,
                'strategy': 'standard',
                'result': result,
                'system_info': self.compatibility_manager.get_compatibility_report()
            }
            
        except Exception as e:
            logger.error(f"Standard import failed: {e}")
            return self._import_fallback(file_path, str(e))
    
    def _import_high_performance(self, file_path: str) -> Dict[str, Any]:
        """High-performance import strategy for capable systems."""
        logger.info("Using high-performance import strategy")
        
        try:
            # Use robust Excel importer with full processing
            from robust_excel_importer_working import RobustExcelImporter
            
            importer = RobustExcelImporter()
            if not importer.initialize_staging():
                raise Exception("Failed to initialize database")
            
            success, result = importer.process_excel_file(file_path)
            
            if not success:
                raise Exception(f"Import failed: {result}")
            
            return {
                'success': True,
                'strategy': 'high_performance',
                'result': result,
                'system_info': self.compatibility_manager.get_compatibility_report()
            }
            
        except Exception as e:
            logger.error(f"High-performance import failed: {e}")
            return self._import_fallback(file_path, str(e))
    
    def _import_fallback(self, file_path: str, original_error: str) -> Dict[str, Any]:
        """Fallback import method using basic pandas functionality."""
        logger.warning(f"Using fallback import method due to: {original_error}")

        try:
            import pandas as pd
            from locale_compatibility_handler import SafeLocaleContext

            # Use safe locale context for consistent data parsing
            with SafeLocaleContext():
                # Basic Excel reading with minimal dependencies
                excel_file = pd.ExcelFile(file_path)
                sheets_processed = []
                total_rows = 0

                for sheet_name in excel_file.sheet_names:
                    try:
                        df = pd.read_excel(excel_file, sheet_name=sheet_name)
                        total_rows += len(df)
                        sheets_processed.append(sheet_name)
                        logger.info(f"Read sheet '{sheet_name}': {len(df)} rows")
                    except Exception as e:
                        logger.warning(f"Could not read sheet '{sheet_name}': {e}")

                return {
                    'success': True,
                    'strategy': 'fallback',
                    'result': {
                        'sheets_processed': sheets_processed,
                        'total_rows_read': total_rows,
                        'note': 'Fallback mode - data read but not imported to database'
                    },
                    'original_error': original_error,
                    'system_info': self.compatibility_manager.get_compatibility_report()
                }

        except Exception as e:
            logger.error(f"Fallback import also failed: {e}")
            return {
                'success': False,
                'error': f"All import methods failed. Original: {original_error}, Fallback: {e}",
                'system_info': self.compatibility_manager.get_compatibility_report()
            }

# Main import function for compatibility with existing code
def import_excel_cross_system_compatible(file_path: str) -> Dict[str, Any]:
    """
    Cross-system compatible Excel import function.
    
    This function provides a drop-in replacement for existing Excel import
    functionality with enhanced cross-system compatibility.
    
    Args:
        file_path (str): Path to Excel file to import
        
    Returns:
        dict: Import results with system compatibility information
    """
    importer = CrossSystemExcelImporter()
    return importer.import_excel_cross_system(file_path)

if __name__ == "__main__":
    # Command-line interface for testing
    if len(sys.argv) != 2:
        print("Usage: python cross_system_excel_importer.py <excel_file>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    result = import_excel_cross_system_compatible(file_path)
    
    print("=" * 60)
    print("CROSS-SYSTEM EXCEL IMPORT RESULT")
    print("=" * 60)
    print(f"Success: {result['success']}")
    
    if result['success']:
        print(f"Strategy: {result['strategy']}")
        print(f"Result: {result['result']}")
    else:
        print(f"Error: {result['error']}")
    
    print("\nSystem Information:")
    system_info = result['system_info']
    print(f"Platform: {system_info['system_info']['platform']}")
    print(f"Memory: {system_info['system_info']['memory_gb']:.1f} GB")
    print(f"Processing Strategy: {system_info['processing_strategy']}")
    
    if system_info['recommendations']:
        print("\nRecommendations:")
        for rec in system_info['recommendations']:
            print(f"- {rec}")
