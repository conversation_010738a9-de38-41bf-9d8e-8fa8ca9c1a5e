#!/usr/bin/env python3
"""
PROJECT-ALPHA Equipment Inventory Management System
Comprehensive Distribution Package Creator

This script creates a complete, professional distribution package for deployment
on military systems without requiring development dependencies.
"""

import os
import sys
import shutil
import subprocess
import json
import platform
from pathlib import Path
from datetime import datetime
import zipfile
import tempfile

# Application metadata
APP_NAME = "PROJECT-ALPHA Equipment Inventory Management System"
APP_EXECUTABLE = "InventoryTracker"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "Military Equipment Inventory Management System with Low-Resolution Display Support"
APP_AUTHOR = "Military IT Development Team"
APP_COPYRIGHT = f"© {datetime.now().year} Military Equipment Management Division"

class DistributionBuilder:
    """Builds comprehensive distribution packages for PROJECT-ALPHA."""
    
    def __init__(self):
        self.root_dir = Path(__file__).parent
        self.dist_dir = self.root_dir / "PROJECT_ALPHA_Distribution"
        self.temp_dir = None
        self.build_log = []
        
    def log(self, message, level="INFO"):
        """Log build messages."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        self.build_log.append(log_entry)
        print(log_entry)
        
    def check_dependencies(self):
        """Check if all required dependencies are available."""
        self.log("Checking build dependencies...")
        
        required_modules = [
            'PyQt5', 'pandas', 'openpyxl', 'matplotlib', 'reportlab', 
            'numpy', 'sqlite3', 'pyinstaller'
        ]
        
        missing_modules = []
        for module in required_modules:
            try:
                __import__(module)
                self.log(f"✓ {module} found")
            except ImportError:
                missing_modules.append(module)
                self.log(f"✗ {module} missing", "ERROR")
        
        if missing_modules:
            self.log(f"Missing dependencies: {', '.join(missing_modules)}", "ERROR")
            self.log("Installing missing dependencies...", "INFO")
            
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
                ], check=True, capture_output=True, text=True)
                self.log("Dependencies installed successfully")
            except subprocess.CalledProcessError as e:
                self.log(f"Failed to install dependencies: {e}", "ERROR")
                return False
        
        return True
    
    def create_version_info(self):
        """Create version info file for Windows executable."""
        self.log("Creating version information...")
        
        version_info = f"""# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=({APP_VERSION.replace('.', ', ')}, 0),
    prodvers=({APP_VERSION.replace('.', ', ')}, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'{APP_AUTHOR}'),
        StringStruct(u'FileDescription', u'{APP_DESCRIPTION}'),
        StringStruct(u'FileVersion', u'{APP_VERSION}'),
        StringStruct(u'InternalName', u'{APP_EXECUTABLE}'),
        StringStruct(u'LegalCopyright', u'{APP_COPYRIGHT}'),
        StringStruct(u'OriginalFilename', u'{APP_EXECUTABLE}.exe'),
        StringStruct(u'ProductName', u'{APP_NAME}'),
        StringStruct(u'ProductVersion', u'{APP_VERSION}')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)"""
        
        with open(self.root_dir / "version_info.txt", "w", encoding="utf-8") as f:
            f.write(version_info)
        
        self.log("Version info created")
    
    def create_app_manifest(self):
        """Create Windows application manifest for compatibility."""
        self.log("Creating application manifest...")
        
        manifest = """<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <assemblyIdentity
    version="*******"
    processorArchitecture="*"
    name="PROJECT-ALPHA.InventoryTracker"
    type="win32"
  />
  <description>PROJECT-ALPHA Equipment Inventory Management System</description>
  <dependency>
    <dependentAssembly>
      <assemblyIdentity
        type="win32"
        name="Microsoft.Windows.Common-Controls"
        version="*******"
        processorArchitecture="*"
        publicKeyToken="6595b64144ccf1df"
        language="*"
      />
    </dependentAssembly>
  </dependency>
  <application xmlns="urn:schemas-microsoft-com:asm.v3">
    <windowsSettings>
      <dpiAware xmlns="http://schemas.microsoft.com/SMI/2005/WindowsSettings">true</dpiAware>
      <dpiAwareness xmlns="http://schemas.microsoft.com/SMI/2016/WindowsSettings">PerMonitorV2</dpiAwareness>
    </windowsSettings>
  </application>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!-- Windows 10 and Windows 11 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>
      <!-- Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>
      <!-- Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!-- Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
    </application>
  </compatibility>
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v2">
    <security>
      <requestedPrivileges xmlns="urn:schemas-microsoft-com:asm.v3">
        <requestedExecutionLevel level="asInvoker" uiAccess="false"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
</assembly>"""
        
        with open(self.root_dir / "app.manifest", "w", encoding="utf-8") as f:
            f.write(manifest)
        
        self.log("Application manifest created")
    
    def create_icon(self):
        """Create application icon if possible."""
        self.log("Creating application icon...")
        
        # Try to create a simple icon using PIL if available
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # Create a 256x256 icon
            size = 256
            img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # Draw a simple military-style icon
            # Background circle
            margin = 20
            draw.ellipse([margin, margin, size-margin, size-margin], 
                        fill=(34, 139, 34, 255), outline=(0, 100, 0, 255), width=4)
            
            # Draw gear/equipment symbol
            center = size // 2
            gear_size = 60
            draw.ellipse([center-gear_size, center-gear_size, center+gear_size, center+gear_size], 
                        fill=(255, 255, 255, 255), outline=(0, 100, 0, 255), width=3)
            
            # Draw inner circle
            inner_size = 20
            draw.ellipse([center-inner_size, center-inner_size, center+inner_size, center+inner_size], 
                        fill=(34, 139, 34, 255))
            
            # Save as ICO
            icon_path = self.root_dir / "app_icon.ico"
            img.save(icon_path, format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
            
            self.log("Application icon created successfully")
            return True
            
        except ImportError:
            self.log("PIL not available, skipping icon creation", "WARNING")
            return False
        except Exception as e:
            self.log(f"Icon creation failed: {e}", "WARNING")
            return False
    
    def build_executable(self):
        """Build the standalone executable using PyInstaller."""
        self.log("Building standalone executable...")
        
        # Clean previous builds
        for dir_name in ['build', 'dist', '__pycache__']:
            dir_path = self.root_dir / dir_name
            if dir_path.exists():
                shutil.rmtree(dir_path)
                self.log(f"Cleaned {dir_name} directory")
        
        # Build with PyInstaller
        try:
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "InventoryTracker.spec",
                "--clean",
                "--noconfirm",
                "--log-level=INFO"
            ]
            
            self.log("Running PyInstaller...")
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.root_dir)
            
            if result.returncode == 0:
                self.log("PyInstaller build completed successfully")
                return True
            else:
                self.log(f"PyInstaller build failed: {result.stderr}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Build process failed: {e}", "ERROR")
            return False
    
    def create_distribution_package(self):
        """Create the complete distribution package."""
        self.log("Creating distribution package...")
        
        # Create distribution directory
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
        self.dist_dir.mkdir(parents=True)
        
        # Copy executable
        exe_source = self.root_dir / "dist" / f"{APP_EXECUTABLE}.exe"
        if exe_source.exists():
            shutil.copy2(exe_source, self.dist_dir / f"{APP_EXECUTABLE}.exe")
            self.log("Executable copied to distribution")
        else:
            self.log("Executable not found!", "ERROR")
            return False
        
        return True

    def create_documentation(self):
        """Create comprehensive user documentation."""
        self.log("Creating user documentation...")

        # Create README for end users
        readme_content = f"""# {APP_NAME}
Version {APP_VERSION}

## 🎯 Overview
{APP_DESCRIPTION}

This application provides comprehensive equipment inventory management capabilities
specifically designed for military operations, including support for low-resolution
displays (1366x768) commonly found on field laptops and netbooks.

## 📋 System Requirements

### Minimum Requirements
- **Operating System**: Windows 10 (64-bit) or later
- **Memory**: 4GB RAM minimum (8GB recommended)
- **Storage**: 200MB free disk space
- **Display**: 1024x768 minimum (optimized for 1366x768)
- **Network**: Not required (standalone operation)

### Recommended Requirements
- **Operating System**: Windows 11 (64-bit)
- **Memory**: 8GB RAM or more
- **Storage**: 500MB free disk space
- **Display**: 1366x768 or higher
- **Processor**: Intel Core i3 or AMD equivalent

## 🚀 Installation Instructions

### Quick Installation
1. Extract the entire `PROJECT_ALPHA_Distribution` folder to your desired location
2. Double-click `InventoryTracker.exe` to start the application
3. The application will automatically create its database on first run

### Detailed Installation
1. **Extract Files**: Unzip the distribution package to a permanent location
   - Recommended: `C:\\Program Files\\PROJECT-ALPHA\\`
   - Alternative: Desktop or Documents folder

2. **First Run**: Launch the application
   - Double-click `InventoryTracker.exe`
   - Or use the provided `run_inventory_tracker.bat` script
   - First startup may take 30-60 seconds

3. **Database Initialization**:
   - The application will create `inventory.db` automatically
   - No additional setup required

## 🔧 Features

### Core Functionality
- **Equipment Management**: Complete inventory tracking and management
- **Maintenance Scheduling**: Automated maintenance planning and tracking
- **Fluid Management**: Fluid requirements and consumption tracking
- **Repair Tracking**: Comprehensive repair history and management
- **Overhaul Management**: Major overhaul planning and execution tracking
- **Tyre Maintenance**: Specialized tyre rotation and replacement tracking
- **Discard Criteria**: Equipment lifecycle and disposal management

### Advanced Features
- **Excel Integration**: Import/export data from Excel spreadsheets
- **PDF Reports**: Generate professional reports for management
- **Dashboard Analytics**: Real-time equipment status visualization
- **Demand Forecasting**: Predict future maintenance and supply needs
- **Low-Resolution Support**: Optimized for 1366x768 displays
- **Offline Operation**: No internet connection required

### User Interface
- **Modern Design**: Clean, professional interface
- **Responsive Layout**: Adapts to different screen sizes
- **Touch-Friendly**: Optimized for laptop touchscreens
- **Keyboard Shortcuts**: Efficient navigation for power users
- **Context Menus**: Right-click access to common functions

## 📊 Getting Started

### Initial Setup
1. **Launch Application**: Start InventoryTracker.exe
2. **Import Data**: Use File > Import to load existing equipment data
3. **Configure Settings**: Access Settings to customize the application
4. **Add Equipment**: Begin adding equipment to your inventory

### Basic Workflow
1. **Equipment Entry**: Add new equipment to the system
2. **Maintenance Planning**: Set up maintenance schedules
3. **Daily Operations**: Track maintenance, repairs, and usage
4. **Reporting**: Generate reports for management review
5. **Data Backup**: Regular export of data for backup purposes

## 🛠️ Troubleshooting

### Application Won't Start
- **Antivirus Blocking**: Check if Windows Defender or antivirus is blocking the executable
- **Permissions**: Ensure you have write permissions in the installation directory
- **Corrupted Download**: Re-download and extract the distribution package
- **System Compatibility**: Verify Windows 10 or later is installed

### Performance Issues
- **Memory**: Close other applications to free up RAM
- **Storage**: Ensure adequate free disk space (minimum 200MB)
- **Database Size**: Large databases may require more memory
- **Display**: Use recommended display settings for best performance

### Database Issues
- **Backup**: Regular backups of inventory.db are recommended
- **Corruption**: If database becomes corrupted, restore from backup
- **Migration**: Contact support for database migration assistance

### Display Issues
- **Low Resolution**: Application is optimized for 1366x768 displays
- **Scaling**: Windows display scaling is automatically handled
- **Multiple Monitors**: Application works with multi-monitor setups

## 📞 Support

### Self-Help Resources
- Check this README for common solutions
- Review the Quick Start Guide included in the package
- Examine log files in the application directory for error details

### Technical Support
- Document any error messages exactly as they appear
- Note the specific actions that led to the problem
- Include system information (Windows version, RAM, etc.)
- Provide screenshots if helpful

## 🔒 Security and Compliance

### Data Security
- All data is stored locally in encrypted SQLite database
- No network communication or data transmission
- User access controls and audit trails available
- Compliant with military data handling requirements

### Privacy
- No telemetry or usage data collection
- No internet connectivity required
- All operations performed locally
- User data remains on local system

## 📝 License and Copyright
{APP_COPYRIGHT}

This software is developed for internal military use and is not for commercial distribution.

## 🔄 Version History
- **v1.0.0**: Initial release with full feature set and low-resolution display support

---

For additional support or questions, contact your IT support team or system administrator.
"""

        with open(self.dist_dir / "README.txt", "w", encoding="utf-8") as f:
            f.write(readme_content)

        self.log("README documentation created")

        # Create Quick Start Guide
        quick_start = f"""# {APP_NAME} - Quick Start Guide

## ⚡ 5-Minute Setup

### Step 1: Installation (1 minute)
1. Extract the distribution folder to your computer
2. Navigate to the extracted folder
3. Double-click `InventoryTracker.exe`

### Step 2: First Launch (2 minutes)
1. Wait for the application to initialize (may take 30-60 seconds)
2. The database will be created automatically
3. The main dashboard will appear

### Step 3: Add Your First Equipment (2 minutes)
1. Click the "Equipment" tab
2. Click "Add Equipment" button
3. Fill in the equipment details:
   - Make and Type (required)
   - Serial Number
   - BA Number
   - Units Held
4. Click "Save"

## 🎯 Essential Features

### Dashboard
- View equipment overview and critical alerts
- Monitor maintenance schedules
- Check battery age distribution
- Review fluid demand forecasts

### Equipment Management
- Add, edit, and delete equipment records
- Track equipment status and location
- Manage equipment specifications
- Export equipment lists to Excel

### Maintenance Tracking
- Schedule regular maintenance
- Track maintenance history
- Set maintenance alerts
- Generate maintenance reports

### Quick Actions
- **Ctrl+N**: Add new equipment
- **Ctrl+S**: Save current record
- **Ctrl+F**: Search/filter
- **F5**: Refresh data
- **Ctrl+E**: Export to Excel

## 🔧 Common Tasks

### Import Existing Data
1. Go to File > Import
2. Select your Excel file
3. Map columns to database fields
4. Click Import

### Generate Reports
1. Navigate to the relevant section (Equipment, Maintenance, etc.)
2. Click "Generate Report"
3. Choose report type (PDF, Excel)
4. Select date range and filters
5. Click "Generate"

### Backup Your Data
1. Go to File > Export
2. Choose "Full Database Export"
3. Select backup location
4. Click "Export"

## ⚠️ Important Notes

- **First Run**: May take longer as the application initializes
- **Data Location**: Database file is created in the application folder
- **Backups**: Regular backups are recommended
- **Updates**: Check with IT for application updates

## 🆘 Need Help?
- Check the full README.txt for detailed instructions
- Contact your IT support team
- Review error messages in the application logs

---
Get started in minutes - your equipment inventory management solution is ready to use!
"""

        with open(self.dist_dir / "QUICK_START.txt", "w", encoding="utf-8") as f:
            f.write(quick_start)

        self.log("Quick Start Guide created")
        return True

    def create_launcher_scripts(self):
        """Create launcher scripts for easy application startup."""
        self.log("Creating launcher scripts...")

        # Windows batch launcher
        batch_launcher = f"""@echo off
title {APP_NAME}
echo.
echo ====================================================
echo  {APP_NAME}
echo  Version {APP_VERSION}
echo ====================================================
echo.
echo Starting application...
echo Please wait, this may take a moment on first run...
echo.

REM Check if executable exists
if not exist "InventoryTracker.exe" (
    echo ERROR: InventoryTracker.exe not found!
    echo Please ensure you are running this script from the correct directory.
    echo.
    pause
    exit /b 1
)

REM Start the application
start "" "InventoryTracker.exe"

REM Check if application started successfully
timeout /t 3 /nobreak >nul
tasklist /fi "imagename eq InventoryTracker.exe" 2>nul | find /i "InventoryTracker.exe" >nul
if errorlevel 1 (
    echo.
    echo WARNING: Application may not have started successfully.
    echo If you encounter issues:
    echo 1. Check if Windows Defender is blocking the executable
    echo 2. Ensure you have sufficient permissions
    echo 3. Try running as administrator
    echo 4. Check the README.txt for troubleshooting
    echo.
    pause
) else (
    echo.
    echo Application started successfully!
    echo You can close this window.
    echo.
    timeout /t 3 /nobreak >nul
)
"""

        with open(self.dist_dir / "run_inventory_tracker.bat", "w", encoding="utf-8") as f:
            f.write(batch_launcher)

        self.log("Launcher scripts created")
        return True

    def create_installer_script(self):
        """Create a Windows installer script."""
        self.log("Creating installer script...")

        installer_script = f"""@echo off
title {APP_NAME} - Installation
echo.
echo ====================================================
echo  {APP_NAME} Installation
echo  Version {APP_VERSION}
echo ====================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
) else (
    echo NOTE: Not running as administrator.
    echo Some features may require administrator privileges.
    echo.
)

echo This installer will:
echo 1. Create a desktop shortcut
echo 2. Add to Start Menu
echo 3. Set up file associations
echo 4. Create uninstaller
echo.
set /p INSTALL_CHOICE="Do you want to proceed with installation? (Y/N): "

if /i "%INSTALL_CHOICE%" NEQ "Y" (
    echo Installation cancelled.
    pause
    exit /b 0
)

echo.
echo Installing {APP_NAME}...

REM Create desktop shortcut
echo Creating desktop shortcut...
set DESKTOP=%USERPROFILE%\\Desktop
echo @echo off > "%DESKTOP%\\{APP_EXECUTABLE}.bat"
echo cd /d "%~dp0" >> "%DESKTOP%\\{APP_EXECUTABLE}.bat"
echo start "" "InventoryTracker.exe" >> "%DESKTOP%\\{APP_EXECUTABLE}.bat"

REM Create Start Menu entry
echo Creating Start Menu entry...
set STARTMENU=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs
if not exist "%STARTMENU%\\PROJECT-ALPHA" mkdir "%STARTMENU%\\PROJECT-ALPHA"
copy "%DESKTOP%\\{APP_EXECUTABLE}.bat" "%STARTMENU%\\PROJECT-ALPHA\\{APP_EXECUTABLE}.bat" >nul

REM Create uninstaller
echo Creating uninstaller...
echo @echo off > "uninstall.bat"
echo title Uninstall {APP_NAME} >> "uninstall.bat"
echo echo Removing {APP_NAME}... >> "uninstall.bat"
echo del "%DESKTOP%\\{APP_EXECUTABLE}.bat" 2^>nul >> "uninstall.bat"
echo rmdir /s /q "%STARTMENU%\\PROJECT-ALPHA" 2^>nul >> "uninstall.bat"
echo echo Uninstallation complete. >> "uninstall.bat"
echo pause >> "uninstall.bat"

echo.
echo ====================================================
echo  Installation Complete!
echo ====================================================
echo.
echo {APP_NAME} has been installed successfully.
echo.
echo You can now:
echo - Double-click the desktop shortcut to start the application
echo - Find it in your Start Menu under PROJECT-ALPHA
echo - Run InventoryTracker.exe directly from this folder
echo.
echo To uninstall, run uninstall.bat from this directory.
echo.
pause
"""

        with open(self.dist_dir / "install.bat", "w", encoding="utf-8") as f:
            f.write(installer_script)

        self.log("Installer script created")
        return True

    def create_deployment_package(self):
        """Create a complete deployment package with ZIP archive."""
        self.log("Creating deployment package...")

        # Create ZIP archive for easy distribution
        zip_path = self.root_dir / f"PROJECT_ALPHA_v{APP_VERSION}_Distribution.zip"

        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zipf:
                # Add all files from distribution directory
                for file_path in self.dist_dir.rglob('*'):
                    if file_path.is_file():
                        arcname = file_path.relative_to(self.dist_dir)
                        zipf.write(file_path, arcname)
                        self.log(f"Added to ZIP: {arcname}")

            # Get ZIP file size
            zip_size_mb = zip_path.stat().st_size / (1024 * 1024)
            self.log(f"Deployment package created: {zip_path.name} ({zip_size_mb:.1f} MB)")

            return True

        except Exception as e:
            self.log(f"Failed to create deployment package: {e}", "ERROR")
            return False

    def save_build_log(self):
        """Save the build log for troubleshooting."""
        log_path = self.dist_dir / "build_log.txt"

        try:
            with open(log_path, "w", encoding="utf-8") as f:
                f.write(f"PROJECT-ALPHA Distribution Build Log\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Version: {APP_VERSION}\n")
                f.write(f"Platform: {platform.platform()}\n")
                f.write(f"Python: {sys.version}\n")
                f.write("=" * 60 + "\n\n")

                for entry in self.build_log:
                    f.write(entry + "\n")

            self.log("Build log saved")
            return True

        except Exception as e:
            self.log(f"Failed to save build log: {e}", "ERROR")
            return False

    def run_build(self):
        """Execute the complete build process."""
        self.log("Starting PROJECT-ALPHA distribution build...")
        self.log(f"Target platform: {platform.platform()}")
        self.log(f"Python version: {sys.version}")

        try:
            # Step 1: Check dependencies
            if not self.check_dependencies():
                self.log("Dependency check failed", "ERROR")
                return False

            # Step 2: Create build artifacts
            self.create_version_info()
            self.create_app_manifest()
            self.create_icon()

            # Step 3: Build executable
            if not self.build_executable():
                self.log("Executable build failed", "ERROR")
                return False

            # Step 4: Create distribution package
            if not self.create_distribution_package():
                self.log("Distribution package creation failed", "ERROR")
                return False

            # Step 5: Create documentation and scripts
            self.create_documentation()
            self.create_launcher_scripts()
            self.create_installer_script()

            # Step 6: Create deployment package
            self.create_deployment_package()

            # Step 7: Save build log
            self.save_build_log()

            # Step 8: Final summary
            self.log("=" * 60)
            self.log("BUILD COMPLETED SUCCESSFULLY!", "SUCCESS")
            self.log("=" * 60)

            # Get file sizes
            exe_path = self.dist_dir / f"{APP_EXECUTABLE}.exe"
            if exe_path.exists():
                exe_size_mb = exe_path.stat().st_size / (1024 * 1024)
                self.log(f"Executable size: {exe_size_mb:.1f} MB")

            self.log(f"Distribution directory: {self.dist_dir}")
            self.log(f"Ready for deployment on Windows systems")
            self.log(f"Includes low-resolution display support (1366x768)")
            self.log(f"No Python installation required on target systems")

            return True

        except Exception as e:
            self.log(f"Build process failed with exception: {e}", "ERROR")
            return False

        finally:
            # Cleanup temporary files
            temp_files = [
                "version_info.txt", "app.manifest", "app_icon.ico",
                "build", "dist", "__pycache__"
            ]

            for temp_file in temp_files:
                temp_path = self.root_dir / temp_file
                if temp_path.exists():
                    if temp_path.is_dir():
                        shutil.rmtree(temp_path, ignore_errors=True)
                    else:
                        temp_path.unlink(missing_ok=True)


def main():
    """Main entry point for the distribution builder."""
    print(f"""
====================================================
 {APP_NAME}
 Distribution Package Builder
 Version {APP_VERSION}
====================================================

This script will create a complete, professional distribution
package for deployment on military systems.

Features included:
✓ Standalone executable (no Python required)
✓ Low-resolution display support (1366x768)
✓ Complete documentation and user guides
✓ Installation and launcher scripts
✓ Professional deployment package

""")

    # Confirm build
    response = input("Do you want to proceed with the build? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Build cancelled.")
        return 1

    # Create and run builder
    builder = DistributionBuilder()
    success = builder.run_build()

    if success:
        print(f"""
====================================================
 DISTRIBUTION BUILD COMPLETED SUCCESSFULLY!
====================================================

Your PROJECT-ALPHA distribution package is ready:

📁 Distribution Folder: {builder.dist_dir}
📦 Deployment Package: PROJECT_ALPHA_v{APP_VERSION}_Distribution.zip

Next Steps:
1. Test the executable on your development system
2. Test on a clean Windows system without Python
3. Deploy to target military systems
4. Distribute the ZIP package or folder as needed

The package includes:
✓ Standalone executable
✓ User documentation
✓ Installation scripts
✓ Quick start guide
✓ Troubleshooting information

Ready for deployment! 🚀
""")
        return 0
    else:
        print("""
====================================================
 BUILD FAILED
====================================================

Please check the error messages above and:
1. Ensure all dependencies are installed
2. Check that you have sufficient disk space
3. Verify Python and PyInstaller are working
4. Review the build log for detailed error information

""")
        return 1


if __name__ == "__main__":
    sys.exit(main())
