# 🛠️ Army Equipment Inventory Management System

A comprehensive desktop application for managing military equipment inventory, maintenance schedules, fluid requirements, repairs, and operational tracking. Built with PyQt5 and SQLite for robust equipment fleet management.

![Python](https://img.shields.io/badge/python-3.8+-blue.svg)
![PyQt5](https://img.shields.io/badge/PyQt5-5.15+-green.svg)
![SQLite](https://img.shields.io/badge/SQLite-3.0+-orange.svg)
![License](https://img.shields.io/badge/license-Internal%20Use-red.svg)

## 📋 Table of Contents

- [Features](#-features)
- [Prerequisites](#-prerequisites)
- [Quick Start](#-quick-start)
- [Installation](#-installation)
- [Running the Application](#-running-the-application)
- [Project Structure](#-project-structure)
- [Usage Guide](#-usage-guide)
- [Data Import/Export](#-data-importexport)
- [Development](#-development)
- [Troubleshooting](#-troubleshooting)
- [Contributing](#-contributing)
- [License](#-license)

## 🚀 Features

### Core Functionality
- **Equipment Management**: Track all equipment details including serial numbers, make/type, meterage, and operational status
- **Fluid Management**: Monitor fluid requirements, usage patterns, and service schedules
- **Maintenance Tracking**: Schedule and track maintenance activities with automated due date notifications
- **Repair History**: Comprehensive repair documentation and historical tracking
- **Tyre Maintenance**: Advanced tyre rotation and condition monitoring
- **Overhaul Management**: Track major overhauls and service intervals
- **Demand Forecasting**: Predict future fluid and component requirements based on usage patterns

### Data Management
- **Excel Import**: Bulk import equipment data from Excel files
- **Export Capabilities**: Export demand forecasts to Excel/CSV formats
- **Real-time Dashboard**: Visual overview of key metrics and maintenance alerts
- **Discrepancy Reporting**: Document and resolve inventory discrepancies
- **BA Number Management**: Track and manage BA (Base Authorization) numbers

### User Interface
- **Modern GUI**: Clean, intuitive interface built with PyQt5
- **Tabbed Navigation**: Organized workflow across different functional areas
- **Real-time Updates**: Automatic data refresh and status updates
- **Interactive Charts**: Visual representations of data and trends

## 🔧 Prerequisites

### System Requirements
- **Operating System**: Windows 10/11, macOS 10.14+, or Linux (Ubuntu 18.04+)
- **Python**: Version 3.9 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: At least 100MB free space for application and database

### Required Knowledge
- Basic understanding of inventory management
- Familiarity with Excel for data import/export
- Basic computer navigation skills

## ⚡ Quick Start

For experienced developers who want to get started immediately:

```bash
# Clone the repository
git clone https://github.com/army-equipment-management/ARMY.git
cd ARMY

# Install dependencies
pip install -r requirements.txt

# Run the application
python main.py
```

## 🛠️ Installation

### Step 1: Clone the Repository
```bash
git clone https://github.com/rohit7nkuamr/ARMY.git
cd ARMY
```

### Step 2: Set Up Python Environment (Recommended)
```bash
# Create a virtual environment
python -m venv venv

# Activate the virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### Step 3: Install Dependencies
```bash
# Install all required packages
pip install -r requirements.txt

# Alternatively, install packages individually:
pip install PyQt5==5.15.10
pip install matplotlib==3.7.1
pip install pandas==1.5.3
pip install openpyxl==3.1.0
pip install numpy==1.24.0
pip install python-dateutil
pip install psycopg2-binary==2.9.10
```

### Step 4: Verify Installation
```bash
# Check if all imports work correctly
python -c "import PyQt5; import pandas; import matplotlib; print('All dependencies installed successfully!')"
```

## 🏃 Running the Application

### GUI Mode (Recommended)
```bash
python main.py
```

### Console Mode (Headless)
```bash
python app.py
```

### Development Mode with Debugging
```bash
# Run with verbose logging
python main.py --debug
```

## 📁 Project Structure

```
ARMY/
├── 📄 main.py                    # Main GUI application entry point
├── 📄 app.py                     # Console/headless application
├── 📄 config.py                  # Application configuration
├── 📄 database.py                # Database connection and management
├── 📄 models.py                  # Data models and database operations
├── 📄 excel_importer.py          # Excel import functionality
├── 📄 utils.py                   # Utility functions
├── 📄 sample_data.py             # Sample data generation
├── 📄 requirements.txt           # Python dependencies
├── 📄 pyproject.toml             # Project configuration
├── 📄 icon.ico                   # Application icon
├── 📄 inventory.db               # SQLite database (created automatically)
│
├── 📁 ui/                        # User interface components
│   ├── 📄 dashboard_widget.py    # Dashboard and overview
│   ├── 📄 equipment_widget.py    # Equipment management
│   ├── 📄 fluids_widget.py       # Fluid management
│   ├── 📄 maintenance_widget.py  # Maintenance tracking
│   ├── 📄 repairs_widget.py      # Repair management
│   ├── 📄 tyre_maintenance_widget.py # Tyre maintenance
│   ├── 📄 discard_criteria_widget.py # Discard criteria
│   ├── 📄 demand_forecast_widget.py # Demand forecasting
│   ├── 📄 dialogs.py             # Dialog windows
│   ├── 📄 custom_widgets.py      # Reusable UI components
│   └── 📄 styles.py              # UI styling
│
├── 📁 migrations/                # Database migration scripts
├── 📁 resources/                 # Application resources
├── 📁 build/                     # Build artifacts
├── 📁 dist/                      # Distribution files
└── 📁 installer_output/          # Installer files
```

## 📖 Usage Guide

### 1. First Launch
- The application will automatically create a SQLite database (`inventory.db`)
- Navigate through tabs to explore different functionality
- Start by adding equipment in the Equipment tab

### 2. Equipment Management
- **Add Equipment**: Use the "Add Equipment" button to register new equipment
- **Edit Equipment**: Double-click on any equipment row to modify details
- **Track Usage**: Monitor KM/HRS usage patterns
- **BA Numbers**: Assign and track Base Authorization numbers

### 3. Maintenance Tracking
- **Schedule Maintenance**: Set up maintenance schedules (TM-I, TM-II, TM-III, TM-IV)
- **Track Overhauls**: Monitor major overhaul requirements (OH-I, OH-II)
- **Due Date Alerts**: Receive notifications for upcoming maintenance

### 4. Fluid Management
- **Monitor Fluid Levels**: Track fluid types and usage patterns
- **Service Schedules**: Manage fluid change intervals
- **Demand Forecasting**: Predict future fluid requirements

### 5. Dashboard Overview
- **Equipment Status**: Real-time overview of equipment health
- **Maintenance Alerts**: Upcoming maintenance requirements
- **Usage Statistics**: KM/HRS usage patterns and trends
- **Fluid Consumption**: Consumption patterns and forecasts

## 🔧 Maintenance Changes

### Recent Maintenance System Enhancements

The maintenance system has been significantly enhanced with new features for maintenance completion, predictive scheduling, and fluid demand forecasting. Here are the key improvements:

#### 1. **Maintenance Completion System**
- **Complete Button**: Added green "Complete" button for all maintenance records (regardless of status)
- **Flexible Completion**: Users can complete maintenance early, late, or re-complete as needed
- **Completion Dialog**: Comprehensive form with:
  - Completion date selection
  - Editable meterage tracking
  - Notes/observations field
  - Equipment meterage sync option

#### 2. **Enhanced Alert System (5-Level)**
- **Normal**: 6+ months (Green)
- **Info**: 3-6 months (Blue)
- **Warning**: 1-3 months (Orange)
- **Critical**: 0-30 days (Red)
- **Waiting Decision**: Past due date (Grey) - replaces "Overdue"

#### 3. **Intelligent Status Management**
- **Waiting Decision**: Equipment past due date shows grey "waiting decision" status
- **Completed Status**: Only shows "completed" when actually completed via Complete button
- **Database Status Priority**: Actual completion status takes precedence over calculated status
- **Smart Filtering**: Updated filter options include "Waiting Decision" instead of "Overdue"

#### 4. **Automated Fluid Demand Forecasting**
- **Post-Completion Creation**: Automatically create fluid demand forecasts after maintenance completion
- **Fiscal Year Selection**: Choose fiscal year for demand forecast creation
- **Fluid Selection Dialog**: Scrollable interface to select relevant fluids
- **Capacity-Based Calculations**: Demand requirements based on fluid capacity values

#### 5. **Individual 10% Top-up Control**
- **Per-Fluid Selection**: Choose 10% top-up inclusion for each fluid individually
- **Smart UI**: Top-up option only enabled for fluids with defined top-up values
- **Dynamic Labels**: Shows actual top-up amounts (e.g., "+1.5 Ltr")
- **Flexible Calculations**: 
  - With top-up: `(capacity + top_up) × units_held`
  - Without top-up: `capacity × units_held`

#### 6. **Enhanced Data Tracking**
- **Completion Notes**: Track observations and issues found during maintenance
- **Meterage Sync**: Automatically update equipment meterage from completion data
- **Audit Trail**: Clear tracking of who completed maintenance and when
- **Enhanced Remarks**: Demand forecasts show whether top-up was included

#### 7. **Improved Export Functionality**
- **Equipment Display**: PDF/Excel exports now show BA numbers instead of "N/A"
- **Proper Equipment Names**: Uses `utils.format_equipment_display()` for consistent formatting
- **Updated Column Headers**: "Equipment" instead of "Name of vehs/eqpts"
- **Accurate Data**: All export formats show proper equipment and fluid data

#### 8. **Database Enhancements**
- **New Columns Added**:
  - `completion_notes` - User observations
  - `status` - Maintenance status tracking
  - `completed_by` - Who completed the maintenance
  - `actual_completion_date` - When it was actually completed
  - `completion_meterage` - Meterage at completion
- **Migration Script**: `update_maintenance_schema.py` for existing databases

#### 9. **User Experience Improvements**
- **Always-Enabled Complete Button**: Gives users full control over completion timing
- **Visual Status Indicators**: Color-coded status display throughout the interface
- **Improved Tooltips**: Better information display in selection dialogs
- **Connected Controls**: Smart enable/disable logic for related UI elements

#### 10. **Technical Improvements**
- **Error Handling**: Better error handling for equipment object vs dictionary issues
- **Logging**: Enhanced logging for debugging and audit purposes
- **Code Organization**: Cleaner separation of UI, business logic, and data layers
- **Performance**: Optimized database queries for better performance

### Usage Examples

**Completing Maintenance:**
1. Select any maintenance record (regardless of status)
2. Click green "Complete" button
3. Fill completion date, meterage, and notes
4. Select fluids for demand forecast
5. Choose 10% top-up for individual fluids
6. Submit to create completion record and demand forecasts

**Fluid Demand Creation:**
- Engine Oil: ✓ Selected, ✓ Include 10% top-up → Gets capacity + top-up
- Brake Fluid: ✓ Selected, ✗ No top-up → Gets only capacity  
- Gear Oil: ✗ Not selected → Not included in forecast

These enhancements provide a comprehensive maintenance management system with predictive capabilities and flexible user control.

## 📊 Data Import/Export

### Excel Import
1. Navigate to **File > Import Excel...**
2. Select your Excel file (.xls or .xlsx)
3. The system will automatically parse and import data
4. Review import results in the status message

### Excel Export
1. Navigate to **File > Export** and choose data type:
   - Fluid Demand
   - Tyre Demand
   - Battery Demand
2. Select export format (Excel or CSV)
3. Choose destination folder

### Supported Excel Format
- **Headers**: Row 1-3 (multi-level headers supported)
- **Data**: Starting from row 4
- **Columns**: Equipment details, maintenance dates, fluid specifications
- **Date Formats**: Multiple date formats supported (DD/MM/YYYY, MM/DD/YYYY, etc.)

## 🔧 Development

### Setting Up Development Environment
```bash
# Clone the repository
git clone https://github.com/army-equipment-management/ARMY.git
cd ARMY

# Create development environment
python -m venv dev_env
source dev_env/bin/activate  # or dev_env\Scripts\activate on Windows

# Install development dependencies
pip install -r requirements.txt
pip install pytest  # for testing
```

### Running Tests
```bash
# Run all tests
python -m pytest

# Run specific test files
python test_model_updates.py
python test_ba_numbers.py
```

### Code Style
- Follow PEP 8 guidelines
- Use meaningful variable names
- Add docstrings to all functions and classes
- Maintain consistent indentation (4 spaces)

### Database Schema
The application uses SQLite with the following main tables:
- `equipment` - Equipment inventory
- `fluids` - Fluid specifications and usage
- `maintenance` - Maintenance schedules and records
- `repairs` - Repair history and details
- `overhauls` - Major overhaul tracking
- `tyre_maintenance` - Tyre maintenance records
- `discard_criteria` - Equipment discard criteria

## 🐛 Troubleshooting

### Common Issues

#### Application Won't Start
```bash
# Check Python version
python --version

# Verify dependencies
pip list | grep PyQt5
pip list | grep pandas

# Try reinstalling dependencies
pip install --force-reinstall -r requirements.txt
```

#### Database Errors
```bash
# Check if database file exists
ls -la inventory.db

# Reset database (WARNING: This will delete all data)
rm inventory.db
python main.py  # Will recreate the database
```

#### Import Errors
- Ensure Excel file format is correct (.xls or .xlsx)
- Check that Excel file has proper headers
- Verify date formats in Excel are consistent

#### Performance Issues
- Close unnecessary applications
- Ensure adequate RAM (8GB recommended)
- Check available disk space

### Getting Help
1. Check the application logs in `inventory_app.log`
2. Review error messages in the status bar
3. Consult the troubleshooting section above
4. Contact the development team

## 🤝 Contributing

### How to Contribute
1. Fork the repository
2. Create a feature branch (`git checkout -b feature/new-feature`)
3. Commit your changes (`git commit -am 'Add new feature'`)
4. Push to the branch (`git push origin feature/new-feature`)
5. Create a Pull Request

### Development Guidelines
- Write clear commit messages
- Add tests for new functionality
- Update documentation as needed
- Follow the existing code style
- Test thoroughly before submitting

## 📝 License

This software is provided for internal evaluation and use only. All rights reserved.

---

## 🆘 Support

For questions, issues, or support requests:
- Check the [Troubleshooting](#-troubleshooting) section
- Review the application logs
- Contact the development team

**Version**: 1.0.0  
**Last Updated**: June 2023  
**Developed by**: Army Equipment Management Team