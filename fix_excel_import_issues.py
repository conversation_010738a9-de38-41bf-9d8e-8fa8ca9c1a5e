#!/usr/bin/env python3
"""
Fix Excel Import Issues - PROJECT-ALPHA
Comprehensive solution for Excel import problems including column mapping issues,
unnamed columns, cross-system compatibility, and import completion monitoring.
"""

import os
import sys
import logging
import traceback
from typing import Dict, Any, Optional
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('excel_import_fix')

class ExcelImportFixer:
    """Comprehensive Excel import issue fixer."""
    
    def __init__(self):
        self.session_id = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.results = {
            'session_id': self.session_id,
            'timestamp': datetime.now().isoformat(),
            'diagnosis': {},
            'import_results': {},
            'fixes_applied': [],
            'success': False,
            'recommendations': []
        }
    
    def fix_excel_import(self, file_path: str, use_enhanced_mapping: bool = True, 
                        use_progress_monitoring: bool = True) -> Dict[str, Any]:
        """
        Fix Excel import issues using comprehensive solution.
        
        Args:
            file_path (str): Path to Excel file
            use_enhanced_mapping (bool): Use enhanced column mapping
            use_progress_monitoring (bool): Enable progress monitoring
            
        Returns:
            dict: Results of fix attempt
        """
        logger.info(f"Starting Excel import fix for: {file_path}")
        logger.info(f"Session ID: {self.session_id}")
        
        try:
            # Step 1: Diagnose issues
            logger.info("Step 1: Diagnosing Excel import issues...")
            self._diagnose_issues(file_path)
            
            # Step 2: Apply fixes based on diagnosis
            logger.info("Step 2: Applying fixes...")
            self._apply_fixes(file_path, use_enhanced_mapping, use_progress_monitoring)
            
            # Step 3: Verify results
            logger.info("Step 3: Verifying import results...")
            self._verify_results()
            
            # Step 4: Generate recommendations
            logger.info("Step 4: Generating recommendations...")
            self._generate_recommendations()
            
            self.results['success'] = True
            logger.info("Excel import fix completed successfully")
            
        except Exception as e:
            error_msg = f"Excel import fix failed: {e}"
            logger.error(error_msg)
            logger.error(f"Traceback: {traceback.format_exc()}")
            
            self.results['error'] = error_msg
            self.results['traceback'] = traceback.format_exc()
            self.results['success'] = False
        
        return self.results
    
    def _diagnose_issues(self, file_path: str):
        """Diagnose Excel import issues."""
        try:
            from excel_import_diagnostic_tool import ExcelImportDiagnosticTool
            
            diagnostic_tool = ExcelImportDiagnosticTool()
            diagnosis = diagnostic_tool.diagnose_excel_file(file_path)
            
            self.results['diagnosis'] = diagnosis
            
            # Log key findings
            issues_found = diagnosis.get('issues_found', [])
            if issues_found:
                logger.warning(f"Diagnosis found {len(issues_found)} issues:")
                for issue in issues_found:
                    logger.warning(f"  - {issue}")
            else:
                logger.info("Diagnosis found no major issues")
            
            # Check for specific issues
            file_analysis = diagnosis.get('file_analysis', {})
            unnamed_columns = file_analysis.get('total_unnamed_columns', 0)
            if unnamed_columns > 0:
                self.results['fixes_applied'].append(f"Detected {unnamed_columns} unnamed columns - will use enhanced mapping")
            
            column_analysis = diagnosis.get('column_analysis', {})
            if not column_analysis.get('mapping_success', True):
                self.results['fixes_applied'].append("Column mapping issues detected - will use enhanced importer")
            
        except Exception as e:
            logger.error(f"Diagnosis failed: {e}")
            self.results['diagnosis'] = {'error': str(e)}
    
    def _apply_fixes(self, file_path: str, use_enhanced_mapping: bool, use_progress_monitoring: bool):
        """Apply fixes based on diagnosis."""
        try:
            # Determine best import strategy
            diagnosis = self.results.get('diagnosis', {})
            file_analysis = diagnosis.get('file_analysis', {})
            system_analysis = diagnosis.get('system_analysis', {})
            
            unnamed_columns = file_analysis.get('total_unnamed_columns', 0)
            has_compatibility_issues = len(system_analysis.get('compatibility_issues', [])) > 0
            
            # Choose import method
            if unnamed_columns > 5 or use_enhanced_mapping:
                logger.info("Using enhanced column mapping importer...")
                self._use_enhanced_mapping_importer(file_path)
                self.results['fixes_applied'].append("Used enhanced column mapping importer")
                
            elif has_compatibility_issues:
                logger.info("Using cross-system compatible importer...")
                self._use_cross_system_importer(file_path)
                self.results['fixes_applied'].append("Used cross-system compatible importer")
                
            else:
                logger.info("Using standard robust importer with progress monitoring...")
                self._use_robust_importer_with_monitoring(file_path)
                self.results['fixes_applied'].append("Used robust importer with progress monitoring")
            
        except Exception as e:
            logger.error(f"Failed to apply fixes: {e}")
            raise
    
    def _use_enhanced_mapping_importer(self, file_path: str):
        """Use enhanced column mapping importer."""
        try:
            from enhanced_column_mapping_importer import import_excel_with_enhanced_mapping
            
            logger.info("Starting enhanced column mapping import...")
            result = import_excel_with_enhanced_mapping(file_path)
            
            self.results['import_results'] = result
            
            if result.get('success', False):
                logger.info(f"Enhanced mapping import successful: {result.get('summary', 'No summary')}")
            else:
                logger.error(f"Enhanced mapping import failed: {result.get('error', 'Unknown error')}")
                raise Exception(f"Enhanced mapping import failed: {result.get('error', 'Unknown error')}")
            
        except ImportError:
            logger.warning("Enhanced column mapping importer not available, falling back to robust importer")
            self._use_robust_importer_with_monitoring(file_path)
    
    def _use_cross_system_importer(self, file_path: str):
        """Use cross-system compatible importer."""
        try:
            from cross_system_excel_importer import import_excel_cross_system_compatible
            
            logger.info("Starting cross-system compatible import...")
            result = import_excel_cross_system_compatible(file_path)
            
            self.results['import_results'] = result
            
            if result.get('success', False):
                logger.info(f"Cross-system import successful: {result.get('strategy', 'unknown strategy')}")
            else:
                logger.error(f"Cross-system import failed: {result.get('error', 'Unknown error')}")
                raise Exception(f"Cross-system import failed: {result.get('error', 'Unknown error')}")
            
        except ImportError:
            logger.warning("Cross-system importer not available, falling back to robust importer")
            self._use_robust_importer_with_monitoring(file_path)
    
    def _use_robust_importer_with_monitoring(self, file_path: str):
        """Use robust importer with progress monitoring."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            
            logger.info("Starting robust import with progress monitoring...")
            
            importer = RobustExcelImporter()
            if not importer.initialize_staging():
                raise Exception("Failed to initialize database")
            
            success, result = importer.process_excel_file(file_path)
            
            self.results['import_results'] = {
                'success': success,
                'result': result,
                'method': 'robust_importer_with_monitoring'
            }
            
            if success:
                logger.info(f"Robust import successful: {result}")
            else:
                logger.error(f"Robust import failed: {result}")
                raise Exception(f"Robust import failed: {result}")
            
        except Exception as e:
            logger.error(f"Robust importer failed: {e}")
            raise
    
    def _verify_results(self):
        """Verify import results and data integrity."""
        try:
            import sqlite3
            import config

            if not os.path.exists(config.DB_PATH):
                logger.warning("Database file not found - cannot verify results")
                return

            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()

            # Count imported records
            cursor.execute("SELECT COUNT(*) FROM equipment WHERE is_active = 1")
            equipment_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM fluids")
            fluids_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM maintenance")
            maintenance_count = cursor.fetchone()[0]

            # Check for recent imports
            cursor.execute("""
                SELECT COUNT(*) FROM equipment
                WHERE remarks LIKE '%Imported from%'
                AND equipment_id > (SELECT COALESCE(MAX(equipment_id), 0) - 100 FROM equipment)
            """)
            recent_imports = cursor.fetchone()[0]

            # Check for overhaul alerts
            cursor.execute("""
                SELECT COUNT(*) as alert_count
                FROM overhauls o
                JOIN equipment e ON o.equipment_id = e.equipment_id
                WHERE o.status IN ('Critical', 'Warning', 'Overdue') AND e.is_active = 1
            """)
            overhaul_alerts = cursor.fetchone()[0]

            # Check for maintenance alerts
            cursor.execute("""
                SELECT COUNT(*) as alert_count
                FROM maintenance m
                JOIN equipment e ON m.equipment_id = e.equipment_id
                WHERE m.due_date < date('now') AND e.is_active = 1
            """)
            maintenance_alerts = cursor.fetchone()[0]

            # Check data integrity - equipment with BA numbers
            cursor.execute("SELECT COUNT(*) FROM equipment WHERE ba_number IS NOT NULL AND ba_number != ''")
            equipment_with_ba = cursor.fetchone()[0]

            # Check data integrity - equipment with fluids
            cursor.execute("""
                SELECT COUNT(DISTINCT e.equipment_id)
                FROM equipment e
                JOIN fluids f ON e.equipment_id = f.equipment_id
                WHERE e.is_active = 1
            """)
            equipment_with_fluids = cursor.fetchone()[0]

            conn.close()

            verification_results = {
                'equipment_count': equipment_count,
                'fluids_count': fluids_count,
                'maintenance_count': maintenance_count,
                'recent_imports': recent_imports,
                'overhaul_alerts': overhaul_alerts,
                'maintenance_alerts': maintenance_alerts,
                'equipment_with_ba': equipment_with_ba,
                'equipment_with_fluids': equipment_with_fluids,
                'data_integrity_score': 0.0
            }

            # Calculate data integrity score
            if equipment_count > 0:
                ba_ratio = equipment_with_ba / equipment_count
                fluid_ratio = equipment_with_fluids / equipment_count
                verification_results['data_integrity_score'] = (ba_ratio + fluid_ratio) / 2 * 100

            self.results['verification'] = verification_results

            logger.info(f"Verification results: {verification_results}")

            # Check if import was successful
            if equipment_count == 0:
                logger.warning("No equipment found in database - import may have failed")
            elif recent_imports == 0:
                logger.warning("No recent imports detected - import may not have completed")
            else:
                logger.info(f"Import verification successful: {recent_imports} recent imports found")

            # Check data integrity
            integrity_score = verification_results['data_integrity_score']
            if integrity_score < 50:
                logger.warning(f"Low data integrity score: {integrity_score:.1f}% - check column mapping")
            else:
                logger.info(f"Good data integrity score: {integrity_score:.1f}%")

        except Exception as e:
            logger.error(f"Verification failed: {e}")
            self.results['verification'] = {'error': str(e)}
    
    def _generate_recommendations(self):
        """Generate recommendations based on results."""
        recommendations = []
        
        # Check import results
        import_results = self.results.get('import_results', {})
        if not import_results.get('success', False):
            recommendations.append("Import failed - check error logs and try alternative import method")
            recommendations.append("Consider cleaning Excel file to remove unnamed columns and formatting issues")
        
        # Check verification
        verification = self.results.get('verification', {})
        equipment_count = verification.get('equipment_count', 0)
        recent_imports = verification.get('recent_imports', 0)
        
        if equipment_count == 0:
            recommendations.append("No equipment found in database - verify Excel file contains valid equipment data")
            recommendations.append("Check Excel file format and column headers")
        
        if recent_imports == 0 and equipment_count > 0:
            recommendations.append("No recent imports detected but equipment exists - may be updating existing records")
        
        # Check diagnosis issues
        diagnosis = self.results.get('diagnosis', {})
        issues_found = diagnosis.get('issues_found', [])
        
        if any('unnamed' in issue.lower() for issue in issues_found):
            recommendations.append("Clean Excel file by removing empty columns and fixing merged headers")
            recommendations.append("Use enhanced column mapping for better handling of malformed headers")
        
        if any('compatibility' in issue.lower() for issue in issues_found):
            recommendations.append("Use cross-system compatible importer for deployment environments")
            recommendations.append("Ensure all required libraries are installed")
        
        # Dashboard recommendations
        if equipment_count > 0 and verification.get('fluids_count', 0) == 0:
            recommendations.append("Equipment imported but no fluids - check fluid column mapping")
        
        if not recommendations:
            recommendations.append("Import appears successful - check dashboard for updated equipment data")
            recommendations.append("Run overhaul status recalculation if alerts are not showing")
        
        self.results['recommendations'] = recommendations
    
    def generate_fix_report(self) -> str:
        """Generate human-readable fix report."""
        report_lines = [
            "=" * 80,
            "PROJECT-ALPHA EXCEL IMPORT FIX REPORT",
            "=" * 80,
            f"Session ID: {self.session_id}",
            f"Timestamp: {self.results['timestamp']}",
            f"Success: {'✅ YES' if self.results['success'] else '❌ NO'}",
            ""
        ]
        
        # Fixes applied
        fixes_applied = self.results.get('fixes_applied', [])
        if fixes_applied:
            report_lines.extend([
                "🔧 FIXES APPLIED:",
                ""
            ])
            for fix in fixes_applied:
                report_lines.append(f"  ✅ {fix}")
            report_lines.append("")
        
        # Import results
        import_results = self.results.get('import_results', {})
        if import_results:
            report_lines.extend([
                "📊 IMPORT RESULTS:",
                f"  Success: {import_results.get('success', 'Unknown')}",
                f"  Method: {import_results.get('method', 'Unknown')}",
                ""
            ])
            
            result_data = import_results.get('result', {})
            if isinstance(result_data, dict):
                for key, value in result_data.items():
                    if key.startswith('total_'):
                        report_lines.append(f"  {key.replace('total_', '').title()}: {value}")
            report_lines.append("")
        
        # Verification
        verification = self.results.get('verification', {})
        if verification and 'error' not in verification:
            report_lines.extend([
                "✅ VERIFICATION:",
                f"  Equipment count: {verification.get('equipment_count', 0)}",
                f"  Fluids count: {verification.get('fluids_count', 0)}",
                f"  Maintenance count: {verification.get('maintenance_count', 0)}",
                f"  Recent imports: {verification.get('recent_imports', 0)}",
                ""
            ])
        
        # Recommendations
        recommendations = self.results.get('recommendations', [])
        if recommendations:
            report_lines.extend([
                "💡 RECOMMENDATIONS:",
                ""
            ])
            for rec in recommendations:
                report_lines.append(f"  📝 {rec}")
            report_lines.append("")
        
        # Error details
        if not self.results['success']:
            error = self.results.get('error', 'Unknown error')
            report_lines.extend([
                "❌ ERROR DETAILS:",
                f"  {error}",
                ""
            ])
        
        return "\n".join(report_lines)

def fix_excel_import_issues(file_path: str, output_report: Optional[str] = None, 
                           use_enhanced_mapping: bool = True, 
                           use_progress_monitoring: bool = True) -> Dict[str, Any]:
    """
    Fix Excel import issues with comprehensive solution.
    
    Args:
        file_path (str): Path to Excel file
        output_report (str, optional): Path to save fix report
        use_enhanced_mapping (bool): Use enhanced column mapping
        use_progress_monitoring (bool): Enable progress monitoring
        
    Returns:
        dict: Fix results
    """
    fixer = ExcelImportFixer()
    results = fixer.fix_excel_import(file_path, use_enhanced_mapping, use_progress_monitoring)
    
    # Generate and save report
    report = fixer.generate_fix_report()
    
    if output_report:
        try:
            with open(output_report, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"Fix report saved: {output_report}")
        except Exception as e:
            logger.error(f"Failed to save report: {e}")
    
    print(report)
    return results

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='PROJECT-ALPHA Excel Import Issue Fixer')
    parser.add_argument('excel_file', help='Path to Excel file to import')
    parser.add_argument('--output', '-o', help='Output file for fix report')
    parser.add_argument('--no-enhanced-mapping', action='store_true', 
                       help='Disable enhanced column mapping')
    parser.add_argument('--no-progress-monitoring', action='store_true', 
                       help='Disable progress monitoring')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.excel_file):
        print(f"❌ Excel file not found: {args.excel_file}")
        sys.exit(1)
    
    print("🔧 Starting Excel import issue fix...")
    
    results = fix_excel_import_issues(
        args.excel_file,
        args.output,
        use_enhanced_mapping=not args.no_enhanced_mapping,
        use_progress_monitoring=not args.no_progress_monitoring
    )
    
    # Exit with appropriate code
    if results['success']:
        print("\n✅ Excel import fix completed successfully")
        sys.exit(0)
    else:
        print("\n❌ Excel import fix failed - see report for details")
        sys.exit(1)
