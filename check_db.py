import sqlite3
import os
from config import get_db_path

# Get the correct database path
db_path = get_db_path()
print(f"Database path: {db_path}")
print("Database exists:", os.path.exists(db_path))

conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Check tables
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
print("Tables:", [t[0] for t in tables])

# If equipment table exists, check data
if any('equipment' in str(t) for t in tables):
    cursor.execute("SELECT COUNT(*) FROM equipment")
    count = cursor.fetchone()[0]
    print(f"Equipment records: {count}")
    
    if count > 0:
        # Check column structure first
        cursor.execute("PRAGMA table_info(equipment)")
        columns = cursor.fetchall()
        print("\nEquipment table columns:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # Check KPL data
        cursor.execute("SELECT ba_number, srtr_ltr_hr, rd_towing_ltr_100km FROM equipment LIMIT 10")
        rows = cursor.fetchall()
        print("\nSample KPL data:")
        for row in rows:
            print(f"  {row[0]}: SRTR={row[1]}, RD/TOWING={row[2]} -> Display: {row[1]}/{row[2]}")
        
        # Check for problematic values
        cursor.execute("SELECT ba_number, srtr_ltr_hr, rd_towing_ltr_100km FROM equipment WHERE rd_towing_ltr_100km > 1000 LIMIT 5")
        problem_rows = cursor.fetchall()
        if problem_rows:
            print("\nProblematic high RD/TOWING values:")
            for row in problem_rows:
                print(f"  {row[0]}: SRTR={row[1]}, RD/TOWING={row[2]}")
else:
    print("No equipment table found")

conn.close()
