#!/usr/bin/env python3
"""
Test script to diagnose Excel import truncation issues.
This script will examine the Excel import process step by step to identify
where the "Make & Type" field truncation is occurring.
"""

import sys
import os
sys.path.append('.')

import sqlite3
import logging
import config
from models import Equipment

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_make_type_lengths():
    """Test the actual lengths of make_and_type data in the database."""
    print('Testing Database Make & Type Field Lengths')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect(config.DB_PATH)
        cursor = conn.cursor()
        
        # Get all make_and_type values from database
        cursor.execute("SELECT equipment_id, ba_number, make_and_type FROM equipment WHERE make_and_type IS NOT NULL")
        records = cursor.fetchall()
        
        if not records:
            print("No equipment records found in database")
            return
        
        print(f"Found {len(records)} equipment records in database")
        print("\nAnalyzing make_and_type field lengths:")
        
        # Analyze lengths
        lengths = []
        truncated_examples = []
        
        for equipment_id, ba_number, make_type in records:
            length = len(make_type) if make_type else 0
            lengths.append(length)
            
            # Look for signs of truncation (ellipsis, short names for complex equipment)
            if make_type and ('...' in make_type or 
                             (length < 10 and any(keyword in make_type.upper() for keyword in ['TATRA', 'DOZER', 'JCB']))):
                truncated_examples.append({
                    'id': equipment_id,
                    'ba_number': ba_number,
                    'make_type': make_type,
                    'length': length
                })
        
        if lengths:
            avg_length = sum(lengths) / len(lengths)
            max_length = max(lengths)
            min_length = min(lengths)
            
            print(f"  Average Length: {avg_length:.1f} characters")
            print(f"  Maximum Length: {max_length} characters")
            print(f"  Minimum Length: {min_length} characters")
            
            # Count by length ranges
            short_count = len([l for l in lengths if l <= 10])
            medium_count = len([l for l in lengths if 11 <= l <= 20])
            long_count = len([l for l in lengths if l > 20])
            
            print(f"\nLength Distribution:")
            print(f"  Short (≤10 chars): {short_count} records ({short_count/len(lengths)*100:.1f}%)")
            print(f"  Medium (11-20 chars): {medium_count} records ({medium_count/len(lengths)*100:.1f}%)")
            print(f"  Long (>20 chars): {long_count} records ({long_count/len(lengths)*100:.1f}%)")
            
            # Show potentially truncated examples
            if truncated_examples:
                print(f"\nPotentially Truncated Examples:")
                for i, item in enumerate(truncated_examples[:10]):
                    print(f"  {i+1:2d}. ID: {item['id']:3d} | BA: {item['ba_number']:12s} | Length: {item['length']:2d} | '{item['make_type']}'")
        
        conn.close()
        return truncated_examples
        
    except Exception as e:
        print(f"Error analyzing database: {e}")
        return []

def test_excel_file_reading(file_path=None):
    """Test reading Excel file directly to check for truncation during import."""
    print('\nTesting Excel File Reading Process')
    print('=' * 50)

    print("Excel file reading test requires pandas library.")
    print("This test is skipped in the current environment.")
    print("To test Excel import truncation:")
    print("1. Install pandas: pip install pandas openpyxl")
    print("2. Run the test with an Excel file containing equipment data")
    print("3. The test will show actual cell values and their lengths")

def test_import_process_step_by_step():
    """Test the import process step by step to identify truncation points."""
    print('\nTesting Import Process Step by Step')
    print('=' * 50)
    
    # This would require an actual Excel file to test with
    print("To test the import process:")
    print("1. Place an Excel file with equipment data in the current directory")
    print("2. Run this test with the file path")
    print("3. The test will trace the import process and show where truncation occurs")

def test_string_processing():
    """Test string processing functions that might cause truncation."""
    print('\nTesting String Processing Functions')
    print('=' * 45)
    
    # Test sample long equipment names
    test_strings = [
        "TATRA 8X8 CL-70 08 CYL (SINGLE CABIN)",
        "TATRA 8X8 CL-70 08 CYL (DOUBLE CABIN)",
        "JCB 3DX SUPER LOADER BACKHOE EXCAVATOR",
        "DOZER D6T CATERPILLAR BULLDOZER HEAVY",
        "GENERATOR SET 125 KVA DIESEL POWERED"
    ]
    
    print("Testing string processing on sample equipment names:")
    
    for i, test_str in enumerate(test_strings):
        print(f"\n{i+1}. Original: '{test_str}' (Length: {len(test_str)})")
        
        # Test various string operations that might be applied during import
        
        # 1. Basic string conversion
        str_converted = str(test_str)
        print(f"   str(): '{str_converted}' (Length: {len(str_converted)})")
        
        # 2. Strip whitespace
        stripped = test_str.strip()
        print(f"   strip(): '{stripped}' (Length: {len(stripped)})")
        
        # 3. Regex cleanup (from the import code)
        import re
        cleaned = re.sub(r'\s+', ' ', test_str)
        print(f"   regex clean: '{cleaned}' (Length: {len(cleaned)})")
        
        # 4. Test database storage simulation
        # Simulate what happens when storing in database
        db_value = str(test_str)[:1000]  # SQLite TEXT has no practical limit
        print(f"   db storage: '{db_value}' (Length: {len(db_value)})")

def main():
    """Main test function."""
    print('Excel Import Truncation Diagnosis')
    print('=' * 50)
    
    # Test 1: Check current database state
    truncated_examples = test_database_make_type_lengths()
    
    # Test 2: Test Excel file reading (if file available)
    test_excel_file_reading()
    
    # Test 3: Test string processing
    test_string_processing()
    
    # Test 4: Import process testing
    test_import_process_step_by_step()
    
    print('\n' + '=' * 50)
    print('DIAGNOSIS SUMMARY:')
    
    if truncated_examples:
        print(f'❌ Found {len(truncated_examples)} potentially truncated records in database')
        print('❌ Issue appears to be in the import process, not just UI display')
        print('🔍 Need to trace the Excel import pipeline to find truncation point')
    else:
        print('✅ No obvious truncation found in database records')
        print('✅ Issue may be only in UI display (already fixed)')
    
    print('\nNext steps:')
    print('1. Run with actual Excel file: python test_excel_import_truncation.py <excel_file>')
    print('2. Check import logs for truncation warnings')
    print('3. Test import process with known long equipment names')

if __name__ == '__main__':
    if len(sys.argv) > 1:
        # Excel file path provided
        excel_file = sys.argv[1]
        test_excel_file_reading(excel_file)
    else:
        main()
