"""
Main entry point for PROJECT-ALPHA Equipment Inventory Management System
"""
import sys
import os

# Add project root to Python path to import from root-level modules
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def main():
    """Main entry point for the application."""
    try:
        # Import the main function from the root-level main.py
        from main import main as app_main
        return app_main()
    except Exception as e:
        print(f"Error starting application: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 