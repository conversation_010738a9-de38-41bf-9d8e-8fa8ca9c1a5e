#!/usr/bin/env python3
"""
Database Debug Script for InventoryTracker Executable
Tests database initialization and migration in executable environment
"""

import sys
import os
import logging
import traceback
from pathlib import Path

# Configure logging for debugging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('db_debug_executable.log')
    ]
)

logger = logging.getLogger('db_debug')

def test_environment():
    """Test the executable environment"""
    logger.info("=== ENVIRONMENT TEST ===")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"sys.frozen: {getattr(sys, 'frozen', False)}")
    logger.info(f"sys.executable: {sys.executable}")
    
    if hasattr(sys, '_MEIPASS'):
        logger.info(f"PyInstaller temp dir: {getattr(sys, '_MEIPASS', 'Not found')}")
    
    logger.info(f"Current working directory: {os.getcwd()}")
    logger.info(f"sys.path: {sys.path[:5]}...")  # First 5 entries
    
    # Test module imports
    modules_to_test = [
        'config', 'database', 'database_ready', 'policy_service', 
        'policy_models', 'models', 'utils'
    ]
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            logger.info(f"✅ Module '{module_name}' imported successfully")
        except ImportError as e:
            logger.error(f"❌ Failed to import '{module_name}': {e}")
        except Exception as e:
            logger.error(f"❌ Unexpected error importing '{module_name}': {e}")

def test_config_paths():
    """Test config path generation"""
    logger.info("=== CONFIG PATHS TEST ===")
    try:
        import config
        
        logger.info(f"App name: {config.APP_NAME}")
        logger.info(f"App version: {config.APP_VERSION}")
        
        # Test path functions
        try:
            user_dir = config.get_user_writable_dir()
            logger.info(f"✅ User writable dir: {user_dir}")
            logger.info(f"   Exists: {os.path.exists(user_dir)}")
        except Exception as e:
            logger.error(f"❌ Error getting user writable dir: {e}")
        
        try:
            db_path = config.get_db_path()
            logger.info(f"✅ Database path: {db_path}")
            logger.info(f"   Exists: {os.path.exists(db_path)}")
            logger.info(f"   Parent dir exists: {os.path.exists(os.path.dirname(db_path))}")
        except Exception as e:
            logger.error(f"❌ Error getting database path: {e}")
        
        try:
            log_path = config.get_log_path()
            logger.info(f"✅ Log path: {log_path}")
            logger.info(f"   Exists: {os.path.exists(log_path)}")
        except Exception as e:
            logger.error(f"❌ Error getting log path: {e}")
            
    except Exception as e:
        logger.error(f"❌ Failed to import or test config: {e}")

def test_database_connection():
    """Test basic database connection"""
    logger.info("=== DATABASE CONNECTION TEST ===")
    try:
        import config
        import sqlite3
        
        db_path = config.get_db_path()
        logger.info(f"Testing connection to: {db_path}")
        
        # Create parent directory if it doesn't exist
        parent_dir = os.path.dirname(db_path)
        if not os.path.exists(parent_dir):
            os.makedirs(parent_dir)
            logger.info(f"Created parent directory: {parent_dir}")
        
        # Test basic SQLite connection
        conn = sqlite3.connect(db_path, timeout=30.0)
        conn.execute("SELECT 1").fetchone()
        conn.close()
        logger.info("✅ Basic SQLite connection successful")
        
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        logger.error(traceback.format_exc())

def test_database_initialization():
    """Test database initialization process"""
    logger.info("=== DATABASE INITIALIZATION TEST ===")
    try:
        from database_ready import ensure_db_ready
        
        logger.info("Starting database initialization...")
        success, first_run, corruption_recovered, error_message = ensure_db_ready()
        
        logger.info(f"Initialization result:")
        logger.info(f"  Success: {success}")
        logger.info(f"  First run: {first_run}")
        logger.info(f"  Corruption recovered: {corruption_recovered}")
        logger.info(f"  Error message: {error_message}")
        
        if success:
            logger.info("✅ Database initialization successful")
        else:
            logger.error("❌ Database initialization failed")
            
    except Exception as e:
        logger.error(f"❌ Database initialization test failed: {e}")
        logger.error(traceback.format_exc())

def test_database_schema():
    """Test database schema and tables"""
    logger.info("=== DATABASE SCHEMA TEST ===")
    try:
        import config
        import sqlite3
        
        db_path = config.get_db_path()
        conn = sqlite3.connect(db_path)
        
        # Get list of tables
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        logger.info(f"Found {len(tables)} tables: {tables}")
        
        # Test each table
        for table in tables:
            try:
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                logger.info(f"  Table '{table}': {len(columns)} columns")
            except Exception as e:
                logger.error(f"  ❌ Error checking table '{table}': {e}")
        
        conn.close()
        logger.info("✅ Database schema test completed")
        
    except Exception as e:
        logger.error(f"❌ Database schema test failed: {e}")
        logger.error(traceback.format_exc())

def main():
    """Run all tests"""
    logger.info("Starting InventoryTracker Database Debug Tests")
    logger.info("=" * 60)
    
    try:
        test_environment()
        test_config_paths()
        test_database_connection()
        test_database_initialization()
        test_database_schema()
        
        logger.info("=" * 60)
        logger.info("All tests completed. Check the log above for issues.")
        
    except Exception as e:
        logger.error(f"Fatal error during testing: {e}")
        logger.error(traceback.format_exc())
    
    input("Press Enter to exit...")

if __name__ == "__main__":
    main() 