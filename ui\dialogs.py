"""Dialog classes for the equipment inventory application."""
from PyQt5.QtWidgets import (QDialog, QDialogButtonBox, QVBoxLayout, QHBoxLayout,
                           QFormLayout, QLabel, QLineEdit, QDateEdit, QSpinBox,
                           QDoubleSpinBox, QComboBox, QCheckBox, QMessageBox,
                           QGroupBox, QPushButton, QTextEdit, QScrollArea, QWidget,
                           QRadioButton, QButtonGroup, QListWidget)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QIcon, QDoubleValidator, QFont
from datetime import date
import utils
import database
import models

class EquipmentDialog(QDialog):
    """Dialog for adding or editing equipment."""
    
    def __init__(self, equipment=None, parent=None):
        super().__init__(parent)
        self.equipment = equipment
        self.setWindowTitle("Add Equipment" if equipment is None else "Edit Equipment")
        self.resize(450, 550)
        
        self.setup_ui()
        self.populate_fields()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        
        # Form layout for fields
        form_layout = QFormLayout()
        
        # Serial Number
        self.serial_number_edit = QLineEdit()
        form_layout.addRow("Serial Number:", self.serial_number_edit)
        
        # Make and Type
        self.make_type_edit = QLineEdit()
        form_layout.addRow("Make and Type:", self.make_type_edit)
        
        # Units Held
        self.units_held_spin = QSpinBox()
        self.units_held_spin.setMinimum(1)
        self.units_held_spin.setMaximum(999)
        form_layout.addRow("Units Held:", self.units_held_spin)
        
        # Vintage Years
        self.vintage_years_spin = QDoubleSpinBox()
        self.vintage_years_spin.setMinimum(0)
        self.vintage_years_spin.setMaximum(100)
        self.vintage_years_spin.setDecimals(3)
        form_layout.addRow("Vintage Years:", self.vintage_years_spin)
        
        # Meterage KMs
        self.meterage_spin = QDoubleSpinBox()
        self.meterage_spin.setMinimum(0)
        self.meterage_spin.setMaximum(999999)
        self.meterage_spin.setDecimals(3)
        form_layout.addRow("Meterage (KMs):", self.meterage_spin)
        
        # KM/Hrs Run Previous Month
        self.prev_month_spin = QDoubleSpinBox()
        self.prev_month_spin.setMinimum(0)
        self.prev_month_spin.setMaximum(999999)
        self.prev_month_spin.setDecimals(3)
        form_layout.addRow("KM/Hrs Run Previous Month:", self.prev_month_spin)
        
        # KM/Hrs Run Current Month
        self.current_month_spin = QDoubleSpinBox()
        self.current_month_spin.setMinimum(0)
        self.current_month_spin.setMaximum(999999)
        self.current_month_spin.setDecimals(3)
        form_layout.addRow("KM/Hrs Run Current Month:", self.current_month_spin)
        
        # Is Active
        self.is_active_check = QCheckBox("Equipment is currently active")
        self.is_active_check.setChecked(True)
        form_layout.addRow("", self.is_active_check)
        
        # Remarks
        self.remarks_edit = QTextEdit()
        self.remarks_edit.setMaximumHeight(100)
        form_layout.addRow("Remarks:", self.remarks_edit)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def populate_fields(self):
        """Populate dialog fields with equipment data if editing."""
        if not self.equipment:
            return
        
        self.serial_number_edit.setText(self.equipment.get('SerialNumber', ''))
        self.make_type_edit.setText(self.equipment.get('MakeAndType', ''))
        self.units_held_spin.setValue(int(self.equipment.get('UnitsHeld', 1)))
        self.vintage_years_spin.setValue(float(self.equipment.get('VintageYears', 0)))
        self.meterage_spin.setValue(float(self.equipment.get('MeterageKMs', 0)))
        self.prev_month_spin.setValue(float(self.equipment.get('KMHrsRunPreviousMonth', 0)))
        self.current_month_spin.setValue(float(self.equipment.get('KMHrsRunCurrentMonth', 0)))
        self.is_active_check.setChecked(bool(self.equipment.get('IsActive', True)))
        self.remarks_edit.setText(self.equipment.get('Remarks', ''))
    
    def get_equipment_data(self):
        """Get equipment data from dialog fields."""
        equipment = {}
        if self.equipment:
            equipment['EquipmentID'] = self.equipment.get('EquipmentID')
        
        equipment['SerialNumber'] = self.serial_number_edit.text()
        equipment['MakeAndType'] = self.make_type_edit.text()
        equipment['UnitsHeld'] = self.units_held_spin.value()
        equipment['VintageYears'] = self.vintage_years_spin.value()
        equipment['MeterageKMs'] = self.meterage_spin.value()
        equipment['KMHrsRunPreviousMonth'] = self.prev_month_spin.value()
        equipment['KMHrsRunCurrentMonth'] = self.current_month_spin.value()
        equipment['IsActive'] = self.is_active_check.isChecked()
        equipment['Remarks'] = self.remarks_edit.toPlainText()
        
        return equipment
    
    def validate(self):
        """Validate the input data."""
        if not self.serial_number_edit.text().strip():
            QMessageBox.warning(self, "Validation Error", "Serial Number is required.")
            return False
        
        if not self.make_type_edit.text().strip():
            QMessageBox.warning(self, "Validation Error", "Make and Type is required.")
            return False
        
        return True
    
    def accept(self):
        """Handle dialog acceptance."""
        if not self.validate():
            return
        
        super().accept()


class FluidDialog(QDialog):
    """Dialog for adding or editing fluid records."""
    
    def __init__(self, fluid=None, equipment_list=None, parent=None):
        super().__init__(parent)
        self.fluid = fluid
        self.equipment_list = equipment_list or []
        self.setWindowTitle("Add Fluid" if fluid is None else "Edit Fluid")
        self.resize(450, 600)
        
        self.setup_ui()
        self.populate_fields()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        
        # Form layout for fields
        form_layout = QFormLayout()
        
        # Equipment
        self.equipment_combo = QComboBox()
        for equip in self.equipment_list:
            from utils import format_equipment_for_dropdown
            display_text = format_equipment_for_dropdown(equip)
            equipment_id = equip.get('equipment_id') or equip.get('EquipmentID')
            self.equipment_combo.addItem(display_text, equipment_id)
        form_layout.addRow("Equipment:", self.equipment_combo)
        
        # Fluid Type
        self.fluid_type_edit = QLineEdit()
        form_layout.addRow("Fluid Type:", self.fluid_type_edit)
        
        # Sub Type
        self.sub_type_edit = QLineEdit()
        form_layout.addRow("Sub Type:", self.sub_type_edit)
        
        # Accounting Unit
        self.accounting_unit_combo = QComboBox()
        self.accounting_unit_combo.addItems(["Ltr", "Kg"])
        form_layout.addRow("Accounting Unit:", self.accounting_unit_combo)
        
        # Capacity
        self.capacity_spin = QDoubleSpinBox()
        self.capacity_spin.setMinimum(0)
        self.capacity_spin.setMaximum(999999)
        self.capacity_spin.setDecimals(3)
        form_layout.addRow("Capacity (Ltrs/Kg):", self.capacity_spin)
        
        # 10% Top Up
        self.top_up_spin = QDoubleSpinBox()
        self.top_up_spin.setMinimum(0)
        self.top_up_spin.setMaximum(999999)
        self.top_up_spin.setDecimals(3)
        form_layout.addRow("Additional 10% Top Up:", self.top_up_spin)
        
        # Grade
        self.grade_edit = QLineEdit()
        form_layout.addRow("Grade:", self.grade_edit)
        
        # Periodicity KM
        self.periodicity_km_spin = QSpinBox()
        self.periodicity_km_spin.setMinimum(0)
        self.periodicity_km_spin.setMaximum(999999)
        form_layout.addRow("Periodicity KM:", self.periodicity_km_spin)
        
        # Periodicity Hours
        self.periodicity_hrs_spin = QSpinBox()
        self.periodicity_hrs_spin.setMinimum(0)
        self.periodicity_hrs_spin.setMaximum(999999)
        form_layout.addRow("Periodicity Hours:", self.periodicity_hrs_spin)
        
        # Periodicity Months
        self.periodicity_months_spin = QSpinBox()
        self.periodicity_months_spin.setMinimum(0)
        self.periodicity_months_spin.setMaximum(120)
        form_layout.addRow("Periodicity Months:", self.periodicity_months_spin)
        
        # Last Serviced Date
        self.last_serviced_date = QDateEdit()
        self.last_serviced_date.setCalendarPopup(True)
        self.last_serviced_date.setDate(QDate.currentDate())
        form_layout.addRow("Last Serviced Date:", self.last_serviced_date)
        
        # Last Serviced Meterage
        self.last_serviced_meterage_spin = QDoubleSpinBox()
        self.last_serviced_meterage_spin.setMinimum(0)
        self.last_serviced_meterage_spin.setMaximum(999999)
        self.last_serviced_meterage_spin.setDecimals(3)
        form_layout.addRow("Last Serviced Meterage:", self.last_serviced_meterage_spin)
        
        layout.addLayout(form_layout)
        
        # Add button to calculate top-up
        calc_button = QPushButton("Calculate 10% Top-up")
        calc_button.clicked.connect(self.calculate_top_up)
        layout.addWidget(calc_button)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def calculate_top_up(self):
        """Calculate 10% top-up based on capacity."""
        capacity = self.capacity_spin.value()
        top_up = capacity * 0.1
        self.top_up_spin.setValue(top_up)
    
    def populate_fields(self):
        """Populate dialog fields with fluid data if editing."""
        if not self.fluid:
            return
        
        # Find equipment in combo box
        equipment_id = self.fluid.get('EquipmentID')
        for i in range(self.equipment_combo.count()):
            if self.equipment_combo.itemData(i) == equipment_id:
                self.equipment_combo.setCurrentIndex(i)
                break
        
        self.fluid_type_edit.setText(self.fluid.get('FluidType', ''))
        self.sub_type_edit.setText(self.fluid.get('SubType', ''))
        
        # Set accounting unit
        accounting_unit = self.fluid.get('AccountingUnit', 'Ltr')
        index = self.accounting_unit_combo.findText(accounting_unit)
        if index >= 0:
            self.accounting_unit_combo.setCurrentIndex(index)
        
        self.capacity_spin.setValue(float(self.fluid.get('CapacityLtrsKg', 0)))
        self.top_up_spin.setValue(float(self.fluid.get('Addl10PercentTopUp', 0)))
        self.grade_edit.setText(self.fluid.get('Grade', ''))
        self.periodicity_km_spin.setValue(int(self.fluid.get('PeriodicityKM', 0)))
        self.periodicity_hrs_spin.setValue(int(self.fluid.get('PeriodicityHrs', 0)))
        self.periodicity_months_spin.setValue(int(self.fluid.get('PeriodicityMonths', 0)))
        
        # Set last serviced date
        last_serviced_date = self.fluid.get('LastServicedDate')
        if last_serviced_date:
            date_obj = utils.str_to_date(last_serviced_date)
            if date_obj:
                self.last_serviced_date.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))
        
        self.last_serviced_meterage_spin.setValue(float(self.fluid.get('LastServicedMeterage', 0)))
    
    def get_fluid_data(self):
        """Get fluid data from dialog fields."""
        fluid = {}
        if self.fluid:
            fluid['FluidID'] = self.fluid.get('FluidID')
        
        fluid['EquipmentID'] = self.equipment_combo.currentData()
        fluid['FluidType'] = self.fluid_type_edit.text()
        fluid['SubType'] = self.sub_type_edit.text()
        fluid['AccountingUnit'] = self.accounting_unit_combo.currentText()
        fluid['CapacityLtrsKg'] = self.capacity_spin.value()
        fluid['Addl10PercentTopUp'] = self.top_up_spin.value()
        fluid['Grade'] = self.grade_edit.text()
        fluid['PeriodicityKM'] = self.periodicity_km_spin.value()
        fluid['PeriodicityHrs'] = self.periodicity_hrs_spin.value()
        fluid['PeriodicityMonths'] = self.periodicity_months_spin.value()
        
        date_obj = self.last_serviced_date.date().toPyDate()
        fluid['LastServicedDate'] = date_obj.isoformat()
        fluid['LastServicedMeterage'] = self.last_serviced_meterage_spin.value()
        
        return fluid
    
    def validate(self):
        """Validate the input data."""
        if not self.fluid_type_edit.text().strip():
            QMessageBox.warning(self, "Validation Error", "Fluid Type is required.")
            return False
        
        return True
    
    def accept(self):
        """Handle dialog acceptance."""
        if not self.validate():
            return
        
        super().accept()


class MaintenanceCompletionDialog(QDialog):
    """Dialog for completing maintenance with fluid demand creation option."""
    
    def __init__(self, maintenance, equipment_list=None, parent=None):
        super().__init__(parent)
        self.maintenance = maintenance
        self.equipment_list = equipment_list or []
        self.equipment_id = maintenance.get('equipment_id')
        self.selected_fluids = []
        
        self.setWindowTitle("Complete Maintenance")
        self.resize(600, 500)
        
        self.setup_ui()
        self.populate_fields()
    
    def setup_ui(self):
        """Set up the completion dialog UI."""
        layout = QVBoxLayout(self)
        
        # Main form layout
        form_layout = QFormLayout()
        
        # Equipment display (read-only)
        self.equipment_label = QLabel()
        form_layout.addRow("Equipment:", self.equipment_label)
        
        # Maintenance type display (read-only)
        self.maintenance_type_label = QLabel()
        form_layout.addRow("Maintenance Type:", self.maintenance_type_label)
        
        # Actual completion date
        self.completion_date = QDateEdit()
        self.completion_date.setCalendarPopup(True)
        self.completion_date.setDate(QDate.currentDate())
        form_layout.addRow("Completion Date:", self.completion_date)
        
        # Current meterage with update option
        meterage_layout = QHBoxLayout()
        self.meterage_spin = QDoubleSpinBox()
        self.meterage_spin.setMinimum(0)
        self.meterage_spin.setMaximum(1000000)
        self.meterage_spin.setDecimals(2)
        self.meterage_spin.setSuffix(" km")
        
        self.update_equipment_meterage_check = QCheckBox("Update equipment meterage")
        self.update_equipment_meterage_check.setChecked(True)
        
        meterage_layout.addWidget(self.meterage_spin)
        meterage_layout.addWidget(self.update_equipment_meterage_check)
        form_layout.addRow("Meterage at Completion:", meterage_layout)
        
        # Completion notes
        self.notes_text = QTextEdit()
        self.notes_text.setMaximumHeight(100)
        self.notes_text.setPlaceholderText("Enter any observations, issues found, or additional notes...")
        form_layout.addRow("Notes/Observations:", self.notes_text)
        
        layout.addLayout(form_layout)
        
        # Fluid demand section
        fluid_group = QGroupBox("Create Fluid Demand Forecast")
        fluid_layout = QVBoxLayout(fluid_group)
        
        self.create_demand_check = QCheckBox("Create demand forecast for fluids used")
        self.create_demand_check.setChecked(True)
        self.create_demand_check.toggled.connect(self.toggle_fluid_selection)
        fluid_layout.addWidget(self.create_demand_check)
        
        # Fiscal year selection
        fiscal_layout = QHBoxLayout()
        fiscal_layout.addWidget(QLabel("Fiscal Year:"))
        self.fiscal_year_combo = QComboBox()
        from utils import generate_fiscal_years
        fiscal_years = generate_fiscal_years(3)
        self.fiscal_year_combo.addItems(fiscal_years)
        fiscal_layout.addWidget(self.fiscal_year_combo)
        fiscal_layout.addStretch()
        fluid_layout.addLayout(fiscal_layout)
        
        # Fluid selection button
        self.select_fluids_button = QPushButton("Select Fluids for Demand Forecast")
        self.select_fluids_button.clicked.connect(self.select_fluids)
        fluid_layout.addWidget(self.select_fluids_button)
        
        # Selected fluids display
        self.selected_fluids_label = QLabel("No fluids selected")
        self.selected_fluids_label.setWordWrap(True)
        self.selected_fluids_label.setStyleSheet("color: #666; font-style: italic;")
        fluid_layout.addWidget(self.selected_fluids_label)
        
        layout.addWidget(fluid_group)
        
        # Dialog buttons
        button_layout = QHBoxLayout()
        self.complete_button = QPushButton("Complete Maintenance")
        self.complete_button.clicked.connect(self.accept)
        self.complete_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(self.complete_button)
        layout.addLayout(button_layout)
        
        self.toggle_fluid_selection()
    
    def populate_fields(self):
        """Populate dialog fields with maintenance data."""
        if not self.maintenance:
            return
        
        # Set equipment display
        from utils import format_equipment_display
        equipment_text = format_equipment_display(self.maintenance)
        self.equipment_label.setText(equipment_text)
        
        # Set maintenance type
        self.maintenance_type_label.setText(self.maintenance.get('maintenance_type', 'Unknown'))
        
        # Set current meterage from equipment
        current_meterage = self.maintenance.get('meterage_kms', 0)
        # Handle None values by defaulting to 0
        if current_meterage is None:
            current_meterage = 0.0

        # If maintenance meterage is 0 or None, try to get current equipment meterage
        if current_meterage == 0.0 and self.equipment_id:
            try:
                from models import Equipment
                equipment = Equipment.get_by_id(self.equipment_id)
                if equipment and equipment.get('meterage_kms') is not None:
                    current_meterage = float(equipment.get('meterage_kms', 0))
            except Exception as e:
                # If we can't get equipment meterage, just use 0
                pass

        self.meterage_spin.setValue(float(current_meterage))
    
    def toggle_fluid_selection(self):
        """Enable/disable fluid selection based on checkbox."""
        enabled = self.create_demand_check.isChecked()
        self.fiscal_year_combo.setEnabled(enabled)
        self.select_fluids_button.setEnabled(enabled)
    
    def select_fluids(self):
        """Open fluid selection dialog."""
        from models import Fluid
        
        # Get fluids for this equipment
        fluids = Fluid.get_by_equipment(self.equipment_id)
        
        if not fluids:
            QMessageBox.information(
                self,
                "No Fluids",
                "No fluids found for this equipment. Please add fluids first."
            )
            return
        
        # Create fluid selection dialog
        dialog = FluidSelectionDialog(fluids, self.selected_fluids, self)
        if dialog.exec_() == QDialog.Accepted:
            self.selected_fluids = dialog.get_selected_fluids()
            self.update_selected_fluids_display()
    
    def update_selected_fluids_display(self):
        """Update the display of selected fluids with demand options."""
        if not self.selected_fluids:
            self.selected_fluids_label.setText("No fluids selected")
            self.selected_fluids_label.setStyleSheet("color: #666; font-style: italic;")
        else:
            # Define demand option labels for display
            option_labels = {
                'full_only': 'Full Change Only',
                'topup_only': '10% Top-up Only',
                'full_plus_topup': 'Full Change + 10% Top-up'
            }
            
            fluid_details = []
            for fluid in self.selected_fluids:
                fluid_type = fluid.get('fluid_type', 'Unknown')
                demand_option = fluid.get('demand_option', 'full_only')
                option_label = option_labels.get(demand_option, 'Full Change Only')
                
                # Add capacity info for context
                capacity = fluid.get('capacity_ltrs_kg', 0)
                unit = fluid.get('accounting_unit', 'Ltr')
                
                # Create detailed display based on option
                if demand_option == 'full_only':
                    detail = f"• {fluid_type} - {option_label} ({capacity} {unit})"
                elif demand_option == 'topup_only':
                    topup = fluid.get('addl_10_percent_top_up', 0)
                    detail = f"• {fluid_type} - {option_label} (+{topup} {unit})"
                elif demand_option == 'full_plus_topup':
                    topup = fluid.get('addl_10_percent_top_up', 0)
                    total = capacity + topup
                    detail = f"• {fluid_type} - {option_label} ({total} {unit} total)"
                else:
                    detail = f"• {fluid_type} - {option_label}"
                
                fluid_details.append(detail)
            
            # Calculate total demands that will be created
            total_demands = 0
            for fluid in self.selected_fluids:
                if fluid.get('demand_option') == 'full_plus_topup':
                    total_demands += 2  # This option creates 2 demands
                else:
                    total_demands += 1  # Other options create 1 demand each
            
            text = f"Selected fluids ({len(self.selected_fluids)}) - {total_demands} demand(s) will be created:\n"
            text += "\n".join(fluid_details)
            
            # Add helpful note
            if any(f.get('demand_option') == 'full_plus_topup' for f in self.selected_fluids):
                text += "\n\n📝 Note: 'Full Change + 10% Top-up' creates two separate demand records"
            
            self.selected_fluids_label.setText(text)
            self.selected_fluids_label.setStyleSheet("color: #333; font-size: 11px; line-height: 1.4;")
    
    def get_completion_data(self):
        """Get completion data from dialog fields."""
        completion_date = self.completion_date.date().toPyDate()
        meterage = self.meterage_spin.value()
        notes = self.notes_text.toPlainText().strip()
        
        return {
            'completion_date': completion_date.isoformat(),
            'completion_meterage': meterage,
            'notes': notes,
            'update_equipment_meterage': self.update_equipment_meterage_check.isChecked(),
            'create_demand': self.create_demand_check.isChecked(),
            'fiscal_year': self.fiscal_year_combo.currentText(),
            'selected_fluids': self.selected_fluids
        }
    
    def validate(self):
        """Validate the completion data."""
        if self.completion_date.date() > QDate.currentDate():
            QMessageBox.warning(
                self,
                "Validation Error",
                "Completion date cannot be in the future."
            )
            return False
        
        if self.meterage_spin.value() < 0:
            QMessageBox.warning(
                self,
                "Validation Error",
                "Meterage cannot be negative."
            )
            return False
        
        return True
    
    def accept(self):
        """Handle dialog acceptance."""
        if not self.validate():
            return
        
        super().accept()


class FluidSelectionDialog(QDialog):
    """Dialog for selecting fluids for demand forecast creation."""
    
    # Define demand options
    DEMAND_OPTIONS = {
        'full_only': 'Full Fluid Change Only',
        'topup_only': '10% Top-up Only',
        'full_plus_topup': 'Full Change + 10% Top-up'
    }
    
    def __init__(self, available_fluids, selected_fluids=None, parent=None):
        super().__init__(parent)
        self.available_fluids = available_fluids
        self.selected_fluids = selected_fluids or []
        
        self.setWindowTitle("Select Fluids for Demand Forecast")
        self.resize(650, 500)  # Made wider to accommodate radio buttons
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the fluid selection dialog UI."""
        layout = QVBoxLayout(self)
        
        # Instructions
        instructions = QLabel(
            "Select the fluids that were used or need to be forecasted for this maintenance.\n"
            "Choose the appropriate demand option for each selected fluid:"
        )
        instructions.setWordWrap(True)
        instructions.setStyleSheet("margin-bottom: 10px; font-size: 11px; color: #444;")
        layout.addWidget(instructions)
        
        # Fluid list with checkboxes and radio buttons
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        self.fluid_checkboxes = []
        self.demand_option_groups = []  # List of QButtonGroup for radio buttons
        
        for fluid in self.available_fluids:
            # Create a container for each fluid
            fluid_container = QWidget()
            fluid_container.setStyleSheet("""
                QWidget {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    margin: 2px;
                    padding: 8px;
                    background-color: #fafafa;
                }
            """)
            container_layout = QVBoxLayout(fluid_container)
            container_layout.setContentsMargins(8, 8, 8, 8)
            
            # Main fluid checkbox row
            main_row = QHBoxLayout()
            
            # Main fluid checkbox
            checkbox = QCheckBox()
            
            # Create fluid display text
            fluid_text = f"{fluid['fluid_type']}"
            if fluid.get('sub_type'):
                fluid_text += f" ({fluid['sub_type']})"
            
            capacity = fluid.get('capacity_ltrs_kg', 0)
            unit = fluid.get('accounting_unit', 'Ltr')
            fluid_text += f" - {capacity} {unit}"
            
            checkbox.setText(fluid_text)
            checkbox.setToolTip(f"Grade: {fluid.get('grade', 'N/A')}")
            checkbox.setStyleSheet("font-weight: bold; color: #333;")
            
            # Check if already selected
            if any(sf['fluid_id'] == fluid['fluid_id'] for sf in self.selected_fluids):
                checkbox.setChecked(True)
            
            checkbox.fluid_data = fluid
            self.fluid_checkboxes.append(checkbox)
            
            main_row.addWidget(checkbox)
            main_row.addStretch()
            container_layout.addLayout(main_row)
            
            # Radio button group for demand options
            options_frame = QWidget()
            options_layout = QHBoxLayout(options_frame)
            options_layout.setContentsMargins(20, 5, 5, 5)  # Indent under main checkbox
            
            # Create button group for this fluid
            button_group = QButtonGroup()
            
            # Get top-up value for this fluid
            topup_value = fluid.get('addl_10_percent_top_up', 0)
            has_topup = topup_value > 0
            
            # Option 1: Full Only
            full_only_radio = QRadioButton(self.DEMAND_OPTIONS['full_only'])
            full_only_radio.setChecked(True)  # Default selection
            full_only_radio.setToolTip(f"Replace entire fluid capacity ({capacity} {unit})")
            button_group.addButton(full_only_radio, 0)
            options_layout.addWidget(full_only_radio)
            
            # Option 2: 10% Only (enabled only if top-up is defined)
            topup_only_radio = QRadioButton(
                f"10% Top-up Only (+{topup_value} {unit})" if has_topup 
                else "10% Top-up Only (Not defined)"
            )
            topup_only_radio.setEnabled(has_topup)
            topup_only_radio.setToolTip(
                f"Add only {topup_value} {unit} top-up" if has_topup 
                else "No 10% top-up value defined for this fluid"
            )
            button_group.addButton(topup_only_radio, 1)
            options_layout.addWidget(topup_only_radio)
            
            # Option 3: Full + 10% (enabled only if top-up is defined)
            full_plus_radio = QRadioButton(
                f"Full Change + 10% Top-up ({capacity + topup_value} {unit})" if has_topup
                else "Full Change + 10% Top-up (Not available)"
            )
            full_plus_radio.setEnabled(has_topup)
            full_plus_radio.setToolTip(
                f"Replace entire fluid ({capacity} {unit}) plus add {topup_value} {unit} top-up" if has_topup
                else "No 10% top-up value defined for this fluid"
            )
            button_group.addButton(full_plus_radio, 2)
            options_layout.addWidget(full_plus_radio)
            
            # Store button group reference with fluid data
            self.demand_option_groups.append((button_group, fluid))
            
            # Initially disable options until fluid is selected
            for button in button_group.buttons():
                button.setEnabled(False)
            
            # Connect main checkbox to enable/disable radio buttons
            checkbox.toggled.connect(
                lambda checked, bg=button_group, has_tu=has_topup: self._toggle_demand_options(bg, checked, has_tu)
            )
            
            container_layout.addWidget(options_frame)
            scroll_layout.addWidget(fluid_container)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        # Selection buttons
        button_layout = QHBoxLayout()
        
        select_all_button = QPushButton("Select All Fluids")
        select_all_button.clicked.connect(self.select_all)
        select_all_button.setToolTip("Select all fluids with 'Full Change Only' option")
        button_layout.addWidget(select_all_button)
        
        clear_all_button = QPushButton("Clear All")
        clear_all_button.clicked.connect(self.clear_all)
        button_layout.addWidget(clear_all_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # Dialog buttons
        dialog_buttons = QHBoxLayout()
        
        ok_button = QPushButton("OK")
        ok_button.clicked.connect(self.accept)
        ok_button.setDefault(True)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        
        dialog_buttons.addStretch()
        dialog_buttons.addWidget(cancel_button)
        dialog_buttons.addWidget(ok_button)
        layout.addLayout(dialog_buttons)
    
    def _toggle_demand_options(self, button_group, enabled, has_topup):
        """Enable/disable demand option radio buttons based on main checkbox."""
        for i, button in enumerate(button_group.buttons()):
            if i == 0:  # Full Only - always available when fluid is selected
                button.setEnabled(enabled)
            else:  # Top-up options - only available if fluid has top-up defined
                button.setEnabled(enabled and has_topup)
    
    def select_all(self):
        """Select all fluid checkboxes with default 'Full Only' option."""
        for checkbox in self.fluid_checkboxes:
            checkbox.setChecked(True)
        # Radio buttons will be automatically set to "Full Only" (default)
    
    def clear_all(self):
        """Clear all fluid checkboxes."""
        for checkbox in self.fluid_checkboxes:
            checkbox.setChecked(False)
        # Radio buttons will be automatically disabled
    
    def get_selected_fluids(self):
        """Get list of selected fluids with demand option information."""
        selected = []
        for i, checkbox in enumerate(self.fluid_checkboxes):
            if checkbox.isChecked():
                fluid_data = checkbox.fluid_data.copy()
                
                # Get selected demand option from radio button group
                button_group, fluid_info = self.demand_option_groups[i]
                checked_button = button_group.checkedButton()
                
                if checked_button:
                    button_id = button_group.id(checked_button)
                    if button_id == 0:
                        fluid_data['demand_option'] = 'full_only'
                    elif button_id == 1:
                        fluid_data['demand_option'] = 'topup_only'
                    elif button_id == 2:
                        fluid_data['demand_option'] = 'full_plus_topup'
                    else:
                        fluid_data['demand_option'] = 'full_only'  # Fallback
                else:
                    fluid_data['demand_option'] = 'full_only'  # Fallback if no radio button selected
                
                # Keep backward compatibility flag for now
                fluid_data['include_topup'] = fluid_data['demand_option'] in ['topup_only', 'full_plus_topup']
                
                selected.append(fluid_data)
        return selected


class MaintenanceDialog(QDialog):
    """Dialog for adding or editing maintenance records."""
    
    def __init__(self, maintenance=None, equipment_list=None, parent=None):
        super().__init__(parent)
        self.maintenance = maintenance
        self.equipment_list = equipment_list or []
        self.setWindowTitle("Add Maintenance" if maintenance is None else "Edit Maintenance")
        self.resize(400, 450)
        
        self.setup_ui()
        self.populate_fields()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        
        # Form layout for fields
        form_layout = QFormLayout()
        
        # Equipment
        self.equipment_combo = QComboBox()
        for equip in self.equipment_list:
            from utils import format_equipment_for_dropdown
            display_text = format_equipment_for_dropdown(equip)
            equipment_id = equip.get('equipment_id') or equip.get('EquipmentID')
            self.equipment_combo.addItem(display_text, equipment_id)
        form_layout.addRow("Equipment:", self.equipment_combo)
        
        # Maintenance Type
        self.maintenance_type_combo = QComboBox()
        self.maintenance_type_combo.addItems([
            "TM-I", "TM-II", "MR-I", "MR-II", "MR-III", 
            "OH-I", "OH-II", "Battery", "Tyre Rotation", "Other"
        ])
        self.maintenance_type_combo.setEditable(True)
        form_layout.addRow("Maintenance Type:", self.maintenance_type_combo)
        
        # Done Date
        self.done_date = QDateEdit()
        self.done_date.setCalendarPopup(True)
        self.done_date.setDate(QDate.currentDate())
        form_layout.addRow("Done Date:", self.done_date)
        
        # Due Date
        self.due_date = QDateEdit()
        self.due_date.setCalendarPopup(True)
        self.due_date.setDate(QDate.currentDate().addMonths(3))
        form_layout.addRow("Due Date:", self.due_date)
        
        # Vintage Years
        self.vintage_years_spin = QDoubleSpinBox()
        self.vintage_years_spin.setMinimum(0)
        self.vintage_years_spin.setMaximum(100)
        self.vintage_years_spin.setDecimals(3)
        form_layout.addRow("Vintage Years:", self.vintage_years_spin)
        
        # Meterage KMs
        self.meterage_spin = QDoubleSpinBox()
        self.meterage_spin.setMinimum(0)
        self.meterage_spin.setMaximum(999999)
        self.meterage_spin.setDecimals(3)
        form_layout.addRow("Meterage (KMs):", self.meterage_spin)
        
        layout.addLayout(form_layout)
        
        # Add button to get current equipment meterage
        meterage_button = QPushButton("Get Current Equipment Meterage")
        meterage_button.clicked.connect(self.get_current_meterage)
        layout.addWidget(meterage_button)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def get_current_meterage(self):
        """Get current meterage from selected equipment."""
        equipment_id = self.equipment_combo.currentData()
        for equip in self.equipment_list:
            if equip['EquipmentID'] == equipment_id:
                self.meterage_spin.setValue(float(equip.get('MeterageKMs', 0)))
                self.vintage_years_spin.setValue(float(equip.get('VintageYears', 0)))
                break
    
    def populate_fields(self):
        """Populate dialog fields with maintenance data if editing."""
        if not self.maintenance:
            return
        
        # Find equipment in combo box
        equipment_id = self.maintenance.get('EquipmentID')
        for i in range(self.equipment_combo.count()):
            if self.equipment_combo.itemData(i) == equipment_id:
                self.equipment_combo.setCurrentIndex(i)
                break
        
        # Find maintenance type or add it
        maintenance_type = self.maintenance.get('MaintenanceType', '')
        index = self.maintenance_type_combo.findText(maintenance_type)
        if index >= 0:
            self.maintenance_type_combo.setCurrentIndex(index)
        else:
            self.maintenance_type_combo.addItem(maintenance_type)
            self.maintenance_type_combo.setCurrentText(maintenance_type)
        
        # Set done date
        done_date = self.maintenance.get('DoneDate')
        if done_date:
            date_obj = utils.str_to_date(done_date)
            if date_obj:
                self.done_date.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))
        
        # Set due date
        due_date = self.maintenance.get('DueDate')
        if due_date:
            date_obj = utils.str_to_date(due_date)
            if date_obj:
                self.due_date.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))
        
        self.vintage_years_spin.setValue(float(self.maintenance.get('VintageYears', 0)))
        self.meterage_spin.setValue(float(self.maintenance.get('MeterageKMs', 0)))
    
    def get_maintenance_data(self):
        """Get maintenance data from dialog fields."""
        maintenance = {}
        if self.maintenance:
            maintenance['MaintenanceID'] = self.maintenance.get('MaintenanceID')
        
        maintenance['EquipmentID'] = self.equipment_combo.currentData()
        maintenance['MaintenanceType'] = self.maintenance_type_combo.currentText()
        
        done_date = self.done_date.date().toPyDate()
        maintenance['DoneDate'] = done_date.isoformat()
        
        due_date = self.due_date.date().toPyDate()
        maintenance['DueDate'] = due_date.isoformat()
        
        maintenance['VintageYears'] = self.vintage_years_spin.value()
        maintenance['MeterageKMs'] = self.meterage_spin.value()
        
        return maintenance
    
    def validate(self):
        """Validate the input data."""
        if not self.maintenance_type_combo.currentText().strip():
            QMessageBox.warning(self, "Validation Error", "Maintenance Type is required.")
            return False
        
        if self.done_date.date() > self.due_date.date():
            QMessageBox.warning(self, "Validation Error", "Due Date must be after Done Date.")
            return False
        
        return True
    
    def accept(self):
        """Handle dialog acceptance."""
        if not self.validate():
            return
        
        super().accept()


class RepairDialog(QDialog):
    """Dialog for adding or editing repair records."""
    
    def __init__(self, repair=None, equipment_list=None, parent=None):
        super().__init__(parent)
        self.repair = repair
        self.equipment_list = equipment_list or []
        self.setWindowTitle("Add Repair" if repair is None else "Edit Repair")
        self.resize(400, 350)
        
        self.setup_ui()
        self.populate_fields()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        
        # Form layout for fields
        form_layout = QFormLayout()
        
        # Equipment
        self.equipment_combo = QComboBox()
        for equip in self.equipment_list:
            from utils import format_equipment_for_dropdown
            display_text = format_equipment_for_dropdown(equip)
            equipment_id = equip.get('equipment_id') or equip.get('EquipmentID')
            self.equipment_combo.addItem(display_text, equipment_id)
        form_layout.addRow("Equipment:", self.equipment_combo)
        
        # Repair Type
        self.repair_type_combo = QComboBox()
        self.repair_type_combo.addItems([
            "Major Repair", "Miscellaneous Repair", "Component Replacement",
            "Preventive Repair", "Emergency Repair", "Other"
        ])
        self.repair_type_combo.setEditable(True)
        form_layout.addRow("Repair Type:", self.repair_type_combo)
        
        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        form_layout.addRow("Description:", self.description_edit)
        
        # Repair Date
        self.repair_date = QDateEdit()
        self.repair_date.setCalendarPopup(True)
        self.repair_date.setDate(QDate.currentDate())
        form_layout.addRow("Repair Date:", self.repair_date)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def populate_fields(self):
        """Populate dialog fields with repair data if editing."""
        if not self.repair:
            return
        
        # Find equipment in combo box
        equipment_id = self.repair.get('EquipmentID')
        for i in range(self.equipment_combo.count()):
            if self.equipment_combo.itemData(i) == equipment_id:
                self.equipment_combo.setCurrentIndex(i)
                break
        
        # Find repair type or add it
        repair_type = self.repair.get('RepairType', '')
        index = self.repair_type_combo.findText(repair_type)
        if index >= 0:
            self.repair_type_combo.setCurrentIndex(index)
        else:
            self.repair_type_combo.addItem(repair_type)
            self.repair_type_combo.setCurrentText(repair_type)
        
        self.description_edit.setText(self.repair.get('Description', ''))
        
        # Set repair date
        repair_date = self.repair.get('RepairDate')
        if repair_date:
            date_obj = utils.str_to_date(repair_date)
            if date_obj:
                self.repair_date.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))
    
    def get_repair_data(self):
        """Get repair data from dialog fields."""
        repair = {}
        if self.repair:
            repair['RepairID'] = self.repair.get('RepairID')
        
        repair['EquipmentID'] = self.equipment_combo.currentData()
        repair['RepairType'] = self.repair_type_combo.currentText()
        repair['Description'] = self.description_edit.toPlainText()
        
        repair_date = self.repair_date.date().toPyDate()
        repair['RepairDate'] = repair_date.isoformat()
        
        return repair
    
    def validate(self):
        """Validate the input data."""
        if not self.repair_type_combo.currentText().strip():
            QMessageBox.warning(self, "Validation Error", "Repair Type is required.")
            return False
        
        return True
    
    def accept(self):
        """Handle dialog acceptance."""
        if not self.validate():
            return
        
        super().accept()


class DiscrepancyDialog(QDialog):
    """Dialog for adding or editing discrepancy records."""
    
    def __init__(self, discrepancy=None, equipment_list=None, parent=None):
        super().__init__(parent)
        self.discrepancy = discrepancy
        self.equipment_list = equipment_list or []
        self.setWindowTitle("Add Discrepancy" if discrepancy is None else "Edit Discrepancy")
        self.resize(400, 450)
        
        self.setup_ui()
        self.populate_fields()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        
        # Form layout for fields
        form_layout = QFormLayout()
        
        # Equipment
        self.equipment_combo = QComboBox()
        # Equipment dict keys may be snake_case from DB; handle both cases safely
        for equip in self.equipment_list:
            make_type = equip.get('make_and_type') or equip.get('MakeAndType') or ''
            serial_no = equip.get('serial_number') or equip.get('SerialNumber') or ''
            equip_id = equip.get('equipment_id') or equip.get('EquipmentID')
            self.equipment_combo.addItem(
                f"{make_type} ({serial_no})", 
                equip_id
            )
        form_layout.addRow("Equipment:", self.equipment_combo)
        
        # Fluid Type
        self.fluid_type_combo = QComboBox()
        self.fluid_type_combo.addItems([
            "Engine Oil", "Transmission Oil", "Hydraulic Oil", 
            "Coolant", "Grease", "Brake Fluid", "Other"
        ])
        self.fluid_type_combo.setEditable(True)
        form_layout.addRow("Fluid Type:", self.fluid_type_combo)
        
        # Sub Type
        self.sub_type_edit = QLineEdit()
        form_layout.addRow("Sub Type:", self.sub_type_edit)
        
        # Difference Ltrs/Kg
        self.difference_spin = QDoubleSpinBox()
        self.difference_spin.setMinimum(-999999)
        self.difference_spin.setMaximum(999999)
        self.difference_spin.setDecimals(3)
        form_layout.addRow("Difference (Ltrs/Kg):", self.difference_spin)
        
        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("Notes:", self.notes_edit)
        
        # Reported Date
        self.reported_date = QDateEdit()
        self.reported_date.setCalendarPopup(True)
        self.reported_date.setDate(QDate.currentDate())
        form_layout.addRow("Reported Date:", self.reported_date)
        
        # Resolved
        self.resolved_check = QCheckBox("Discrepancy has been resolved")
        form_layout.addRow("", self.resolved_check)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def populate_fields(self):
        """Populate dialog fields with discrepancy data if editing."""
        if not self.discrepancy:
            return
        
        # Find equipment in combo box
        equipment_id = self.discrepancy.get('EquipmentID') or self.discrepancy.get('equipment_id')
        for i in range(self.equipment_combo.count()):
            if self.equipment_combo.itemData(i) == equipment_id:
                self.equipment_combo.setCurrentIndex(i)
                break
        
        # Find fluid type or add it
        fluid_type = self.discrepancy.get('FluidType') or self.discrepancy.get('fluid_type') or ''
        index = self.fluid_type_combo.findText(fluid_type)
        if index >= 0:
            self.fluid_type_combo.setCurrentIndex(index)
        else:
            self.fluid_type_combo.addItem(fluid_type)
            self.fluid_type_combo.setCurrentText(fluid_type)
        
        self.sub_type_edit.setText(self.discrepancy.get('SubType') or self.discrepancy.get('sub_type') or '')
        self.difference_spin.setValue(float(self.discrepancy.get('DifferenceLtrsKg') or self.discrepancy.get('difference_ltrs_kg') or 0))
        self.notes_edit.setText(self.discrepancy.get('Notes') or self.discrepancy.get('notes') or '')
        
        # Set reported date
        reported_date = self.discrepancy.get('ReportedDate') or self.discrepancy.get('reported_date')
        if reported_date:
            date_obj = utils.str_to_date(reported_date)
            if date_obj:
                self.reported_date.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))
        
        self.resolved_check.setChecked(bool(self.discrepancy.get('Resolved') or self.discrepancy.get('resolved') or False))
    
    def get_discrepancy_data(self):
        """Get discrepancy data from dialog fields."""
        discrepancy = {}
        if self.discrepancy:
            discrepancy['DiscrepancyID'] = self.discrepancy.get('DiscrepancyID')
        
        discrepancy['EquipmentID'] = self.equipment_combo.currentData()
        discrepancy['FluidType'] = self.fluid_type_combo.currentText()
        discrepancy['SubType'] = self.sub_type_edit.text()
        discrepancy['DifferenceLtrsKg'] = self.difference_spin.value()
        discrepancy['Notes'] = self.notes_edit.toPlainText()
        
        reported_date = self.reported_date.date().toPyDate()
        discrepancy['ReportedDate'] = reported_date.isoformat()
        
        discrepancy['Resolved'] = self.resolved_check.isChecked()
        
        return discrepancy
    
    def validate(self):
        """Validate the input data."""
        if not self.fluid_type_combo.currentText().strip():
            QMessageBox.warning(self, "Validation Error", "Fluid Type is required.")
            return False
        
        return True
    
    def accept(self):
        """Handle dialog acceptance."""
        if not self.validate():
            return
        
        super().accept()


class DiscardCriteriaDialog(QDialog):
    """Dialog for adding or editing discard criteria records."""
    
    def __init__(self, criteria=None, equipment_list=None, parent=None):
        super().__init__(parent)
        self.criteria = criteria
        self.equipment_list = equipment_list or []
        self.setWindowTitle("Add Discard Criteria" if criteria is None else "Edit Discard Criteria")
        self.resize(400, 300)
        
        self.setup_ui()
        self.populate_fields()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        
        # Form layout for fields
        form_layout = QFormLayout()
        
        # Equipment
        self.equipment_combo = QComboBox()
        # Equipment dict keys may be snake_case from DB; handle both cases safely
        for equip in self.equipment_list:
            ba_number = equip.get('ba_number') or equip.get('BaNumber') or ''
            make_type = equip.get('make_and_type') or equip.get('MakeAndType') or ''
            equip_id = equip.get('equipment_id') or equip.get('EquipmentID')
            display_text = f"{ba_number} - {make_type}" if ba_number else make_type
            self.equipment_combo.addItem(display_text, equip_id)
        form_layout.addRow("Equipment:", self.equipment_combo)
        
        # Criteria Years
        self.criteria_years_spin = QSpinBox()
        self.criteria_years_spin.setMinimum(0)
        self.criteria_years_spin.setMaximum(100)
        form_layout.addRow("Criteria Years:", self.criteria_years_spin)
        
        # Criteria KMs
        self.criteria_kms_spin = QSpinBox()
        self.criteria_kms_spin.setMinimum(0)
        self.criteria_kms_spin.setMaximum(999999)
        form_layout.addRow("Criteria KMs:", self.criteria_kms_spin)
        
        # Criteria Hours
        self.criteria_hours_spin = QSpinBox()
        self.criteria_hours_spin.setMinimum(0)
        self.criteria_hours_spin.setMaximum(999999)
        form_layout.addRow("Criteria Hours:", self.criteria_hours_spin)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def populate_fields(self):
        """Populate dialog fields with discard criteria data if editing."""
        if not self.criteria:
            return
        
        # Find equipment in combo box
        equipment_id = self.criteria.get('EquipmentID') or self.criteria.get('equipment_id')
        for i in range(self.equipment_combo.count()):
            if self.equipment_combo.itemData(i) == equipment_id:
                self.equipment_combo.setCurrentIndex(i)
                break
        
        self.criteria_years_spin.setValue(int(self.criteria.get('CriteriaYears') or self.criteria.get('criteria_years') or 0))
        self.criteria_kms_spin.setValue(int(self.criteria.get('CriteriaKMs') or self.criteria.get('criteria_kms') or 0))
        self.criteria_hours_spin.setValue(int(self.criteria.get('CriteriaHours') or self.criteria.get('criteria_hours') or 0))
    
    def get_criteria_data(self):
        """Get discard criteria data from dialog fields."""
        criteria = {}
        if self.criteria:
            criteria['DiscardCriteriaID'] = self.criteria.get('DiscardCriteriaID')
        
        criteria['EquipmentID'] = self.equipment_combo.currentData()
        criteria['CriteriaYears'] = self.criteria_years_spin.value()
        criteria['CriteriaKMs'] = self.criteria_kms_spin.value()
        criteria['CriteriaHours'] = self.criteria_hours_spin.value()
        
        return criteria
    
    def validate(self):
        """Validate the input data."""
        if self.criteria_years_spin.value() == 0 and self.criteria_kms_spin.value() == 0 and self.criteria_hours_spin.value() == 0:
            QMessageBox.warning(self, "Validation Error", 
                              "At least one criteria (Years, KMs, or Hours) must be specified.")
            return False
        
        return True
    
    def accept(self):
        """Handle dialog acceptance."""
        if not self.validate():
            return
        
        super().accept()


class ConditioningDialog(QDialog):
    """Dialog for adding or editing conditioning records."""
    
    def __init__(self, tyre_maintenance=None, equipment_list=None, parent=None):
        super().__init__(parent)
        self.tyre_maintenance = tyre_maintenance
        self.equipment_list = equipment_list or []
        self.setWindowTitle("Add Conditioning" if tyre_maintenance is None else "Edit Conditioning")
        self.resize(400, 350)
        
        self.setup_ui()
        self.populate_fields()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("Conditioning Configuration")
        self.setModal(True)
        self.resize(400, 350)
        
        layout = QVBoxLayout(self)
        
        # Form layout
        form_layout = QFormLayout()
        
        # Equipment selection
        self.equipment_combo = QComboBox()
        self.equipment_combo.setEditable(False)
        
        # Populate equipment combo
        for equipment in self.equipment_list:
            ba_number = equipment.get('ba_number') or equipment.get('BaNumber') or ''
            make_type = equipment.get('make_and_type') or equipment.get('MakeAndType') or ''
            display_text = f"{ba_number} - {make_type}" if ba_number else make_type
            equipment_id = equipment.get('equipment_id') or equipment.get('EquipmentID')
            self.equipment_combo.addItem(display_text, equipment_id)
        
        form_layout.addRow("Equipment:", self.equipment_combo)
        
        # Date of Change
        self.date_of_change = QDateEdit()
        self.date_of_change.setCalendarPopup(True)
        self.date_of_change.setDate(QDate.currentDate())
        form_layout.addRow("Date of Change:", self.date_of_change)
        
        # Last Rotation Date
        self.last_rotation_date = QDateEdit()
        self.last_rotation_date.setCalendarPopup(True)
        self.last_rotation_date.setDate(QDate.currentDate())
        form_layout.addRow("Last Rotation Date:", self.last_rotation_date)
        
        # Rotation Interval KMs (this is the interval for rotations)
        self.rotation_kms_spin = QSpinBox()
        self.rotation_kms_spin.setMinimum(1000)
        self.rotation_kms_spin.setMaximum(100000)
        self.rotation_kms_spin.setSuffix(" KM")
        self.rotation_kms_spin.setValue(5000)  # Default to 5000 KM
        form_layout.addRow("Rotation Interval (KMs):", self.rotation_kms_spin)
        
        # Inspection Condition KMs (optional)
        self.condition_kms_spin = QSpinBox()
        self.condition_kms_spin.setMinimum(0)
        self.condition_kms_spin.setMaximum(1000000)
        self.condition_kms_spin.setSuffix(" KM")
        form_layout.addRow("Inspection KMs (Optional):", self.condition_kms_spin)
        
        # Inspection Condition Years (optional)
        self.condition_years_spin = QSpinBox()
        self.condition_years_spin.setMinimum(0)
        self.condition_years_spin.setMaximum(50)
        self.condition_years_spin.setSuffix(" years")
        form_layout.addRow("Condition Years:", self.condition_years_spin)
        
        # Quantity
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(50)
        self.quantity_spin.setValue(1)
        self.quantity_spin.setSuffix(" tyres")
        form_layout.addRow("Quantity:", self.quantity_spin)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def populate_fields(self):
        """Populate dialog fields with tyre maintenance data if editing."""
        if not self.tyre_maintenance:
            return
        
        # Find equipment in combo box
        equipment_id = self.tyre_maintenance.get('EquipmentID') or self.tyre_maintenance.get('equipment_id')
        for i in range(self.equipment_combo.count()):
            if self.equipment_combo.itemData(i) == equipment_id:
                self.equipment_combo.setCurrentIndex(i)
                break
        
        self.rotation_kms_spin.setValue(int(self.tyre_maintenance.get('TyreRotationKMs') or self.tyre_maintenance.get('tyre_rotation_kms') or 5000))
        self.condition_kms_spin.setValue(int(self.tyre_maintenance.get('TyreConditionKMs') or self.tyre_maintenance.get('tyre_condition_kms') or 0))
        self.condition_years_spin.setValue(int(self.tyre_maintenance.get('TyreConditionYears') or self.tyre_maintenance.get('tyre_condition_years') or 0))
        self.quantity_spin.setValue(int(self.tyre_maintenance.get('quantity') or 1))
        
        # Populate date of change
        date_of_change = self.tyre_maintenance.get('DateOfChange') or self.tyre_maintenance.get('date_of_change')
        if date_of_change:
            date_obj = utils.str_to_date(date_of_change)
            if date_obj:
                self.date_of_change.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))
        
        # Populate last rotation date
        last_rotation_date = self.tyre_maintenance.get('LastRotationDate') or self.tyre_maintenance.get('last_rotation_date')
        if last_rotation_date:
            date_obj = utils.str_to_date(last_rotation_date)
            if date_obj:
                self.last_rotation_date.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))
    
    def get_tyre_maintenance_data(self):
        """Get tyre maintenance data from dialog fields."""
        tyre_maintenance = {}
        if self.tyre_maintenance:
            tyre_maintenance['TyreMaintenanceID'] = self.tyre_maintenance.get('TyreMaintenanceID')
        
        tyre_maintenance['EquipmentID'] = self.equipment_combo.currentData()
        
        date_of_change = self.date_of_change.date().toPyDate()
        tyre_maintenance['DateOfChange'] = date_of_change.isoformat()
        
        tyre_maintenance['TyreRotationKMs'] = self.rotation_kms_spin.value()
        tyre_maintenance['TyreConditionKMs'] = self.condition_kms_spin.value()
        tyre_maintenance['TyreConditionYears'] = self.condition_years_spin.value()
        
        last_rotation_date = self.last_rotation_date.date().toPyDate()
        tyre_maintenance['LastRotationDate'] = last_rotation_date.isoformat()
        
        tyre_maintenance['quantity'] = self.quantity_spin.value()
        
        return tyre_maintenance
    
    def validate(self):
        """Validate the input data."""
        if self.rotation_kms_spin.value() < 1000:
            QMessageBox.warning(self, "Validation Error", "Rotation interval must be at least 1000 KMs.")
            return False
        
        return True
    
    def accept(self):
        """Handle dialog acceptance."""
        if not self.validate():
            return
        
        super().accept()


class DemandForecastDialog(QDialog):
    """Dialog for adding or editing demand forecast records."""
    
    def __init__(self, demand=None, fluid_list=None, parent=None):
        super().__init__(parent)
        self.demand = demand
        self.fluid_list = fluid_list or []
        self.setWindowTitle("Add Demand Forecast" if demand is None else "Edit Demand Forecast")
        self.resize(450, 350)
        
        self.setup_ui()
        self.populate_fields()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        
        # Form layout for fields
        form_layout = QFormLayout()
        
        # Fluid
        self.fluid_combo = QComboBox()
        for fluid in self.fluid_list:
            equip_name = fluid.get('make_and_type', 'Unknown')
            fluid_type = fluid.get('fluid_type', 'Unknown')
            sub_type = fluid.get('sub_type', '')
            
            display_text = f"{equip_name} - {fluid_type}"
            if sub_type:
                display_text += f" ({sub_type})"
            
            self.fluid_combo.addItem(display_text, fluid['fluid_id'])
        form_layout.addRow("Fluid:", self.fluid_combo)
        
        # Fiscal Year
        self.fiscal_year_combo = QComboBox()
        for year in utils.generate_fiscal_years(5):
            self.fiscal_year_combo.addItem(year)
        self.fiscal_year_combo.setEditable(True)
        form_layout.addRow("Fiscal Year:", self.fiscal_year_combo)
        
        # Total Requirement
        self.total_requirement_spin = QDoubleSpinBox()
        self.total_requirement_spin.setMinimum(0)
        self.total_requirement_spin.setMaximum(9999999)
        self.total_requirement_spin.setDecimals(3)
        form_layout.addRow("Total Requirement:", self.total_requirement_spin)
        
        # Remarks
        self.remarks_edit = QTextEdit()
        self.remarks_edit.setMaximumHeight(100)
        form_layout.addRow("Remarks:", self.remarks_edit)
        
        layout.addLayout(form_layout)
        
        # Add button to calculate demand
        calc_button = QPushButton("Calculate Demand")
        calc_button.clicked.connect(self.calculate_demand)
        layout.addWidget(calc_button)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def calculate_demand(self):
        """Calculate demand based on fluid and equipment data."""
        fluid_id = self.fluid_combo.currentData()
        if not fluid_id:
            return
        
        # Find the fluid in the list
        fluid = None
        equipment = None
        for f in self.fluid_list:
            if f['fluid_id'] == fluid_id:
                fluid = f
                # Get equipment data
                with database.get_db_connection() as conn:
                    equipment = conn.execute(
                        'SELECT * FROM Equipment WHERE equipment_id = ?',
                        (f['equipment_id'],)
                    ).fetchone()
                break
        
        if not fluid or not equipment:
            return
        
        # Calculate demand
        fiscal_year = self.fiscal_year_combo.currentText()
        total_requirement = utils.calculate_fluid_demand(fluid, equipment, fiscal_year)
        
        # Update the UI
        self.total_requirement_spin.setValue(total_requirement)
    
    def populate_fields(self):
        """Populate dialog fields with demand forecast data if editing."""
        if not self.demand:
            return
        
        # Find fluid in combo box
        fluid_id = self.demand.get('fluid_id')
        for i in range(self.fluid_combo.count()):
            if self.fluid_combo.itemData(i) == fluid_id:
                self.fluid_combo.setCurrentIndex(i)
                break
        
        # Find fiscal year or add it
        fiscal_year = self.demand.get('fiscal_year', '')
        index = self.fiscal_year_combo.findText(fiscal_year)
        if index >= 0:
            self.fiscal_year_combo.setCurrentIndex(index)
        else:
            self.fiscal_year_combo.addItem(fiscal_year)
            self.fiscal_year_combo.setCurrentText(fiscal_year)
        
        self.total_requirement_spin.setValue(float(self.demand.get('total_requirement', 0)))
        self.remarks_edit.setText(self.demand.get('remarks', ''))
    
    def get_demand_data(self):
        """Get demand forecast data from dialog fields."""
        demand = {}
        if self.demand:
            demand['demand_id'] = self.demand.get('demand_id')
        
        demand['fluid_id'] = self.fluid_combo.currentData()
        demand['fiscal_year'] = self.fiscal_year_combo.currentText()
        demand['total_requirement'] = self.total_requirement_spin.value()
        demand['remarks'] = self.remarks_edit.toPlainText()
        
        return demand
    
    def validate(self):
        """Validate the input data."""
        if not self.fiscal_year_combo.currentText().strip():
            QMessageBox.warning(self, "Validation Error", "Fiscal Year is required.")
            return False
        
        if self.total_requirement_spin.value() <= 0:
            QMessageBox.warning(self, "Validation Error", "Total Requirement must be greater than zero.")
            return False
        
        return True
    
    def accept(self):
        """Handle dialog acceptance."""
        if not self.validate():
            return
        
        super().accept()


class OverhaulForecastDialog(QDialog):
    """Dialog for adding/editing overhaul forecasts."""
    
    def __init__(self, forecast=None, parent=None):
        super().__init__(parent)
        self.forecast = forecast
        self.setup_ui()
        if forecast:
            self.load_forecast_data()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("Overhaul Forecast")
        layout = QFormLayout(self)
        
        # Equipment selection
        self.equipment_combo = QComboBox()
        equipment_list = models.Equipment.get_all() or []
        for eq in equipment_list:
            from utils import format_equipment_for_dropdown
            text = format_equipment_for_dropdown(eq)
            self.equipment_combo.addItem(text, eq['equipment_id'])
        layout.addRow("Equipment:", self.equipment_combo)
        
        # Fiscal year
        self.fiscal_year_combo = QComboBox()
        fiscal_years = utils.generate_fiscal_years()
        for year in fiscal_years:
            self.fiscal_year_combo.addItem(str(year), year)
        layout.addRow("Fiscal Year:", self.fiscal_year_combo)
        
        # Overhaul type
        self.overhaul_type_combo = QComboBox()
        self.overhaul_type_combo.addItems(["FIRST OH", "SECOND OH", "THIRD OH", "MAJOR OVERHAUL", "MINOR OVERHAUL"])
        layout.addRow("Overhaul Type:", self.overhaul_type_combo)
        
        # Total requirement
        self.requirement_edit = QLineEdit()
        self.requirement_edit.setValidator(QDoubleValidator(0, 999999, 2))  # Only allow valid numbers
        layout.addRow("Total Requirement:", self.requirement_edit)
        
        # Remarks
        self.remarks_edit = QLineEdit()
        layout.addRow("Remarks:", self.remarks_edit)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addRow(button_box)
    
    def load_forecast_data(self):
        """Load forecast data into the form."""
        if not self.forecast:
            return
        
        # Set equipment
        index = self.equipment_combo.findData(self.forecast['equipment_id'])
        if index >= 0:
            self.equipment_combo.setCurrentIndex(index)
        
        # Set fiscal year
        index = self.fiscal_year_combo.findData(self.forecast['fiscal_year'])
        if index >= 0:
            self.fiscal_year_combo.setCurrentIndex(index)
        
        # Set overhaul type
        index = self.overhaul_type_combo.findText(self.forecast['overhaul_type'])
        if index >= 0:
            self.overhaul_type_combo.setCurrentIndex(index)
        
        # Set requirement
        self.requirement_edit.setText(str(self.forecast['total_requirement']))
        
        # Set remarks
        self.remarks_edit.setText(self.forecast['remarks'] or "")
    
    def get_forecast_data(self):
        """Get forecast data from the form with input validation for total_requirement."""
        try:
            total_requirement = float(self.requirement_edit.text() or 0)
        except ValueError:
            QMessageBox.warning(self, "Invalid Input", "Total Requirement must be a number.")
            return None
        return {
            'equipment_id': self.equipment_combo.currentData(),
            'fiscal_year': self.fiscal_year_combo.currentData(),
            'overhaul_type': self.overhaul_type_combo.currentText(),
            'total_requirement': total_requirement,
            'remarks': self.remarks_edit.text()
        }

class ConditioningForecastDialog(QDialog):
    """Dialog for adding/editing conditioning forecasts."""
    
    def __init__(self, forecast=None, parent=None):
        super().__init__(parent)
        self.forecast = forecast
        self.setup_ui()
        if forecast:
            self.load_forecast_data()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("Conditioning Forecast")
        layout = QFormLayout(self)
        
        # Equipment selection
        self.equipment_combo = QComboBox()
        equipment_list = models.Equipment.get_all() or []
        for eq in equipment_list:
            from utils import format_equipment_for_dropdown
            text = format_equipment_for_dropdown(eq)
            self.equipment_combo.addItem(text, eq['equipment_id'])
        layout.addRow("Equipment:", self.equipment_combo)
        
        # Fiscal year
        self.fiscal_year_combo = QComboBox()
        fiscal_years = utils.generate_fiscal_years()
        for year in fiscal_years:
            self.fiscal_year_combo.addItem(str(year), year)
        layout.addRow("Fiscal Year:", self.fiscal_year_combo)
        
        # Conditioning type
        self.conditioning_type_combo = QComboBox()
        self.conditioning_type_combo.addItems([
            "Tyre Rotation", "Tyre Inspection", "Tyre Replacement",
            "Battery Check", "Battery Replacement",
            "Filter Change", "Filter Inspection",
            "General Inspection", "Other"
        ])
        layout.addRow("Conditioning Type:", self.conditioning_type_combo)
        
        # Total requirement
        self.requirement_edit = QLineEdit()
        self.requirement_edit.setValidator(QDoubleValidator(0, 999999, 2))  # Only allow valid numbers
        layout.addRow("Total Requirement:", self.requirement_edit)
        
        # Remarks
        self.remarks_edit = QLineEdit()
        layout.addRow("Remarks:", self.remarks_edit)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addRow(button_box)
    
    def load_forecast_data(self):
        """Load forecast data into the form."""
        if not self.forecast:
            return
        
        # Set equipment
        index = self.equipment_combo.findData(self.forecast['equipment_id'])
        if index >= 0:
            self.equipment_combo.setCurrentIndex(index)
        
        # Set fiscal year
        index = self.fiscal_year_combo.findData(self.forecast['fiscal_year'])
        if index >= 0:
            self.fiscal_year_combo.setCurrentIndex(index)
        
        # Set conditioning type
        index = self.conditioning_type_combo.findText(self.forecast['conditioning_type'])
        if index >= 0:
            self.conditioning_type_combo.setCurrentIndex(index)
        
        # Set requirement
        self.requirement_edit.setText(str(self.forecast['total_requirement']))
        
        # Set remarks
        self.remarks_edit.setText(self.forecast['remarks'] or "")
    
    def get_forecast_data(self):
        """Get forecast data from the form with input validation for total_requirement."""
        try:
            total_requirement = float(self.requirement_edit.text() or 0)
        except ValueError:
            QMessageBox.warning(self, "Invalid Input", "Total Requirement must be a number.")
            return None
        return {
            'equipment_id': self.equipment_combo.currentData(),
            'fiscal_year': self.fiscal_year_combo.currentData(),
            'conditioning_type': self.conditioning_type_combo.currentText(),
            'total_requirement': total_requirement,
            'remarks': self.remarks_edit.text()
        }


class TyreForecastDialog(QDialog):
    """Dialog for adding/editing tyre forecasts."""
    
    TYRE_TYPES = [
        "Cover Outer 1500X21 12 Ply",
        "Cover Outer 1200X20 18 PLy",
        "Cover outer 1100X20 16 Ply",
        "Cover Outer 355/90X20 18 Ply",
        "Cover outer F 78X15 4 Ply",
        "Cover Outer 3.25X19ST 4 Ply",
        "Cover Outer 235/35/70R16",
        "Cover outer 900x20 14 Ply",
        "TYRE REAR 2.75X18.6 PR LUG",
        "Custom"
    ]

    def __init__(self, forecast=None, parent=None):
        super().__init__(parent)
        self.forecast = forecast
        self.setup_ui()
        if forecast:
            self.load_forecast_data()

    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("Tyre Forecast")
        layout = QFormLayout(self)
        # Equipment selection
        self.equipment_combo = QComboBox()
        equipment_list = models.Equipment.get_all() or []
        for eq in equipment_list:
            from utils import format_equipment_for_dropdown
            text = format_equipment_for_dropdown(eq)
            self.equipment_combo.addItem(text, eq['equipment_id'])
        layout.addRow("Equipment:", self.equipment_combo)
        # Fiscal year
        self.fiscal_year_combo = QComboBox()
        fiscal_years = utils.generate_fiscal_years()
        for year in fiscal_years:
            self.fiscal_year_combo.addItem(str(year), year)
        layout.addRow("Fiscal Year:", self.fiscal_year_combo)
        # Tyre type
        self.tyre_type_combo = QComboBox()
        self.tyre_type_combo.addItems(self.TYRE_TYPES)
        self.tyre_type_combo.setEditable(True)
        self.tyre_type_combo.setCurrentIndex(-1)  # No default selection
        layout.addRow("Tyre Type:", self.tyre_type_combo)
        # Quantity required
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(20)
        self.quantity_spin.setValue(4)  # Default to 4 tyres
        layout.addRow("Quantity Required:", self.quantity_spin)
        # Total requirement (cost)
        self.requirement_edit = QLineEdit()
        self.requirement_edit.setValidator(QDoubleValidator(0, 999999, 2))
        layout.addRow("Total Requirement (Cost):", self.requirement_edit)
        # Remarks
        self.remarks_edit = QLineEdit()
        layout.addRow("Remarks:", self.remarks_edit)
        # Buttons
        self.button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.ok_button = self.button_box.button(QDialogButtonBox.Ok)
        self.ok_button.setEnabled(False)  # Disabled initially
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        layout.addRow(self.button_box)
        # Connect validation signals
        self.tyre_type_combo.currentTextChanged.connect(self.validate_form)
        self.tyre_type_combo.editTextChanged.connect(self.validate_form)

    def validate_form(self):
        """Validate form fields and enable/disable OK button."""
        tyre_type = self.tyre_type_combo.currentText().strip()
        self.ok_button.setEnabled(bool(tyre_type))

    def accept(self):
        # Lock dialog if no tyre type is selected
        tyre_type = self.tyre_type_combo.currentText().strip()
        if not tyre_type:
            QMessageBox.warning(self, "Input Required", "Please select a Tyre Type before proceeding.")
            return
        super().accept()

    def load_forecast_data(self):
        """Load forecast data into the form."""
        if not self.forecast:
            return
        
        # Set equipment
        index = self.equipment_combo.findData(self.forecast['equipment_id'])
        if index >= 0:
            self.equipment_combo.setCurrentIndex(index)
        
        # Set fiscal year
        index = self.fiscal_year_combo.findData(self.forecast['fiscal_year'])
        if index >= 0:
            self.fiscal_year_combo.setCurrentIndex(index)
        
        # Set tyre type
        self.tyre_type_combo.setCurrentText(self.forecast['tyre_type'])
        
        # Set quantity
        self.quantity_spin.setValue(self.forecast.get('quantity_required', 4))
        
        # Set requirement
        self.requirement_edit.setText(str(self.forecast['total_requirement']))
        
        # Set remarks
        self.remarks_edit.setText(self.forecast['remarks'] or "")

    def get_forecast_data(self):
        """Get forecast data from the form."""
        try:
            total_requirement = float(self.requirement_edit.text() or 0)
        except ValueError:
            QMessageBox.warning(self, "Invalid Input", "Total Requirement must be a number.")
            return None
        return {
            'equipment_id': self.equipment_combo.currentData(),
            'fiscal_year': self.fiscal_year_combo.currentData(),
            'tyre_type': self.tyre_type_combo.currentText(),
            'quantity_required': self.quantity_spin.value(),
            'total_requirement': total_requirement,
            'remarks': self.remarks_edit.text()
        }


class BatteryForecastDialog(QDialog):
    """Dialog for adding/editing battery forecasts."""
    
    def __init__(self, forecast=None, parent=None):
        super().__init__(parent)
        self.forecast = forecast
        self.setup_ui()
        if forecast:
            self.load_forecast_data()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("Battery Forecast")
        layout = QFormLayout(self)
        
        # Equipment selection
        self.equipment_combo = QComboBox()
        equipment_list = models.Equipment.get_all() or []
        for eq in equipment_list:
            from utils import format_equipment_for_dropdown
            text = format_equipment_for_dropdown(eq)
            self.equipment_combo.addItem(text, eq['equipment_id'])
        layout.addRow("Equipment:", self.equipment_combo)
        
        # Fiscal year
        self.fiscal_year_combo = QComboBox()
        fiscal_years = utils.generate_fiscal_years()
        for year in fiscal_years:
            self.fiscal_year_combo.addItem(str(year), year)
        layout.addRow("Fiscal Year:", self.fiscal_year_combo)
        
        # Battery type
        self.battery_type_combo = QComboBox()
        self.battery_type_combo.addItems([
            "Lead Acid", "AGM", "Gel", "Lithium Ion", "Nickel Metal Hydride",
            "Maintenance Free", "Wet Cell", "Other"
        ])
        self.battery_type_combo.setEditable(True)
        layout.addRow("Battery Type:", self.battery_type_combo)
        
        # Voltage dropdown
        self.voltage_combo = QComboBox()
        self.voltage_combo.addItem("12V", 12)
        self.voltage_combo.addItem("24V", 24)
        self.voltage_combo.addItem("Custom", 0)
        self.voltage_combo.setEditable(False)
        self.voltage_combo.setCurrentText("12V")
        
        # Custom voltage input
        self.custom_voltage_spin = QDoubleSpinBox()
        self.custom_voltage_spin.setMinimum(1.0)
        self.custom_voltage_spin.setMaximum(1000.0)
        self.custom_voltage_spin.setValue(12.0)
        self.custom_voltage_spin.setSuffix(" V")
        self.custom_voltage_spin.setVisible(False)
        
        # Voltage layout
        voltage_layout = QHBoxLayout()
        voltage_layout.addWidget(self.voltage_combo)
        voltage_layout.addWidget(self.custom_voltage_spin)
        
        # Connect signal to show/hide custom voltage input
        def on_voltage_changed():
            is_custom = self.voltage_combo.currentText() == "Custom"
            self.custom_voltage_spin.setVisible(is_custom)
            if not is_custom:
                self.update_battery_type()
        
        self.voltage_combo.currentTextChanged.connect(on_voltage_changed)
        self.voltage_combo.currentTextChanged.connect(self.update_battery_type)
        self.custom_voltage_spin.valueChanged.connect(self.update_battery_type)
        
        layout.addRow("Voltage:", voltage_layout)
        
        # AH (Ampere Hours)
        self.ah_spin = QSpinBox()
        self.ah_spin.setMinimum(1)
        self.ah_spin.setMaximum(9999)
        self.ah_spin.setValue(125)  # Default 125 AH
        self.ah_spin.setSuffix(" AH")
        self.ah_spin.valueChanged.connect(self.update_battery_type)
        layout.addRow("Ampere Hours (AH):", self.ah_spin)
        
        # Quantity required
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(20)
        self.quantity_spin.setValue(1)
        layout.addRow("Quantity Required:", self.quantity_spin)
        
        # Total requirement (cost)
        self.requirement_edit = QLineEdit()
        self.requirement_edit.setValidator(QDoubleValidator(0, 999999, 2))
        layout.addRow("Total Requirement (Cost):", self.requirement_edit)
        
        # Remarks
        self.remarks_edit = QLineEdit()
        layout.addRow("Remarks:", self.remarks_edit)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addRow(button_box)
    
    def update_battery_type(self):
        """Update battery type based on voltage and AH values."""
        # Get voltage value
        if self.voltage_combo.currentText() == "Custom":
            voltage = self.custom_voltage_spin.value()
        else:
            voltage = float(self.voltage_combo.currentText().replace('V', ''))

        # Get AH
        ah = self.ah_spin.value()

        # Create battery type string using utility function
        battery_type = utils.calculate_battery_type(voltage, ah)
        self.battery_type_combo.setCurrentText(battery_type)

        # Add to combo if not already there
        if self.battery_type_combo.findText(battery_type) == -1:
            self.battery_type_combo.addItem(battery_type)
    
    def load_forecast_data(self):
        """Load forecast data into the form."""
        if not self.forecast:
            return
        
        # Set equipment
        index = self.equipment_combo.findData(self.forecast['equipment_id'])
        if index >= 0:
            self.equipment_combo.setCurrentIndex(index)
        
        # Set fiscal year
        index = self.fiscal_year_combo.findData(self.forecast['fiscal_year'])
        if index >= 0:
            self.fiscal_year_combo.setCurrentIndex(index)
        
        # Set battery type
        self.battery_type_combo.setCurrentText(self.forecast['battery_type'])
        
        # Set voltage - handle both dropdown and custom
        voltage = self.forecast.get('voltage', 12.0)
        if voltage in [12.0, 24.0]:
            self.voltage_combo.setCurrentText(f"{int(voltage)}V")
        else:
            self.voltage_combo.setCurrentText("Custom")
            self.custom_voltage_spin.setValue(voltage)
            self.custom_voltage_spin.setVisible(True)
        
        # Set AH
        self.ah_spin.setValue(self.forecast.get('ampere_hours', 125))
        
        # Set quantity
        self.quantity_spin.setValue(self.forecast.get('quantity_required', 1))
        
        # Set requirement
        self.requirement_edit.setText(str(self.forecast['total_requirement']))
        
        # Set remarks
        self.remarks_edit.setText(self.forecast['remarks'] or "")
    
    def get_forecast_data(self):
        """Get forecast data from the form."""
        try:
            total_requirement = float(self.requirement_edit.text() or 0)
        except ValueError:
            QMessageBox.warning(self, "Invalid Input", "Total Requirement must be a number.")
            return None

        # Get voltage value
        if self.voltage_combo.currentText() == "Custom":
            voltage = self.custom_voltage_spin.value()
        else:
            voltage = float(self.voltage_combo.currentText().replace('V', ''))

        # Calculate battery type from voltage and AH (override user input)
        ampere_hours = self.ah_spin.value()
        calculated_battery_type = utils.calculate_battery_type(voltage, ampere_hours)

        return {
            'equipment_id': self.equipment_combo.currentData(),
            'fiscal_year': self.fiscal_year_combo.currentData(),
            'battery_type': calculated_battery_type,  # Use calculated value
            'voltage': voltage,
            'ampere_hours': ampere_hours,
            'quantity_required': self.quantity_spin.value(),
            'total_requirement': total_requirement,
            'remarks': self.remarks_edit.text()
        }


class EquipmentForecastDialog(QDialog):
    """Dialog for adding/editing equipment forecasts."""
    
    def __init__(self, forecast=None, parent=None):
        super().__init__(parent)
        self.forecast = forecast
        self.setup_ui()
        if forecast:
            self.load_forecast_data()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("Equipment Forecast")
        layout = QFormLayout(self)
        
        # Equipment selection
        self.equipment_combo = QComboBox()
        equipment_list = models.Equipment.get_all() or []
        for eq in equipment_list:
            from utils import format_equipment_for_dropdown
            text = format_equipment_for_dropdown(eq)
            self.equipment_combo.addItem(text, eq['equipment_id'])
        layout.addRow("Equipment:", self.equipment_combo)
        
        # Fiscal year
        self.fiscal_year_combo = QComboBox()
        fiscal_years = utils.generate_fiscal_years()
        for year in fiscal_years:
            self.fiscal_year_combo.addItem(str(year), year)
        layout.addRow("Fiscal Year:", self.fiscal_year_combo)
        
        # Equipment type (for replacement)
        self.equipment_type_combo = QComboBox()
        self.equipment_type_combo.addItems([
            "Vehicle", "Generator", "Pump", "Compressor", "Tool",
            "Engine", "Transmission", "Component", "Other"
        ])
        self.equipment_type_combo.setEditable(True)
        layout.addRow("Equipment Type:", self.equipment_type_combo)
        
        # Replacement reason
        self.replacement_reason_combo = QComboBox()
        self.replacement_reason_combo.addItems([
            "End of Life", "Excessive Wear", "Obsolescence", 
            "Efficiency Upgrade", "Damage", "Maintenance Cost",
            "Technology Upgrade", "Other"
        ])
        self.replacement_reason_combo.setEditable(True)
        layout.addRow("Replacement Reason:", self.replacement_reason_combo)
        
        # Quantity required
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(100)
        self.quantity_spin.setValue(1)
        layout.addRow("Quantity Required:", self.quantity_spin)
        
        # Total requirement (cost)
        self.requirement_edit = QLineEdit()
        self.requirement_edit.setValidator(QDoubleValidator(0, 99999999, 2))
        layout.addRow("Total Requirement (Cost):", self.requirement_edit)
        
        # Remarks
        self.remarks_edit = QTextEdit()
        self.remarks_edit.setMaximumHeight(100)
        layout.addRow("Remarks:", self.remarks_edit)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addRow(button_box)
    
    def load_forecast_data(self):
        """Load forecast data into the form."""
        if not self.forecast:
            return
        
        # Set equipment
        index = self.equipment_combo.findData(self.forecast['equipment_id'])
        if index >= 0:
            self.equipment_combo.setCurrentIndex(index)
        
        # Set fiscal year
        index = self.fiscal_year_combo.findData(self.forecast['fiscal_year'])
        if index >= 0:
            self.fiscal_year_combo.setCurrentIndex(index)
        
        # Set equipment type
        self.equipment_type_combo.setCurrentText(self.forecast['equipment_type'])
        
        # Set replacement reason
        self.replacement_reason_combo.setCurrentText(
            self.forecast.get('replacement_reason', '')
        )
        
        # Set quantity
        self.quantity_spin.setValue(self.forecast.get('quantity_required', 1))
        
        # Set requirement
        self.requirement_edit.setText(str(self.forecast['total_requirement']))
        
        # Set remarks
        self.remarks_edit.setPlainText(self.forecast['remarks'] or "")
    
    def get_forecast_data(self):
        """Get forecast data from the form."""
        try:
            total_requirement = float(self.requirement_edit.text() or 0)
        except ValueError:
            QMessageBox.warning(self, "Invalid Input", "Total Requirement must be a number.")
            return None
        
        return {
            'equipment_id': self.equipment_combo.currentData(),
            'fiscal_year': self.fiscal_year_combo.currentData(),
            'equipment_type': self.equipment_type_combo.currentText(),
            'replacement_reason': self.replacement_reason_combo.currentText(),
            'quantity_required': self.quantity_spin.value(),
            'total_requirement': total_requirement,
            'remarks': self.remarks_edit.toPlainText()
        }


class BatteryDialog(QDialog):
    """Dialog for adding or editing battery records."""
    
    def __init__(self, battery=None, equipment_list=None, parent=None):
        super().__init__(parent)
        self.battery = battery
        self.equipment_list = equipment_list or []
        self.setWindowTitle("Add Battery Record" if battery is None else "Edit Battery Record")
        self.resize(400, 300)
        
        self.setup_ui()
        self.populate_fields()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        
        # Form layout for fields
        form_layout = QFormLayout()
        
        # Equipment
        self.equipment_combo = QComboBox()
        for equip in self.equipment_list:
            display_text = utils.format_equipment_display(equip)
            equipment_id = equip.get('equipment_id') or equip.get('EquipmentID')
            self.equipment_combo.addItem(display_text, equipment_id)
        form_layout.addRow("Equipment:", self.equipment_combo)
        
        # Done Date
        self.done_date_edit = QDateEdit()
        self.done_date_edit.setDate(QDate.currentDate())
        self.done_date_edit.setCalendarPopup(True)
        form_layout.addRow("Done Date:", self.done_date_edit)
        
        # Custom Life Months
        self.custom_life_spin = QSpinBox()
        self.custom_life_spin.setMinimum(1)
        self.custom_life_spin.setMaximum(120)  # Max 10 years
        self.custom_life_spin.setValue(24)  # Default 2 years
        self.custom_life_spin.setSuffix(" months")
        form_layout.addRow("Battery Life:", self.custom_life_spin)
        
        # Help text
        help_label = QLabel("Default battery life is 24 months (2 years). Adjust if needed.")
        help_label.setStyleSheet("color: gray; font-size: 10px;")
        form_layout.addRow("", help_label)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def populate_fields(self):
        """Populate dialog fields with battery data if editing."""
        if not self.battery:
            return
        
        # Find and select the equipment
        equipment_id = self.battery.get('equipment_id')
        if equipment_id:
            for i in range(self.equipment_combo.count()):
                if self.equipment_combo.itemData(i) == equipment_id:
                    self.equipment_combo.setCurrentIndex(i)
                    break
        
        # Set done date
        done_date = self.battery.get('done_date')
        if done_date:
            try:
                from datetime import datetime
                date_obj = datetime.strptime(str(done_date), '%Y-%m-%d').date()
                self.done_date_edit.setDate(QDate(date_obj))
            except ValueError:
                pass
        
        # Set custom life
        custom_life = self.battery.get('custom_life_months')
        if custom_life:
            self.custom_life_spin.setValue(custom_life)
    
    def get_battery_data(self):
        """Get battery data from dialog fields."""
        return {
            'battery_id': self.battery.get('battery_id') if self.battery else None,
            'equipment_id': self.equipment_combo.currentData(),
            'done_date': self.done_date_edit.date().toString('yyyy-MM-dd'),
            'custom_life_months': self.custom_life_spin.value()
        }
    
    def validate(self):
        """Validate the input data."""
        if not self.equipment_combo.currentData():
            QMessageBox.warning(self, "Validation Error", "Please select an equipment.")
            return False
        
        return True
    
    def accept(self):
        """Handle dialog acceptance."""
        if not self.validate():
            return
        
        super().accept()


class MaintenanceTypeSelectionDialog(QDialog):
    """Dialog for selecting maintenance type when sending equipment to maintenance."""
    
    def __init__(self, equipment, parent=None):
        super().__init__(parent)
        self.equipment = equipment
        self.setWindowTitle("Send to Maintenance")
        self.setModal(True)
        self.resize(400, 300)
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        
        # Equipment info section
        info_group = QGroupBox("Equipment Information")
        info_layout = QFormLayout()
        
        # Show equipment details
        make_type = self.equipment.get('make_and_type', 'Unknown') if isinstance(self.equipment, dict) else getattr(self.equipment, 'make_and_type', 'Unknown')
        ba_number = self.equipment.get('ba_number', 'N/A') if isinstance(self.equipment, dict) else getattr(self.equipment, 'ba_number', 'N/A')
        serial_number = self.equipment.get('serial_number', 'N/A') if isinstance(self.equipment, dict) else getattr(self.equipment, 'serial_number', 'N/A')
        
        info_layout.addRow("Equipment:", QLabel(make_type))
        info_layout.addRow("BA Number:", QLabel(ba_number))
        info_layout.addRow("Serial Number:", QLabel(serial_number))
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # Maintenance type selection
        type_group = QGroupBox("Select Maintenance Type")
        type_layout = QVBoxLayout()
        
        # Add description
        desc_label = QLabel("Choose the type of maintenance to create for this equipment:")
        desc_label.setWordWrap(True)
        type_layout.addWidget(desc_label)
        
        # Create radio buttons for each maintenance type
        from PyQt5.QtWidgets import QRadioButton, QButtonGroup
        
        self.button_group = QButtonGroup()
        
        # TM-1 (Half Yearly)
        self.tm1_radio = QRadioButton("TM-1 (Half Yearly - 6 months)")
        self.tm1_radio.setToolTip("Technical Maintenance 1 - Due every 6 months")
        self.button_group.addButton(self.tm1_radio, 0)
        type_layout.addWidget(self.tm1_radio)
        
        # TM-2 (Yearly)
        self.tm2_radio = QRadioButton("TM-2 (Yearly - 12 months)")
        self.tm2_radio.setToolTip("Technical Maintenance 2 - Due every 12 months")
        self.button_group.addButton(self.tm2_radio, 1)
        type_layout.addWidget(self.tm2_radio)
        
        # Yearly
        self.yearly_radio = QRadioButton("Yearly (Regular - 12 months)")
        self.yearly_radio.setToolTip("Regular yearly maintenance - Due every 12 months")
        self.button_group.addButton(self.yearly_radio, 2)
        type_layout.addWidget(self.yearly_radio)
        
        # Monthly
        self.monthly_radio = QRadioButton("Monthly (Regular - 1 month)")
        self.monthly_radio.setToolTip("Regular monthly maintenance - Due every month")
        self.button_group.addButton(self.monthly_radio, 3)
        type_layout.addWidget(self.monthly_radio)
        
        # Set TM-1 as default selection
        self.tm1_radio.setChecked(True)
        
        type_group.setLayout(type_layout)
        layout.addWidget(type_group)
        
        # Information section about due date calculation
        info_group = QGroupBox("Information")
        info_layout = QVBoxLayout()
        
        info_text = QLabel(
            "• Due date will be automatically calculated based on maintenance type\n"
            "• TM-1: 6 months from today\n"
            "• TM-2: 12 months from today\n"
            "• Yearly: 12 months from today\n"
            "• Monthly: 1 month from today\n"
            "• Done date will be recorded when maintenance is actually completed"
        )
        info_text.setWordWrap(True)
        info_text.setStyleSheet("color: #666; font-size: 11px; padding: 10px;")
        info_layout.addWidget(info_text)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # Buttons
        from PyQt5.QtWidgets import QDialogButtonBox
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def get_maintenance_data(self):
        """Get the selected maintenance data."""
        # Map button group IDs to categories and their periods
        category_mapping = {
            0: ('TM-1', 6),      # TM-1: 6 months
            1: ('TM-2', 12),     # TM-2: 12 months
            2: ('Yearly', 12),   # Yearly: 12 months
            3: ('Monthly', 1)    # Monthly: 1 month
        }
        
        selected_id = self.button_group.checkedId()
        category, period_months = category_mapping.get(selected_id, ('TM-1', 6))
        
        return {
            'category': category,
            'period_months': period_months,
            'auto_done_date': False,  # Never auto-set done date - only when actually completed
            'auto_due_date': True,    # Always auto-calculate due date
            'equipment_id': self.equipment.get('equipment_id') if isinstance(self.equipment, dict) else getattr(self.equipment, 'equipment_id', None),
            'equipment_name': self.equipment.get('make_and_type', 'Unknown') if isinstance(self.equipment, dict) else getattr(self.equipment, 'make_and_type', 'Unknown')
        }
    
    def validate(self):
        """Validate the selection."""
        # Check if any radio button is selected
        if self.button_group.checkedId() == -1:
            QMessageBox.warning(
                self,
                "Selection Required",
                "Please select a maintenance type.",
                QMessageBox.StandardButton.Ok
            )
            return False
        
        return True
    
    def accept(self):
        """Handle dialog acceptance."""
        if not self.validate():
            return
        
        super().accept()


class PolicyDocsPathDialog(QDialog):
    """Dialog for setting the policy documents path."""
    
    def __init__(self, parent=None, current_path=""):
        super().__init__(parent)
        self.current_path = current_path
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("Set Policy Documents Path")
        self.setModal(True)
        self.resize(500, 250)
        
        layout = QVBoxLayout(self)
        
        # Title and instructions
        title_label = QLabel("Policy Documents Path Configuration")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Instructions
        instructions = QLabel(
            "Enter the full path to your policy documents PDF file or folder.\n"
            "This path will be used when you click the 'Policy Docs' button.\n\n"
            "Examples:\n"
            "• C:\\Documents\\Policies\\Equipment_Policy.pdf\n"
            "• C:\\Company_Policies\\"
        )
        instructions.setStyleSheet("""
            QLabel {
                color: #666;
                padding: 10px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                line-height: 1.4;
            }
        """)
        layout.addWidget(instructions)
        
        # Path input section
        path_group = QGroupBox("Policy Documents Path")
        path_layout = QVBoxLayout(path_group)
        
        # Current path display (if any)
        if self.current_path:
            current_label = QLabel(f"Current path: {self.current_path}")
            current_label.setStyleSheet("color: #28a745; font-weight: bold;")
            path_layout.addWidget(current_label)
        
        # Path input field
        self.path_input = QLineEdit()
        self.path_input.setPlaceholderText("Paste or type the full path here...")
        self.path_input.setText(self.current_path)
        self.path_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                font-size: 12px;
                border: 2px solid #ddd;
                border-radius: 5px;
            }
            QLineEdit:focus {
                border-color: #0078d7;
            }
        """)
        path_layout.addWidget(self.path_input)
        
        # Browse button
        browse_layout = QHBoxLayout()
        browse_layout.addStretch()
        
        browse_btn = QPushButton("Browse...")
        browse_btn.clicked.connect(self.browse_for_path)
        browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 6px 12px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        browse_layout.addWidget(browse_btn)
        
        path_layout.addLayout(browse_layout)
        layout.addWidget(path_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        button_layout.addWidget(cancel_btn)
        
        save_btn = QPushButton("Save Path")
        save_btn.clicked.connect(self.accept)
        save_btn.setDefault(True)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                min-width: 80px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        button_layout.addWidget(save_btn)
        
        layout.addLayout(button_layout)
        
    def browse_for_path(self):
        """Open file/folder browser for path selection."""
        from PyQt5.QtWidgets import QFileDialog
        
        # Ask user what they want to select with clearer options
        reply = QMessageBox.question(
            self,
            "Select Policy Documents",
            "What would you like to configure for policy documents?\n\n"
            "• Click 'Yes' to select a FOLDER containing multiple PDF files\n"
            "• Click 'No' to select a single PDF file\n"
            "• Click 'Cancel' to go back",
            QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
            QMessageBox.Yes
        )
        
        if reply == QMessageBox.Cancel:
            return
        elif reply == QMessageBox.Yes:
            # Select folder (recommended for new functionality)
            folder_path = QFileDialog.getExistingDirectory(
                self,
                "Select Policy Documents Folder",
                "",
                QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
            )
            if folder_path:
                self.path_input.setText(folder_path)
                QMessageBox.information(
                    self,
                    "Folder Selected",
                    f"Policy documents folder selected:\n{folder_path}\n\n"
                    "When you click 'Policy Docs' button, you'll be able to "
                    "choose from all PDF files in this folder."
                )
        else:
            # Select single PDF file (legacy functionality)
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Select Policy Documents PDF",
                "",
                "PDF Files (*.pdf);;All Files (*)"
            )
            if file_path:
                self.path_input.setText(file_path)
                QMessageBox.information(
                    self,
                    "File Selected",
                    f"Policy document file selected:\n{file_path}\n\n"
                    "This PDF will open directly when you click 'Policy Docs' button."
                )
    
    def get_path(self):
        """Get the entered path."""
        return self.path_input.text().strip()


class PDFSelectionDialog(QDialog):
    """Dialog for selecting a specific PDF from a folder containing multiple PDFs."""
    
    def __init__(self, parent=None, folder_path=""):
        super().__init__(parent)
        self.folder_path = folder_path
        self.selected_pdf = None
        self.setup_ui()
        self.load_pdf_files()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("Select Policy Document")
        self.setModal(True)
        self.resize(600, 400)
        
        layout = QVBoxLayout(self)
        
        # Title and folder path
        title_label = QLabel("Select Policy Document")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Folder path display
        folder_label = QLabel(f"Folder: {self.folder_path}")
        folder_label.setStyleSheet("""
            QLabel {
                color: #666;
                padding: 8px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-family: monospace;
            }
        """)
        layout.addWidget(folder_label)
        
        # PDF files list
        list_group = QGroupBox("Available PDF Documents")
        list_layout = QVBoxLayout(list_group)
        
        # Search/filter box
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search PDF files...")
        self.search_input.textChanged.connect(self.filter_pdf_list)
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 6px;
                font-size: 12px;
                border: 2px solid #ddd;
                border-radius: 4px;
                margin-bottom: 5px;
            }
            QLineEdit:focus {
                border-color: #0078d7;
            }
        """)
        list_layout.addWidget(self.search_input)
        
        # PDF list widget
        self.pdf_list = QListWidget()
        self.pdf_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px;
                selection-background-color: #0078d7;
                selection-color: white;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:hover {
                background-color: #f0f8ff;
            }
            QListWidget::item:selected {
                background-color: #0078d7;
                color: white;
            }
        """)
        self.pdf_list.itemDoubleClicked.connect(self.accept)
        list_layout.addWidget(self.pdf_list)
        
        layout.addWidget(list_group)
        
        # Status label
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.status_label)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        # Open folder button
        open_folder_btn = QPushButton("Open Folder")
        open_folder_btn.clicked.connect(self.open_folder)
        open_folder_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        button_layout.addWidget(open_folder_btn)
        
        button_layout.addStretch()
        
        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        button_layout.addWidget(cancel_btn)
        
        open_btn = QPushButton("Open Selected")
        open_btn.clicked.connect(self.accept)
        open_btn.setDefault(True)
        open_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                min-width: 120px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        button_layout.addWidget(open_btn)
        
        layout.addLayout(button_layout)
        
    def load_pdf_files(self):
        """Load PDF files from the specified folder."""
        import os
        
        try:
            if not os.path.exists(self.folder_path) or not os.path.isdir(self.folder_path):
                self.status_label.setText("⚠️ Folder not found or is not a directory")
                return
            
            # Get all PDF files in the folder
            pdf_files = []
            for file in os.listdir(self.folder_path):
                if file.lower().endswith('.pdf'):
                    pdf_files.append(file)
            
            # Sort files alphabetically
            pdf_files.sort()
            
            if not pdf_files:
                self.status_label.setText("ℹ️ No PDF files found in this folder")
                return
            
            # Add files to list
            for pdf_file in pdf_files:
                self.pdf_list.addItem(pdf_file)
            
            self.status_label.setText(f"📄 Found {len(pdf_files)} PDF file(s)")
            
            # Select first item by default
            if pdf_files:
                self.pdf_list.setCurrentRow(0)
                
        except Exception as e:
            self.status_label.setText(f"❌ Error loading PDF files: {str(e)}")
    
    def filter_pdf_list(self):
        """Filter the PDF list based on search text."""
        search_text = self.search_input.text().lower()
        
        for i in range(self.pdf_list.count()):
            item = self.pdf_list.item(i)
            item_text = item.text().lower()
            item.setHidden(search_text not in item_text)
    
    def open_folder(self):
        """Open the folder in file explorer."""
        import os
        import platform
        import subprocess
        
        try:
            system = platform.system()
            if system == "Windows":
                os.startfile(self.folder_path)
            elif system == "Darwin":  # macOS
                subprocess.Popen(["open", self.folder_path])
            else:  # Linux and other Unix-like systems
                subprocess.Popen(["xdg-open", self.folder_path])
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Could not open folder:\n{str(e)}")
    
    def accept(self):
        """Handle dialog acceptance - get selected PDF."""
        current_item = self.pdf_list.currentItem()
        if current_item and not current_item.isHidden():
            self.selected_pdf = current_item.text()
            super().accept()
        else:
            QMessageBox.warning(self, "No Selection", "Please select a PDF file to open.")
    
    def get_selected_pdf_path(self):
        """Get the full path to the selected PDF file."""
        if self.selected_pdf:
            import os
            return os.path.join(self.folder_path, self.selected_pdf)
        return None
