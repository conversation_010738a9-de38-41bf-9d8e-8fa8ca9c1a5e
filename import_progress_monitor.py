#!/usr/bin/env python3
"""
Import Progress Monitor for PROJECT-ALPHA
Provides detailed logging and progress tracking to identify where Excel import 
processes get stuck or fail, with cross-system compatibility.
"""

import os
import sys
import logging
import time
import threading
import json
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from pathlib import Path

logger = logging.getLogger('import_progress_monitor')

class ImportProgressMonitor:
    """Monitors Excel import progress with detailed tracking and diagnostics."""
    
    def __init__(self, session_id: Optional[str] = None):
        self.session_id = session_id or datetime.now().strftime('%Y%m%d_%H%M%S')
        self.progress_data = {
            'session_id': self.session_id,
            'start_time': None,
            'end_time': None,
            'current_phase': 'INITIALIZING',
            'phases': {},
            'sheets': {},
            'errors': [],
            'warnings': [],
            'performance_metrics': {},
            'system_info': {}
        }
        
        self.phase_start_time = None
        self.monitoring_active = False
        self.progress_callbacks = []
        
        # Setup progress logging
        self.setup_progress_logging()
        
        # Collect system info
        self.collect_system_info()
    
    def setup_progress_logging(self):
        """Setup dedicated progress logging."""
        try:
            # Create progress log directory
            log_dir = Path('logs') / 'import_progress'
            log_dir.mkdir(parents=True, exist_ok=True)
            
            # Setup progress log file
            log_file = log_dir / f"import_progress_{self.session_id}.log"
            
            # Create progress logger
            progress_logger = logging.getLogger(f'progress_{self.session_id}')
            progress_logger.setLevel(logging.INFO)
            
            # File handler for progress
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - PROGRESS - %(message)s')
            file_handler.setFormatter(formatter)
            progress_logger.addHandler(file_handler)
            
            self.progress_logger = progress_logger
            logger.info(f"Progress logging setup: {log_file}")
            
        except Exception as e:
            logger.warning(f"Could not setup progress logging: {e}")
            self.progress_logger = logger
    
    def collect_system_info(self):
        """Collect system information for diagnostics."""
        try:
            import platform
            import psutil
            
            self.progress_data['system_info'] = {
                'platform': platform.platform(),
                'python_version': sys.version,
                'memory_total_gb': psutil.virtual_memory().total / (1024**3),
                'memory_available_gb': psutil.virtual_memory().available / (1024**3),
                'cpu_count': os.cpu_count(),
                'timestamp': datetime.now().isoformat()
            }
        except ImportError:
            # Fallback without psutil
            import platform
            self.progress_data['system_info'] = {
                'platform': platform.platform(),
                'python_version': sys.version,
                'cpu_count': os.cpu_count(),
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.warning(f"Could not collect system info: {e}")
    
    def start_import(self, file_path: str):
        """Start import monitoring."""
        self.progress_data['start_time'] = datetime.now().isoformat()
        self.progress_data['file_path'] = file_path
        self.monitoring_active = True
        
        self.progress_logger.info(f"=== IMPORT STARTED ===")
        self.progress_logger.info(f"File: {file_path}")
        self.progress_logger.info(f"Session: {self.session_id}")
        self.progress_logger.info(f"System: {self.progress_data['system_info']['platform']}")
        
        logger.info(f"Import monitoring started for session {self.session_id}")
    
    def start_phase(self, phase_name: str, details: Optional[Dict[str, Any]] = None):
        """Start a new import phase."""
        if self.phase_start_time:
            # End previous phase
            self.end_phase()
        
        self.progress_data['current_phase'] = phase_name
        self.phase_start_time = time.time()
        
        phase_data = {
            'start_time': datetime.now().isoformat(),
            'details': details or {},
            'status': 'IN_PROGRESS',
            'duration_seconds': None,
            'sub_operations': []
        }
        
        self.progress_data['phases'][phase_name] = phase_data
        
        self.progress_logger.info(f"PHASE START: {phase_name}")
        if details:
            self.progress_logger.info(f"  Details: {json.dumps(details, indent=2)}")
        
        # Notify callbacks
        self._notify_callbacks('phase_start', {'phase': phase_name, 'details': details})
    
    def end_phase(self, status: str = 'COMPLETED', error: Optional[str] = None):
        """End current import phase."""
        if not self.phase_start_time:
            return
        
        current_phase = self.progress_data['current_phase']
        duration = time.time() - self.phase_start_time
        
        if current_phase in self.progress_data['phases']:
            self.progress_data['phases'][current_phase].update({
                'end_time': datetime.now().isoformat(),
                'duration_seconds': duration,
                'status': status
            })
            
            if error:
                self.progress_data['phases'][current_phase]['error'] = error
        
        self.progress_logger.info(f"PHASE END: {current_phase} - {status} ({duration:.2f}s)")
        if error:
            self.progress_logger.error(f"  Error: {error}")
        
        self.phase_start_time = None
        
        # Notify callbacks
        self._notify_callbacks('phase_end', {
            'phase': current_phase, 
            'status': status, 
            'duration': duration,
            'error': error
        })
    
    def log_operation(self, operation: str, details: Optional[Dict[str, Any]] = None, level: str = 'INFO'):
        """Log a sub-operation within current phase."""
        current_phase = self.progress_data['current_phase']
        
        operation_data = {
            'timestamp': datetime.now().isoformat(),
            'operation': operation,
            'details': details or {},
            'level': level
        }
        
        if current_phase in self.progress_data['phases']:
            self.progress_data['phases'][current_phase]['sub_operations'].append(operation_data)
        
        log_msg = f"  {operation}"
        if details:
            log_msg += f" - {json.dumps(details)}"
        
        if level == 'ERROR':
            self.progress_logger.error(log_msg)
        elif level == 'WARNING':
            self.progress_logger.warning(log_msg)
        else:
            self.progress_logger.info(log_msg)
    
    def start_sheet_processing(self, sheet_name: str, total_rows: int):
        """Start processing a specific sheet."""
        sheet_data = {
            'start_time': datetime.now().isoformat(),
            'total_rows': total_rows,
            'processed_rows': 0,
            'status': 'IN_PROGRESS',
            'errors': [],
            'warnings': []
        }
        
        self.progress_data['sheets'][sheet_name] = sheet_data
        
        self.progress_logger.info(f"SHEET START: {sheet_name} ({total_rows} rows)")
        self.log_operation(f"Started processing sheet: {sheet_name}", {'total_rows': total_rows})
    
    def update_sheet_progress(self, sheet_name: str, processed_rows: int, details: Optional[Dict[str, Any]] = None):
        """Update sheet processing progress."""
        if sheet_name in self.progress_data['sheets']:
            self.progress_data['sheets'][sheet_name]['processed_rows'] = processed_rows
            
            if details:
                self.progress_data['sheets'][sheet_name].update(details)
            
            total_rows = self.progress_data['sheets'][sheet_name]['total_rows']
            progress_pct = (processed_rows / total_rows * 100) if total_rows > 0 else 0
            
            self.progress_logger.info(f"SHEET PROGRESS: {sheet_name} - {processed_rows}/{total_rows} ({progress_pct:.1f}%)")
    
    def end_sheet_processing(self, sheet_name: str, status: str = 'COMPLETED', final_counts: Optional[Dict[str, int]] = None):
        """End sheet processing."""
        if sheet_name in self.progress_data['sheets']:
            self.progress_data['sheets'][sheet_name].update({
                'end_time': datetime.now().isoformat(),
                'status': status
            })
            
            if final_counts:
                self.progress_data['sheets'][sheet_name]['final_counts'] = final_counts
        
        self.progress_logger.info(f"SHEET END: {sheet_name} - {status}")
        if final_counts:
            self.progress_logger.info(f"  Final counts: {json.dumps(final_counts)}")
    
    def log_error(self, error: str, context: Optional[Dict[str, Any]] = None):
        """Log an error with context."""
        error_data = {
            'timestamp': datetime.now().isoformat(),
            'error': error,
            'context': context or {},
            'phase': self.progress_data['current_phase']
        }
        
        self.progress_data['errors'].append(error_data)
        
        self.progress_logger.error(f"ERROR: {error}")
        if context:
            self.progress_logger.error(f"  Context: {json.dumps(context, indent=2)}")
    
    def log_warning(self, warning: str, context: Optional[Dict[str, Any]] = None):
        """Log a warning with context."""
        warning_data = {
            'timestamp': datetime.now().isoformat(),
            'warning': warning,
            'context': context or {},
            'phase': self.progress_data['current_phase']
        }
        
        self.progress_data['warnings'].append(warning_data)
        
        self.progress_logger.warning(f"WARNING: {warning}")
        if context:
            self.progress_logger.warning(f"  Context: {json.dumps(context, indent=2)}")
    
    def end_import(self, status: str = 'COMPLETED', final_stats: Optional[Dict[str, Any]] = None):
        """End import monitoring."""
        # End current phase if active
        if self.phase_start_time:
            self.end_phase(status)
        
        self.progress_data['end_time'] = datetime.now().isoformat()
        self.progress_data['final_status'] = status
        
        if final_stats:
            self.progress_data['final_stats'] = final_stats
        
        # Calculate total duration
        if self.progress_data['start_time']:
            start_time = datetime.fromisoformat(self.progress_data['start_time'])
            end_time = datetime.fromisoformat(self.progress_data['end_time'])
            total_duration = (end_time - start_time).total_seconds()
            self.progress_data['total_duration_seconds'] = total_duration
        
        self.monitoring_active = False
        
        self.progress_logger.info(f"=== IMPORT ENDED ===")
        self.progress_logger.info(f"Status: {status}")
        self.progress_logger.info(f"Duration: {self.progress_data.get('total_duration_seconds', 0):.2f}s")
        if final_stats:
            self.progress_logger.info(f"Final stats: {json.dumps(final_stats, indent=2)}")
        
        # Save progress data to file
        self.save_progress_data()
        
        logger.info(f"Import monitoring ended for session {self.session_id}")
    
    def save_progress_data(self):
        """Save progress data to JSON file."""
        try:
            progress_dir = Path('logs') / 'import_progress'
            progress_dir.mkdir(parents=True, exist_ok=True)
            
            progress_file = progress_dir / f"progress_data_{self.session_id}.json"
            
            with open(progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Progress data saved: {progress_file}")
            
        except Exception as e:
            logger.error(f"Failed to save progress data: {e}")
    
    def register_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """Register progress callback."""
        self.progress_callbacks.append(callback)
    
    def _notify_callbacks(self, event_type: str, data: Dict[str, Any]):
        """Notify registered callbacks."""
        for callback in self.progress_callbacks:
            try:
                callback(event_type, data)
            except Exception as e:
                logger.error(f"Progress callback error: {e}")
    
    def get_current_status(self) -> Dict[str, Any]:
        """Get current import status."""
        return {
            'session_id': self.session_id,
            'monitoring_active': self.monitoring_active,
            'current_phase': self.progress_data['current_phase'],
            'phases_completed': len([p for p in self.progress_data['phases'].values() if p['status'] == 'COMPLETED']),
            'total_phases': len(self.progress_data['phases']),
            'sheets_processed': len([s for s in self.progress_data['sheets'].values() if s['status'] == 'COMPLETED']),
            'total_sheets': len(self.progress_data['sheets']),
            'errors': len(self.progress_data['errors']),
            'warnings': len(self.progress_data['warnings'])
        }
    
    def generate_progress_report(self) -> str:
        """Generate human-readable progress report."""
        status = self.get_current_status()
        
        report_lines = [
            f"Import Progress Report - Session {self.session_id}",
            "=" * 60,
            f"Status: {'ACTIVE' if status['monitoring_active'] else 'COMPLETED'}",
            f"Current Phase: {status['current_phase']}",
            f"Phases: {status['phases_completed']}/{status['total_phases']} completed",
            f"Sheets: {status['sheets_processed']}/{status['total_sheets']} processed",
            f"Issues: {status['errors']} errors, {status['warnings']} warnings",
            ""
        ]
        
        # Add phase details
        if self.progress_data['phases']:
            report_lines.append("Phase Details:")
            for phase_name, phase_data in self.progress_data['phases'].items():
                duration = phase_data.get('duration_seconds', 0)
                status_str = phase_data.get('status', 'UNKNOWN')
                report_lines.append(f"  {phase_name}: {status_str} ({duration:.2f}s)")
            report_lines.append("")
        
        # Add sheet details
        if self.progress_data['sheets']:
            report_lines.append("Sheet Details:")
            for sheet_name, sheet_data in self.progress_data['sheets'].items():
                processed = sheet_data.get('processed_rows', 0)
                total = sheet_data.get('total_rows', 0)
                status_str = sheet_data.get('status', 'UNKNOWN')
                report_lines.append(f"  {sheet_name}: {processed}/{total} rows, {status_str}")
            report_lines.append("")
        
        # Add recent errors
        if self.progress_data['errors']:
            report_lines.append("Recent Errors:")
            for error in self.progress_data['errors'][-3:]:  # Last 3 errors
                report_lines.append(f"  - {error['error']}")
            report_lines.append("")
        
        return "\n".join(report_lines)

# Global progress monitor instance
_global_monitor = None

def get_progress_monitor(session_id: Optional[str] = None) -> ImportProgressMonitor:
    """Get global progress monitor instance."""
    global _global_monitor
    if _global_monitor is None or session_id:
        _global_monitor = ImportProgressMonitor(session_id)
    return _global_monitor

def start_import_monitoring(file_path: str, session_id: Optional[str] = None):
    """Start import monitoring."""
    monitor = get_progress_monitor(session_id)
    monitor.start_import(file_path)
    return monitor

if __name__ == "__main__":
    # Test progress monitoring
    monitor = ImportProgressMonitor("test_session")
    
    monitor.start_import("test_file.xlsx")
    monitor.start_phase("FILE_ANALYSIS")
    monitor.log_operation("Reading Excel file structure")
    monitor.end_phase("COMPLETED")
    
    monitor.start_phase("DATA_PROCESSING")
    monitor.start_sheet_processing("Sheet1", 100)
    monitor.update_sheet_progress("Sheet1", 50)
    monitor.end_sheet_processing("Sheet1", "COMPLETED", {"equipment": 25, "fluids": 75})
    monitor.end_phase("COMPLETED")
    
    monitor.end_import("COMPLETED", {"total_equipment": 25, "total_fluids": 75})
    
    print(monitor.generate_progress_report())
