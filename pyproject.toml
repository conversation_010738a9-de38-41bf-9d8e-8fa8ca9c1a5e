[build-system]
requires = ["briefcase>=0.3.16"]

[tool.briefcase]
project_name = "PROJECT-ALPHA Equipment Inventory"
bundle = "com.military.inventorytracker"
version = "1.0.0"
url = "https://github.com/ForstvalStudio/PROJECT-ALPHA"
license = {file = "LICENSE"}
author = "Military Equipment Management"
author_email = "<EMAIL>"
description = "Military Equipment Inventory Management System"

[tool.briefcase.app.inventorytracker]
formal_name = "PROJECT-ALPHA Equipment Inventory"
description = "Military Equipment Inventory Management System"
sources = ["src/inventorytracker"]

# Dependencies - Core requirements only
requires = [
    "PyQt5>=5.15.0,<5.16.0",
    "pandas>=1.3.0,<2.1.0", 
    "numpy>=1.19.0,<1.25.0",
    "openpyxl>=3.0.0,<3.2.0",
    "xlrd>=2.0.0,<2.1.0",
    "python-dateutil>=2.8.0,<2.9.0",
    "matplotlib>=3.3.0,<3.8.0",
    "reportlab>=3.6.0,<4.1.0",
    "psutil>=5.8.0,<5.10.0",
    "Pillow>=8.0.0,<10.1.0",
    "pint>=0.20,<0.24.0",
    "fuzzywuzzy>=0.18,<0.19.0",
    "python-Levenshtein>=0.12.0,<0.21.0"
]

# Include resource files
icon = "resources/app_icon"

# Console app setting
console_app = false

# Platform-specific configurations
[tool.briefcase.app.inventorytracker.macOS]
requires = []

[tool.briefcase.app.inventorytracker.linux]
requires = []

[tool.briefcase.app.inventorytracker.windows]
requires = [] 