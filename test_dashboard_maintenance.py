#!/usr/bin/env python3
"""
Test script to verify dashboard maintenance status calculation is working correctly
after the fixes to ensure consistency with maintenance widget.
"""

import sys
import os
sys.path.append('.')

import database
import utils
from datetime import date, datetime

def test_dashboard_maintenance_logic():
    """Test the fixed dashboard maintenance logic."""
    print('Testing Dashboard Maintenance Status Logic After Fixes')
    print('=' * 60)
    
    maintenance_categories = ['TM-1', 'TM-2', 'Yearly', 'Monthly']
    
    for category in maintenance_categories:
        print(f'\nTesting Category: {category}')
        
        # Get maintenance records (same query as dashboard)
        query = """
            SELECT m.*, e.make_and_type, e.ba_number
            FROM maintenance m
            JOIN equipment e ON m.equipment_id = e.equipment_id
            WHERE m.maintenance_category = %s
            AND e.is_active = 1
            AND (m.status != 'archived' OR m.status IS NULL)
            ORDER BY m.due_date
            LIMIT 5
        """
        maintenance_list = database.execute_query(query, (category,))
        
        if not maintenance_list:
            print(f'  No records found for {category}')
            continue
            
        print(f'  Found {len(maintenance_list)} records')
        
        # Test the dashboard logic
        critical_count = 0
        status_distribution = {}
        
        for maintenance in maintenance_list:
            # Apply the FIXED dashboard logic
            done_date_val = maintenance.get('done_date')
            
            if done_date_val:
                # Calculate next due date using standardized logic
                next_due_date = utils.calculate_next_due_date(done_date_val, category)
                if next_due_date:
                    # For completed maintenance, calculate status for the NEXT cycle
                    maintenance_for_status = {
                        'status': 'scheduled',  # Reset status for next cycle calculation
                        'due_date': next_due_date.isoformat()
                    }
                else:
                    # Fallback to original due_date if calculation fails
                    maintenance_for_status = {
                        'status': maintenance.get('status', ''),
                        'due_date': maintenance.get('due_date')
                    }
            else:
                # No done date, use the maintenance record as-is
                maintenance_for_status = maintenance
            
            # Use the centralized status calculation
            calculated_status = utils.calculate_maintenance_status(maintenance_for_status)
            
            # Count status distribution
            status_distribution[calculated_status] = status_distribution.get(calculated_status, 0) + 1
            
            # Count urgent maintenance (FIXED logic)
            if calculated_status in ["overdue", "critical"]:
                critical_count += 1
                
            # Show sample record
            ba_number = maintenance.get('ba_number', 'N/A')
            done_date = maintenance.get('done_date', 'N/A')
            due_date = maintenance.get('due_date', 'N/A')
            print(f'    BA: {ba_number} | Status: {calculated_status} | Done: {done_date} | Due: {due_date}')
        
        print(f'  Status Distribution: {status_distribution}')
        print(f'  Critical/Overdue Count: {critical_count}')

def test_status_consistency():
    """Test that dashboard and maintenance widget use same status calculation."""
    print('\n' + '=' * 60)
    print('Testing Status Calculation Consistency')
    print('=' * 60)
    
    # Test with sample maintenance record
    sample_maintenance = {
        'maintenance_id': 'TEST',
        'status': 'completed',
        'done_date': '2024-02-05',
        'due_date': '2025-02-05',
        'maintenance_category': 'TM-1'
    }
    
    print(f'Sample Record: {sample_maintenance}')
    
    # Calculate next due date
    next_due_date = utils.calculate_next_due_date(sample_maintenance['done_date'], 'TM-1')
    print(f'Next Due Date: {next_due_date}')
    
    # Create maintenance record for status calculation (same logic as both widgets)
    maintenance_for_status = {
        'status': 'scheduled',  # Reset for next cycle
        'due_date': next_due_date.isoformat() if next_due_date else None
    }
    
    # Calculate status using centralized function
    calculated_status = utils.calculate_maintenance_status(maintenance_for_status)
    print(f'Calculated Status: {calculated_status}')
    
    # Verify this would be counted as urgent
    is_urgent = calculated_status in ["overdue", "critical"]
    print(f'Would be counted as urgent: {is_urgent}')
    
    print('\nThis demonstrates that both dashboard and maintenance widget')
    print('now use the same logic and will show consistent results.')

if __name__ == '__main__':
    try:
        test_dashboard_maintenance_logic()
        test_status_consistency()
        print('\n' + '=' * 60)
        print('Dashboard maintenance logic testing completed successfully!')
        print('The fixes ensure consistency between dashboard and maintenance widget.')
    except Exception as e:
        print(f'Error during testing: {e}')
        import traceback
        traceback.print_exc()
