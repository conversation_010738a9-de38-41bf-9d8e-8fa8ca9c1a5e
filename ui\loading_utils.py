"""
Loading utilities and progress indicators for the inventory application.
"""

from PyQt5.QtWidgets import (QProgressDialog, QApplication, QMessageBox, 
                            QWidget, QVBoxLayout, QLabel, QProgressBar)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QMovie, QPixmap
import time
import logging

logger = logging.getLogger('loading_utils')

class LoadingIndicator(QWidget):
    """Simple loading indicator widget with spinner."""
    
    def __init__(self, message="Loading...", parent=None):
        super().__init__(parent)
        self.setWindowTitle("Loading")
        self.setWindowModality(Qt.ApplicationModal)
        self.setFixedSize(300, 100)
        
        layout = QVBoxLayout()
        
        # Message label
        self.message_label = QLabel(message)
        self.message_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.message_label)
        
        # Progress bar (indeterminate)
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        layout.addWidget(self.progress_bar)
        
        self.setLayout(layout)
        
    def update_message(self, message):
        """Update the loading message."""
        self.message_label.setText(message)
        QApplication.processEvents()

class ProgressDialog(QProgressDialog):
    """Enhanced progress dialog with better error handling."""
    
    def __init__(self, title="Processing", message="Please wait...", parent=None):
        super().__init__(message, "Cancel", 0, 100, parent)
        self.setWindowTitle(title)
        self.setWindowModality(Qt.ApplicationModal)
        self.setMinimumDuration(500)  # Show after 500ms
        self.canceled.connect(self.on_canceled)
        self._canceled = False
        
    def on_canceled(self):
        """Handle cancellation."""
        self._canceled = True
        logger.info("Operation canceled by user")
        
    def is_canceled(self):
        """Check if operation was canceled."""
        return self._canceled
        
    def update_progress(self, value, message=None):
        """Update progress and optionally change message."""
        if message:
            self.setLabelText(message)
        self.setValue(value)
        QApplication.processEvents()
        return not self._canceled

class ExcelImportProgressDialog(ProgressDialog):
    """Specialized progress dialog for Excel import operations."""
    
    def __init__(self, parent=None):
        super().__init__(
            title="Excel Import",
            message="Preparing to import Excel file...",
            parent=parent
        )
        self.setRange(0, 100)
        
    def update_import_stage(self, stage, progress=None):
        """Update progress for different import stages."""
        stages = {
            'reading': "Reading Excel file...",
            'validating': "Validating data...",
            'processing': "Processing records...",
            'saving': "Saving to database...",
            'finalizing': "Finalizing import..."
        }
        
        message = stages.get(stage, f"Processing: {stage}")
        if progress is not None:
            self.update_progress(progress, message)
        else:
            self.setLabelText(message)
            QApplication.processEvents()

class DatabaseOperationProgress(ProgressDialog):
    """Progress dialog for database operations."""
    
    def __init__(self, operation_name="Database Operation", parent=None):
        super().__init__(
            title=operation_name,
            message=f"Executing {operation_name.lower()}...",
            parent=parent
        )
        self.setRange(0, 0)  # Indeterminate by default
        
    def set_determinate(self, maximum):
        """Switch to determinate progress."""
        self.setRange(0, maximum)
        
    def set_indeterminate(self):
        """Switch to indeterminate progress."""
        self.setRange(0, 0)

def show_loading_message(parent, title="Loading", message="Please wait..."):
    """Show a simple loading message box."""
    msg_box = QMessageBox(parent)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setStandardButtons(QMessageBox.NoButton)
    msg_box.show()
    QApplication.processEvents()
    return msg_box

def hide_loading_message(msg_box):
    """Hide the loading message box."""
    if msg_box:
        msg_box.close()
        QApplication.processEvents()

def with_loading_indicator(func):
    """Decorator to show loading indicator during function execution."""
    def wrapper(*args, **kwargs):
        # Assume first argument is self (widget)
        widget = args[0] if args else None
        
        loading = LoadingIndicator("Processing...", widget)
        loading.show()
        QApplication.processEvents()
        
        try:
            result = func(*args, **kwargs)
            return result
        except Exception as e:
            logger.error(f"Error during operation: {e}")
            QMessageBox.critical(
                widget, 
                "Operation Failed", 
                f"An error occurred: {str(e)}"
            )
            raise
        finally:
            loading.close()
            QApplication.processEvents()
    
    return wrapper

class AsyncWorker(QThread):
    """Generic async worker for background operations."""
    
    progress_updated = pyqtSignal(int, str)  # progress, message
    operation_completed = pyqtSignal(object)  # result
    operation_failed = pyqtSignal(str)  # error message
    
    def __init__(self, operation_func, *args, **kwargs):
        super().__init__()
        self.operation_func = operation_func
        self.args = args
        self.kwargs = kwargs
        
    def run(self):
        """Execute the operation in background."""
        try:
            result = self.operation_func(*self.args, **self.kwargs)
            self.operation_completed.emit(result)
        except Exception as e:
            logger.error(f"Async operation failed: {e}")
            self.operation_failed.emit(str(e))

def run_with_progress(parent, operation_func, title="Processing", *args, **kwargs):
    """Run an operation with progress dialog."""
    progress = ProgressDialog(title, "Starting operation...", parent)
    progress.show()
    
    worker = AsyncWorker(operation_func, *args, **kwargs)
    
    def on_completed(result):
        progress.close()
        return result
        
    def on_failed(error):
        progress.close()
        QMessageBox.critical(parent, "Operation Failed", f"Error: {error}")
        
    worker.operation_completed.connect(on_completed)
    worker.operation_failed.connect(on_failed)
    worker.start()
    
    return worker 