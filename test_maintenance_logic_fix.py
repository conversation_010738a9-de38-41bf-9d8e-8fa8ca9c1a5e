"""
Test script to validate the corrected maintenance logic.
Run this AFTER the database migration and code updates.
"""
import sys
import os
from datetime import datetime, date, timedelta

# Add project root to Python's import path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_maintenance_completion_chain():
    """Test the corrected maintenance completion logic."""
    try:
        from models import Maintenance, Equipment
        
        print("🧪 Testing Maintenance Logic Fix...")
        print("=" * 50)
        
        # Test Case 1: Check if we can load maintenance records
        print("📋 Test 1: Loading maintenance records...")
        
        # Get all maintenance records to see current state
        all_maintenance = Maintenance.get_all()
        print(f"✅ Found {len(all_maintenance) if all_maintenance else 0} maintenance records")
        
        if all_maintenance and len(all_maintenance) > 0:
            # Show first record structure to verify field names
            first_record = all_maintenance[0]
            print(f"📊 Sample record fields: {list(first_record.keys()) if isinstance(first_record, dict) else 'Not a dict'}")
            
            # Check if next_due_date field exists
            if 'next_due_date' in first_record:
                print("✅ Migration successful - next_due_date field found")
            else:
                print("❌ Migration may not be complete - still using due_date field")
        
        # Test Case 2: Test maintenance completion logic
        print("\n📋 Test 2: Testing maintenance completion...")
        
        # Find a scheduled maintenance to test with
        scheduled_maintenance = None
        if all_maintenance:
            for record in all_maintenance:
                if record.get('status') == 'scheduled':
                    scheduled_maintenance = record
                    break
        
        if scheduled_maintenance:
            print(f"🎯 Testing with maintenance ID: {scheduled_maintenance['maintenance_id']}")
            print(f"   Equipment: {scheduled_maintenance.get('equipment_id')}")
            print(f"   Category: {scheduled_maintenance.get('maintenance_category')}")
            print(f"   Done Date: {scheduled_maintenance.get('done_date')}")
            print(f"   Next Due Date: {scheduled_maintenance.get('next_due_date') or scheduled_maintenance.get('due_date')}")
            print(f"   Status: {scheduled_maintenance.get('status')}")
            
            # Test creating maintenance object
            try:
                maintenance_obj = Maintenance(
                    maintenance_id=scheduled_maintenance['maintenance_id'],
                    equipment_id=scheduled_maintenance['equipment_id'],
                    maintenance_type=scheduled_maintenance.get('maintenance_type'),
                    done_date=scheduled_maintenance.get('done_date'),
                    next_due_date=scheduled_maintenance.get('next_due_date') or scheduled_maintenance.get('due_date'),
                    maintenance_category=scheduled_maintenance.get('maintenance_category', 'TM-1'),
                    status=scheduled_maintenance.get('status', 'scheduled')
                )
                print("✅ Maintenance object created successfully")
                
                # Test duplicate check
                existing_next = Maintenance.get_next_scheduled_maintenance(
                    scheduled_maintenance['equipment_id'],
                    scheduled_maintenance.get('maintenance_category', 'TM-1')
                )
                print(f"🔍 Duplicate check: {'Found existing' if existing_next else 'No duplicates'}")
                
            except Exception as e:
                print(f"❌ Error creating maintenance object: {e}")
        else:
            print("⚠️  No scheduled maintenance found for testing")
        
        # Test Case 3: Test due date calculation
        print("\n📋 Test 3: Testing due date calculation...")
        
        try:
            from utils import calculate_next_due_date
            
            test_done_date = "2024-01-01"
            
            # Test TM-1 (should be 6 months later)
            tm1_next = calculate_next_due_date(test_done_date, "TM-1")
            print(f"✅ TM-1: {test_done_date} → {tm1_next}")
            
            # Test TM-2 (should be 12 months later)
            tm2_next = calculate_next_due_date(test_done_date, "TM-2")
            print(f"✅ TM-2: {test_done_date} → {tm2_next}")
            
            # Verify correct intervals
            expected_tm1 = date(2024, 7, 1)  # 6 months after Jan 1
            expected_tm2 = date(2025, 1, 1)  # 12 months after Jan 1
            
            if tm1_next == expected_tm1:
                print("✅ TM-1 calculation correct")
            else:
                print(f"❌ TM-1 calculation wrong: expected {expected_tm1}, got {tm1_next}")
                
            if tm2_next == expected_tm2:
                print("✅ TM-2 calculation correct")
            else:
                print(f"❌ TM-2 calculation wrong: expected {expected_tm2}, got {tm2_next}")
                
        except Exception as e:
            print(f"❌ Error testing due date calculation: {e}")
        
        # Test Case 4: Check for duplicate scheduled maintenance
        print("\n📋 Test 4: Checking for duplicate scheduled maintenance...")
        
        try:
            import database
            
            duplicate_query = """
                SELECT equipment_id, maintenance_category, COUNT(*) as count
                FROM maintenance 
                WHERE status = 'scheduled'
                GROUP BY equipment_id, maintenance_category
                HAVING COUNT(*) > 1
            """
            
            duplicates = database.execute_query(duplicate_query)
            
            if duplicates and len(duplicates) > 0:
                print(f"⚠️  Found {len(duplicates)} equipment with duplicate scheduled maintenance:")
                for dup in duplicates[:5]:  # Show first 5
                    print(f"   Equipment {dup['equipment_id']}, Category {dup['maintenance_category']}: {dup['count']} records")
                print("   → This indicates the old logic created duplicates")
            else:
                print("✅ No duplicate scheduled maintenance found")
                
        except Exception as e:
            print(f"❌ Error checking duplicates: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 Maintenance Logic Test Completed!")
        
        return True
        
    except Exception as e:
        print(f"💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_quick_validation():
    """Quick validation that everything is working."""
    print("🚀 QUICK MAINTENANCE LOGIC VALIDATION")
    print("=" * 40)
    
    try:
        # Check database connection
        import database
        database.init_db()
        print("✅ Database connection OK")
        
        # Check models import
        from models import Maintenance
        print("✅ Models import OK")
        
        # Check utils import
        from utils import calculate_next_due_date
        print("✅ Utils import OK")
        
        # Run main test
        success = test_maintenance_completion_chain()
        
        if success:
            print("\n🎯 VALIDATION RESULT: READY TO USE!")
            print("✅ You can now use the corrected maintenance logic")
            print("✅ Duplicate prevention is active")
            print("✅ Proper maintenance chain will be maintained")
        else:
            print("\n💥 VALIDATION RESULT: ISSUES FOUND!")
            print("❌ Please check the errors above")
        
        return success
        
    except Exception as e:
        print(f"💥 Validation failed: {e}")
        return False

if __name__ == "__main__":
    run_quick_validation() 