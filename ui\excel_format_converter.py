import pandas as pd
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QLabel, QPushButton, QFileDialog, QTableWidget, QTableWidgetItem, QMessageBox)
from PyQt5.QtCore import Qt

EQUIPMENT_FIELDS = [
    'serial_number', 'make_and_type', 'ba_number', 'units_held', 'vintage_years', 'meterage_kms', 'meterage_description',
    'km_hrs_run_previous_month', 'km_hrs_run_current_month', 'hours_run_total', 'hours_run_previous_month', 'hours_run_current_month',
    'is_active', 'remarks', 'date_of_commission', 'equipment_status'
]

FLUID_FIELDS = [
    'equipment_id', 'fluid_type', 'accounting_unit', 'capacity_ltrs_kg', 'addl_10_percent_top_up', 'top_up_percent',
    'grade', 'periodicity_km', 'periodicity_hrs', 'periodicity_months', 'last_serviced_date', 'last_serviced_meterage', 'date_of_change'
]


class ExcelFormatConverterDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Convert Excel for Import")
        self.setMinimumWidth(700)
        layout = QVBoxLayout()

        layout.addWidget(QLabel("This tool generates Excel templates for import in the required format."))
        layout.addWidget(QLabel("Select the type of template to generate:"))

        from PyQt5.QtWidgets import QComboBox
        self.type_combo = QComboBox()
        self.type_combo.addItems(["Equipment", "Fluids"])
        self.type_combo.currentIndexChanged.connect(self.update_required_fields)
        layout.addWidget(self.type_combo)

        self.fields_label = QLabel()
        layout.addWidget(self.fields_label)
        self.update_required_fields()

        self.select_btn = QPushButton("Generate Blank Template")
        self.select_btn.clicked.connect(self.generate_template)
        layout.addWidget(self.select_btn)

        self.preview_label = QLabel()
        layout.addWidget(self.preview_label)
        self.table_widget = QTableWidget()
        layout.addWidget(self.table_widget)

        self.save_btn = QPushButton("Save Template File")
        self.save_btn.setEnabled(False)
        self.save_btn.clicked.connect(self.save_converted)
        layout.addWidget(self.save_btn)

        self.setLayout(layout)
        self.df_converted = None

        # Add button to load Excel with multi-row headers (single sheet)
        self.load_btn = QPushButton("Load Excel File (with multi-row headers)")
        self.load_btn.clicked.connect(self.load_excel_with_headers)
        layout.insertWidget(0, self.load_btn)

        # Add button to load and combine all sheets
        self.load_all_btn = QPushButton("Load and Combine All Sheets (multi-row headers)")
        self.load_all_btn.clicked.connect(self.load_and_combine_all_sheets)
        layout.insertWidget(1, self.load_all_btn)

    def update_required_fields(self):
        type_selected = self.type_combo.currentText()
        if type_selected == "Equipment":
            self.fields_label.setText("Required columns: " + ", ".join(EQUIPMENT_FIELDS))
        else:
            self.fields_label.setText("Required columns: " + ", ".join(FLUID_FIELDS))

    def generate_template(self):
        type_selected = self.type_combo.currentText()
        if type_selected == "Equipment":
            df = pd.DataFrame(columns=EQUIPMENT_FIELDS)
        else:
            df = pd.DataFrame(columns=FLUID_FIELDS)
        self.df_converted = df
        self.preview_label.setText("Preview of blank template:")
        self.show_preview(df)
        self.save_btn.setEnabled(True)

    def flatten_multiindex_columns(self, columns):
        # columns: pd.MultiIndex or list of tuples
        col_counts = {}
        flat_cols = []
        for col in columns:
            names = [str(x).strip() for x in col if str(x).strip().lower() != 'nan']
            base = names[0] if names else ''
            # Count occurrences
            if base not in col_counts:
                col_counts[base] = 0
            col_counts[base] += 1
            if col_counts[base] > 1:
                # If repeated, use all subheaders
                flat_col = " | ".join(names)
            else:
                # If unique, just use header
                flat_col = base
            flat_cols.append(flat_col)
        return flat_cols

    def load_excel_with_headers(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Select Excel File", "", "Excel Files (*.xlsx *.xls)")
        if not file_path:
            return
        try:
            df = pd.read_excel(file_path, header=[0,1,2])
            df.columns = self.flatten_multiindex_columns(df.columns)
            self.df_converted = df
            self.preview_label.setText(f"Preview of loaded file (first 10 rows): {file_path}")
            self.show_preview(df)
            self.save_btn.setEnabled(True)
        except Exception as e:
            QMessageBox.critical(self, "Load Error", f"Failed to load file: {e}")
            self.save_btn.setEnabled(False)

    def show_preview(self, df):
        self.table_widget.clear()
        nrows = min(10, len(df))
        ncols = len(df.columns)
        self.table_widget.setRowCount(nrows)
        self.table_widget.setColumnCount(ncols)
        self.table_widget.setHorizontalHeaderLabels([str(col) for col in df.columns])
        for i in range(nrows):
            for j in range(ncols):
                item = QTableWidgetItem(str(df.iloc[i, j]))
                self.table_widget.setItem(i, j, item)

    def load_and_combine_all_sheets(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Select Excel Workbook", "", "Excel Files (*.xlsx *.xls)")
        if not file_path:
            return
        try:
            xls = pd.ExcelFile(file_path)
            all_dfs = []
            for sheet in xls.sheet_names:
                df = pd.read_excel(xls, sheet_name=sheet, header=[0,1,2])
                df['__sheet_name__'] = sheet
                all_dfs.append(df)
            if not all_dfs:
                QMessageBox.warning(self, "No Data", "No sheets found in the workbook.")
                return
            full_df = pd.concat(all_dfs, ignore_index=True)
            # Flatten columns: repeated headers get all subheaders
            def flatten_col(col):
                return ' | '.join([str(x).strip() for x in col if str(x).strip().lower() != 'nan'])
            full_df.columns = [flatten_col(col) if isinstance(col, tuple) else str(col) for col in full_df.columns]
            self.df_converted = full_df
            self.preview_label.setText(f"Preview of combined sheets (first 10 rows): {file_path}")
            self.show_preview(full_df)
            self.save_btn.setEnabled(True)
        except Exception as e:
            QMessageBox.critical(self, "Load Error", f"Failed to load and combine sheets: {e}")
            self.save_btn.setEnabled(False)

    def save_converted(self):
        if self.df_converted is None:
            return
        file_path, _ = QFileDialog.getSaveFileName(self, "Save Converted Excel File", "converted_import.xlsx", "Excel Files (*.xlsx)")
        if not file_path:
            return
        try:
            self.df_converted.to_excel(file_path, index=False)
            QMessageBox.information(self, "Success", f"Converted file saved to: {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Save Error", f"Failed to save file: {e}")
