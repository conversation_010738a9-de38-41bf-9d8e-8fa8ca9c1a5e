@echo off
setlocal enabledelayedexpansion

echo ========================================================================
echo  InventoryTracker - Debug Build Script for Database Migration Issues
echo ========================================================================
echo.

echo [1/6] Creating debug executable with console output...

REM Build debug version with console enabled
pyinstaller debug_executable.py ^
    --name="InventoryTracker_Debug" ^
    --onefile ^
    --console ^
    --clean ^
    --noconfirm ^
    --add-data "config.py;." ^
    --add-data "database.py;." ^
    --add-data "database_ready.py;." ^
    --add-data "policy_service.py;." ^
    --add-data "policy_models.py;." ^
    --add-data "models.py;." ^
    --add-data "utils.py;." ^
    --hidden-import="policy_service" ^
    --hidden-import="policy_models" ^
    --hidden-import="database_ready" ^
    --hidden-import="models" ^
    --hidden-import="sqlite3"

if errorlevel 1 (
    echo ERROR: Debug build failed
    pause
    exit /b 1
)

echo [2/6] Building main executable with debug mode...

REM Temporarily enable console mode in main spec
powershell -Command "(gc InventoryTracker.spec) -replace 'console=False', 'console=True' | sc InventoryTracker.spec"

pyinstaller InventoryTracker.spec --clean --noconfirm

if errorlevel 1 (
    echo ERROR: Main executable build failed
    REM Restore original spec
    powershell -Command "(gc InventoryTracker.spec) -replace 'console=True', 'console=False' | sc InventoryTracker.spec"
    pause
    exit /b 1
)

echo [3/6] Restoring original spec file...
REM Restore original spec
powershell -Command "(gc InventoryTracker.spec) -replace 'console=True', 'console=False' | sc InventoryTracker.spec"

echo [4/6] Copying debug files to distribution...
copy dist\InventoryTracker_Debug.exe InventoryTracker_Distribution\
copy dist\InventoryTracker.exe InventoryTracker_Distribution\

echo [5/6] Creating debug batch file...
echo @echo off > InventoryTracker_Distribution\Debug_Database.bat
echo echo Starting Database Debug Test... >> InventoryTracker_Distribution\Debug_Database.bat
echo InventoryTracker_Debug.exe >> InventoryTracker_Distribution\Debug_Database.bat
echo pause >> InventoryTracker_Distribution\Debug_Database.bat

echo [6/6] Creating test instructions...
echo Database Migration Debug Instructions > InventoryTracker_Distribution\DEBUG_INSTRUCTIONS.txt
echo ======================================= >> InventoryTracker_Distribution\DEBUG_INSTRUCTIONS.txt
echo. >> InventoryTracker_Distribution\DEBUG_INSTRUCTIONS.txt
echo 1. Run Debug_Database.bat to test database initialization >> InventoryTracker_Distribution\DEBUG_INSTRUCTIONS.txt
echo 2. Check db_debug_executable.log for detailed error messages >> InventoryTracker_Distribution\DEBUG_INSTRUCTIONS.txt
echo 3. Run InventoryTracker.exe to test the main application >> InventoryTracker_Distribution\DEBUG_INSTRUCTIONS.txt
echo 4. If the app starts but has issues, check the console output >> InventoryTracker_Distribution\DEBUG_INSTRUCTIONS.txt
echo. >> InventoryTracker_Distribution\DEBUG_INSTRUCTIONS.txt
echo Common Issues and Solutions: >> InventoryTracker_Distribution\DEBUG_INSTRUCTIONS.txt
echo - Module not found: Missing hidden imports in spec file >> InventoryTracker_Distribution\DEBUG_INSTRUCTIONS.txt
echo - Path errors: Database path resolution issues >> InventoryTracker_Distribution\DEBUG_INSTRUCTIONS.txt
echo - Permission errors: Try running as administrator >> InventoryTracker_Distribution\DEBUG_INSTRUCTIONS.txt
echo - Import errors: Missing dependencies in executable >> InventoryTracker_Distribution\DEBUG_INSTRUCTIONS.txt

echo.
echo ========================================================================
echo  Debug Build Complete!
echo ========================================================================
echo.
echo Files created:
echo   InventoryTracker_Distribution\InventoryTracker_Debug.exe  (Debug tool)
echo   InventoryTracker_Distribution\InventoryTracker.exe        (Main app with console)
echo   InventoryTracker_Distribution\Debug_Database.bat          (Debug runner)
echo   InventoryTracker_Distribution\DEBUG_INSTRUCTIONS.txt      (Instructions)
echo.
echo Next steps:
echo 1. Run Debug_Database.bat to identify database issues
echo 2. Check the generated log files for specific errors
echo 3. Report back with any error messages found
echo.
pause 