"""Demand forecast management widget for the equipment inventory application."""
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                           QPushButton, QTableWidget, QTableWidgetItem,
                           QHeaderView, QAbstractItemView, QMessageBox,
                           QComboBox, QLineEdit, QFormLayout, QGroupBox,
                           QSplitter, QFrame, QDateEdit, QTabWidget, QDialog,
                           QSizePolicy)
from PyQt5.QtCore import Qt, QSize, pyqtSignal
from PyQt5.QtGui import QIcon, QColor

import models
from ui.custom_widgets import ReadOnlyTableWidget, BarChartWidget, PieChartWidget
from ui.dialogs import DemandForecastDialog, TyreForecastDialog, BatteryForecastDialog, EquipmentForecastDialog
import utils
import database
import datetime
import logging
from utils import EventBus

logger = logging.getLogger(__name__)

try:
    import sip
except ImportError:
    sip = None

class ForecastWidgetBase(QWidget):
    """Base widget for forecasts providing common filtering and data loading functionality."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._forecast_records = []
        self._filters_initialized = False

    def setup_common_ui(self, layout):
        """Set up common UI elements that all forecast widgets should have."""
        splitter = QSplitter(Qt.Horizontal)

        # Left section: filters, table, and buttons
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)

        # Filter controls
        filter_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search forecasts...")
        self.search_edit.textChanged.connect(self.apply_common_filter)
        filter_layout.addWidget(self.search_edit)

        self.fiscal_year_filter = QComboBox()
        self.fiscal_year_filter.addItem("All Fiscal Years", None)
        self.fiscal_year_filter.currentIndexChanged.connect(self.apply_common_filter)
        filter_layout.addWidget(self.fiscal_year_filter)

        self.type_filter = QComboBox()
        self.type_filter.addItem("All Types", None)
        self.type_filter.currentIndexChanged.connect(self.apply_common_filter)
        filter_layout.addWidget(self.type_filter)

        self.equipment_filter = QComboBox()
        self.equipment_filter.addItem("All Equipment", None)
        self.equipment_filter.currentIndexChanged.connect(self.apply_common_filter)
        filter_layout.addWidget(self.equipment_filter)

        left_layout.addLayout(filter_layout)

        # Forecasts table
        self.forecasts_table = ReadOnlyTableWidget()
        self.forecasts_table.setColumnCount(5)  # Default to 5, will be overridden by subclasses if needed
        self.forecasts_table.setHorizontalHeaderLabels(["Fiscal Year", "Equipment", "Type", "Requirement", "Remarks"])
        self.forecasts_table.verticalHeader().setVisible(False)
        self.forecasts_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.forecasts_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.forecasts_table.setSortingEnabled(True)
        self.forecasts_table.itemSelectionChanged.connect(self.forecast_selected)
        left_layout.addWidget(self.forecasts_table)

        # Buttons
        button_layout = QHBoxLayout()
        self.add_button = QPushButton("Add Forecast")
        self.add_button.clicked.connect(self.add_forecast)
        button_layout.addWidget(self.add_button)

        self.edit_button = QPushButton("Edit Forecast")
        self.edit_button.clicked.connect(self.edit_forecast)
        self.edit_button.setEnabled(False)
        button_layout.addWidget(self.edit_button)

        self.delete_button = QPushButton("Delete Forecast")
        self.delete_button.setObjectName("deleteButton")
        self.delete_button.clicked.connect(self.delete_forecast)
        self.delete_button.setEnabled(False)
        button_layout.addWidget(self.delete_button)

        left_layout.addLayout(button_layout)
        splitter.addWidget(left_widget)

        # Right section: forecast details and charts
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)

        # Details panel
        details_group = QGroupBox("Forecast Details")
        details_layout = QFormLayout(details_group)
        self.detail_fiscal_year = QLabel("--")
        details_layout.addRow("Fiscal Year:", self.detail_fiscal_year)
        self.detail_equipment = QLabel("--")
        details_layout.addRow("Equipment:", self.detail_equipment)
        self.detail_type = QLabel("--")
        details_layout.addRow("Type:", self.detail_type)
        self.detail_requirement = QLabel("--")
        self.detail_remarks = QLabel("--")
        self.detail_remarks.setWordWrap(True)
        details_layout.addRow("Remarks:", self.detail_remarks)
        right_layout.addWidget(details_group)

        # Charts panel
        charts_group = QGroupBox("Demand Analysis")
        charts_layout = QVBoxLayout(charts_group)
        
        # Chart controls
        chart_control_layout = QHBoxLayout()
        chart_control_layout.addWidget(QLabel("Select Fiscal Year:"))
        self.chart_fiscal_year = QComboBox()
        chart_control_layout.addWidget(self.chart_fiscal_year)
        self.calculate_button = QPushButton("Calculate Forecasts")
        self.calculate_button.clicked.connect(self.calculate_all_forecasts)
        chart_control_layout.addWidget(self.calculate_button)
        chart_control_layout.addStretch()
        charts_layout.addLayout(chart_control_layout)

        # Charts container - SINGLE CHART LAYOUT
        # Remove the splitter and type_chart, keep only volume_chart with full space
        self.volume_chart = BarChartWidget()
        self.volume_chart.setMinimumHeight(300)  # Ensure minimum height for visibility
        
        # Make chart expandable and responsive
        self.volume_chart.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        charts_layout.addWidget(self.volume_chart)
        
        # Remove the type_chart completely - it's not functional
        # self.type_chart = PieChartWidget()  # REMOVED
        # charts_container.addWidget(self.type_chart)  # REMOVED

        right_layout.addWidget(charts_group)

        splitter.addWidget(right_widget)
        layout.addWidget(splitter)
        
        self._filters_initialized = True

    def apply_common_filter(self):
        """Apply filters to the forecast table."""
        if not self._filters_initialized:
            return
            
        search_text = self.search_edit.text().lower()
        fiscal_year = self.fiscal_year_filter.currentData()
        type_filter = self.type_filter.currentData()
        equipment_id = self.equipment_filter.currentData()

        for row in range(self.forecasts_table.rowCount()):
            show_row = True
            
            # Text search
            if search_text:
                row_text = " ".join([
                    self.forecasts_table.item(row, col).text().lower()
                    for col in range(self.forecasts_table.columnCount())
                    if self.forecasts_table.item(row, col) is not None
                ])
                if search_text not in row_text:
                    show_row = False
            
            # Fiscal year filter
            if fiscal_year is not None:
                item = self.forecasts_table.item(row, 0)
                if item:
                    item_fiscal_year = item.data(Qt.UserRole + 1)
                    if item_fiscal_year != fiscal_year:
                        show_row = False
            
            # Type filter
            if type_filter is not None:
                item = self.forecasts_table.item(row, 2)
                if item:
                    item_type = item.data(Qt.UserRole)
                    if item_type != type_filter:
                        show_row = False
            
            # Equipment filter
            if equipment_id is not None:
                item = self.forecasts_table.item(row, 1)
                if item:
                    item_equipment_id = item.data(Qt.UserRole)
                    if item_equipment_id != equipment_id:
                        show_row = False
            
            self.forecasts_table.setRowHidden(row, not show_row)

    def load_filters(self, forecasts_list, type_field_name="type", type_label="Types"):
        """Load filter options from forecast data."""
        if not self._filters_initialized:
            return
            
        # Fiscal year filter
        self.fiscal_year_filter.blockSignals(True)
        self.fiscal_year_filter.clear()
        self.fiscal_year_filter.addItem("All Fiscal Years", None)
        fiscal_years = {forecast['fiscal_year'] for forecast in forecasts_list}
        for year in sorted(fiscal_years):
            self.fiscal_year_filter.addItem(str(year), year)
        self.fiscal_year_filter.blockSignals(False)

        # Type filter
        self.type_filter.blockSignals(True)
        self.type_filter.clear()
        self.type_filter.addItem(f"All {type_label}", None)
        types = {forecast.get(type_field_name, 'Unknown') for forecast in forecasts_list if forecast.get(type_field_name)}
        for type_val in sorted(types):
            self.type_filter.addItem(type_val, type_val)
        self.type_filter.blockSignals(False)

        # Equipment filter
        self.equipment_filter.blockSignals(True)
        self.equipment_filter.clear()
        self.equipment_filter.addItem("All Equipment", None)
        equipments = {}
        for forecast in forecasts_list:
            equip_id = forecast.get('equipment_id')
            if equip_id and equip_id not in equipments:
                equipments[equip_id] = utils.format_equipment_display(forecast)
        for equip_id, text in sorted(equipments.items(), key=lambda item: item[1]):
            self.equipment_filter.addItem(text, equip_id)
        self.equipment_filter.blockSignals(False)

    def populate_chart_fiscal_years(self, forecasts_list):
        """Populate the fiscal year combo box for charting."""
        self.chart_fiscal_year.blockSignals(True)
        self.chart_fiscal_year.clear()
        fiscal_years = sorted({forecast['fiscal_year'] for forecast in forecasts_list})
        for fy in fiscal_years:
            self.chart_fiscal_year.addItem(str(fy), fy)
        self.chart_fiscal_year.blockSignals(False)
        # Set to latest fiscal year if available
        if self.chart_fiscal_year.count() > 0:
            self.chart_fiscal_year.setCurrentIndex(self.chart_fiscal_year.count() - 1)

    def forecast_selected(self):
        """Handle forecast selection - to be overridden by subclasses."""
        selected_row = self.forecasts_table.currentRow()
        has_selection = selected_row >= 0 and hasattr(self, '_forecast_records') and len(self._forecast_records) > selected_row

        # Enable/disable Edit and Delete buttons based on selection
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)

        if not has_selection:
            self.clear_details()
        else:
            self.update_details_panel(self._forecast_records[selected_row])

    def clear_details(self):
        """Clear the details panel."""
        self.detail_fiscal_year.setText("--")
        self.detail_equipment.setText("--")
        self.detail_type.setText("--")
        self.detail_requirement.setText("--")
        self.detail_remarks.setText("--")

    def update_details_panel(self, forecast):
        """Update details panel with forecast data - to be overridden by subclasses."""
        pass

    def show_status(self, message, timeout=3000):
        """Show status message in main window status bar."""
        mw = self.window()
        if hasattr(mw, 'statusBar'):
            mw.statusBar().showMessage(message, timeout)

    def safe_chart_update(self, chart, title, labels, values, x_label="", y_label=""):
        """Safely update chart with error handling."""
        if chart is None or getattr(chart, '_is_closed', False):
            return
        try:
            if sip and sip.isdeleted(chart):
                logger.warning(f"Chart is deleted, skipping update: {title}")
                return
            chart.update_chart(title, labels, values, x_label, y_label)
        except Exception as e:
            logger.warning(f"Exception in chart update '{title}': {e}")

    # Abstract methods to be implemented by subclasses
    def load_data(self):
        """Load forecast data - must be implemented by subclasses."""
        raise NotImplementedError

    def add_forecast(self):
        """Add new forecast - must be implemented by subclasses."""
        raise NotImplementedError

    def edit_forecast(self):
        """Edit selected forecast - must be implemented by subclasses."""
        raise NotImplementedError

    def delete_forecast(self):
        """Delete selected forecast - must be implemented by subclasses."""
        raise NotImplementedError

    def calculate_all_forecasts(self):
        """Calculate and display forecast charts - must be implemented by subclasses."""
        raise NotImplementedError

    def closeEvent(self, event):
        """Clean up resources on close."""
        try:
            if hasattr(self, 'chart_fiscal_year'):
                self.chart_fiscal_year.currentIndexChanged.disconnect()
        except Exception:
            pass
        if hasattr(self, 'volume_chart'):
            self.volume_chart = None
        # Remove type_chart reference since it's no longer used
        # if hasattr(self, 'type_chart'):
        #     self.type_chart = None
        super().closeEvent(event)


class DemandForecastWidget(QWidget):
    """Widget for managing demand forecasts."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the demand forecast widget UI with new tab structure."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # Create QTabWidget container
        self.tab_widget = QTabWidget(self)

        # Instantiate sub-widgets
        fluids_widget = FluidsForecastWidget()
        tyre_widget = TyreForecastWidget()
        battery_widget = BatteryForecastWidget()
        # equipment_widget = EquipmentForecastWidget()  # Hidden as requested

        # Add tabs in new order (Equipment tab hidden as requested)
        self.tab_widget.addTab(fluids_widget, "Fluids")
        self.tab_widget.addTab(tyre_widget, "Tyre")
        self.tab_widget.addTab(battery_widget, "Battery")
        # self.tab_widget.addTab(equipment_widget, "Equipment")  # Hidden as requested

        main_layout.addWidget(self.tab_widget)

    def load_data(self):
        """Load data for all sub-widgets."""
        for i in range(self.tab_widget.count()):
            widget = self.tab_widget.widget(i)
            if hasattr(widget, 'load_data'):
                widget.load_data()


class FluidsForecastWidget(ForecastWidgetBase):
    """Widget for managing fluid demand forecasts."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()
        EventBus.subscribe('forecast_data_changed', self.on_forecast_data_changed)

    def setup_ui(self):
        """Set up the fluids forecast widget UI."""
        layout = QVBoxLayout(self)
        self.setup_common_ui(layout)
        
        # Customize table headers for fluids - Add Grade column and rename Fluid Type to Assembly
        self.forecasts_table.setColumnCount(6)  # Increased from 5 to 6 columns
        self.forecasts_table.setHorizontalHeaderLabels([
            "Fiscal Year", "Equipment", "Assembly", "Grade", "Requirement", "Remarks"
        ])

    def load_data(self):
        """Load fluid forecast data."""
        try:
            query = '''
                SELECT df.*, f.fluid_type, f.sub_type, f.accounting_unit, f.grade,
                       e.equipment_id, e.make_and_type, e.serial_number, e.ba_number, df.fiscal_year, df.demand_id
                FROM demand_forecast df
                JOIN fluids f ON df.fluid_id = f.fluid_id
                JOIN equipment e ON f.equipment_id = e.equipment_id
                ORDER BY e.make_and_type, f.fluid_type
            '''
            with database.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query)
                forecasts_list = cursor.fetchall()

            self._forecast_records = forecasts_list

            # Load filters
            self.load_filters(forecasts_list, 'fluid_type', 'Fluid Types')
            self.populate_chart_fiscal_years(forecasts_list)

            # Populate table
            self.forecasts_table.setRowCount(0)
            for row, forecast in enumerate(forecasts_list):
                self.forecasts_table.insertRow(row)
                
                # Fiscal Year
                item = QTableWidgetItem(str(forecast['fiscal_year']))
                item.setData(Qt.UserRole, forecast['demand_id'])
                item.setData(Qt.UserRole + 1, forecast['fiscal_year'])
                self.forecasts_table.setItem(row, 0, item)
                
                # Equipment - Add to table, not just details panel
                equipment_display = utils.format_equipment_display(forecast)
                equipment_item = QTableWidgetItem(equipment_display)
                self.forecasts_table.setItem(row, 1, equipment_item)
                
                # Assembly (previously labeled as "Fluid Type")
                assembly = forecast['fluid_type']  # This is actually the assembly name
                if forecast.get('sub_type'):
                    assembly += f" ({forecast['sub_type']})"
                assembly_item = QTableWidgetItem(assembly)
                assembly_item.setData(Qt.UserRole, forecast['fluid_type'])
                self.forecasts_table.setItem(row, 2, assembly_item)
                
                # Grade - New column showing actual fluid grade
                grade = forecast.get('grade', '') or ''  # Handle None values
                grade_item = QTableWidgetItem(grade)
                grade_item.setData(Qt.UserRole, grade)
                self.forecasts_table.setItem(row, 3, grade_item)
                
                # Requirement
                requirement = f"{utils.format_decimal(forecast['total_requirement'], 3)} {forecast['accounting_unit']}"
                self.forecasts_table.setItem(row, 4, QTableWidgetItem(requirement))
                
                # Remarks
                remarks = forecast['remarks'] or ""
                if len(remarks) > 50:
                    remarks = remarks[:47] + "..."
                self.forecasts_table.setItem(row, 5, QTableWidgetItem(remarks))

            self.forecasts_table.resizeColumnsToContents()

        except Exception as e:
            logger.error(f"Error loading fluid forecast data: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load forecast data: {str(e)}")

    def update_details_panel(self, forecast):
        """Update details panel with fluid forecast data."""
        self.detail_fiscal_year.setText(str(forecast.get('fiscal_year', '--')))
        self.detail_equipment.setText(utils.format_equipment_display(forecast))
        
        # Show Assembly (what was previously called fluid_type)
        assembly = forecast.get('fluid_type', '--')
        if forecast.get('sub_type'):
            assembly += f" ({forecast['sub_type']})"
        self.detail_type.setText(assembly)
        
        # If there's a grade field in the details panel, we should add it
        # For now, we can append grade info to the assembly display if needed
        grade = forecast.get('grade', '')
        if grade:
            assembly_with_grade = f"{assembly} | Grade: {grade}"
            self.detail_type.setText(assembly_with_grade)
        
        requirement = f"{utils.format_decimal(forecast.get('total_requirement', 0), 3)} {forecast.get('accounting_unit', '--')}"
        self.detail_requirement.setText(requirement)
        self.detail_remarks.setText(forecast.get('remarks', '--'))

    def on_forecast_data_changed(self, *args, **kwargs):
        """Handle forecast data change event."""
        self.load_data()
        self.calculate_all_forecasts()

    def add_forecast(self):
        """Add a new fluid forecast."""
        try:
            fluid_list = models.Fluid.get_all() or []
            dialog = DemandForecastDialog(fluid_list=fluid_list, parent=self)
            if dialog.exec_():
                data = dialog.get_demand_data()
                forecast = models.DemandForecast(
                    fluid_id=data['fluid_id'],
                    fiscal_year=data['fiscal_year'],
                    total_requirement=data['total_requirement'],
                    remarks=data['remarks']
                )
                forecast.save()
                EventBus.emit('forecast_data_changed')
                self.show_status("Demand forecast added successfully.")
        except Exception as e:
            logger.error(f"Error adding forecast: {e}")
            self.show_status(f"Failed to add demand forecast: {str(e)}", 5000)
            QMessageBox.critical(self, "Error", f"Failed to add demand forecast: {str(e)}")

    def edit_forecast(self):
        """Edit the selected fluid forecast."""
        try:
            selected_row = self.forecasts_table.currentRow()
            if selected_row < 0:
                return
                
            demand_id = self.forecasts_table.item(selected_row, 0).data(Qt.UserRole)
            if not demand_id:
                return
                
            # Get the full record
            record = models.DemandForecast.get_by_id(demand_id)
            if not record:
                return
                
            fluid_list = models.Fluid.get_all() or []
            dialog = DemandForecastDialog(demand=record, fluid_list=fluid_list, parent=self)
            if dialog.exec_():
                data = dialog.get_demand_data()
                forecast = models.DemandForecast(
                    demand_id=record['demand_id'],
                    fluid_id=data['fluid_id'],
                    fiscal_year=data['fiscal_year'],
                    total_requirement=data['total_requirement'],
                    remarks=data['remarks']
                )
                forecast.save()
                EventBus.emit('forecast_data_changed')
                self.show_status("Demand forecast updated successfully.")
        except Exception as e:
            logger.error(f"Error editing forecast: {e}")
            self.show_status(f"Failed to update demand forecast: {str(e)}", 5000)
            QMessageBox.critical(self, "Error", f"Failed to update demand forecast: {str(e)}")

    def delete_forecast(self):
        """Delete the selected fluid forecast."""
        try:
            selected_row = self.forecasts_table.currentRow()
            if selected_row < 0:
                return
                
            demand_id = self.forecasts_table.item(selected_row, 0).data(Qt.UserRole)
            if not demand_id:
                return
                
            confirm = QMessageBox.question(
                self, "Delete Forecast", 
                "Are you sure you want to delete this forecast?", 
                QMessageBox.Yes | QMessageBox.No
            )
            if confirm == QMessageBox.Yes:
                models.DemandForecast.delete(demand_id)
                EventBus.emit('forecast_data_changed')
                self.show_status("Demand forecast deleted.")
        except Exception as e:
            logger.error(f"Error deleting forecast: {e}")
            self.show_status(f"Failed to delete demand forecast: {str(e)}", 5000)
            QMessageBox.critical(self, "Error", f"Failed to delete demand forecast: {str(e)}")

    def calculate_all_forecasts(self):
        """Calculate and display fluid forecast charts."""
        fiscal_year = self.chart_fiscal_year.currentData() if hasattr(self, 'chart_fiscal_year') else None
        
        if not fiscal_year:
            self.safe_chart_update(self.volume_chart, "Total Fluid Requirement", [], [], "Fluid Type", "Requirement")
            return

        try:
            summary = models.DemandForecast.get_summary_by_fiscal_year(fiscal_year)
            if not summary:
                self.safe_chart_update(self.volume_chart, "Total Fluid Requirement", [], [], "Fluid Type", "Requirement")
                return

            labels = [f"{row['fluid_grade']} ({row['accounting_unit']})" for row in summary]
            values = [row['total'] for row in summary]
            
            self.safe_chart_update(
                self.volume_chart, 
                f"Total Fluid Requirement for {fiscal_year}", 
                labels, values, "Fluid Grade", "Requirement"
            )

        except Exception as e:
            logger.error(f"Error calculating fluid forecasts: {e}")
            self.safe_chart_update(self.volume_chart, "Total Fluid Requirement", [], [], "Fluid Type", "Requirement")


class TyreForecastWidget(ForecastWidgetBase):
    """Widget for managing tyre demand forecasts."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Set up the tyre forecast widget UI."""
        layout = QVBoxLayout(self)
        self.setup_common_ui(layout)
        
        # Customize table headers for tyres
        self.forecasts_table.setHorizontalHeaderLabels(["Fiscal Year", "Equipment", "Tyre Type", "Requirement", "Remarks"])

    def load_data(self):
        """Load tyre forecast data."""
        try:
            forecasts_list = models.TyreForecast.get_all() or []
            self._forecast_records = forecasts_list

            # Load filters
            self.load_filters(forecasts_list, 'tyre_type', 'Tyre Types')
            self.populate_chart_fiscal_years(forecasts_list)

            # Populate table
            self.forecasts_table.setRowCount(0)
            for row, forecast in enumerate(forecasts_list):
                self.forecasts_table.insertRow(row)
                
                # Fiscal Year
                item = QTableWidgetItem(str(forecast['fiscal_year']))
                item.setData(Qt.UserRole, forecast.get('forecast_id', forecast.get('id')))
                item.setData(Qt.UserRole + 1, forecast['fiscal_year'])
                self.forecasts_table.setItem(row, 0, item)
                
                # Equipment
                equipment_display = utils.format_equipment_display(forecast)
                equipment_item = QTableWidgetItem(equipment_display)
                self.forecasts_table.setItem(row, 1, equipment_item)
                
                # Tyre Type
                tyre_item = QTableWidgetItem(forecast['tyre_type'])
                tyre_item.setData(Qt.UserRole, forecast['tyre_type'])
                self.forecasts_table.setItem(row, 2, tyre_item)
                
                # Requirement
                self.forecasts_table.setItem(row, 3, QTableWidgetItem(str(forecast['total_requirement'])))
                
                # Remarks
                remarks = forecast['remarks'] or ""
                if len(remarks) > 50:
                    remarks = remarks[:47] + "..."
                self.forecasts_table.setItem(row, 4, QTableWidgetItem(remarks))

            self.forecasts_table.resizeColumnsToContents()

        except Exception as e:
            logger.error(f"Error loading tyre forecast data: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load tyre forecast data: {str(e)}")

    def update_details_panel(self, forecast):
        """Update details panel with tyre forecast data."""
        self.detail_fiscal_year.setText(str(forecast.get('fiscal_year', '--')))
        self.detail_equipment.setText(utils.format_equipment_display(forecast))
        self.detail_type.setText(forecast.get('tyre_type', '--'))
        self.detail_requirement.setText(str(forecast.get('total_requirement', '--')))
        self.detail_remarks.setText(forecast.get('remarks', '--'))

    def add_forecast(self):
        """Add a new tyre forecast."""
        try:
            dialog = TyreForecastDialog(parent=self)
            if dialog.exec_() == QDialog.Accepted:
                forecast_data = dialog.get_forecast_data()
                if forecast_data:
                    forecast = models.TyreForecast(
                        equipment_id=forecast_data['equipment_id'],
                        fiscal_year=forecast_data['fiscal_year'],
                        tyre_type=forecast_data['tyre_type'],
                        total_requirement=forecast_data['total_requirement'],
                        remarks=forecast_data['remarks']
                    )
                    forecast.save()
                    self.load_data()
                    self.show_status("Tyre forecast added successfully.")
        except Exception as e:
            logger.error(f"Error adding tyre forecast: {e}")
            QMessageBox.critical(self, "Error", f"Failed to add forecast: {str(e)}")

    def edit_forecast(self):
        """Edit the selected tyre forecast."""
        try:
            selected_row = self.forecasts_table.currentRow()
            if selected_row < 0 or selected_row >= len(self._forecast_records):
                return
                
            forecast = self._forecast_records[selected_row]
            dialog = TyreForecastDialog(forecast, parent=self)
            if dialog.exec_() == QDialog.Accepted:
                forecast_data = dialog.get_forecast_data()
                if forecast_data:
                    forecast_data['forecast_id'] = forecast.get('forecast_id') or forecast.get('id')
                    # Update the existing forecast
                    updated_forecast = models.TyreForecast(
                        forecast_id=forecast_data['forecast_id'],
                        equipment_id=forecast_data['equipment_id'],
                        fiscal_year=forecast_data['fiscal_year'],
                        tyre_type=forecast_data['tyre_type'],
                        total_requirement=forecast_data['total_requirement'],
                        remarks=forecast_data['remarks']
                    )
                    updated_forecast.save()
                    self.load_data()
                    self.show_status("Tyre forecast updated successfully.")
        except Exception as e:
            logger.error(f"Error editing tyre forecast: {e}")
            QMessageBox.critical(self, "Error", f"Failed to update forecast: {str(e)}")

    def delete_forecast(self):
        """Delete the selected tyre forecast."""
        try:
            selected_row = self.forecasts_table.currentRow()
            if selected_row < 0 or selected_row >= len(self._forecast_records):
                return
                
            forecast = self._forecast_records[selected_row]
            reply = QMessageBox.question(
                self, "Confirm Delete",
                "Are you sure you want to delete this forecast?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                models.TyreForecast.delete(forecast.get('forecast_id') or forecast.get('id'))
                self.load_data()
                self.show_status("Tyre forecast deleted successfully.")
        except Exception as e:
            logger.error(f"Error deleting tyre forecast: {e}")
            QMessageBox.critical(self, "Error", f"Failed to delete forecast: {str(e)}")

    def calculate_all_forecasts(self):
        """Calculate and display tyre forecast charts."""
        fiscal_year = self.chart_fiscal_year.currentData() if hasattr(self, 'chart_fiscal_year') else None
        
        if not fiscal_year:
            self.safe_chart_update(self.volume_chart, "Total Tyre Requirement", [], [], "Tyre Type", "Requirement")
            return

        try:
            forecasts = [f for f in getattr(self, '_forecast_records', []) if f['fiscal_year'] == fiscal_year]
            if not forecasts:
                self.safe_chart_update(self.volume_chart, "Total Tyre Requirement", [], [], "Tyre Type", "Requirement")
                return

            # Aggregate by tyre type
            type_totals = {}
            for f in forecasts:
                t = f['tyre_type']
                type_totals[t] = type_totals.get(t, 0) + f['total_requirement']
                
            labels = list(type_totals.keys())
            values = list(type_totals.values())
            
            self.safe_chart_update(
                self.volume_chart,
                f"Total Tyre Requirement for {fiscal_year}", 
                labels, values, "Tyre Type", "Requirement"
            )

        except Exception as e:
            logger.error(f"Error calculating tyre forecasts: {e}")
            self.safe_chart_update(self.volume_chart, "Total Tyre Requirement", [], [], "Tyre Type", "Requirement")


class BatteryForecastWidget(ForecastWidgetBase):
    """Widget for managing battery demand forecasts."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Set up the battery forecast widget UI."""
        layout = QVBoxLayout(self)
        self.setup_common_ui(layout)
        
        # Customize table headers for batteries
        self.forecasts_table.setHorizontalHeaderLabels(["Fiscal Year", "Equipment", "Battery Type", "Voltage", "AH", "Remarks"])

    def load_data(self):
        """Load battery forecast data."""
        try:
            forecasts_list = models.BatteryForecast.get_all() or []
            self._forecast_records = forecasts_list

            # Calculate battery types for filtering
            for forecast in forecasts_list:
                voltage = forecast.get('voltage', 12.0)
                ampere_hours = forecast.get('ampere_hours', 125)
                forecast['calculated_battery_type'] = utils.calculate_battery_type(voltage, ampere_hours)

            # Load filters
            self.load_filters(forecasts_list, 'calculated_battery_type', 'Battery Types')
            self.populate_chart_fiscal_years(forecasts_list)

            # Populate table
            self.forecasts_table.setRowCount(0)
            for row, forecast in enumerate(forecasts_list):
                self.forecasts_table.insertRow(row)
                
                # Fiscal Year
                item = QTableWidgetItem(str(forecast['fiscal_year']))
                item.setData(Qt.UserRole, forecast.get('forecast_id', forecast.get('id')))
                item.setData(Qt.UserRole + 1, forecast['fiscal_year'])
                self.forecasts_table.setItem(row, 0, item)
                
                # Equipment
                equipment_display = utils.format_equipment_display(forecast)
                equipment_item = QTableWidgetItem(equipment_display)
                self.forecasts_table.setItem(row, 1, equipment_item)
                
                # Battery Type - Calculate dynamically from voltage and AH
                voltage = forecast.get('voltage', 12.0)
                ampere_hours = forecast.get('ampere_hours', 125)
                calculated_battery_type = utils.calculate_battery_type(voltage, ampere_hours)
                battery_item = QTableWidgetItem(calculated_battery_type)
                battery_item.setData(Qt.UserRole, calculated_battery_type)
                self.forecasts_table.setItem(row, 2, battery_item)
                
                # Voltage
                voltage = forecast.get('voltage', 0)
                voltage_item = QTableWidgetItem(f"{voltage}V")
                self.forecasts_table.setItem(row, 3, voltage_item)
                
                # AH (Ampere Hours)
                ah = forecast.get('ampere_hours', 0)
                ah_item = QTableWidgetItem(f"{ah}AH")
                self.forecasts_table.setItem(row, 4, ah_item)
                
                # Remarks
                remarks = forecast['remarks'] or ""
                if len(remarks) > 50:
                    remarks = remarks[:47] + "..."
                self.forecasts_table.setItem(row, 5, QTableWidgetItem(remarks))

            self.forecasts_table.resizeColumnsToContents()

        except Exception as e:
            logger.error(f"Error loading battery forecast data: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load battery forecast data: {str(e)}")

    def update_details_panel(self, forecast):
        """Update details panel with battery forecast data."""
        self.detail_fiscal_year.setText(str(forecast.get('fiscal_year', '--')))
        self.detail_equipment.setText(utils.format_equipment_display(forecast))
        self.detail_type.setText(forecast.get('battery_type', '--'))
        self.detail_remarks.setText(forecast.get('remarks', '--'))

    def add_forecast(self):
        """Add a new battery forecast."""
        try:
            dialog = BatteryForecastDialog(parent=self)
            if dialog.exec_() == QDialog.Accepted:
                forecast_data = dialog.get_forecast_data()
                if forecast_data:
                    forecast = models.BatteryForecast(
                        equipment_id=forecast_data['equipment_id'],
                        fiscal_year=forecast_data['fiscal_year'],
                        battery_type=forecast_data['battery_type'],
                        total_requirement=forecast_data['total_requirement'],
                        remarks=forecast_data['remarks']
                    )
                    forecast.save()
                    self.load_data()
                    self.show_status("Battery forecast added successfully.")
        except Exception as e:
            logger.error(f"Error adding battery forecast: {e}")
            QMessageBox.critical(self, "Error", f"Failed to add forecast: {str(e)}")

    def edit_forecast(self):
        """Edit the selected battery forecast."""
        try:
            selected_row = self.forecasts_table.currentRow()
            if selected_row < 0 or selected_row >= len(self._forecast_records):
                return
                
            forecast = self._forecast_records[selected_row]
            dialog = BatteryForecastDialog(forecast, parent=self)
            if dialog.exec_() == QDialog.Accepted:
                forecast_data = dialog.get_forecast_data()
                if forecast_data:
                    forecast_data['forecast_id'] = forecast.get('forecast_id') or forecast.get('id')
                    # Update the existing forecast
                    updated_forecast = models.BatteryForecast(
                        forecast_id=forecast_data['forecast_id'],
                        equipment_id=forecast_data['equipment_id'],
                        fiscal_year=forecast_data['fiscal_year'],
                        battery_type=forecast_data['battery_type'],
                        total_requirement=forecast_data['total_requirement'],
                        remarks=forecast_data['remarks']
                    )
                    updated_forecast.save()
                    self.load_data()
                    self.show_status("Battery forecast updated successfully.")
        except Exception as e:
            logger.error(f"Error editing battery forecast: {e}")
            QMessageBox.critical(self, "Error", f"Failed to update forecast: {str(e)}")

    def delete_forecast(self):
        """Delete the selected battery forecast."""
        try:
            selected_row = self.forecasts_table.currentRow()
            if selected_row < 0 or selected_row >= len(self._forecast_records):
                return
                
            forecast = self._forecast_records[selected_row]
            reply = QMessageBox.question(
                self, "Confirm Delete",
                "Are you sure you want to delete this forecast?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                models.BatteryForecast.delete(forecast.get('forecast_id') or forecast.get('id'))
                self.load_data()
                self.show_status("Battery forecast deleted successfully.")
        except Exception as e:
            logger.error(f"Error deleting battery forecast: {e}")
            QMessageBox.critical(self, "Error", f"Failed to delete forecast: {str(e)}")

    def calculate_all_forecasts(self):
        """Calculate and display battery forecast charts."""
        fiscal_year = self.chart_fiscal_year.currentData() if hasattr(self, 'chart_fiscal_year') else None
        
        if not fiscal_year:
            self.safe_chart_update(self.volume_chart, "Total Battery Requirement", [], [], "Battery Type", "Requirement")
            return

        try:
            forecasts = [f for f in getattr(self, '_forecast_records', []) if f['fiscal_year'] == fiscal_year]
            if not forecasts:
                self.safe_chart_update(self.volume_chart, "Total Battery Requirement", [], [], "Battery Type", "Requirement")
                return

            # Aggregate by calculated battery type (same as displayed in table)
            type_totals = {}
            for f in forecasts:
                # Use the same calculation as in load_data method
                voltage = f.get('voltage', 12.0)
                ampere_hours = f.get('ampere_hours', 125)
                calculated_battery_type = utils.calculate_battery_type(voltage, ampere_hours)
                type_totals[calculated_battery_type] = type_totals.get(calculated_battery_type, 0) + f['total_requirement']
                
            labels = list(type_totals.keys())
            values = list(type_totals.values())
            
            self.safe_chart_update(
                self.volume_chart,
                f"Total Battery Requirement for {fiscal_year}", 
                labels, values, "Battery Type", "Requirement"
            )

        except Exception as e:
            logger.error(f"Error calculating battery forecasts: {e}")
            self.safe_chart_update(self.volume_chart, "Total Battery Requirement", [], [], "Battery Type", "Requirement")


class EquipmentForecastWidget(ForecastWidgetBase):
    """Widget for managing equipment demand forecasts."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Set up the equipment forecast widget UI."""
        layout = QVBoxLayout(self)
        self.setup_common_ui(layout)
        
        # Customize table headers for equipment
        self.forecasts_table.setHorizontalHeaderLabels(["Fiscal Year", "Equipment", "Equipment Type", "Requirement", "Remarks"])

    def load_data(self):
        """Load equipment forecast data."""
        try:
            forecasts_list = models.EquipmentForecast.get_all() or []
            self._forecast_records = forecasts_list

            # Load filters
            self.load_filters(forecasts_list, 'equipment_type', 'Equipment Types')
            self.populate_chart_fiscal_years(forecasts_list)

            # Populate table
            self.forecasts_table.setRowCount(0)
            for row, forecast in enumerate(forecasts_list):
                self.forecasts_table.insertRow(row)
                
                # Fiscal Year
                item = QTableWidgetItem(str(forecast['fiscal_year']))
                item.setData(Qt.UserRole, forecast.get('forecast_id', forecast.get('id')))
                item.setData(Qt.UserRole + 1, forecast['fiscal_year'])
                self.forecasts_table.setItem(row, 0, item)
                
                # Equipment
                equipment_display = utils.format_equipment_display(forecast)
                equipment_item = QTableWidgetItem(equipment_display)
                self.forecasts_table.setItem(row, 1, equipment_item)
                
                # Equipment Type
                equipment_item = QTableWidgetItem(forecast['equipment_type'])
                equipment_item.setData(Qt.UserRole, forecast['equipment_type'])
                self.forecasts_table.setItem(row, 2, equipment_item)
                
                # Requirement
                self.forecasts_table.setItem(row, 3, QTableWidgetItem(str(forecast['total_requirement'])))
                
                # Remarks
                remarks = forecast['remarks'] or ""
                if len(remarks) > 50:
                    remarks = remarks[:47] + "..."
                self.forecasts_table.setItem(row, 4, QTableWidgetItem(remarks))

            self.forecasts_table.resizeColumnsToContents()

        except Exception as e:
            logger.error(f"Error loading equipment forecast data: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load equipment forecast data: {str(e)}")

    def update_details_panel(self, forecast):
        """Update details panel with equipment forecast data."""
        self.detail_fiscal_year.setText(str(forecast.get('fiscal_year', '--')))
        self.detail_equipment.setText(utils.format_equipment_display(forecast))
        self.detail_type.setText(forecast.get('equipment_type', '--'))
        self.detail_requirement.setText(str(forecast.get('total_requirement', '--')))
        self.detail_remarks.setText(forecast.get('remarks', '--'))

    def add_forecast(self):
        """Add a new equipment forecast."""
        try:
            dialog = EquipmentForecastDialog(parent=self)
            if dialog.exec_() == QDialog.Accepted:
                forecast_data = dialog.get_forecast_data()
                if forecast_data:
                    forecast = models.EquipmentForecast(
                        equipment_id=forecast_data['equipment_id'],
                        fiscal_year=forecast_data['fiscal_year'],
                        equipment_type=forecast_data['equipment_type'],
                        total_requirement=forecast_data['total_requirement'],
                        remarks=forecast_data['remarks']
                    )
                    forecast.save()
                    self.load_data()
                    self.show_status("Equipment forecast added successfully.")
        except Exception as e:
            logger.error(f"Error adding equipment forecast: {e}")
            QMessageBox.critical(self, "Error", f"Failed to add forecast: {str(e)}")

    def edit_forecast(self):
        """Edit the selected equipment forecast."""
        try:
            selected_row = self.forecasts_table.currentRow()
            if selected_row < 0 or selected_row >= len(self._forecast_records):
                return
                
            forecast = self._forecast_records[selected_row]
            dialog = EquipmentForecastDialog(forecast, parent=self)
            if dialog.exec_() == QDialog.Accepted:
                forecast_data = dialog.get_forecast_data()
                if forecast_data:
                    forecast_data['forecast_id'] = forecast.get('forecast_id') or forecast.get('id')
                    # Update the existing forecast
                    updated_forecast = models.EquipmentForecast(
                        forecast_id=forecast_data['forecast_id'],
                        equipment_id=forecast_data['equipment_id'],
                        fiscal_year=forecast_data['fiscal_year'],
                        equipment_type=forecast_data['equipment_type'],
                        total_requirement=forecast_data['total_requirement'],
                        remarks=forecast_data['remarks']
                    )
                    updated_forecast.save()
                    self.load_data()
                    self.show_status("Equipment forecast updated successfully.")
        except Exception as e:
            logger.error(f"Error editing equipment forecast: {e}")
            QMessageBox.critical(self, "Error", f"Failed to update forecast: {str(e)}")

    def delete_forecast(self):
        """Delete the selected equipment forecast."""
        try:
            selected_row = self.forecasts_table.currentRow()
            if selected_row < 0 or selected_row >= len(self._forecast_records):
                return
                
            forecast = self._forecast_records[selected_row]
            reply = QMessageBox.question(
                self, "Confirm Delete",
                "Are you sure you want to delete this forecast?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                models.EquipmentForecast.delete(forecast.get('forecast_id') or forecast.get('id'))
                self.load_data()
                self.show_status("Equipment forecast deleted successfully.")
        except Exception as e:
            logger.error(f"Error deleting equipment forecast: {e}")
            QMessageBox.critical(self, "Error", f"Failed to delete forecast: {str(e)}")

    def calculate_all_forecasts(self):
        """Calculate and display equipment forecast charts."""
        fiscal_year = self.chart_fiscal_year.currentData() if hasattr(self, 'chart_fiscal_year') else None
        
        if not fiscal_year:
            self.safe_chart_update(self.volume_chart, "Total Equipment Requirement", [], [], "Equipment Type", "Requirement")
            return

        try:
            forecasts = [f for f in getattr(self, '_forecast_records', []) if f['fiscal_year'] == fiscal_year]
            if not forecasts:
                self.safe_chart_update(self.volume_chart, "Total Equipment Requirement", [], [], "Equipment Type", "Requirement")
                return

            # Aggregate by equipment type
            type_totals = {}
            for f in forecasts:
                t = f['equipment_type']
                type_totals[t] = type_totals.get(t, 0) + f['total_requirement']
                
            labels = list(type_totals.keys())
            values = list(type_totals.values())
            
            self.safe_chart_update(
                self.volume_chart,
                f"Total Equipment Requirement for {fiscal_year}", 
                labels, values, "Equipment Type", "Requirement"
            )

        except Exception as e:
            logger.error(f"Error calculating equipment forecasts: {e}")
            self.safe_chart_update(self.volume_chart, "Total Equipment Requirement", [], [], "Equipment Type", "Requirement")
