"""Utility functions for the equipment inventory application."""
import logging
from datetime import datetime, date, timedelta
import calendar

# Utility functions module

def calculate_overhaul_status(overhaul_type, due_date, done_date, date_of_commission=None, oh1_done_date=None, custom_intervals=None, meterage_km=None):
    """
    Date-based overhaul status calculation (meterage ignored per user requirement).

    Date-Based Status Rules (applies to both OH-I and OH-II):
    - Scheduled: Due date is more than 2 years in the future (>730 days)
    - Warning: Due date is between 1-2 years in the future (365-730 days)
    - Critical: Due date is within 1 year (0-365 days)
    - Overdue: Due date has passed (negative days)

    OH-I Rules:
    - Due after 15 years from commission date

    OH-II Rules:
    - Due 10 years after OH-I completion
    - After OH-II completion, equipment should be discarded after 10 more years
    """
    try:
        today = date.today()

        # Handle completion status first
        if done_date and done_date not in [None, '', 'None', 'No']:
            if isinstance(done_date, str):
                try:
                    done_date_parsed = date.fromisoformat(done_date.split(' ')[0])
                except Exception:
                    done_date_parsed = None
            else:
                done_date_parsed = done_date if isinstance(done_date, date) else None

            if done_date_parsed:
                if overhaul_type == 'OH-II':
                    # Check if equipment should be discarded (10 years after OH-II)
                    discard_date = done_date_parsed + timedelta(days=365*10)
                    if today >= discard_date:
                        return "discard"
                    else:
                        return "completed"
                else:
                    return "completed"

        # Calculate due date if not provided
        if isinstance(due_date, str):
            try:
                due_date = date.fromisoformat(due_date.split(" ")[0])
            except Exception:
                due_date = None

        if due_date is None:
            if overhaul_type == 'OH-I' and date_of_commission:
                if isinstance(date_of_commission, str):
                    try:
                        date_of_commission = date.fromisoformat(date_of_commission.split(" ")[0])
                    except Exception:
                        date_of_commission = None
                if date_of_commission:
                    due_date = date_of_commission + timedelta(days=365*15)
            elif overhaul_type == 'OH-II' and oh1_done_date:
                if isinstance(oh1_done_date, str):
                    try:
                        oh1_done_date = date.fromisoformat(oh1_done_date.split(" ")[0])
                    except Exception:
                        oh1_done_date = None
                if oh1_done_date:
                    due_date = oh1_done_date + timedelta(days=365*10)

        if due_date is None:
            return "unknown"

        days_diff = (due_date - today).days

        # Updated date-based status calculation with new thresholds
        if days_diff < 0:
            status = "overdue"          # Past due date
        elif days_diff <= 365:
            status = "critical"         # Within 1 year (0-365 days)
        elif days_diff <= 730:
            status = "warning"          # Between 1-2 years (365-730 days)
        else:
            status = "scheduled"        # More than 2 years (>730 days)

        # Date-only calculation - meterage logic removed per user requirement

        return status

    except Exception as e:
        logging.error(f"Error in calculate_overhaul_status: {e}")
        return "unknown"

# Configure logger
logger = logging.getLogger('utils')
logger.setLevel(logging.WARNING)

def check_month_rollover():
    """Check if month has changed and update equipment usage metrics.
    
    This function compares the current date with the last recorded month.
    If the month has changed, it moves current month values to previous month
    and resets current month values to zero.
    
    Returns:
        bool: True if rollover occurred, False otherwise
    """
    from models import Equipment
    import database
    
    try:
        # Get the last recorded month update from settings or create if not exists
        query = "SELECT value FROM settings WHERE key = 'last_month_rollover'"
        result = database.execute_query(query, fetchall=False)
        
        today = date.today()
        current_month = f"{today.year}-{today.month:02d}"
        
        if not result:
            # First time setup
            query = "INSERT INTO settings (key, value) VALUES (?, ?)"
            database.execute_query(query, ("last_month_rollover", current_month), fetchall=False)
            logger.info(f"Initialized month rollover tracking with {current_month}")
            return False
            
        last_month = result['value']
        
        # If month has changed
        if current_month != last_month:
            logger.info(f"Month changed from {last_month} to {current_month}. Performing rollover.")
            
            # Update all equipment records
            query = """
                UPDATE equipment 
                SET km_hrs_run_previous_month = km_hrs_run_current_month,
                    hours_run_previous_month = hours_run_current_month,
                    km_hrs_run_current_month = 0,
                    hours_run_current_month = 0
            """
            database.execute_query(query)
            
            # Update the last rollover date
            query = "UPDATE settings SET value = ? WHERE key = 'last_month_rollover'"
            database.execute_query(query, (current_month,), fetchall=False)
            
            logger.info("Monthly usage rollover completed successfully")
            return True
            
        return False
    except Exception as e:
        logger.error(f"Error during month rollover check: {e}")
        return False

def date_to_str(date_obj):
    """Convert date object to string."""
    if date_obj is None:
        return ""
    
    try:
        from config import DATE_FORMAT
        return date_obj.strftime(DATE_FORMAT)
    except Exception as e:
        logger.error(f"Error converting date to string: {e}")
        return str(date_obj)

def str_to_date(date_str):
    """Convert string to date object."""
    if not date_str:
        return None
    
    try:
        from config import DATE_FORMAT
        return datetime.strptime(date_str, DATE_FORMAT).date()
    except Exception as e:
        logger.error(f"Error converting string to date: {e}")
        return None

def format_date_for_display(date_value):
    """Format date for display in tables and forms."""
    if not date_value:
        return ""
    
    try:
        # Handle both string and date object inputs
        if isinstance(date_value, str):
            # Try to parse ISO format first
            try:
                date_obj = datetime.fromisoformat(date_value).date()
            except ValueError:
                # Try other common formats
                for fmt in ["%Y-%m-%d", "%d/%m/%Y", "%m/%d/%Y", "%d-%m-%Y"]:
                    try:
                        date_obj = datetime.strptime(date_value, fmt).date()
                        break
                    except ValueError:
                        continue
                else:
                    return date_value  # Return as-is if can't parse
        elif isinstance(date_value, (date, datetime)):
            date_obj = date_value.date() if isinstance(date_value, datetime) else date_value
        else:
            return str(date_value)
        
        # Format for display (DD/MM/YYYY)
        return date_obj.strftime("%d/%m/%Y")
    except Exception as e:
        logger.error(f"Error formatting date for display: {e}")
        return str(date_value) if date_value else ""

def format_decimal(value, precision=2):
    """Format decimal to string with specified precision."""
    if value is None:
        return "0.00"
    
    try:
        return f"{float(value):.{precision}f}"
    except Exception as e:
        logger.error(f"Error formatting decimal: {e}")
        return "0.00"

def get_maintenance_status(due_date):
    """Get maintenance status based on due date with updated thresholds."""
    if due_date is None:
        return "unknown"
    
    # Convert string due_date to date object if necessary
    if isinstance(due_date, str):
        try:
            due_date = date.fromisoformat(due_date)
        except ValueError:
            try:
                due_date = datetime.fromisoformat(due_date).date()
            except Exception as e:
                logger.error(f"Invalid due_date format: {due_date}: {e}")
                return "unknown"
    
    today = date.today()
    days_until_due = (due_date - today).days
    
    # Import constants from config
    from config import MAINTENANCE_CRITICAL_DAYS, MAINTENANCE_WARNING_DAYS, MAINTENANCE_UPCOMING_DAYS
    
    if days_until_due < 0:
        return "overdue"     # Past due - Dark Red
    elif days_until_due <= MAINTENANCE_CRITICAL_DAYS:  # 7 days
        return "critical"    # Critical - 0-7 days - Red  
    elif days_until_due <= MAINTENANCE_WARNING_DAYS:   # 30 days
        return "warning"     # Warning - 8-30 days - Orange
    elif days_until_due <= MAINTENANCE_UPCOMING_DAYS:  # 90 days (3 months)
        return "upcoming"    # Upcoming - 31-90 days - Blue
    else:
        return "pending"     # Pending - 90+ days - Green

def get_meterage_status(current_meterage, last_service_meterage, service_interval):
    """Get status based on meterage."""
    if current_meterage is None or last_service_meterage is None or service_interval is None:
        return "unknown"
    
    if service_interval <= 0:
        return "normal"  # No service interval specified
    
    meterage_since_service = current_meterage - last_service_meterage
    percentage_used = (meterage_since_service / service_interval) * 100
    
    if percentage_used >= 100:
        return "critical"  # Over service interval
    elif percentage_used >= 80:
        return "warning"   # Approaching service interval
    else:
        return "normal"    # Well within service interval

def calculate_due_date(done_date, maintenance_category):
    """Calculate due date based on maintenance category and done date."""
    if done_date is None:
        return None
    
    try:
        from config import MAINTENANCE_CATEGORIES
        from dateutil.relativedelta import relativedelta
        
        # Get months to add for the category
        category_config = MAINTENANCE_CATEGORIES.get(maintenance_category, {'months': 12})
        months_to_add = category_config['months']
        
        # Convert done_date to date object if it's a string
        if isinstance(done_date, str):
            done_date = date.fromisoformat(done_date)
        
        # Calculate due date by adding months
        due_date = done_date + relativedelta(months=months_to_add)
        
        return due_date
    except Exception as e:
        logger.error(f"Error calculating due date: {e}")
        # Fallback to adding 6 months
        return done_date + timedelta(days=180)

def get_next_fiscal_year():
    """Get the next fiscal year in the format YYYY-YY."""
    from config import CURRENT_FISCAL_YEAR
    
    try:
        current_year_str = CURRENT_FISCAL_YEAR.split('-')[0]
        current_year = int(current_year_str)
        next_year = current_year + 1
        next_next_year = next_year + 1
        
        return f"{next_year}-{str(next_next_year)[-2:]}"
    except Exception as e:
        logger.error(f"Error calculating next fiscal year: {e}")
        this_year = date.today().year
        next_year = this_year + 1
        return f"{this_year}-{str(next_year)[-2:]}"

def calculate_fluid_demand(fluid, equipment, fiscal_year=None):
    """Calculate the fluid demand for the given equipment and fiscal year."""
    # Default to current fiscal year if not specified
    if fiscal_year is None:
        from config import CURRENT_FISCAL_YEAR
        fiscal_year = CURRENT_FISCAL_YEAR
    
    try:
        # Basic calculation factors
        units_held = equipment.get('units_held', 1)
        capacity = fluid.get('capacity_ltrs_kg', 0)
        top_up = fluid.get('addl_10_percent_top_up', 0)
        
        # Determine number of services per year
        periodicity_months = fluid.get('periodicity_months', 12)
        if periodicity_months <= 0:
            periodicity_months = 12  # Default to annual service
        
        services_per_year = 12 / periodicity_months
        
        # Calculate total requirement
        total_capacity = capacity + top_up
        annual_requirement = total_capacity * services_per_year * units_held
        
        # Add a buffer for unexpected servicing (20%)
        total_requirement = annual_requirement * 1.2
        
        return round(total_requirement, 2)
    except Exception as e:
        logger.error(f"Error calculating fluid demand: {e}")
        return 0

def generate_fiscal_years(num_years=5):
    """Generate a list of fiscal years starting from current."""
    from config import CURRENT_FISCAL_YEAR
    
    try:
        current_year_str = CURRENT_FISCAL_YEAR.split('-')[0]
        current_year = int(current_year_str)
        
        fiscal_years = []
        for i in range(num_years):
            year = current_year + i
            next_year = year + 1
            fiscal_years.append(f"{year}-{str(next_year)[-2:]}")
        
        return fiscal_years
    except Exception as e:
        logger.error(f"Error generating fiscal years: {e}")
        return []

def calculate_next_due_date(done_date, maintenance_category):
    """
    CORRECTED: Calculate next due date from done_date baseline (NOT completion date).
    This maintains the proper maintenance schedule chain.

    Args:
        done_date: Previous maintenance done_date baseline (date object or string)
        maintenance_category: Category of maintenance ('TM-1', 'TM-2', 'Yearly', 'Monthly')

    Returns:
        date: Next due date, or None if calculation fails
    """
    try:
        from dateutil.relativedelta import relativedelta
        from datetime import datetime, date
        import config

        # Parse done_date if it's a string
        if isinstance(done_date, str):
            try:
                done_date_parsed = datetime.strptime(done_date, config.DATE_FORMAT).date()
            except Exception:
                try:
                    done_date_parsed = datetime.fromisoformat(done_date).date()
                except Exception:
                    return None
        else:
            done_date_parsed = done_date

        # Get maintenance interval configuration
        intervals = {
            'TM-1': 6,      # 6 months
            'TM-2': 12,     # 12 months
            'Yearly': 12,   # 12 months
            'Monthly': 1    # 1 month
        }
        
        months = intervals.get(maintenance_category, 6)
        
        # Calculate next due date from done_date baseline (NOT actual completion date)
        next_due = done_date_parsed + relativedelta(months=months)
        logger.info(f"Calculated next due date: {next_due} (from done_date: {done_date_parsed}, category: {maintenance_category})")
        return next_due

    except Exception as e:
        logger.error(f"Error calculating next due date: {e}")
        return None

def calculate_maintenance_status(maintenance):
    """
    Centralized maintenance status calculation logic.
    Used consistently across UI and backend to avoid discrepancies.

    Status categories:
    - "completed": Manually completed via Complete button (green)
    - "overdue": Past due date (dark red)
    - "critical": Due within 7 days (red)
    - "warning": Due within 30 days (orange)
    - "upcoming": Due within 90 days (light blue)
    - "scheduled": Future scheduled (blue)
    - "unknown": Invalid/missing data (gray)
    """
    from datetime import datetime, date
    import config

    try:
        # Check database status field first - this is the source of truth for completion
        db_status = maintenance.get('status', '')

        # If explicitly marked as completed in database, show as completed
        if db_status == 'completed':
            return "completed"

        # For non-completed maintenance, calculate status based on due date
        due_date_val = maintenance.get('next_due_date') or maintenance.get('due_date')
        if not due_date_val:
            return "unknown"

        # Parse due date
        due_date = None
        if isinstance(due_date_val, str):
            try:
                due_date = datetime.strptime(due_date_val, config.DATE_FORMAT).date()
            except Exception:
                try:
                    due_date = datetime.fromisoformat(due_date_val).date()
                except Exception:
                    due_date = None
        else:
            due_date = due_date_val

        if due_date:
            today = date.today()
            days_until_due = (due_date - today).days

            # Use configuration constants for consistency
            if days_until_due < 0:
                return "overdue"    # Past due date
            elif days_until_due <= config.MAINTENANCE_CRITICAL_DAYS:  # 7 days
                return "critical"   # Due within 7 days
            elif days_until_due <= config.MAINTENANCE_WARNING_DAYS:   # 30 days
                return "warning"    # Due within 30 days
            elif days_until_due <= config.MAINTENANCE_UPCOMING_DAYS:  # 90 days
                return "upcoming"   # Due within 90 days
            else:
                return "scheduled"  # Future scheduled
        else:
            return "unknown"

    except Exception as e:
        logger.error(f"Error calculating maintenance status: {e}")
        return "unknown"

def get_status_color(status):
    """Get color for status display with comprehensive status mapping."""
    colors = {
        "completed": "#33cc33",    # Bright Green - for manually completed maintenance
        "critical": "#ff6600",     # Orange-Red - due within 7 days
        "warning": "#ff9900",      # Orange - due within 30 days  
        "overdue": "#cc0000",      # Red - past due date
        "scheduled": "#009900",    # Green - future scheduled
        "upcoming": "#669900",     # Olive Green - due within 90 days
        "unknown": "#cc33ff",      # Purple/Magenta - unknown/invalid status
        
        # Legacy status mappings for backward compatibility
        "normal": "#33cc33",       # Use new completed color
        "neutral": "#cc33ff"       # Use new unknown color
    }
    return colors.get(status.lower(), "#cc33ff")  # Default to purple

class EventBus:
    """Simple event bus for application-wide signals."""
    _subscribers = {}

    @classmethod
    def subscribe(cls, event, callback):
        if event not in cls._subscribers:
            cls._subscribers[event] = []
        cls._subscribers[event].append(callback)

    @classmethod
    def unsubscribe(cls, event, callback):
        if event in cls._subscribers:
            cls._subscribers[event] = [cb for cb in cls._subscribers[event] if cb != callback]

    @classmethod
    def emit(cls, event, *args, **kwargs):
        for callback in cls._subscribers.get(event, []):
            try:
                callback(*args, **kwargs)
            except Exception as e:
                logger.error(f"EventBus error in '{event}': {e}")

def validate_ba_number(ba_number):
    """Validate BA number format."""
    if not ba_number:
        return True  # Allow empty BA numbers during transition
    
    import re
    import config
    pattern = config.BA_NUMBER_VALIDATION_REGEX
    return bool(re.match(pattern, ba_number.replace(' ', '')))  # Remove spaces for validation

def format_ba_number(ba_number):
    """Format BA number for display (remove extra spaces)."""
    if not ba_number:
        return 'Not Assigned'
    return ba_number.strip()

def format_equipment_display(equipment):
    """Format equipment for display with BA number priority."""
    if not equipment:
        return 'Unknown Equipment'
    
    ba_number = equipment.get('ba_number') or equipment.get('BANumber')
    make_type = equipment.get('make_and_type') or equipment.get('MakeAndType') or ''
    serial = equipment.get('serial_number') or equipment.get('SerialNumber') or ''
    
    if ba_number and ba_number.strip():  # Check if BA number exists and is not empty
        return f"{format_ba_number(ba_number)} - {make_type}"
    else:
        return f"SN: {serial} - {make_type}"

def generate_next_ba_number():
    """Generate next sequential BA number (if needed for import)."""
    # This would be customized based on your organization's numbering scheme
    # For now, return None to indicate manual assignment needed
    return None

def format_equipment_for_dropdown(equipment):
    """Format equipment for dropdown display with BA number priority (compact style)."""
    if not equipment:
        return 'Unknown Equipment'
    ba_number = equipment.get('ba_number') or equipment.get('BANumber')
    make_type = equipment.get('make_and_type') or equipment.get('MakeAndType') or 'Unknown Type'
    serial_number = equipment.get('serial_number') or equipment.get('SerialNumber') or 'Unknown'
    if ba_number and ba_number.strip():
        return f"{ba_number.strip()} - {make_type}"
    else:
        return f"SN: {serial_number} - {make_type}"

def calculate_battery_type(voltage, ampere_hours):
    """
    Calculate battery type string from voltage and ampere hours.

    Args:
        voltage (float): Battery voltage
        ampere_hours (int): Battery ampere hours (AH)

    Returns:
        str: Formatted battery type string (e.g., "24V130AH", "12V125AH")
    """
    if not voltage or not ampere_hours:
        return "Unknown"

    return f"{int(voltage)}V{ampere_hours}AH"