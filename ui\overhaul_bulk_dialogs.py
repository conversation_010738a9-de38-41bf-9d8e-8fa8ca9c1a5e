"""
Bulk operation dialogs for overhaul management.
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                           QPushButton, QTableWidget, QTableWidgetItem, 
                           QComboBox, QDateEdit, QTextEdit, QMessageBox,
                           QCheckBox, QGroupBox)
from PyQt5.QtCore import Qt, QDate
from datetime import datetime
import logging

from models import Equipment, Overhaul
from overhaul_service import (bulk_create_overhauls, bulk_update_status,
                            bulk_delete_overhauls)

logger = logging.getLogger(__name__)

class BulkOverhaulDialog(QDialog):
    """Dialog for performing bulk operations on overhauls."""
    
    def __init__(self, parent=None, operation='create'):
        super().__init__(parent)
        self.operation = operation
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle(f"Bulk {self.operation.title()} Overhauls")
        self.setMinimumWidth(600)
        
        layout = QVBoxLayout()
        
        # Equipment selection
        equipment_group = QGroupBox("Select Equipment")
        equipment_layout = QVBoxLayout()
        
        self.equipment_table = QTableWidget()
        self.equipment_table.setColumnCount(4)
        self.equipment_table.setHorizontalHeaderLabels([
            "Select", "Equipment ID", "Make & Type", "Status"
        ])
        self.equipment_table.horizontalHeader().setStretchLastSection(True)
        
        equipment_layout.addWidget(self.equipment_table)
        equipment_group.setLayout(equipment_layout)
        layout.addWidget(equipment_group)
        
        # Operation specific fields
        if self.operation == 'create':
            self.setup_create_fields(layout)
        elif self.operation == 'update':
            self.setup_update_fields(layout)
        elif self.operation == 'delete':
            self.setup_delete_fields(layout)
            
        # Buttons
        button_layout = QHBoxLayout()
        self.apply_button = QPushButton("Apply")
        self.apply_button.clicked.connect(self.apply_operation)
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.apply_button)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        self.load_equipment()
        
    def setup_create_fields(self, layout):
        """Set up fields for bulk creation."""
        fields_group = QGroupBox("Overhaul Details")
        fields_layout = QVBoxLayout()
        
        # Overhaul type
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("Type:"))
        self.type_combo = QComboBox()
        self.type_combo.addItems(["OH-I", "OH-II", "Custom"])
        self.type_combo.currentTextChanged.connect(self.on_type_changed)
        type_layout.addWidget(self.type_combo)
        fields_layout.addLayout(type_layout)
        
        # Custom type
        self.custom_type_layout = QHBoxLayout()
        self.custom_type_layout.addWidget(QLabel("Custom Type:"))
        self.custom_type_edit = QTextEdit()
        self.custom_type_edit.setMaximumHeight(30)
        self.custom_type_layout.addWidget(self.custom_type_edit)
        self.custom_type_layout.setVisible(False)
        fields_layout.addLayout(self.custom_type_layout)
        
        # Due date
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("Due Date:"))
        self.due_date = QDateEdit()
        self.due_date.setDate(QDate.currentDate())
        self.due_date.setCalendarPopup(True)
        date_layout.addWidget(self.due_date)
        fields_layout.addLayout(date_layout)
        
        # Description
        desc_layout = QHBoxLayout()
        desc_layout.addWidget(QLabel("Description:"))
        self.description = QTextEdit()
        self.description.setMaximumHeight(60)
        desc_layout.addWidget(self.description)
        fields_layout.addLayout(desc_layout)
        
        fields_group.setLayout(fields_layout)
        layout.addWidget(fields_group)
        
    def setup_update_fields(self, layout):
        """Set up fields for bulk update."""
        fields_group = QGroupBox("Update Details")
        fields_layout = QVBoxLayout()
        
        # New status
        status_layout = QHBoxLayout()
        status_layout.addWidget(QLabel("New Status:"))
        self.status_combo = QComboBox()
        self.status_combo.addItems(["scheduled", "in_progress", "completed", "cancelled"])
        status_layout.addWidget(self.status_combo)
        fields_layout.addLayout(status_layout)
        
        # Reason
        reason_layout = QHBoxLayout()
        reason_layout.addWidget(QLabel("Reason:"))
        self.reason = QTextEdit()
        self.reason.setMaximumHeight(60)
        reason_layout.addWidget(self.reason)
        fields_layout.addLayout(reason_layout)
        
        fields_group.setLayout(fields_layout)
        layout.addWidget(fields_group)
        
    def setup_delete_fields(self, layout):
        """Set up fields for bulk delete."""
        fields_group = QGroupBox("Delete Details")
        fields_layout = QVBoxLayout()
        
        # Reason
        reason_layout = QHBoxLayout()
        reason_layout.addWidget(QLabel("Reason:"))
        self.reason = QTextEdit()
        self.reason.setMaximumHeight(60)
        reason_layout.addWidget(self.reason)
        fields_layout.addLayout(reason_layout)
        
        fields_group.setLayout(fields_layout)
        layout.addWidget(fields_group)
        
    def load_equipment(self):
        """Load equipment data into the table."""
        try:
            equipment_list = Equipment.get_active()
            self.equipment_table.setRowCount(len(equipment_list))
            
            for row, equipment in enumerate(equipment_list):
                # Checkbox
                checkbox = QCheckBox()
                checkbox_widget = QTableWidgetItem()
                checkbox_widget.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
                checkbox_widget.setCheckState(Qt.Unchecked)
                self.equipment_table.setItem(row, 0, checkbox_widget)
                
                # Equipment details
                self.equipment_table.setItem(row, 1, QTableWidgetItem(equipment.get('equipment_id')))
                self.equipment_table.setItem(row, 2, QTableWidgetItem(equipment.get('make_and_type')))
                self.equipment_table.setItem(row, 3, QTableWidgetItem(equipment.get('status')))
                
        except Exception as e:
            logger.error(f"Error loading equipment: {e}")
            QMessageBox.critical(self, "Error", "Failed to load equipment data")
            
    def get_selected_equipment(self):
        """Get list of selected equipment IDs."""
        selected = []
        for row in range(self.equipment_table.rowCount()):
            if self.equipment_table.item(row, 0).checkState() == Qt.Checked:
                selected.append(self.equipment_table.item(row, 1).text())
        return selected
        
    def on_type_changed(self, type_text):
        """Handle overhaul type change."""
        self.custom_type_layout.setVisible(type_text == "Custom")
        
    def apply_operation(self):
        """Apply the bulk operation."""
        try:
            selected = self.get_selected_equipment()
            if not selected:
                QMessageBox.warning(self, "Warning", "Please select at least one equipment")
                return
                
            if self.operation == 'create':
                self.apply_create(selected)
            elif self.operation == 'update':
                self.apply_update(selected)
            elif self.operation == 'delete':
                self.apply_delete(selected)
                
        except Exception as e:
            logger.error(f"Error applying bulk operation: {e}")
            QMessageBox.critical(self, "Error", f"Operation failed: {str(e)}")
            
    def apply_create(self, equipment_ids):
        """Apply bulk create operation."""
        overhaul_type = self.type_combo.currentText()
        if overhaul_type == "Custom":
            overhaul_type = self.custom_type_edit.toPlainText().strip()
            if not overhaul_type:
                QMessageBox.warning(self, "Warning", "Please enter a custom type")
                return
                
        due_date = self.due_date.date().toPyDate()
        description = self.description.toPlainText().strip()
        
        success, failed, errors = bulk_create_overhauls(
            equipment_ids,
            overhaul_type,
            due_date,
            description
        )
        
        self.show_result(success, failed, errors)
        
    def apply_update(self, equipment_ids):
        """Apply bulk update operation."""
        new_status = self.status_combo.currentText()
        reason = self.reason.toPlainText().strip()
        
        success, failed, errors = bulk_update_status(
            equipment_ids,
            new_status,
            "system",  # TODO: Get actual user
            reason
        )
        
        self.show_result(success, failed, errors)
        
    def apply_delete(self, equipment_ids):
        """Apply bulk delete operation."""
        reason = self.reason.toPlainText().strip()
        
        success, failed, errors = bulk_delete_overhauls(
            equipment_ids,
            "system",  # TODO: Get actual user
            reason
        )
        
        self.show_result(success, failed, errors)
        
    def show_result(self, success, failed, errors):
        """Show operation results."""
        message = f"Operation completed:\n"
        message += f"Successfully processed: {success}\n"
        message += f"Failed: {failed}\n"
        
        if errors:
            message += "\nErrors:\n"
            message += "\n".join(errors)
            
        QMessageBox.information(self, "Result", message)
        self.accept() 