"""Dialogs for overhaul management in the equipment inventory application."""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLabel, QLineEdit, QComboBox, QDateEdit, QTextEdit,
                            QPushButton, QDialogButtonBox, QMessageBox, QTableWidget,
                            QTableWidgetItem)
from PyQt5.QtCore import Qt, QDate
from datetime import datetime, date
import logging

from models import Equipment, Overhaul, OverhaulHistory
from utils import validate_date
import overhaul_service

logger = logging.getLogger(__name__)

class OverhaulCreationDialog(QDialog):
    """Dialog for creating new overhaul records."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Create New Overhaul")
        self.setMinimumWidth(500)
        self.setup_ui()
        self.load_equipment()
        
    def setup_ui(self):
        """Set up the dialog UI components."""
        layout = QVBoxLayout()
        
        # Form layout for input fields
        form_layout = QFormLayout()
        
        # Equipment selection
        self.equipment_combo = QComboBox()
        self.equipment_search = QLineEdit()
        self.equipment_search.setPlaceholderText("Search equipment...")
        self.equipment_search.textChanged.connect(self.filter_equipment)
        form_layout.addRow("Equipment:", self.equipment_combo)
        form_layout.addRow("Search:", self.equipment_search)
        
        # Overhaul type
        self.type_combo = QComboBox()
        self.type_combo.addItems(["OH-I", "OH-II", "Custom"])
        self.type_combo.currentTextChanged.connect(self.on_type_changed)
        form_layout.addRow("Type:", self.type_combo)
        
        # Custom type input
        self.custom_type = QLineEdit()
        self.custom_type.setVisible(False)
        form_layout.addRow("Custom Type:", self.custom_type)
        
        # Due date
        self.due_date = QDateEdit()
        self.due_date.setCalendarPopup(True)
        self.due_date.setDate(QDate.currentDate())
        form_layout.addRow("Due Date:", self.due_date)
        
        # Description
        self.description = QTextEdit()
        self.description.setMaximumHeight(100)
        form_layout.addRow("Description:", self.description)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.validate_and_accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
        
    def load_equipment(self):
        """Load equipment list into combo box."""
        try:
            equipment_list = Equipment.get_active()
            self.equipment_combo.clear()
            for equipment in equipment_list:
                self.equipment_combo.addItem(
                    f"{equipment['make_and_type']} ({equipment['ba_number']})",
                    equipment['equipment_id']
                )
        except Exception as e:
            logger.error(f"Error loading equipment: {e}")
            QMessageBox.critical(self, "Error", "Failed to load equipment list")
            
    def filter_equipment(self, search_text):
        """Filter equipment list based on search text."""
        try:
            equipment_list = Equipment.get_active()
            self.equipment_combo.clear()
            
            for equipment in equipment_list:
                if (search_text.lower() in equipment['make_and_type'].lower() or
                    search_text.lower() in equipment['ba_number'].lower()):
                    self.equipment_combo.addItem(
                        f"{equipment['make_and_type']} ({equipment['ba_number']})",
                        equipment['equipment_id']
                    )
        except Exception as e:
            logger.error(f"Error filtering equipment: {e}")
            
    def on_type_changed(self, type_text):
        """Handle overhaul type selection change."""
        self.custom_type.setVisible(type_text == "Custom")
        
        # When OH-I is selected, try to calculate due date based on release date
        if type_text == "OH-I" and self.equipment_combo.currentData():
            try:
                equipment_id = self.equipment_combo.currentData()
                calculated_date = overhaul_service.calculate_oh1_due_date(equipment_id)
                if calculated_date:
                    self.due_date.setDate(QDate(calculated_date.year, calculated_date.month, calculated_date.day))
                    # Make due date field read-only for OH-I
                    self.due_date.setEnabled(False)
                else:
                    self.due_date.setEnabled(True)  # Enable if calculation failed
            except Exception as e:
                logger.warning(f"Could not automatically set OH-I due date: {e}")
                self.due_date.setEnabled(True)
        else:
            # For other types, enable manual due date entry
            self.due_date.setEnabled(True)
        
    def validate_and_accept(self):
        """Validate inputs and accept the dialog if valid."""
        try:
            # Get selected equipment ID
            equipment_id = self.equipment_combo.currentData()
            if not equipment_id:
                raise ValueError("Please select equipment")
                
            # Get overhaul type
            overhaul_type = self.type_combo.currentText()
            if overhaul_type == "Custom":
                overhaul_type = self.custom_type.text().strip()
                if not overhaul_type:
                    raise ValueError("Please enter custom overhaul type")
                    
            # Validate due date
            due_date = self.due_date.date().toPyDate()
            if not validate_date(due_date):
                raise ValueError("Invalid due date")
                
            # For OH-I, recalculate due date using global rule
            if overhaul_type == "OH-I":
                calculated_due_date = overhaul_service.calculate_oh1_due_date(equipment_id)
                if calculated_due_date:
                    due_date = calculated_due_date
                    logger.info(f"Using calculated OH-I due date: {due_date} for equipment ID {equipment_id}")
                else:
                    logger.warning(f"Could not calculate OH-I due date for equipment ID {equipment_id}, using manual date")

                
            # Create overhaul record
            overhaul = Overhaul(
                equipment_id=equipment_id,
                overhaul_type=overhaul_type,
                due_date=due_date.isoformat(),
                description=self.description.toPlainText().strip(),
                status='scheduled'
            )
            
            # Save and log history
            overhaul_id = overhaul.save()
            if overhaul_id:
                OverhaulHistory.log_status_change(
                    overhaul_id,
                    None,
                    'scheduled',
                    'system',
                    'Initial creation'
                )
                self.accept()
            else:
                raise ValueError("Failed to save overhaul record")
                
        except ValueError as e:
            QMessageBox.warning(self, "Validation Error", str(e))
        except Exception as e:
            logger.error(f"Error creating overhaul: {e}")
            QMessageBox.critical(self, "Error", "Failed to create overhaul record")

class OverhaulHistoryDialog(QDialog):
    """Dialog for viewing overhaul history."""
    
    def __init__(self, overhaul_id, parent=None):
        super().__init__(parent)
        self.overhaul_id = overhaul_id
        self.setWindowTitle("Overhaul History")
        self.setMinimumWidth(600)
        self.setup_ui()
        self.load_history()
        
    def setup_ui(self):
        """Set up the dialog UI components."""
        layout = QVBoxLayout()
        
        # History table
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(6)
        self.history_table.setHorizontalHeaderLabels([
            "Date", "From", "To", "Changed By", "Reason", "ID"
        ])
        layout.addWidget(self.history_table)
        
        # Close button
        button_box = QDialogButtonBox(QDialogButtonBox.Close)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
        
    def load_history(self):
        """Load overhaul history data."""
        try:
            history = OverhaulHistory.get_by_overhaul(self.overhaul_id)
            self.history_table.setRowCount(len(history))
            
            for row, entry in enumerate(history):
                self.history_table.setItem(row, 0, QTableWidgetItem(
                    entry['changed_at'].strftime('%Y-%m-%d %H:%M:%S')
                ))
                self.history_table.setItem(row, 1, QTableWidgetItem(
                    entry['status_from'] or 'N/A'
                ))
                self.history_table.setItem(row, 2, QTableWidgetItem(
                    entry['status_to']
                ))
                self.history_table.setItem(row, 3, QTableWidgetItem(
                    entry['changed_by']
                ))
                self.history_table.setItem(row, 4, QTableWidgetItem(
                    entry['change_reason'] or ''
                ))
                self.history_table.setItem(row, 5, QTableWidgetItem(
                    str(entry['history_id'])
                ))
                
        except Exception as e:
            logger.error(f"Error loading overhaul history: {e}")
            QMessageBox.critical(self, "Error", "Failed to load overhaul history") 