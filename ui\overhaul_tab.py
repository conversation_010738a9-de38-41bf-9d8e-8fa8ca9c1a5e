"""
Overhaul tab implementation.
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                           QTabWidget, QTableWidget, QTableWidgetItem, QLabel,
                           QMessageBox, QLineEdit, QComboBox, QHeaderView, QSpacerItem, QSizePolicy,
                           QDateEdit, QTextEdit, QFrame, QGroupBox, QDialog, QFileDialog)
from PyQt5.QtCore import Qt, QDate
import logging
import csv
from datetime import datetime

from models import Overhaul, Equipment, OverhaulHistory
from overhaul_service import get_overhaul_status
from ui.overhaul_dialogs import OverhaulCreationDialog, OverhaulHistoryDialog
from ui.overhaul_bulk_dialogs import BulkOverhaulDialog

logger = logging.getLogger(__name__)

class OverhaulTab(QWidget):
    """Main overhaul management tab with persistent detail view."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_overhaul_data = None # To hold data of the currently selected overhaul
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the main tab UI with left (table) and right (details) sections."""
        main_layout = QHBoxLayout()
        
        # Left section: Tables and bulk operations
        left_layout = QVBoxLayout()
        
        # Bulk operation buttons at the top
        bulk_layout = QHBoxLayout()
        self.bulk_create_btn = QPushButton("Bulk Create")
        self.bulk_create_btn.clicked.connect(self.show_bulk_create)
        bulk_layout.addWidget(self.bulk_create_btn)
        
        self.bulk_update_btn = QPushButton("Bulk Update")
        self.bulk_update_btn.clicked.connect(self.show_bulk_update)
        bulk_layout.addWidget(self.bulk_update_btn)
        
        self.bulk_delete_btn = QPushButton("Bulk Delete")
        self.bulk_delete_btn.clicked.connect(self.show_bulk_delete)
        bulk_layout.addWidget(self.bulk_delete_btn)
        
        left_layout.addLayout(bulk_layout)
        
        # Tab widget for OH-I and OH-II overhauls
        self.tab_widget = QTabWidget()
        
        self.oh1_tab = QWidget()
        self.setup_oh_tab_content(self.oh1_tab, "OH-I")
        self.tab_widget.addTab(self.oh1_tab, "OH-I (First Overhaul)")
        
        self.oh2_tab = QWidget()
        self.setup_oh_tab_content(self.oh2_tab, "OH-II")
        self.tab_widget.addTab(self.oh2_tab, "OH-II (Second Overhaul)")
        
        left_layout.addWidget(self.tab_widget)
        main_layout.addLayout(left_layout, 2) # Give more space to the left section
        
        # Right section: Overhaul Details Panel
        self.details_panel = QFrame()
        self.details_panel.setFrameShape(QFrame.StyledPanel)
        self.details_panel.setFrameShadow(QFrame.Raised)
        self.setup_details_panel()
        main_layout.addWidget(self.details_panel, 1) # Give less space to the right section
        
        self.setLayout(main_layout)
        self.load_all_overhauls() # Load data for both tabs initially

    def setup_oh_tab_content(self, tab, overhaul_type):
        """Set up the content for an OH-I or OH-II tab."""
        tab_layout = QVBoxLayout()

        # Header section
        header_layout = QHBoxLayout()
        header_layout.addWidget(QLabel(f"Equipment {overhaul_type} Overhauls"))
        header_layout.addStretch()

        # Export CSV button
        export_csv_btn = QPushButton("Export CSV")
        export_csv_btn.clicked.connect(lambda: self.export_overhauls_to_csv(overhaul_type))
        header_layout.addWidget(export_csv_btn)

        tab_layout.addLayout(header_layout)

        # Enhanced paginated table for overhauls with BA filtering and grouping
        from ui.paginated_table_widget import PaginatedTableWidget
        table = PaginatedTableWidget(
            page_size=100,
            max_total_rows=50000,
            enable_ba_filter=True,
            enable_ba_grouping=True
        )
        table.row_selected.connect(self.on_overhaul_selected_paginated)

        tab_layout.addWidget(table)
        tab.setLayout(tab_layout)

        # Store table reference
        setattr(self, f"{overhaul_type.lower()}_table", table)
        
    def setup_details_panel(self):
        """Set up the persistent details panel on the right."""
        details_layout = QVBoxLayout()
        
        header_label = QLabel("OH-I Overhaul Details") # Will dynamically change for OH-II
        header_label.setAlignment(Qt.AlignCenter)
        header_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        details_layout.addWidget(header_label)
        
        form_layout = QVBoxLayout()
        
        # Equipment selection for new overhauls
        form_layout.addWidget(QLabel("Equipment:"))
        self.equipment_combo = QComboBox()
        self.equipment_combo.setVisible(False)  # Hidden by default
        form_layout.addWidget(self.equipment_combo)
        
        # Equipment ID (Read-only from selected table row)
        self.detail_equipment_id_label = QLabel("")
        form_layout.addWidget(self.detail_equipment_id_label)
        
        # Meterage KM (Read-only from selected table row)
        form_layout.addWidget(QLabel("Meterage KM:"))
        self.detail_meterage_km_label = QLabel("")
        form_layout.addWidget(self.detail_meterage_km_label)
        
        # Date of Release (Read-only from selected table row, from Equipment model)
        form_layout.addWidget(QLabel("Date of Release:"))
        self.detail_date_of_release_label = QLabel("")
        form_layout.addWidget(self.detail_date_of_release_label)
        
        # OH-I Done Date / Overhaul Done Date (Editable)
        form_layout.addWidget(QLabel("OH-I Done Date:")) # Will dynamically change
        self.detail_done_date_edit = QDateEdit()
        self.detail_done_date_edit.setCalendarPopup(True)
        self.detail_done_date_edit.setDate(QDate.currentDate())
        form_layout.addWidget(self.detail_done_date_edit)
        
        # OH-I Status / Overhaul Status (Read-only, visually represented)
        form_layout.addWidget(QLabel("OH-I Status:")) # Will dynamically change
        self.detail_status_label = QLabel("")
        self.detail_status_label.setStyleSheet("background-color: lightgray; padding: 5px; border-radius: 3px;")
        self.detail_status_label.setAlignment(Qt.AlignCenter)
        form_layout.addWidget(self.detail_status_label)
        
        # Notes field for status changes
        form_layout.addWidget(QLabel("Notes:"))
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        form_layout.addWidget(self.notes_edit)
        
        # Spacer to push elements to top
        form_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
        details_layout.addLayout(form_layout)
        
        # Action buttons for the selected overhaul
        button_layout = QHBoxLayout()
        self.add_new_btn = QPushButton("Add New")
        self.add_new_btn.clicked.connect(self.clear_details_for_new)
        button_layout.addWidget(self.add_new_btn)
        
        self.edit_btn = QPushButton("Edit")
        self.edit_btn.clicked.connect(self.enable_edit_mode)
        button_layout.addWidget(self.edit_btn)
        
        self.save_btn = QPushButton("Save")
        self.save_btn.clicked.connect(self.save_overhaul_details)
        button_layout.addWidget(self.save_btn)
        
        self.delete_btn = QPushButton("Delete")
        self.delete_btn.clicked.connect(self.delete_selected_overhaul)
        button_layout.addWidget(self.delete_btn)
        
        self.oh_comp_btn = QPushButton("Mark Complete")
        self.oh_comp_btn.clicked.connect(self.mark_overhaul_completed)
        button_layout.addWidget(self.oh_comp_btn)
        
        details_layout.addLayout(button_layout)
        
        self.details_panel.setLayout(details_layout)
        self.set_details_panel_read_only(True)
        self.oh_comp_btn.setVisible(False)

    def load_all_overhauls(self):
        """Load overhauls for both OH-I and OH-II tabs."""
        self.load_overhauls("OH-I")
        self.load_overhauls("OH-II")

    def load_overhauls(self, overhaul_type):
        """Load overhauls for the specified type and populate the table."""
        try:
            table = getattr(self, f"{overhaul_type.lower()}_table")
            all_overhauls = Overhaul.get_by_type(overhaul_type)

            # Filter out discarded equipment
            filtered_overhauls = []
            for oh in all_overhauls:
                equipment = Equipment.get_by_id(oh.get('equipment_id'))
                if equipment and equipment.equipment_status != 'discarded':
                    filtered_overhauls.append(oh)

            # Prepare data for enhanced table with BA grouping
            headers = ["ID", "BA Number", "Make & Type", "Meterage KM", "Done Date", "Due Date", "Status"]
            data = []

            for overhaul in filtered_overhauls:
                equipment = Equipment.get_by_id(overhaul.get('equipment_id'))
                if not equipment:
                    continue

                # Format status for display
                status = overhaul.get('status', '').replace('_', ' ').title()

                row_data = {
                    "ID": overhaul.get('id'),
                    "BA Number": equipment.ba_number or "Not Assigned",
                    "Make & Type": equipment.make_and_type or "",
                    "Meterage KM": str(overhaul.get('meter_reading', '')),
                    "Done Date": overhaul.get('done_date', ''),
                    "Due Date": overhaul.get('due_date', ''),
                    "Status": status
                }
                data.append(row_data)

            # Sort by BA Number, then Make & Type for better grouping
            data.sort(key=lambda x: (x["BA Number"], x["Make & Type"]))

            # Set data in enhanced table
            table.set_data(headers, data, id_column="ID")

            # Hide the ID column
            table.setColumnHidden(0, True)

        except Exception as e:
            logger.error(f"Error loading overhauls: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load overhauls: {str(e)}")

    def on_overhaul_selected(self):
        """Handle selection of an overhaul in the table to populate the details panel (legacy method)."""
        selected_items = self.sender().selectedItems()
        if not selected_items:
            self.clear_details_panel()
            return

        # Get the full overhaul data from the first selected item (Status column)
        selected_overhaul = selected_items[3].data(Qt.UserRole)
        self.current_overhaul_data = selected_overhaul

        if selected_overhaul:
            self._populate_details_panel(selected_overhaul)

    def on_overhaul_selected_paginated(self, row_data):
        """Handle selection of an overhaul from paginated table."""
        try:
            if row_data and 'ID' in row_data:
                overhaul_id = row_data['ID']

                # Get full overhaul data
                overhaul = Overhaul.get_by_id(overhaul_id)
                if overhaul:
                    self.current_overhaul_data = overhaul
                    self._populate_details_panel(overhaul)
                else:
                    self.clear_details_panel()
            else:
                self.clear_details_panel()
        except Exception as e:
            logger.error(f"Error in overhaul selection: {e}")
            self.clear_details_panel()

    def _populate_details_panel(self, selected_overhaul):
        """Populate the details panel with overhaul data."""
        try:
            equipment_id = selected_overhaul.get('equipment_id')
            equipment = Equipment.get_by_id(equipment_id)

            # Update details panel labels/edits
            current_tab = self.tab_widget.currentWidget()
            overhaul_type = "OH-I" if current_tab == self.oh1_tab else "OH-II"

            # Find and update the header label
            for child in self.details_panel.findChildren(QLabel):
                if "Overhaul Details" in child.text():
                    child.setText(f"{overhaul_type} Overhaul Details")
                    break

            # Hide equipment combo, show label
            self.equipment_combo.setVisible(False)
            self.detail_equipment_id_label.setVisible(True)
            self.detail_equipment_id_label.setText(str(equipment_id) if equipment_id else "")

            self.detail_meterage_km_label.setText(str(equipment.meterage_kms) if equipment else "N/A")
            self.detail_date_of_release_label.setText(equipment.date_of_commission or "N/A")

            done_date_str = selected_overhaul.get('done_date')
            if done_date_str:
                self.detail_done_date_edit.setDate(QDate.fromString(done_date_str, Qt.ISODate))
            else:
                self.detail_done_date_edit.clear()

            status = selected_overhaul.get('status', 'N/A')
            self.detail_status_label.setText(status.replace('_', ' ').title())
            self.detail_status_label.setStyleSheet(f"background-color: {self.get_status_color(status).name()}; padding: 5px; border-radius: 3px;")

            # Clear notes
            self.notes_edit.clear()

            self.set_details_panel_read_only(True)
            self.oh_comp_btn.setVisible(status not in ['completed', 'cancelled'])

        except Exception as e:
            logger.error(f"Error populating details panel: {e}")
            self.clear_details_panel()

    def clear_details_for_new(self):
        """Clear the details panel for creating a new overhaul."""
        self.current_overhaul_data = None
        
        # Show equipment combo, hide label
        self.equipment_combo.setVisible(True)
        self.detail_equipment_id_label.setVisible(False)
        
        # Clear all fields
        self.detail_meterage_km_label.clear()
        self.detail_date_of_release_label.clear()
        self.detail_done_date_edit.setDate(QDate.currentDate())
        self.detail_status_label.clear()
        self.notes_edit.clear()
        
        # Populate equipment combo
        self.equipment_combo.clear()
        equipment_list = Equipment.get_all()
        for eq in equipment_list:
            if eq.equipment_status != 'discarded':
                self.equipment_combo.addItem(f"{eq.equipment_id} - {eq.ba_number}", eq.equipment_id)
        
        # Enable editing
        self.set_details_panel_read_only(False)
        self.oh_comp_btn.setVisible(False)

    def save_overhaul_details(self):
        """Save the current overhaul details."""
        try:
            # Get current tab to determine overhaul type
            current_tab = self.tab_widget.currentWidget()
            overhaul_type = "OH-I" if current_tab == self.oh1_tab else "OH-II"
            
            if self.current_overhaul_data:  # Editing existing overhaul
                overhaul_id = self.current_overhaul_data.get('id')
                old_status = self.current_overhaul_data.get('status')
                
                # Get new values
                done_date = self.detail_done_date_edit.date().toString('yyyy-MM-dd')
                notes = self.notes_edit.toPlainText().strip()
                
                # Update overhaul
                if Overhaul.update(overhaul_id, done_date=done_date):
                    # Log status change if notes provided
                    if notes:
                        OverhaulHistory.log_status_change(
                            overhaul_id,
                            old_status,
                            old_status,  # Status unchanged
                            "user",
                            notes
                        )
                    self.load_all_overhauls()
                    QMessageBox.information(self, "Success", "Overhaul updated successfully.")
                else:
                    QMessageBox.warning(self, "Error", "Failed to update overhaul.")
                    
            else:  # Creating new overhaul
                equipment_id = self.equipment_combo.currentData()
                if not equipment_id:
                    QMessageBox.warning(self, "Error", "Please select equipment.")
                    return
                    
                done_date = self.detail_done_date_edit.date().toString('yyyy-MM-dd')
                notes = self.notes_edit.toPlainText().strip()
                
                # Create new overhaul
                overhaul = Overhaul(
                    equipment_id=equipment_id,
                    overhaul_type=overhaul_type,
                    done_date=done_date,
                    status='scheduled'
                )
                
                overhaul_id = overhaul.save()
                if overhaul_id:
                    # Log initial creation
                    OverhaulHistory.log_status_change(
                        overhaul_id,
                        None,
                        'scheduled',
                        "user",
                        notes if notes else "Initial creation"
                    )
                    self.load_all_overhauls()
                    QMessageBox.information(self, "Success", "Overhaul created successfully.")
                else:
                    QMessageBox.warning(self, "Error", "Failed to create overhaul.")
                    
        except Exception as e:
            logger.error(f"Error saving overhaul: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save overhaul: {str(e)}")

    def mark_overhaul_completed(self):
        """Mark the current overhaul as completed."""
        if not self.current_overhaul_data:
            return
            
        try:
            overhaul_id = self.current_overhaul_data.get('id')
            old_status = self.current_overhaul_data.get('status')
            
            # Get completion notes
            notes = self.notes_edit.toPlainText().strip()
            if not notes:
                QMessageBox.warning(self, "Error", "Please provide completion notes.")
                return
                
            # Update status to completed
            if Overhaul.update(overhaul_id, status='completed'):
                # Log status change
                OverhaulHistory.log_status_change(
                    overhaul_id,
                    old_status,
                    'completed',
                    "user",
                    notes
                )
                self.load_all_overhauls()
                QMessageBox.information(self, "Success", "Overhaul marked as completed.")
            else:
                QMessageBox.warning(self, "Error", "Failed to mark overhaul as completed.")
                
        except Exception as e:
            logger.error(f"Error completing overhaul: {e}")
            QMessageBox.critical(self, "Error", f"Failed to complete overhaul: {str(e)}")

    def get_status_color(self, status):
        """Get QColor for status string with updated overhaul status support."""
        colors = {
            'scheduled': Qt.blue,           # More than 2 years (>730 days)
            'warning': Qt.darkYellow,       # 1-2 years (365-730 days)
            'critical': Qt.red,             # Within 1 year (0-365 days)
            'overdue': Qt.darkRed,          # Past due date
            'in_progress': Qt.yellow,       # Currently being worked on
            'completed': Qt.green,          # Completed
            'cancelled': Qt.gray,           # Cancelled
            'discard': Qt.magenta           # Equipment ready for discard
        }
        return colors.get(status, Qt.black)

    def show_history(self, overhaul_id):
        """Show overhaul history dialog."""
        dialog = OverhaulHistoryDialog(overhaul_id, self)
        dialog.exec_()

    def show_bulk_create(self):
        """Show bulk create dialog."""
        dialog = BulkOverhaulDialog(self, 'create')
        if dialog.exec_() == QDialog.Accepted:
            self.load_all_overhauls()
            
    def show_bulk_update(self):
        """Show bulk update dialog."""
        dialog = BulkOverhaulDialog(self, 'update')
        if dialog.exec_() == QDialog.Accepted:
            self.load_all_overhauls()
            
    def show_bulk_delete(self):
        """Show bulk delete dialog."""
        dialog = BulkOverhaulDialog(self, 'delete')
        if dialog.exec_() == QDialog.Accepted:
            self.load_all_overhauls()

    # Filter methods removed - now handled by PaginatedTableWidget

    def export_overhauls_to_csv(self, overhaul_type):
        """Export overhauls to CSV file."""
        try:
            # Get file path from user
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Export Overhauls",
                f"{overhaul_type}_overhauls.csv",
                "CSV Files (*.csv)"
            )

            if not file_path:
                return

            # Get current table data from paginated table
            table = getattr(self, f"{overhaul_type.lower()}_table")
            all_data = table.get_all_data()

            with open(file_path, 'w', newline='') as csvfile:
                writer = csv.writer(csvfile)

                # Write header
                writer.writerow([
                    "BA Number", "Make & Type", "Meterage KM",
                    "Done Date", "Due Date", "Status"
                ])

                # Write data
                for row_data in all_data:
                    writer.writerow([
                        row_data.get('BA Number', ''),
                        row_data.get('Make & Type', ''),
                        row_data.get('Meterage KM', ''),
                        row_data.get('Done Date', ''),
                        row_data.get('Due Date', ''),
                        row_data.get('Status', '')
                    ])

            QMessageBox.information(self, "Success", "Overhauls exported successfully.")

        except Exception as e:
            logger.error(f"Error exporting overhauls: {e}")
            QMessageBox.critical(self, "Error", f"Failed to export overhauls: {str(e)}")

    def set_details_panel_read_only(self, read_only):
        """Set the details panel fields to read-only or editable."""
        self.detail_done_date_edit.setReadOnly(read_only)
        self.notes_edit.setReadOnly(read_only)
        self.save_btn.setEnabled(not read_only)
        self.edit_btn.setEnabled(read_only and self.current_overhaul_data is not None)
        self.delete_btn.setEnabled(read_only and self.current_overhaul_data is not None)

    def enable_edit_mode(self):
        """Enable editing for the details panel fields."""
        if self.current_overhaul_data:
            self.set_details_panel_read_only(False)
            self.save_btn.setEnabled(True)
            self.edit_btn.setEnabled(False)
            self.oh_comp_btn.setVisible(False) # Hide completion button during editing

    def delete_selected_overhaul(self):
        """Delete the currently selected overhaul."""
        if self.current_overhaul_data:
            overhaul_id = self.current_overhaul_data.get('id')
            reply = QMessageBox.question(self, 'Delete Overhaul', 
                                       f'Are you sure you want to delete overhaul ID {overhaul_id}?',
                                       QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                try:
                    if Overhaul.delete(overhaul_id):
                        # Log deletion in history (status 'deleted')
                        OverhaulHistory.log_status_change(overhaul_id, self.current_overhaul_data.get('status'), 'deleted', "user", "Deleted via UI")
                        QMessageBox.information(self, "Success", "Overhaul deleted successfully.")
                        self.load_all_overhauls()
                        self.clear_details_panel() # Clear panel after deletion
                    else:
                        QMessageBox.warning(self, "Error", "Failed to delete overhaul.")
                except Exception as e:
                    logger.error(f"Error deleting overhaul {overhaul_id}: {e}")
                    QMessageBox.critical(self, "Error", f"Failed to delete overhaul: {str(e)}")

    def clear_details_panel(self):
        """Clear the details panel and reset to default state for new entry."""
        self.current_overhaul_data = None
        self.details_panel.findChild(QLabel, "OH-I Overhaul Details").setText("Overhaul Details")
        self.detail_equipment_id_label.setText("")
        self.detail_meterage_km_label.setText("")
        self.detail_date_of_release_label.setText("")
        self.detail_done_date_edit.clear()
        self.detail_status_label.setText("No Overhaul Selected")
        self.detail_status_label.setStyleSheet("background-color: lightgray; padding: 5px; border-radius: 3px;")
        self.set_details_panel_read_only(True)
        self.oh_comp_btn.setVisible(False)
        self.edit_btn.setEnabled(False)
        self.save_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
        self.add_new_btn.setText("Add New") # Ensure Add New button is correctly labeled