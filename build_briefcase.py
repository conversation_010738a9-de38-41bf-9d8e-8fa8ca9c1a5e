#!/usr/bin/env python3
"""
PROJECT-ALPHA Briefcase Build Script
Complete setup and build process for the Equipment Inventory Management System
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

class BriefcaseBuilder:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.build_log = []
        
    def log(self, message, level="INFO"):
        """Log build messages."""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        self.build_log.append(log_entry)
        print(log_entry)
        
    def run_command(self, cmd, description, check=True):
        """Run a command and handle errors."""
        self.log(f"🔄 {description}...")
        try:
            if isinstance(cmd, str):
                result = subprocess.run(cmd, shell=True, check=check, capture_output=True, text=True, cwd=self.project_root)
            else:
                result = subprocess.run(cmd, check=check, capture_output=True, text=True, cwd=self.project_root)
            
            if result.stdout:
                self.log(f"Output: {result.stdout.strip()}", "DEBUG")
            if result.stderr and result.returncode == 0:
                self.log(f"Warnings: {result.stderr.strip()}", "WARN")
                
            self.log(f"✅ {description} completed successfully")
            return True, result.stdout
            
        except subprocess.CalledProcessError as e:
            self.log(f"❌ {description} failed:", "ERROR")
            self.log(f"Command: {cmd}", "ERROR")
            self.log(f"Return code: {e.returncode}", "ERROR")
            if e.stdout:
                self.log(f"Stdout: {e.stdout}", "ERROR")
            if e.stderr:
                self.log(f"Stderr: {e.stderr}", "ERROR")
            return False, e.stderr
            
    def check_python_version(self):
        """Check if Python version is compatible."""
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            self.log("❌ Python 3.8+ is required for Briefcase", "ERROR")
            return False
        self.log(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    
    def check_project_structure(self):
        """Check if project structure is correct."""
        self.log("🔍 Checking project structure...")
        
        required_files = [
            "main.py",
            "config.py", 
            "database.py",
            "models.py",
            "utils.py",
            "excel_importer.py",
            "requirements.txt",
            "pyproject.toml",
            "app.py"
        ]
        
        required_dirs = [
            "ui",
            "resources"
        ]
        
        missing_files = []
        missing_dirs = []
        
        for file in required_files:
            if not (self.project_root / file).exists():
                missing_files.append(file)
                
        for dir in required_dirs:
            if not (self.project_root / dir).is_dir():
                missing_dirs.append(dir)
        
        if missing_files:
            self.log(f"❌ Missing required files: {missing_files}", "ERROR")
            return False
            
        if missing_dirs:
            self.log(f"❌ Missing required directories: {missing_dirs}", "ERROR")
            return False
            
        self.log("✅ Project structure is valid")
        return True
    
    def check_app_icon(self):
        """Check for app icon and create basic one if missing."""
        self.log("🖼️ Checking for app icon...")
        
        icon_files = [
            "resources/app_icon.ico",
            "resources/app_icon.png", 
            "resources/app_icon.svg"
        ]
        
        icon_found = False
        for icon_file in icon_files:
            if (self.project_root / icon_file).exists():
                icon_found = True
                self.log(f"✅ Found app icon: {icon_file}")
                break
                
        if not icon_found:
            self.log("⚠️ No app icon found. Creating basic icon...", "WARN")
            # Create a basic icon using PIL if available
            try:
                from PIL import Image, ImageDraw, ImageFont
                
                # Create resources directory if it doesn't exist
                (self.project_root / "resources").mkdir(exist_ok=True)
                
                # Create a simple 256x256 icon
                img = Image.new('RGBA', (256, 256), (33, 150, 243, 255))  # Blue background
                draw = ImageDraw.Draw(img)
                
                # Draw some simple graphics
                # Draw a circle
                draw.ellipse([64, 64, 192, 192], fill=(255, 255, 255, 255), outline=(0, 0, 0, 255), width=4)
                
                # Draw text
                try:
                    font = ImageFont.truetype("arial.ttf", 32)
                except:
                    font = ImageFont.load_default()
                    
                text = "PA"
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                x = (256 - text_width) // 2
                y = (256 - text_height) // 2
                draw.text((x, y), text, fill=(0, 0, 0, 255), font=font)
                
                # Save as PNG and ICO
                png_path = self.project_root / "resources" / "app_icon.png"
                ico_path = self.project_root / "resources" / "app_icon.ico"
                
                img.save(png_path, "PNG")
                img.save(ico_path, "ICO", sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
                
                self.log(f"✅ Created basic app icon: {png_path} and {ico_path}")
                return True
                
            except ImportError:
                self.log("⚠️ PIL not available, using default icon", "WARN")
            except Exception as e:
                self.log(f"⚠️ Could not create icon: {e}", "WARN")
                
        return True
    
    def install_briefcase(self):
        """Install Briefcase and verify installation."""
        self.log("📦 Installing Briefcase...")
        
        success, output = self.run_command(
            [sys.executable, "-m", "pip", "install", "briefcase>=0.3.16"],
            "Installing Briefcase"
        )
        if not success:
            return False
            
        # Verify installation
        success, version_output = self.run_command(
            [sys.executable, "-m", "briefcase", "--version"],
            "Verifying Briefcase installation"
        )
        
        if success:
            self.log(f"✅ Briefcase installed: {version_output.strip()}")
        
        return success
    
    def create_briefcase_project(self):
        """Initialize the Briefcase project."""
        self.log("🏗️ Initializing Briefcase project...")
        
        # Check if this is already a Briefcase project
        if (self.project_root / "pyproject.toml").exists():
            self.log("✅ Briefcase project already initialized")
            return True
            
        self.log("❌ pyproject.toml not found", "ERROR")
        return False
    
    def build_application(self):
        """Build the application."""
        self.log("🔨 Building application...")
        
        success, output = self.run_command(
            [sys.executable, "-m", "briefcase", "build"],
            "Building application"
        )
        
        return success
    
    def package_application(self, platform="windows"):
        """Package the application for distribution."""
        self.log(f"📦 Packaging application for {platform}...")
        
        success, output = self.run_command(
            [sys.executable, "-m", "briefcase", "package", platform],
            f"Packaging for {platform}"
        )
        
        if success:
            self.log("✅ Application packaged successfully!")
            self.log("📁 Installer files are located in the 'dist' directory")
            
        return success
    
    def run_tests(self):
        """Run the application in development mode to test."""
        self.log("🧪 Testing application in development mode...")
        
        success, output = self.run_command(
            [sys.executable, "-m", "briefcase", "dev"],
            "Running application in dev mode",
            check=False  # Don't fail if user closes the app
        )
        
        return True  # Always return True for testing
    
    def build_all_platforms(self):
        """Build for all supported platforms."""
        platforms = ["windows", "linux", "macOS"]
        
        for platform in platforms:
            if platform == "macOS" and sys.platform != "darwin":
                self.log(f"⚠️ Skipping {platform} build (requires macOS)", "WARN")
                continue
                
            if platform == "linux" and sys.platform == "win32":
                self.log(f"⚠️ Skipping {platform} build (cross-compilation not recommended)", "WARN")
                continue
                
            self.package_application(platform)
    
    def cleanup_build_artifacts(self):
        """Clean up build artifacts."""
        self.log("🧹 Cleaning up build artifacts...")
        
        cleanup_dirs = ["build", ".briefcase"]
        
        for dir_name in cleanup_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                try:
                    shutil.rmtree(dir_path)
                    self.log(f"✅ Cleaned up {dir_name}")
                except Exception as e:
                    self.log(f"⚠️ Could not clean up {dir_name}: {e}", "WARN")
    
    def save_build_log(self):
        """Save build log to file."""
        log_file = self.project_root / "build.log"
        try:
            with open(log_file, 'w') as f:
                f.write("\n".join(self.build_log))
            self.log(f"✅ Build log saved to {log_file}")
        except Exception as e:
            self.log(f"⚠️ Could not save build log: {e}", "WARN")
    
    def full_build_process(self, skip_tests=False):
        """Run the complete build process."""
        self.log("🚀 PROJECT-ALPHA Briefcase Build Process")
        self.log("=" * 60)
        
        steps = [
            ("check_python_version", "Checking Python version"),
            ("check_project_structure", "Validating project structure"),
            ("check_app_icon", "Checking app icon"),
            ("install_briefcase", "Installing Briefcase"),
            ("create_briefcase_project", "Setting up Briefcase project"),
            ("build_application", "Building application"),
        ]
        
        if not skip_tests:
            steps.append(("run_tests", "Testing application"))
            
        steps.append(("package_application", "Packaging for current platform"))
        
        for step_method, step_description in steps:
            self.log(f"\n📋 Step: {step_description}")
            method = getattr(self, step_method)
            
            if step_method == "package_application":
                result = method()  # Use default platform
            else:
                result = method()
                
            if not result:
                self.log(f"❌ Build process failed at: {step_description}", "ERROR")
                self.save_build_log()
                return False
                
        self.log("\n🎉 Build process completed successfully!")
        self.log("\n📁 Your installer is ready in the 'dist' directory")
        
        # Show final summary
        self.show_build_summary()
        self.save_build_log()
        return True
    
    def show_build_summary(self):
        """Show build summary."""
        self.log("\n" + "=" * 60)
        self.log("📊 BUILD SUMMARY")
        self.log("=" * 60)
        
        dist_dir = self.project_root / "dist"
        if dist_dir.exists():
            self.log("📁 Generated files:")
            for file in dist_dir.rglob("*"):
                if file.is_file():
                    size_mb = file.stat().st_size / (1024 * 1024)
                    self.log(f"   📄 {file.relative_to(self.project_root)} ({size_mb:.1f} MB)")
        
        self.log("\n🎯 Next steps:")
        self.log("   1. Test the installer on a clean system")
        self.log("   2. Distribute to target machines")
        self.log("   3. Document installation procedures")

def main():
    """Main build script entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="PROJECT-ALPHA Briefcase Build Script")
    parser.add_argument("--skip-tests", action="store_true", help="Skip running tests")
    parser.add_argument("--clean", action="store_true", help="Clean build artifacts before building")
    parser.add_argument("--all-platforms", action="store_true", help="Build for all platforms")
    
    args = parser.parse_args()
    
    builder = BriefcaseBuilder()
    
    try:
        if args.clean:
            builder.cleanup_build_artifacts()
        
        if args.all_platforms:
            if builder.full_build_process(skip_tests=args.skip_tests):
                builder.build_all_platforms()
        else:
            builder.full_build_process(skip_tests=args.skip_tests)
            
    except KeyboardInterrupt:
        builder.log("\n⚠️ Build interrupted by user")
        builder.save_build_log()
        return 1
    except Exception as e:
        builder.log(f"\n❌ Build failed with exception: {e}", "ERROR")
        builder.save_build_log()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 