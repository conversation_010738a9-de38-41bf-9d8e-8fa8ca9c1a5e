#!/usr/bin/env python3
"""
Memory and Resource Manager for PROJECT-ALPHA
Optimizes for low-resource military systems with proper memory management,
resource cleanup, and adaptive processing strategies.
"""

import os
import sys
import gc
import logging
import threading
import time
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from contextlib import contextmanager
import weakref

logger = logging.getLogger('memory_resource_manager')

class MemoryMonitor:
    """Monitors system memory usage and provides alerts."""
    
    def __init__(self, warning_threshold_mb: int = 512, critical_threshold_mb: int = 256):
        self.warning_threshold_mb = warning_threshold_mb
        self.critical_threshold_mb = critical_threshold_mb
        self.monitoring_active = False
        self.monitor_thread = None
        self.callbacks = {
            'warning': [],
            'critical': [],
            'normal': []
        }
        
        # Try to import psutil for accurate memory monitoring
        try:
            import psutil
            self.psutil = psutil
            self.has_psutil = True
        except ImportError:
            self.psutil = None
            self.has_psutil = False
            logger.warning("psutil not available - using fallback memory monitoring")
    
    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage information."""
        if self.has_psutil:
            try:
                memory = self.psutil.virtual_memory()
                return {
                    'total_mb': memory.total / (1024 * 1024),
                    'available_mb': memory.available / (1024 * 1024),
                    'used_mb': memory.used / (1024 * 1024),
                    'percent': memory.percent
                }
            except Exception as e:
                logger.warning(f"psutil memory check failed: {e}")
        
        # Fallback memory estimation
        return self._estimate_memory_usage()
    
    def _estimate_memory_usage(self) -> Dict[str, float]:
        """Estimate memory usage without psutil."""
        try:
            # Try Windows-specific memory check
            if sys.platform == 'win32':
                import ctypes
                kernel32 = ctypes.windll.kernel32
                c_ulong = ctypes.c_ulong
                
                class MEMORYSTATUSEX(ctypes.Structure):
                    _fields_ = [
                        ('dwLength', c_ulong),
                        ('dwMemoryLoad', c_ulong),
                        ('ullTotalPhys', ctypes.c_ulonglong),
                        ('ullAvailPhys', ctypes.c_ulonglong),
                        ('ullTotalPageFile', ctypes.c_ulonglong),
                        ('ullAvailPageFile', ctypes.c_ulonglong),
                        ('ullTotalVirtual', ctypes.c_ulonglong),
                        ('ullAvailVirtual', ctypes.c_ulonglong),
                        ('sullAvailExtendedVirtual', ctypes.c_ulonglong),
                    ]
                
                memory_status = MEMORYSTATUSEX()
                memory_status.dwLength = ctypes.sizeof(MEMORYSTATUSEX)
                kernel32.GlobalMemoryStatusEx(ctypes.byref(memory_status))
                
                total_mb = memory_status.ullTotalPhys / (1024 * 1024)
                available_mb = memory_status.ullAvailPhys / (1024 * 1024)
                used_mb = total_mb - available_mb
                percent = (used_mb / total_mb) * 100
                
                return {
                    'total_mb': total_mb,
                    'available_mb': available_mb,
                    'used_mb': used_mb,
                    'percent': percent
                }
        except Exception as e:
            logger.debug(f"Windows memory check failed: {e}")
        
        # Conservative fallback estimates
        return {
            'total_mb': 4096.0,  # Assume 4GB
            'available_mb': 1024.0,  # Assume 1GB available
            'used_mb': 3072.0,
            'percent': 75.0
        }
    
    def register_callback(self, level: str, callback: Callable[[Dict[str, float]], None]):
        """Register callback for memory level changes."""
        if level in self.callbacks:
            self.callbacks[level].append(callback)
    
    def start_monitoring(self, interval_seconds: int = 5):
        """Start continuous memory monitoring."""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval_seconds,))
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        logger.info(f"Started memory monitoring (interval: {interval_seconds}s)")
    
    def stop_monitoring(self):
        """Stop memory monitoring."""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        logger.info("Stopped memory monitoring")
    
    def _monitor_loop(self, interval_seconds: int):
        """Memory monitoring loop."""
        last_level = 'normal'
        
        while self.monitoring_active:
            try:
                memory_info = self.get_memory_usage()
                available_mb = memory_info['available_mb']
                
                # Determine current level
                if available_mb <= self.critical_threshold_mb:
                    current_level = 'critical'
                elif available_mb <= self.warning_threshold_mb:
                    current_level = 'warning'
                else:
                    current_level = 'normal'
                
                # Trigger callbacks if level changed
                if current_level != last_level:
                    logger.info(f"Memory level changed: {last_level} -> {current_level} (available: {available_mb:.1f}MB)")
                    for callback in self.callbacks[current_level]:
                        try:
                            callback(memory_info)
                        except Exception as e:
                            logger.error(f"Memory callback error: {e}")
                    last_level = current_level
                
                time.sleep(interval_seconds)
                
            except Exception as e:
                logger.error(f"Memory monitoring error: {e}")
                time.sleep(interval_seconds)

class ResourceManager:
    """Manages system resources with cleanup and optimization."""
    
    def __init__(self):
        self.temp_files = set()
        self.temp_dirs = set()
        self.open_files = weakref.WeakSet()
        self.memory_monitor = MemoryMonitor()
        self.cleanup_callbacks = []
        
        # Register memory callbacks
        self.memory_monitor.register_callback('warning', self._handle_memory_warning)
        self.memory_monitor.register_callback('critical', self._handle_memory_critical)
        
        # Start monitoring
        self.memory_monitor.start_monitoring()
    
    def create_temp_file(self, suffix: str = '', prefix: str = 'PROJECT_ALPHA_', dir: Optional[str] = None) -> str:
        """Create temporary file with automatic cleanup tracking."""
        try:
            fd, temp_path = tempfile.mkstemp(suffix=suffix, prefix=prefix, dir=dir)
            os.close(fd)  # Close file descriptor
            
            self.temp_files.add(temp_path)
            logger.debug(f"Created temp file: {temp_path}")
            return temp_path
            
        except Exception as e:
            logger.error(f"Failed to create temp file: {e}")
            raise
    
    def create_temp_dir(self, suffix: str = '', prefix: str = 'PROJECT_ALPHA_', dir: Optional[str] = None) -> str:
        """Create temporary directory with automatic cleanup tracking."""
        try:
            temp_dir = tempfile.mkdtemp(suffix=suffix, prefix=prefix, dir=dir)
            self.temp_dirs.add(temp_dir)
            logger.debug(f"Created temp dir: {temp_dir}")
            return temp_dir
            
        except Exception as e:
            logger.error(f"Failed to create temp dir: {e}")
            raise
    
    def register_file(self, file_obj) -> None:
        """Register file object for automatic cleanup."""
        self.open_files.add(file_obj)
    
    def register_cleanup_callback(self, callback: Callable[[], None]):
        """Register callback to be called during cleanup."""
        self.cleanup_callbacks.append(callback)
    
    def force_garbage_collection(self) -> Dict[str, int]:
        """Force garbage collection and return statistics."""
        logger.info("Forcing garbage collection...")
        
        # Collect statistics before
        before_objects = len(gc.get_objects())
        
        # Force collection
        collected = [gc.collect() for _ in range(3)]  # Multiple passes
        
        # Statistics after
        after_objects = len(gc.get_objects())
        freed_objects = before_objects - after_objects
        
        stats = {
            'objects_before': before_objects,
            'objects_after': after_objects,
            'objects_freed': freed_objects,
            'collection_passes': collected
        }
        
        logger.info(f"GC completed: freed {freed_objects} objects")
        return stats
    
    def cleanup_temp_files(self):
        """Clean up all tracked temporary files."""
        cleaned_files = 0
        for temp_file in list(self.temp_files):
            try:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
                    cleaned_files += 1
                self.temp_files.discard(temp_file)
            except Exception as e:
                logger.warning(f"Could not remove temp file {temp_file}: {e}")
        
        logger.info(f"Cleaned up {cleaned_files} temporary files")
    
    def cleanup_temp_dirs(self):
        """Clean up all tracked temporary directories."""
        cleaned_dirs = 0
        for temp_dir in list(self.temp_dirs):
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                    cleaned_dirs += 1
                self.temp_dirs.discard(temp_dir)
            except Exception as e:
                logger.warning(f"Could not remove temp dir {temp_dir}: {e}")
        
        logger.info(f"Cleaned up {cleaned_dirs} temporary directories")
    
    def close_open_files(self):
        """Close all tracked open files."""
        closed_files = 0
        for file_obj in list(self.open_files):
            try:
                if hasattr(file_obj, 'close') and not file_obj.closed:
                    file_obj.close()
                    closed_files += 1
            except Exception as e:
                logger.warning(f"Could not close file: {e}")
        
        logger.info(f"Closed {closed_files} open files")
    
    def full_cleanup(self):
        """Perform comprehensive resource cleanup."""
        logger.info("Starting full resource cleanup...")
        
        # Call registered cleanup callbacks
        for callback in self.cleanup_callbacks:
            try:
                callback()
            except Exception as e:
                logger.error(f"Cleanup callback error: {e}")
        
        # Close files
        self.close_open_files()
        
        # Clean temp resources
        self.cleanup_temp_files()
        self.cleanup_temp_dirs()
        
        # Force garbage collection
        self.force_garbage_collection()
        
        logger.info("Full resource cleanup completed")
    
    def _handle_memory_warning(self, memory_info: Dict[str, float]):
        """Handle memory warning level."""
        logger.warning(f"Memory warning: {memory_info['available_mb']:.1f}MB available")
        
        # Light cleanup
        self.force_garbage_collection()
        self.close_open_files()
    
    def _handle_memory_critical(self, memory_info: Dict[str, float]):
        """Handle critical memory level."""
        logger.critical(f"Critical memory level: {memory_info['available_mb']:.1f}MB available")
        
        # Aggressive cleanup
        self.full_cleanup()
    
    def get_resource_status(self) -> Dict[str, Any]:
        """Get current resource usage status."""
        memory_info = self.memory_monitor.get_memory_usage()
        
        return {
            'memory': memory_info,
            'temp_files': len(self.temp_files),
            'temp_dirs': len(self.temp_dirs),
            'open_files': len(self.open_files),
            'gc_objects': len(gc.get_objects())
        }
    
    def shutdown(self):
        """Shutdown resource manager and cleanup."""
        logger.info("Shutting down resource manager...")
        self.memory_monitor.stop_monitoring()
        self.full_cleanup()

class LowResourceProcessor:
    """Processor optimized for low-resource environments."""
    
    def __init__(self, resource_manager: ResourceManager):
        self.resource_manager = resource_manager
        self.chunk_size = 500  # Conservative chunk size
        self.max_memory_mb = 256  # Conservative memory limit
    
    def process_large_dataset(self, data_source, processor_func: Callable, **kwargs) -> Any:
        """Process large dataset with memory-conscious chunking."""
        logger.info("Starting low-resource dataset processing...")
        
        results = []
        chunk_count = 0
        
        try:
            # Process in chunks
            for chunk in self._chunk_data(data_source):
                chunk_count += 1
                logger.debug(f"Processing chunk {chunk_count} (size: {len(chunk)})")
                
                # Check memory before processing
                memory_info = self.resource_manager.memory_monitor.get_memory_usage()
                if memory_info['available_mb'] < self.max_memory_mb:
                    logger.warning("Low memory detected, forcing cleanup...")
                    self.resource_manager.force_garbage_collection()
                
                # Process chunk
                chunk_result = processor_func(chunk, **kwargs)
                results.append(chunk_result)
                
                # Cleanup after each chunk
                del chunk
                gc.collect()
            
            logger.info(f"Completed processing {chunk_count} chunks")
            return results
            
        except Exception as e:
            logger.error(f"Low-resource processing failed: {e}")
            raise
    
    def _chunk_data(self, data_source):
        """Generate data chunks for processing."""
        if hasattr(data_source, '__len__'):
            # List-like data
            for i in range(0, len(data_source), self.chunk_size):
                yield data_source[i:i + self.chunk_size]
        elif hasattr(data_source, '__iter__'):
            # Iterator-like data
            chunk = []
            for item in data_source:
                chunk.append(item)
                if len(chunk) >= self.chunk_size:
                    yield chunk
                    chunk = []
            if chunk:
                yield chunk
        else:
            # Single item
            yield [data_source]

# Context manager for resource management
@contextmanager
def managed_resources():
    """Context manager for automatic resource management."""
    resource_manager = ResourceManager()
    try:
        yield resource_manager
    finally:
        resource_manager.shutdown()

# Global resource manager instance
_global_resource_manager = None

def get_resource_manager() -> ResourceManager:
    """Get global resource manager instance."""
    global _global_resource_manager
    if _global_resource_manager is None:
        _global_resource_manager = ResourceManager()
    return _global_resource_manager

def cleanup_resources():
    """Cleanup global resources."""
    global _global_resource_manager
    if _global_resource_manager:
        _global_resource_manager.full_cleanup()

def get_memory_status() -> Dict[str, Any]:
    """Get current memory status."""
    return get_resource_manager().get_resource_status()

def force_memory_cleanup():
    """Force memory cleanup."""
    get_resource_manager().force_garbage_collection()

if __name__ == "__main__":
    # Test memory and resource management
    print("=" * 60)
    print("PROJECT-ALPHA MEMORY & RESOURCE MANAGEMENT TEST")
    print("=" * 60)
    
    with managed_resources() as rm:
        # Get initial status
        status = rm.get_resource_status()
        print(f"Initial Memory: {status['memory']['available_mb']:.1f}MB available")
        print(f"GC Objects: {status['gc_objects']}")
        
        # Create some temporary resources
        temp_file = rm.create_temp_file(suffix='.test')
        temp_dir = rm.create_temp_dir(suffix='_test')
        
        print(f"Created temp file: {temp_file}")
        print(f"Created temp dir: {temp_dir}")
        
        # Force cleanup test
        print("\nTesting cleanup...")
        rm.force_garbage_collection()
        
        # Final status
        final_status = rm.get_resource_status()
        print(f"Final Memory: {final_status['memory']['available_mb']:.1f}MB available")
        print(f"Temp Files: {final_status['temp_files']}")
        print(f"Temp Dirs: {final_status['temp_dirs']}")
    
    print("\nResource management test completed.")
