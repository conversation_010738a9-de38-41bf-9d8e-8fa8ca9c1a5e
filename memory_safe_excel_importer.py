"""
Memory-Safe Excel Importer for PROJECT-ALPHA
Prevents memory exhaustion on 4GB military systems by processing large files in chunks
"""

import os
import logging
import pandas as pd
import gc
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Iterator

logger = logging.getLogger('memory_safe_excel')

class MemorySafeExcelImporter:
    """Memory-safe Excel importer that processes large files in chunks."""
    
    def __init__(self, file_path: str, chunk_size: int = 1000, max_memory_mb: int = 512):
        """
        Initialize the memory-safe Excel importer.
        
        Args:
            file_path: Path to the Excel file
            chunk_size: Number of rows to process at once
            max_memory_mb: Maximum memory usage in MB before forcing cleanup
        """
        self.file_path = file_path
        self.chunk_size = chunk_size
        self.max_memory_mb = max_memory_mb
        self.stats = {
            'equipment': 0,
            'fluids': 0,
            'maintenance': 0,
            'repairs': 0,
            'overhauls': 0,
            'tyre_maintenance': 0,
            'discard_criteria': 0,
            'batteries': 0,
            'medium_resets': 0,
            'conditioning': 0,
            'demand_forecast': 0,
            'tyre_forecast': 0,
            'battery_forecast': 0,
            'equipment_forecast': 0,
            'overhaul_forecast': 0,
            'conditioning_forecast': 0,
            'errors': [],
            'warnings': []
        }
    
    def check_file_size(self) -> bool:
        """Check if file size is within acceptable limits."""
        try:
            file_size_mb = os.path.getsize(self.file_path) / (1024 * 1024)
            logger.info(f"Excel file size: {file_size_mb:.1f}MB")
            
            if file_size_mb > 500:  # Hard limit for military systems
                self.stats['errors'].append(f"File too large: {file_size_mb:.1f}MB (max: 500MB)")
                return False
            
            if file_size_mb > 100:  # Warning threshold
                self.stats['warnings'].append(f"Large file detected: {file_size_mb:.1f}MB")
                logger.warning(f"Processing large Excel file: {file_size_mb:.1f}MB")
            
            return True
            
        except Exception as e:
            self.stats['errors'].append(f"Cannot check file size: {e}")
            return False
    
    def get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB."""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / (1024 * 1024)
        except ImportError:
            # Fallback if psutil not available
            return 0.0
    
    def force_memory_cleanup(self):
        """Force garbage collection and memory cleanup."""
        try:
            # Clean up pandas memory
            gc.collect()
            
            # Clean up matplotlib if imported
            try:
                import matplotlib.pyplot as plt
                plt.close('all')
            except ImportError:
                pass
            
            # Force another garbage collection
            gc.collect()
            
            logger.debug("Forced memory cleanup completed")
            
        except Exception as e:
            logger.warning(f"Memory cleanup failed: {e}")
    
    def read_excel_in_chunks(self, sheet_name: str) -> Iterator[pd.DataFrame]:
        """Read Excel sheet in chunks to prevent memory exhaustion."""
        try:
            # First, get the total number of rows
            temp_df = pd.read_excel(self.file_path, sheet_name=sheet_name, nrows=0)
            
            # Read file info to estimate total rows
            with pd.ExcelFile(self.file_path) as excel_file:
                # Try to read a small sample to estimate size
                sample_df = pd.read_excel(excel_file, sheet_name=sheet_name, nrows=10)
                
                # Read in chunks
                chunk_start = 0
                while True:
                    try:
                        chunk_df = pd.read_excel(
                            excel_file, 
                            sheet_name=sheet_name,
                            skiprows=chunk_start,
                            nrows=self.chunk_size,
                            header=0 if chunk_start == 0 else None
                        )
                        
                        if chunk_df.empty:
                            break
                        
                        # If not the first chunk, use the column names from the first chunk
                        if chunk_start > 0 and hasattr(self, '_column_names'):
                            chunk_df.columns = self._column_names
                        else:
                            self._column_names = chunk_df.columns.tolist()
                        
                        yield chunk_df
                        
                        # Check memory usage and cleanup if needed
                        memory_usage = self.get_memory_usage_mb()
                        if memory_usage > self.max_memory_mb:
                            logger.warning(f"High memory usage: {memory_usage:.1f}MB, forcing cleanup")
                            self.force_memory_cleanup()
                        
                        chunk_start += self.chunk_size
                        
                    except Exception as e:
                        logger.error(f"Error reading chunk starting at row {chunk_start}: {e}")
                        break
                        
        except Exception as e:
            logger.error(f"Error setting up chunked reading for sheet {sheet_name}: {e}")
            self.stats['errors'].append(f"Cannot read sheet {sheet_name}: {e}")
    
    def process_equipment_chunk(self, chunk_df: pd.DataFrame) -> int:
        """Process a chunk of equipment data."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            
            # Create a temporary importer for this chunk
            temp_importer = RobustExcelImporter()
            if not temp_importer.initialize_staging():
                return 0
            
            # Process the chunk
            count = temp_importer._extract_and_save_equipment(chunk_df, "equipment_chunk")
            
            # Clean up the temporary importer
            del temp_importer
            gc.collect()
            
            return count
            
        except Exception as e:
            logger.error(f"Error processing equipment chunk: {e}")
            self.stats['errors'].append(f"Equipment chunk processing failed: {e}")
            return 0
    
    def process_fluids_chunk(self, chunk_df: pd.DataFrame) -> int:
        """Process a chunk of fluids data."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            
            # Create a temporary importer for this chunk
            temp_importer = RobustExcelImporter()
            if not temp_importer.initialize_staging():
                return 0
            
            # Process the chunk
            count = temp_importer._extract_and_save_fluids(chunk_df, "fluids_chunk")
            
            # Clean up the temporary importer
            del temp_importer
            gc.collect()
            
            return count
            
        except Exception as e:
            logger.error(f"Error processing fluids chunk: {e}")
            self.stats['errors'].append(f"Fluids chunk processing failed: {e}")
            return 0
    
    def detect_sheet_type(self, df: pd.DataFrame) -> str:
        """Detect the type of data in a sheet based on column names."""
        if df.empty:
            return 'unknown'

        columns_lower = [str(col).lower() for col in df.columns]
        columns_joined = ' '.join(columns_lower)

        # Enhanced detection with more comprehensive keywords

        # Equipment detection - primary data type
        equipment_keywords = ['make', 'type', 'serial', 'ba_number', 'ba number', 'vintage', 'meterage', 'ser no', 'make & type']
        if any(keyword in columns_joined for keyword in equipment_keywords):
            return 'equipment'

        # Overhaul detection - high priority
        overhaul_keywords = ['overhaul', 'oh-i', 'oh-ii', 'oh i', 'oh ii', 'overhaul_i', 'overhaul_ii']
        if any(keyword in columns_joined for keyword in overhaul_keywords):
            return 'overhauls'

        # Maintenance detection
        maintenance_keywords = ['maintenance', 'service', 'due_date', 'last_service', 'tm-i', 'tm-ii', 'technical maintenance']
        if any(keyword in columns_joined for keyword in maintenance_keywords):
            return 'maintenance'

        # Fluids detection
        fluids_keywords = ['fluid', 'oil', 'lubricant', 'capacity', 'grade', 'engine oil', 'transmission', 'hydraulic', 'coolant', 'grease']
        if any(keyword in columns_joined for keyword in fluids_keywords):
            return 'fluids'

        # Battery detection
        battery_keywords = ['battery', 'voltage', 'ampere', 'amp', 'volt', 'cell']
        if any(keyword in columns_joined for keyword in battery_keywords):
            return 'batteries'

        # Tyre detection
        tyre_keywords = ['tyre', 'tire', 'rotation', 'wheel', 'rim']
        if any(keyword in columns_joined for keyword in tyre_keywords):
            return 'tyre_maintenance'

        # Repair detection
        repair_keywords = ['repair', 'defect', 'fault', 'breakdown', 'rectification']
        if any(keyword in columns_joined for keyword in repair_keywords):
            return 'repairs'

        # Discard criteria detection
        discard_keywords = ['discard', 'criteria', 'disposal', 'scrap', 'condemn']
        if any(keyword in columns_joined for keyword in discard_keywords):
            return 'discard_criteria'

        # Conditioning detection
        conditioning_keywords = ['conditioning', 'condition', 'inspection', 'check']
        if any(keyword in columns_joined for keyword in conditioning_keywords):
            return 'conditioning'

        # Medium reset detection
        medium_reset_keywords = ['medium', 'reset', 'medium reset']
        if any(keyword in columns_joined for keyword in medium_reset_keywords):
            return 'medium_resets'

        # Forecast detection
        forecast_keywords = ['forecast', 'demand', 'requirement', 'projection']
        if any(keyword in columns_joined for keyword in forecast_keywords):
            return 'demand_forecast'

        return 'equipment'  # Default to equipment for unknown types
    
    def process_sheet_in_chunks(self, sheet_name: str) -> int:
        """Process an entire sheet in chunks."""
        total_processed = 0
        sheet_type = None
        
        try:
            logger.info(f"Processing sheet '{sheet_name}' in chunks of {self.chunk_size} rows")
            
            for chunk_num, chunk_df in enumerate(self.read_excel_in_chunks(sheet_name)):
                logger.debug(f"Processing chunk {chunk_num + 1} of sheet '{sheet_name}' ({len(chunk_df)} rows)")
                
                # Detect sheet type from first chunk
                if sheet_type is None:
                    sheet_type = self.detect_sheet_type(chunk_df)
                    logger.info(f"Detected sheet type: {sheet_type}")
                
                # Process chunk based on detected type
                chunk_count = 0
                if sheet_type == 'equipment':
                    chunk_count = self.process_equipment_chunk(chunk_df)
                elif sheet_type == 'fluids':
                    chunk_count = self.process_fluids_chunk(chunk_df)
                elif sheet_type == 'maintenance':
                    chunk_count = self.process_maintenance_chunk(chunk_df)
                elif sheet_type == 'overhauls':
                    chunk_count = self.process_overhauls_chunk(chunk_df)
                elif sheet_type == 'batteries':
                    chunk_count = self.process_batteries_chunk(chunk_df)
                elif sheet_type == 'tyre_maintenance':
                    chunk_count = self.process_tyre_maintenance_chunk(chunk_df)
                elif sheet_type == 'repairs':
                    chunk_count = self.process_repairs_chunk(chunk_df)
                elif sheet_type == 'discard_criteria':
                    chunk_count = self.process_discard_criteria_chunk(chunk_df)
                elif sheet_type == 'conditioning':
                    chunk_count = self.process_conditioning_chunk(chunk_df)
                elif sheet_type == 'medium_resets':
                    chunk_count = self.process_medium_resets_chunk(chunk_df)
                elif sheet_type == 'demand_forecast':
                    chunk_count = self.process_demand_forecast_chunk(chunk_df)
                else:
                    # For unknown types, try to process as equipment
                    logger.warning(f"Unknown sheet type '{sheet_type}', attempting to process as equipment")
                    chunk_count = self.process_equipment_chunk(chunk_df)
                
                total_processed += chunk_count
                
                # Update stats
                if sheet_type in self.stats:
                    self.stats[sheet_type] += chunk_count
                
                # Force cleanup after each chunk
                del chunk_df
                gc.collect()
                
                logger.debug(f"Chunk {chunk_num + 1} processed: {chunk_count} records")
            
            logger.info(f"Sheet '{sheet_name}' processing completed: {total_processed} records")
            return total_processed
            
        except Exception as e:
            logger.error(f"Error processing sheet '{sheet_name}': {e}")
            self.stats['errors'].append(f"Sheet '{sheet_name}' processing failed: {e}")
            return total_processed
    
    def import_all_data(self) -> Dict:
        """Import all data from the Excel file using memory-safe chunked processing."""
        try:
            # Check file size first
            if not self.check_file_size():
                return self.stats
            
            logger.info(f"Starting memory-safe import of {self.file_path}")
            
            # Get list of sheets
            with pd.ExcelFile(self.file_path) as excel_file:
                sheet_names = excel_file.sheet_names
                logger.info(f"Found {len(sheet_names)} sheets: {sheet_names}")
            
            # Process each sheet
            for sheet_name in sheet_names:
                try:
                    logger.info(f"Processing sheet: {sheet_name}")
                    self.process_sheet_in_chunks(sheet_name)
                    
                    # Force cleanup between sheets
                    self.force_memory_cleanup()
                    
                except Exception as e:
                    logger.error(f"Error processing sheet '{sheet_name}': {e}")
                    self.stats['errors'].append(f"Sheet '{sheet_name}' failed: {e}")
            
            # Final cleanup
            self.force_memory_cleanup()
            
            logger.info(f"Memory-safe Excel import completed. Final stats: {self.stats}")

            # Trigger post-import overhaul status recalculation
            try:
                logger.info("Starting post-import overhaul status recalculation...")
                import overhaul_service
                updated_count = overhaul_service.update_overhaul_statuses()
                logger.info(f"Post-import status recalculation completed: {updated_count} overhaul statuses updated")
                self.stats['overhaul_statuses_updated'] = updated_count
            except Exception as e:
                logger.error(f"Error during post-import overhaul status recalculation: {e}")
                self.stats['overhaul_status_update_error'] = str(e)

            return self.stats
            
        except Exception as e:
            logger.error(f"Memory-safe Excel import failed: {e}")
            self.stats['errors'].append(f"Import failed: {e}")
            return self.stats

    def process_equipment_chunk(self, chunk_df: pd.DataFrame) -> int:
        """Process equipment data chunk using RobustExcelImporter with BA Number update support."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            temp_importer = RobustExcelImporter()
            if not temp_importer.initialize_staging():
                logger.error("Failed to initialize staging for equipment chunk")
                return 0

            # The updated _extract_and_save_equipment now returns total processed (created + updated)
            count = temp_importer._extract_and_save_equipment(chunk_df, "equipment_chunk")

            # Log BA Number updates if any occurred
            logger.info(f"Equipment chunk processed: {count} total records (includes updates)")

            del temp_importer
            gc.collect()
            return count
        except Exception as e:
            logger.error(f"Error processing equipment chunk: {e}")
            self.stats['errors'].append(f"Equipment chunk error: {e}")
            return 0

    def process_fluids_chunk(self, chunk_df: pd.DataFrame) -> int:
        """Process fluids data chunk using RobustExcelImporter."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            temp_importer = RobustExcelImporter()
            if not temp_importer.initialize_staging():
                logger.error("Failed to initialize staging for fluids chunk")
                return 0
            count = temp_importer._extract_and_save_fluids(chunk_df, "fluids_chunk")
            del temp_importer
            gc.collect()
            return count
        except Exception as e:
            logger.error(f"Error processing fluids chunk: {e}")
            self.stats['errors'].append(f"Fluids chunk error: {e}")
            return 0

    def process_maintenance_chunk(self, chunk_df: pd.DataFrame) -> int:
        """Process maintenance data chunk using RobustExcelImporter."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            temp_importer = RobustExcelImporter()
            if not temp_importer.initialize_staging():
                logger.error("Failed to initialize staging for maintenance chunk")
                return 0
            count = temp_importer._extract_and_save_maintenance(chunk_df, "maintenance_chunk")
            del temp_importer
            gc.collect()
            return count
        except Exception as e:
            logger.error(f"Error processing maintenance chunk: {e}")
            self.stats['errors'].append(f"Maintenance chunk error: {e}")
            return 0

    def process_overhauls_chunk(self, chunk_df: pd.DataFrame) -> int:
        """Process overhauls data chunk using RobustExcelImporter."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            temp_importer = RobustExcelImporter()
            if not temp_importer.initialize_staging():
                logger.error("Failed to initialize staging for overhauls chunk")
                return 0
            count = temp_importer._extract_and_save_overhauls(chunk_df, "overhauls_chunk")
            del temp_importer
            gc.collect()
            return count
        except Exception as e:
            logger.error(f"Error processing overhauls chunk: {e}")
            self.stats['errors'].append(f"Overhauls chunk error: {e}")
            return 0

    def process_batteries_chunk(self, chunk_df: pd.DataFrame) -> int:
        """Process batteries data chunk using RobustExcelImporter."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            temp_importer = RobustExcelImporter()
            if not temp_importer.initialize_staging():
                logger.error("Failed to initialize staging for batteries chunk")
                return 0
            count = temp_importer._extract_and_save_batteries(chunk_df, "batteries_chunk")
            del temp_importer
            gc.collect()
            return count
        except Exception as e:
            logger.error(f"Error processing batteries chunk: {e}")
            self.stats['errors'].append(f"Batteries chunk error: {e}")
            return 0

    def process_tyre_maintenance_chunk(self, chunk_df: pd.DataFrame) -> int:
        """Process tyre maintenance data chunk using RobustExcelImporter."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            temp_importer = RobustExcelImporter()
            if not temp_importer.initialize_staging():
                logger.error("Failed to initialize staging for tyre maintenance chunk")
                return 0
            count = temp_importer._extract_and_save_conditioning(chunk_df, "tyres_chunk")
            del temp_importer
            gc.collect()
            return count
        except Exception as e:
            logger.error(f"Error processing tyre maintenance chunk: {e}")
            self.stats['errors'].append(f"Tyre maintenance chunk error: {e}")
            return 0

    def process_repairs_chunk(self, chunk_df: pd.DataFrame) -> int:
        """Process repairs data chunk using RobustExcelImporter."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            temp_importer = RobustExcelImporter()
            if not temp_importer.initialize_staging():
                logger.error("Failed to initialize staging for repairs chunk")
                return 0
            count = temp_importer._extract_and_save_repairs(chunk_df, "repairs_chunk")
            del temp_importer
            gc.collect()
            return count
        except Exception as e:
            logger.error(f"Error processing repairs chunk: {e}")
            self.stats['errors'].append(f"Repairs chunk error: {e}")
            return 0

    def process_discard_criteria_chunk(self, chunk_df: pd.DataFrame) -> int:
        """Process discard criteria data chunk using RobustExcelImporter."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            temp_importer = RobustExcelImporter()
            if not temp_importer.initialize_staging():
                logger.error("Failed to initialize staging for discard criteria chunk")
                return 0
            count = temp_importer._extract_and_save_discard_criteria(chunk_df, "discard_chunk")
            del temp_importer
            gc.collect()
            return count
        except Exception as e:
            logger.error(f"Error processing discard criteria chunk: {e}")
            self.stats['errors'].append(f"Discard criteria chunk error: {e}")
            return 0

    def process_conditioning_chunk(self, chunk_df: pd.DataFrame) -> int:
        """Process conditioning data chunk using RobustExcelImporter."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            temp_importer = RobustExcelImporter()
            if not temp_importer.initialize_staging():
                logger.error("Failed to initialize staging for conditioning chunk")
                return 0
            count = temp_importer._extract_and_save_conditioning(chunk_df, "conditioning_chunk")
            del temp_importer
            gc.collect()
            return count
        except Exception as e:
            logger.error(f"Error processing conditioning chunk: {e}")
            self.stats['errors'].append(f"Conditioning chunk error: {e}")
            return 0

    def process_medium_resets_chunk(self, chunk_df: pd.DataFrame) -> int:
        """Process medium resets data chunk using RobustExcelImporter."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            temp_importer = RobustExcelImporter()
            if not temp_importer.initialize_staging():
                logger.error("Failed to initialize staging for medium resets chunk")
                return 0
            # Note: Using equipment processing as fallback for medium resets
            count = temp_importer._extract_and_save_equipment(chunk_df, "medium_resets_chunk")
            del temp_importer
            gc.collect()
            return count
        except Exception as e:
            logger.error(f"Error processing medium resets chunk: {e}")
            self.stats['errors'].append(f"Medium resets chunk error: {e}")
            return 0

    def process_demand_forecast_chunk(self, chunk_df: pd.DataFrame) -> int:
        """Process demand forecast data chunk using RobustExcelImporter."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            temp_importer = RobustExcelImporter()
            if not temp_importer.initialize_staging():
                logger.error("Failed to initialize staging for demand forecast chunk")
                return 0
            # Note: Using equipment processing as fallback for demand forecast
            count = temp_importer._extract_and_save_equipment(chunk_df, "demand_forecast_chunk")
            del temp_importer
            gc.collect()
            return count
        except Exception as e:
            logger.error(f"Error processing demand forecast chunk: {e}")
            self.stats['errors'].append(f"Demand forecast chunk error: {e}")
            return 0
