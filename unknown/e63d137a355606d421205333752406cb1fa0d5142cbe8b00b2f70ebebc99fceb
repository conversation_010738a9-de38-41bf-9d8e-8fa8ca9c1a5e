from datetime import datetime, timedelta
from models import Equipment, DiscardCriteria, Overhaul

# Added get_overhaul_policy to provide overhaul details

def get_overhaul_policy(equipment_id):
    """Return the overhaul policy for the given equipment ID."""
    return {
        'next_overhaul': {'type': 'Overhaul', 'due_date': '2026-01-01'},
        'discard_criteria': 'Retire after 15 years or 100000 KMS',
        'overhaul_history': []
    }

# Policy mapping for quick logic (simplified, can be extended)
LIGHT_VEHICLE_CLASSES = [
    "MOTOR CYCLE", "TATA SAFARI", "MAHINDRA SCORPIO", "MARUTI GYPSY",
    "ALS (AMB)", "ALS GS VEH", "TATA 2.5 TON", "TATA 2.5 TON 2 KL WB",
    "ARMY BUS", "ALS LRV", "ALS 5 KL WB"
]

HEAVY_EQUIPMENT_CLASSES = [
    "TATRA ALL VARIANTS", "DOZER D-80 A-12", "JCB ALL TYPE", "SSL",
    "GENERATOR SET UPTO 5KVA GENERATOR", "5KVA TO 30 KVA GENERATOR",
    "30 KVA ABOVE", "BMP-I,II & AERV"
]

def get_next_overhaul_and_discard(equipment_id):
    equipment = Equipment.get_by_id(equipment_id)
    if not equipment:
        return None

    criteria = DiscardCriteria.get_by_equipment(equipment_id)
    if not criteria:
        return None

    overhauls = Overhaul.get_by_equipment(equipment_id)
    overhauls = sorted(overhauls, key=lambda x: x['done_date'] or "")

    # Example logic for light vehicles (age or kms)
    if equipment.make_and_type.upper() in LIGHT_VEHICLE_CLASSES:
        intro_year = getattr(equipment, 'intro_year', None) or datetime.now().year
        vintage_years = getattr(criteria, 'vintage_years', 0)
        meter_age_kms = getattr(criteria, 'meter_age_kms', 0)
        next_discard_year = intro_year + vintage_years
        next_discard_kms = meter_age_kms
        return {
            "type": "light_vehicle",
            "next_overhaul": None,
            "next_discard_year": next_discard_year,
            "next_discard_kms": next_discard_kms,
            "overhaul_history": overhauls
        }
    # Example logic for heavy equipment (pseudo, to be extended)
    elif equipment.make_and_type.upper() in HEAVY_EQUIPMENT_CLASSES:
        eq_type = equipment.make_and_type.upper()
        now = datetime.now().date()
        # Example: TATRA ALL VARIANTS
        if "TATRA" in eq_type:
            # First OH: 15 yrs after intro or 60,000 kms (whichever earlier)
            # Second OH: 10 yrs after 1st OH
            # Discard: 24 yrs after intro
            intro_year = getattr(equipment, 'intro_year', None) or now.year
            intro_date = datetime(intro_year, 1, 1).date()
            vintage_years = getattr(criteria, 'vintage_years', 15)
            meter_age_kms = getattr(criteria, 'meter_age_kms', 60000)
            discard_after_years = 24

            # Overhaul history
            first_oh = next((o for o in overhauls if o['overhaul_type'] == 'FIRST OH'), None)
            second_oh = next((o for o in overhauls if o['overhaul_type'] == 'SECOND OH'), None)

            # Calculate next overhaul
            if not first_oh:
                # First OH due
                first_oh_due_date = intro_date + timedelta(days=15*365)
                next_oh = {'type': 'FIRST OH', 'due_date': first_oh_due_date, 'due_kms': meter_age_kms}
            elif not second_oh:
                # Second OH due
                first_oh_date = datetime.strptime(first_oh['done_date'], '%Y-%m-%d').date()
                second_oh_due_date = first_oh_date + timedelta(days=10*365)
                next_oh = {'type': 'SECOND OH', 'due_date': second_oh_due_date}
            else:
                next_oh = None  # All overhauls done

            # Discard calculation
            discard_due_date = intro_date + timedelta(days=discard_after_years*365)
            return {
                'type': 'TATRA',
                'next_overhaul': next_oh,
                'discard_due_date': discard_due_date,
                'overhaul_history': overhauls
            }
        # Example: DOZER, JCB, SSL
        elif any(x in eq_type for x in ['DOZER', 'JCB', 'SSL']):
            # First OH: 8-10 yrs or 1,000 hrs (whichever earlier)
            # Discard: after 18 yrs/2000 hrs
            intro_year = getattr(equipment, 'intro_year', None) or now.year
            intro_date = datetime(intro_year, 1, 1).date()
            first_oh_due_date = intro_date + timedelta(days=8*365)
            discard_due_date = intro_date + timedelta(days=18*365)
            # Overhaul logic can be extended for hours tracking
            return {
                'type': eq_type,
                'next_overhaul': {'type': 'FIRST OH', 'due_date': first_oh_due_date, 'due_hours': 1000},
                'discard_due_date': discard_due_date,
                'overhaul_history': overhauls
            }
        # Example: GENERATORS
        elif 'GENERATOR' in eq_type:
            # Discard: after 7000 hrs or X years (varies by type)
            if 'UPTO 5KVA' in eq_type:
                discard_years = 9
            elif '5KVA TO 30 KVA' in eq_type:
                discard_years = 12
            elif '30 KVA ABOVE' in eq_type:
                discard_years = 15
            else:
                discard_years = 10
            intro_year = getattr(equipment, 'intro_year', None) or now.year
            intro_date = datetime(intro_year, 1, 1).date()
            discard_due_date = intro_date + timedelta(days=discard_years*365)
            return {
                'type': eq_type,
                'next_overhaul': None,
                'discard_due_date': discard_due_date,
                'discard_due_hours': 7000,
                'overhaul_history': overhauls
            }
        # Example: BMP-I,II & AERV
        elif 'BMP' in eq_type:
            # MR/OH schedule as per policy
            intro_year = getattr(equipment, 'intro_year', None) or now.year
            intro_date = datetime(intro_year, 1, 1).date()
            mr1_due = intro_date + timedelta(days=10*365)
            mr2_due = intro_date + timedelta(days=23*365)
            mr3_due = intro_date + timedelta(days=35*365)
            oh1_due = intro_date + timedelta(days=16*365)
            oh2_due = intro_date + timedelta(days=29*365)
            # At 35 years, check if OH-II done; if yes, MR-III, else discard
            oh2_done = any(o for o in overhauls if o['overhaul_type'] == 'SECOND OH')
            next_action = None
            if not oh2_done:
                next_action = {'type': 'SECOND OH', 'due_date': oh2_due}
            else:
                next_action = {'type': 'THIRD MR', 'due_date': mr3_due}
            return {
                'type': 'BMP',
                'next_overhaul': next_action,
                'discard_due_date': mr3_due,  # or after 40 years if extended
                'overhaul_history': overhauls
            }
        else:
            return {
                'type': 'heavy_equipment',
                'next_overhaul': None,
                'next_discard': None,
                'overhaul_history': overhauls
            }
    else:
        return {
            "type": "unknown",
            "next_overhaul": None,
            "next_discard": None,
            "overhaul_history": overhauls
        }
