"""Maintenance history widget for viewing archived maintenance records."""
import logging
import json
from datetime import datetime, date
from calendar import month_name

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
                           QTreeWidget, QTreeWidgetItem, QLabel, QPushButton,
                           QGroupBox, QFormLayout, QTextEdit, QMessageBox,
                           QHeaderView, QAbstractItemView, QDialog,
                           QDialogButtonBox, QFileDialog, QProgressDialog)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon, QFont

import database
import utils
from models import MaintenanceArchive
from ui.custom_widgets import ReadOnlyTableWidget

# Configure logger
logger = logging.getLogger('maintenance_history_widget')

class MaintenanceHistoryWidget(QWidget):
    """Widget for viewing maintenance history and archives."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_archive = None
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the maintenance history widget UI."""
        # Create main layout
        main_layout = QVBoxLayout(self)
        
        # Create header
        header_layout = QHBoxLayout()
        
        # Title
        title_label = QLabel("<h2>📋 Maintenance History & Archives</h2>")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Refresh button
        self.refresh_button = QPushButton("🔄 Refresh")
        self.refresh_button.clicked.connect(self.load_data)
        header_layout.addWidget(self.refresh_button)
        
        main_layout.addLayout(header_layout)
        
        # Create splitter for main content
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left panel - Archive tree
        left_panel = self.create_archive_tree_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Archive details
        right_panel = self.create_archive_details_panel()
        splitter.addWidget(right_panel)
        
        # Set initial sizes (40% left, 60% right)
        splitter.setSizes([400, 600])
        
        main_layout.addWidget(splitter)
        
    def create_archive_tree_panel(self):
        """Create the left panel with archive tree view."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Panel header
        header_label = QLabel("<h3>📁 Archive Browser</h3>")
        layout.addWidget(header_label)
        
        # Create tree widget
        self.archive_tree = QTreeWidget()
        self.archive_tree.setHeaderLabels(["Archive", "Records", "Date"])
        self.archive_tree.setRootIsDecorated(True)
        self.archive_tree.setAlternatingRowColors(True)
        self.archive_tree.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        
        # Set column widths
        header = self.archive_tree.header()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        
        # Connect selection change
        self.archive_tree.itemSelectionChanged.connect(self.archive_selected)
        
        layout.addWidget(self.archive_tree)
        
        # Archive management buttons
        button_layout = QHBoxLayout()
        
        self.view_button = QPushButton("👁️ View Details")
        self.view_button.setEnabled(False)
        self.view_button.clicked.connect(self.view_archive_details)
        
        self.export_button = QPushButton("📤 Export")
        self.export_button.setEnabled(False)
        self.export_button.clicked.connect(self.export_archive)
        
        self.delete_button = QPushButton("🗑️ Delete")
        self.delete_button.setEnabled(False)
        self.delete_button.clicked.connect(self.delete_archive)
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        
        button_layout.addWidget(self.view_button)
        button_layout.addWidget(self.export_button)
        button_layout.addStretch()
        button_layout.addWidget(self.delete_button)
        
        layout.addLayout(button_layout)
        
        return panel
        
    def create_archive_details_panel(self):
        """Create the right panel for archive details."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Details header
        self.details_header = QLabel("<h3>📊 Archive Details</h3>")
        layout.addWidget(self.details_header)
        
        # Archive info group
        self.info_group = QGroupBox("Archive Information")
        info_layout = QFormLayout()
        
        self.archive_name_label = QLabel("No archive selected")
        self.archive_type_label = QLabel("-")
        self.category_label = QLabel("-")
        self.period_label = QLabel("-")
        self.created_label = QLabel("-")
        self.record_count_label = QLabel("-")
        
        info_layout.addRow("Archive Name:", self.archive_name_label)
        info_layout.addRow("Type:", self.archive_type_label)
        info_layout.addRow("Category:", self.category_label)
        info_layout.addRow("Period:", self.period_label)
        info_layout.addRow("Created:", self.created_label)
        info_layout.addRow("Records:", self.record_count_label)
        
        self.info_group.setLayout(info_layout)
        layout.addWidget(self.info_group)
        
        # Notes section
        self.notes_group = QGroupBox("Archive Notes")
        notes_layout = QVBoxLayout()
        
        self.notes_text = QTextEdit()
        self.notes_text.setMaximumHeight(100)
        self.notes_text.setReadOnly(True)
        self.notes_text.setPlaceholderText("No notes available")
        
        notes_layout.addWidget(self.notes_text)
        self.notes_group.setLayout(notes_layout)
        layout.addWidget(self.notes_group)
        
        # Record preview section
        self.preview_group = QGroupBox("Record Preview")
        preview_layout = QVBoxLayout()
        
        # Preview table
        self.preview_table = ReadOnlyTableWidget()
        self.preview_table.setMaximumHeight(200)
        
        preview_layout.addWidget(self.preview_table)
        
        # View all button
        self.view_all_button = QPushButton("📋 View All Records")
        self.view_all_button.setEnabled(False)
        self.view_all_button.clicked.connect(self.view_all_records)
        self.view_all_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        
        preview_layout.addWidget(self.view_all_button)
        
        self.preview_group.setLayout(preview_layout)
        layout.addWidget(self.preview_group)
        
        layout.addStretch()
        
        return panel
        
    def load_data(self):
        """Load archive data and populate the tree."""
        logger.info("Loading maintenance archives")
        try:
            # Clear existing tree
            self.archive_tree.clear()
            
            # Get all archives
            archives = MaintenanceArchive.get_all()
            
            if not archives:
                # Add placeholder item
                no_data_item = QTreeWidgetItem(self.archive_tree)
                no_data_item.setText(0, "No archives found")
                no_data_item.setText(1, "0")
                no_data_item.setText(2, "-")
                no_data_item.setDisabled(True)
                return
            
            # Organize archives by year and quarter
            years = {}
            
            for archive in archives:
                try:
                    # Parse the period start to get year and quarter
                    if isinstance(archive['period_start'], str):
                        period_start = datetime.strptime(archive['period_start'], '%Y-%m-%d').date()
                    else:
                        period_start = archive['period_start']
                    
                    year = period_start.year
                    month = period_start.month
                    quarter = (month - 1) // 3 + 1
                    
                    if year not in years:
                        years[year] = {}
                    
                    if quarter not in years[year]:
                        years[year][quarter] = []
                    
                    years[year][quarter].append(archive)
                    
                except Exception as e:
                    logger.warning(f"Error processing archive {archive.get('archive_id', 'unknown')}: {e}")
                    continue
            
            # Create tree items
            for year in sorted(years.keys(), reverse=True):
                year_item = QTreeWidgetItem(self.archive_tree)
                year_item.setText(0, f"📅 {year}")
                
                # Count total records for the year
                year_record_count = 0
                for quarter_archives in years[year].values():
                    year_record_count += sum(archive.get('record_count', 0) for archive in quarter_archives)
                
                year_item.setText(1, str(year_record_count))
                year_item.setText(2, f"{len(sum(years[year].values(), []))} archives")
                
                # Make year item bold
                font = year_item.font(0)
                font.setBold(True)
                year_item.setFont(0, font)
                
                # Add quarters
                for quarter in sorted(years[year].keys()):
                    quarter_item = QTreeWidgetItem(year_item)
                    quarter_item.setText(0, f"📊 Q{quarter} {year}")
                    
                    quarter_record_count = sum(archive.get('record_count', 0) for archive in years[year][quarter])
                    quarter_item.setText(1, str(quarter_record_count))
                    quarter_item.setText(2, f"{len(years[year][quarter])} archives")
                    
                    # Add individual archives
                    for archive in sorted(years[year][quarter], key=lambda x: x.get('created_date', ''), reverse=True):
                        archive_item = QTreeWidgetItem(quarter_item)
                        
                        # Set archive data
                        archive_item.setData(0, Qt.ItemDataRole.UserRole, archive)
                        
                        # Format archive name with icon based on type
                        type_icon = {
                            'monthly': '📅',
                            'yearly': '🗓️',
                            'custom': '⚙️'
                        }.get(archive.get('archive_type', 'custom'), '📋')
                        
                        archive_item.setText(0, f"{type_icon} {archive.get('archive_name', 'Unnamed Archive')}")
                        archive_item.setText(1, str(archive.get('record_count', 0)))
                        
                        # Format creation date
                        created_date = archive.get('created_date', '')
                        if created_date:
                            try:
                                if isinstance(created_date, str):
                                    created_dt = datetime.fromisoformat(created_date.replace('Z', '+00:00'))
                                else:
                                    created_dt = created_date
                                archive_item.setText(2, created_dt.strftime('%Y-%m-%d'))
                            except:
                                archive_item.setText(2, str(created_date)[:10])
                        else:
                            archive_item.setText(2, "-")
                
            # Expand first year by default
            if self.archive_tree.topLevelItemCount() > 0:
                self.archive_tree.topLevelItem(0).setExpanded(True)
                
            logger.info(f"Loaded {len(archives)} archives")
            
        except Exception as e:
            logger.error(f"Error loading archive data: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to load archive data:\n{str(e)}",
                QMessageBox.StandardButton.Ok
            )
    
    def archive_selected(self):
        """Handle archive selection in the tree."""
        selected_items = self.archive_tree.selectedItems()
        
        if not selected_items:
            self.clear_details()
            return
        
        item = selected_items[0]
        archive_data = item.data(0, Qt.ItemDataRole.UserRole)
        
        if archive_data:
            self.selected_archive = archive_data
            self.load_archive_details(archive_data)
            self.enable_archive_buttons(True)
        else:
            self.clear_details()
            self.enable_archive_buttons(False)
    
    def clear_details(self):
        """Clear the archive details panel."""
        self.selected_archive = None
        self.archive_name_label.setText("No archive selected")
        self.archive_type_label.setText("-")
        self.category_label.setText("-")
        self.period_label.setText("-")
        self.created_label.setText("-")
        self.record_count_label.setText("-")
        self.notes_text.clear()
        if hasattr(self, 'preview_table'):
            self.preview_table.clear()
    
    def enable_archive_buttons(self, enabled):
        """Enable/disable archive action buttons."""
        if hasattr(self, 'view_button'):
            self.view_button.setEnabled(enabled)
        if hasattr(self, 'export_button'):
            self.export_button.setEnabled(enabled)
        if hasattr(self, 'delete_button'):
            self.delete_button.setEnabled(enabled)
        if hasattr(self, 'view_all_button'):
            self.view_all_button.setEnabled(enabled)
    
    def load_archive_details(self, archive):
        """Load details for the selected archive."""
        try:
            # Update info labels
            self.archive_name_label.setText(archive.get('archive_name', 'Unnamed Archive'))
            if hasattr(self, 'archive_type_label'):
                self.archive_type_label.setText(archive.get('archive_type', 'Unknown').title())
            if hasattr(self, 'category_label'):
                self.category_label.setText(archive.get('maintenance_category', 'Unknown'))
            
            # Format period
            start_date = archive.get('period_start', '')
            end_date = archive.get('period_end', '')
            if start_date and end_date and hasattr(self, 'period_label'):
                self.period_label.setText(f"{start_date} to {end_date}")
            
            # Format creation date
            created_date = archive.get('created_date', '')
            if created_date and hasattr(self, 'created_label'):
                try:
                    if isinstance(created_date, str):
                        created_dt = datetime.fromisoformat(created_date.replace('Z', '+00:00'))
                    else:
                        created_dt = created_date
                    
                    created_by = archive.get('created_by', '')
                    created_text = created_dt.strftime('%Y-%m-%d %H:%M')
                    if created_by:
                        created_text += f" by {created_by}"
                    
                    self.created_label.setText(created_text)
                except:
                    self.created_label.setText(str(created_date))
            
            if hasattr(self, 'record_count_label'):
                self.record_count_label.setText(str(archive.get('record_count', 0)))
            
            # Load notes
            notes = archive.get('notes', '')
            if hasattr(self, 'notes_text'):
                self.notes_text.setPlainText(notes if notes else "No notes available")
            
            # Load preview data if preview table exists
            if hasattr(self, 'preview_table'):
                self.load_archive_preview(archive)
                
        except Exception as e:
            logger.error(f"Error loading archive details: {e}")
    
    def load_archive_preview(self, archive):
        """Load preview of archived records."""
        try:
            # Get archived records
            archive_obj = MaintenanceArchive()
            archive_obj.archive_data = archive.get('archive_data', '')
            records = archive_obj.get_archived_records()
            
            if not records:
                self.preview_table.clear()
                return
            
            # Show first 5 records as preview
            preview_records = records[:5]
            
            # Set up table headers
            headers = ["Equipment", "Type", "Done Date", "Status", "Meterage"]
            self.preview_table.setColumnCount(len(headers))
            self.preview_table.setHorizontalHeaderLabels(headers)
            self.preview_table.setRowCount(len(preview_records))
            
            # Import QTableWidgetItem
            from PyQt5.QtWidgets import QTableWidgetItem
            
            # Populate preview table
            for row, record in enumerate(preview_records):
                # Equipment
                equipment_text = utils.format_equipment_display(record)
                self.preview_table.setItem(row, 0, QTableWidgetItem(str(equipment_text)))
                
                # Maintenance type
                self.preview_table.setItem(row, 1, QTableWidgetItem(str(record.get('maintenance_type', ''))))
                
                # Done date
                done_date = record.get('actual_completion_date') or record.get('done_date', '')
                self.preview_table.setItem(row, 2, QTableWidgetItem(str(done_date)))
                
                # Status
                status = record.get('status', 'Unknown')
                self.preview_table.setItem(row, 3, QTableWidgetItem(str(status).title()))
                
                # Meterage
                meterage = record.get('completion_meterage') or record.get('meterage_kms', 0)
                self.preview_table.setItem(row, 4, QTableWidgetItem(f"{meterage} km"))
            
            # Resize columns to fit content
            self.preview_table.resizeColumnsToContents()
            
            # Show count info
            if hasattr(self, 'preview_group'):
                if len(records) > 5:
                    self.preview_group.setTitle(f"Record Preview (showing 5 of {len(records)} records)")
                else:
                    self.preview_group.setTitle(f"Record Preview ({len(records)} records)")
                
        except Exception as e:
            logger.error(f"Error loading archive preview: {e}")
            if hasattr(self, 'preview_table'):
                self.preview_table.clear()
    
    def view_archive_details(self):
        """Open detailed view of the selected archive."""
        if not self.selected_archive:
            return
        
        dialog = ArchiveDetailDialog(self.selected_archive, self)
        dialog.exec_()
    
    def view_all_records(self):
        """View all records in the selected archive."""
        self.view_archive_details()
    
    def export_archive(self):
        """Export the selected archive to Excel/CSV."""
        if not self.selected_archive:
            return
        
        try:
            # Get archived records
            archive_obj = MaintenanceArchive()
            archive_obj.archive_data = self.selected_archive.get('archive_data', '')
            records = archive_obj.get_archived_records()
            
            if not records:
                QMessageBox.information(
                    self,
                    "No Data",
                    "This archive contains no records to export.",
                    QMessageBox.StandardButton.Ok
                )
                return
            
            # Get export filename
            archive_name = self.selected_archive.get('archive_name', 'archive')
            safe_name = "".join(c for c in archive_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "Export Archive",
                f"{safe_name}.xlsx",
                "Excel Files (*.xlsx);;CSV Files (*.csv)"
            )
            
            if not filename:
                return
            
            # Export data
            self.export_records_to_file(records, filename)
            
        except Exception as e:
            logger.error(f"Error exporting archive: {e}")
            QMessageBox.critical(
                self,
                "Export Error",
                f"Failed to export archive:\n{str(e)}",
                QMessageBox.StandardButton.Ok
            )
    
    def export_records_to_file(self, records, filename):
        """Export records to Excel or CSV file."""
        try:
            import pandas as pd
            
            # Prepare data for export
            export_data = []
            for record in records:
                export_data.append({
                    'Equipment': utils.format_equipment_display(record),
                    'BA Number': record.get('ba_number', ''),
                    'Maintenance Type': record.get('maintenance_type', ''),
                    'Category': record.get('maintenance_category', ''),
                    'Done Date': record.get('actual_completion_date') or record.get('done_date', ''),
                    'Due Date': record.get('due_date', ''),
                    'Status': record.get('status', ''),
                    'Meterage (km)': record.get('completion_meterage') or record.get('meterage_kms', ''),
                    'Vintage (years)': record.get('vintage_years', ''),
                    'Completed By': record.get('completed_by', ''),
                    'Notes': record.get('completion_notes', '')
                })
            
            df = pd.DataFrame(export_data)
            
            if filename.lower().endswith('.xlsx'):
                df.to_excel(filename, index=False)
            else:
                df.to_csv(filename, index=False)
            
            QMessageBox.information(
                self,
                "Export Complete",
                f"Archive exported successfully to:\n{filename}",
                QMessageBox.StandardButton.Ok
            )
            
        except ImportError:
            QMessageBox.critical(
                self,
                "Export Error",
                "pandas library is required for export functionality.\nPlease install it using: pip install pandas",
                QMessageBox.StandardButton.Ok
            )
        except Exception as e:
            logger.error(f"Error in export_records_to_file: {e}")
            raise
    
    def delete_archive(self):
        """Delete the selected archive."""
        if not self.selected_archive:
            return
        
        archive_name = self.selected_archive.get('archive_name', 'this archive')
        record_count = self.selected_archive.get('record_count', 0)
        
        # Confirm deletion
        confirm = QMessageBox.warning(
            self,
            "Delete Archive",
            f"Are you sure you want to delete '{archive_name}'?\n\n"
            f"This will permanently remove the archive and its {record_count} records.\n"
            "This action cannot be undone.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if confirm == QMessageBox.StandardButton.Yes:
            try:
                archive_id = self.selected_archive.get('archive_id')
                if MaintenanceArchive.delete(archive_id):
                    QMessageBox.information(
                        self,
                        "Archive Deleted",
                        f"Archive '{archive_name}' has been deleted successfully.",
                        QMessageBox.StandardButton.Ok
                    )
                    
                    # Reload data
                    self.load_data()
                    self.clear_details()
                    self.enable_archive_buttons(False)
                else:
                    QMessageBox.critical(
                        self,
                        "Delete Failed",
                        "Failed to delete the archive. Please try again.",
                        QMessageBox.StandardButton.Ok
                    )
                    
            except Exception as e:
                logger.error(f"Error deleting archive: {e}")
                QMessageBox.critical(
                    self,
                    "Delete Error",
                    f"An error occurred while deleting the archive:\n{str(e)}",
                    QMessageBox.StandardButton.Ok
                )


class ArchiveDetailDialog(QDialog):
    """Dialog for viewing detailed archive information and records."""
    
    def __init__(self, archive_data, parent=None):
        super().__init__(parent)
        self.archive_data = archive_data
        self.setWindowTitle(f"Archive Details: {archive_data.get('archive_name', 'Unknown')}")
        self.setModal(True)
        self.resize(900, 600)
        
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        
        # Header with archive info
        header_group = QGroupBox("Archive Information")
        header_layout = QFormLayout()
        
        self.name_label = QLabel(self.archive_data.get('archive_name', 'Unknown'))
        self.type_label = QLabel(self.archive_data.get('archive_type', 'Unknown').title())
        self.category_label = QLabel(self.archive_data.get('maintenance_category', 'Unknown'))
        
        period_start = self.archive_data.get('period_start', '')
        period_end = self.archive_data.get('period_end', '')
        period_text = f"{period_start} to {period_end}" if period_start and period_end else "Unknown"
        self.period_label = QLabel(period_text)
        
        self.record_count_label = QLabel(str(self.archive_data.get('record_count', 0)))
        
        header_layout.addRow("Archive Name:", self.name_label)
        header_layout.addRow("Type:", self.type_label)
        header_layout.addRow("Category:", self.category_label)
        header_layout.addRow("Period:", self.period_label)
        header_layout.addRow("Records:", self.record_count_label)
        
        header_group.setLayout(header_layout)
        layout.addWidget(header_group)
        
        # Records table
        records_group = QGroupBox("Archived Records")
        records_layout = QVBoxLayout()
        
        self.records_table = ReadOnlyTableWidget()
        records_layout.addWidget(self.records_table)
        
        # Export button
        export_button = QPushButton("📤 Export Records")
        export_button.clicked.connect(self.export_records)
        records_layout.addWidget(export_button)
        
        records_group.setLayout(records_layout)
        layout.addWidget(records_group)
        
        # Dialog buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def load_data(self):
        """Load archived records into the table."""
        try:
            # Import QTableWidgetItem
            from PyQt5.QtWidgets import QTableWidgetItem
            
            # Get archived records
            archive_obj = MaintenanceArchive()
            archive_obj.archive_data = self.archive_data.get('archive_data', '')
            records = archive_obj.get_archived_records()
            
            if not records:
                return
            
            # Set up table
            headers = [
                "Equipment", "BA Number", "Type", "Category", 
                "Done Date", "Due Date", "Status", "Meterage (km)",
                "Vintage (years)", "Completed By", "Notes"
            ]
            
            self.records_table.setColumnCount(len(headers))
            self.records_table.setHorizontalHeaderLabels(headers)
            self.records_table.setRowCount(len(records))
            
            # Populate table
            for row, record in enumerate(records):
                self.records_table.setItem(row, 0, QTableWidgetItem(str(utils.format_equipment_display(record))))
                self.records_table.setItem(row, 1, QTableWidgetItem(str(record.get('ba_number', ''))))
                self.records_table.setItem(row, 2, QTableWidgetItem(str(record.get('maintenance_type', ''))))
                self.records_table.setItem(row, 3, QTableWidgetItem(str(record.get('maintenance_category', ''))))
                self.records_table.setItem(row, 4, QTableWidgetItem(str(record.get('actual_completion_date') or record.get('done_date', ''))))
                self.records_table.setItem(row, 5, QTableWidgetItem(str(record.get('due_date', ''))))
                self.records_table.setItem(row, 6, QTableWidgetItem(str(record.get('status', ''))))
                self.records_table.setItem(row, 7, QTableWidgetItem(str(record.get('completion_meterage') or record.get('meterage_kms', ''))))
                self.records_table.setItem(row, 8, QTableWidgetItem(str(record.get('vintage_years', ''))))
                self.records_table.setItem(row, 9, QTableWidgetItem(str(record.get('completed_by', ''))))
                self.records_table.setItem(row, 10, QTableWidgetItem(str(record.get('completion_notes', ''))))
            
            # Resize columns
            self.records_table.resizeColumnsToContents()
            
        except Exception as e:
            logger.error(f"Error loading archive detail data: {e}")
    
    def export_records(self):
        """Export the archived records."""
        try:
            # Get archived records
            archive_obj = MaintenanceArchive()
            archive_obj.archive_data = self.archive_data.get('archive_data', '')
            records = archive_obj.get_archived_records()
            
            if not records:
                QMessageBox.information(
                    self,
                    "No Records",
                    "This archive contains no records to export.",
                    QMessageBox.StandardButton.Ok
                )
                return
            
            # Get export filename
            archive_name = self.archive_data.get('archive_name', 'archive')
            safe_name = "".join(c for c in archive_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "Export Archive Records",
                f"{safe_name}_detailed.xlsx",
                "Excel Files (*.xlsx);;CSV Files (*.csv)"
            )
            
            if filename:
                self.export_records_to_file(records, filename)
                
        except Exception as e:
            logger.error(f"Error exporting archive records: {e}")
            QMessageBox.critical(
                self,
                "Export Error",
                f"Failed to export records:\n{str(e)}",
                QMessageBox.StandardButton.Ok
            )
    
    def export_records_to_file(self, records, filename):
        """Export records to Excel or CSV file."""
        try:
            import pandas as pd
            
            # Prepare data for export
            export_data = []
            for record in records:
                export_data.append({
                    'Equipment': utils.format_equipment_display(record),
                    'BA Number': record.get('ba_number', ''),
                    'Maintenance Type': record.get('maintenance_type', ''),
                    'Category': record.get('maintenance_category', ''),
                    'Done Date': record.get('actual_completion_date') or record.get('done_date', ''),
                    'Due Date': record.get('due_date', ''),
                    'Status': record.get('status', ''),
                    'Meterage (km)': record.get('completion_meterage') or record.get('meterage_kms', ''),
                    'Vintage (years)': record.get('vintage_years', ''),
                    'Completed By': record.get('completed_by', ''),
                    'Notes': record.get('completion_notes', '')
                })
            
            df = pd.DataFrame(export_data)
            
            if filename.lower().endswith('.xlsx'):
                df.to_excel(filename, index=False)
            else:
                df.to_csv(filename, index=False)
            
            QMessageBox.information(
                self,
                "Export Complete",
                f"Archive exported successfully to:\n{filename}",
                QMessageBox.StandardButton.Ok
            )
            
        except ImportError:
            QMessageBox.critical(
                self,
                "Export Error",
                "pandas library is required for export functionality.\nPlease install it using: pip install pandas",
                QMessageBox.StandardButton.Ok
            )
        except Exception as e:
            logger.error(f"Error in export_records_to_file: {e}")
            raise 