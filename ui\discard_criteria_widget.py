"""Discard criteria management widget for the equipment inventory application."""
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                           QPushButton, QTableWidget, QTableWidgetItem,
                           QHeaderView, QAbstractItemView, QMessageBox,
                           QComboBox, QLineEdit, QFormLayout, QGroupBox,
                           QSplitter, QFrame, QProgressDialog, QDialog)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QColor
import logging

import models
from ui.custom_widgets import ReadOnlyTableWidget, StatusLabel
from ui.dialogs import DiscardCriteriaDialog
import utils
from datetime import datetime
from ui.common_styles import *
from discard_service import (create_all_automatic_discard_criteria, 
                            get_equipment_discard_status, 
                            match_equipment_to_criteria)

# Check if policy module is available
POLICY_SUPPORT = True
try:
    import sys
    import os
    
    # Make sure project root is in path - use absolute path to be certain
    project_root = os.path.abspath(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # Add to sys.path if not already there
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
        
    # Attempt import with explicit path
    import policy_service
    from policy_models import VehicleClassPolicy, PolicyCondition
    
    # Ensure policy tables exist when this module is loaded
    policy_service.ensure_tables_exist()
    
    # Log success
    logging.info("Policy module successfully loaded and tables verified")
except ImportError as e:
    POLICY_SUPPORT = False
    logging.warning(f"Policy module not available in DiscardCriteriaWidget. Some features will be disabled. Error: {e}")

class DiscardCriteriaWidget(QWidget):
    """Widget for managing equipment discard criteria."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.setup_ui()
        self.load_data()
        
        # Register for overhaul status change notifications
        try:
            import overhaul_service
            overhaul_service.register_overhaul_update_callback(self.refresh_on_overhaul_change)
        except Exception as e:
            logging.error(f"Error registering for overhaul status change notifications: {e}")
        
    def setup_ui(self):
        """Set up the discard criteria widget UI."""
        # Main layout
        main_layout = QVBoxLayout(self)
        apply_standard_layout(main_layout)
        
        # Title
        title_label = QLabel("Discard Criteria Management")
        title_label.setObjectName("titleLabel")
        title_label.setStyleSheet("""
            QLabel#titleLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # Create a splitter for the main content
        content_splitter = QSplitter(Qt.Horizontal)
        
        # Left side - Discard criteria list
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        
        # Search and filter controls
        filter_layout = QHBoxLayout()
        
        # Search box
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search criteria...")
        self.search_edit.textChanged.connect(self.filter_criteria)
        filter_layout.addWidget(QLabel("Search:"))
        filter_layout.addWidget(self.search_edit)
        
        # Equipment filter
        self.equipment_filter = QComboBox()
        self.equipment_filter.addItem("All Equipment", None)
        self.equipment_filter.currentIndexChanged.connect(self.filter_criteria)
        filter_layout.addWidget(QLabel("Equipment:"))
        filter_layout.addWidget(self.equipment_filter)
        
        # Status filter
        self.status_filter = QComboBox()
        self.status_filter.addItem("All Status", None)
        self.status_filter.addItem("Normal", "normal")
        self.status_filter.addItem("Meeting Discard Criteria", "discard")
        self.status_filter.addItem("Discarded from Overhaul", "auto_discard")
        self.status_filter.currentIndexChanged.connect(self.filter_criteria)
        filter_layout.addWidget(QLabel("Status:"))
        filter_layout.addWidget(self.status_filter)
        
        filter_layout.addStretch()
        left_layout.addLayout(filter_layout)
        
        # Discard criteria table - GROUPED VIEW
        self.criteria_table = ReadOnlyTableWidget()
        self.criteria_table.setColumnCount(5)
        self.criteria_table.setHorizontalHeaderLabels([
            "Make and Type", "Count", "Status Summary", "Date Range", "Criteria"
        ])
        self.criteria_table.verticalHeader().setVisible(False)
        self.criteria_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.criteria_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.criteria_table.setSortingEnabled(True)
        self.criteria_table.row_clicked.connect(self.criteria_selected)
        
        # Set DPI-aware column widths using the new method - increased Make & Type width
        base_widths = [300, 120, 180, 80, 80]  # Make and Type (increased), Date of Commission, Status, Years, KMs
        self.criteria_table.set_responsive_column_widths(base_widths)
        
        left_layout.addWidget(self.criteria_table)
        
        # Buttons
        button_layout = QHBoxLayout()
        apply_button_layout(button_layout)

        self.add_button = QPushButton("Add Criteria")
        self.add_button.clicked.connect(self.add_criteria)
        apply_button_style(self.add_button, "primary")
        button_layout.addWidget(self.add_button)

        # Edit and Delete buttons removed - now in GroupDetailDialog

        self.policy_button = QPushButton("Policies")
        self.policy_button.clicked.connect(self.open_policy_management)
        apply_button_style(self.policy_button, "info")
        button_layout.addWidget(self.policy_button)
        
        button_layout.addStretch()

        self.create_demand_button = QPushButton("Create Demand")
        self.create_demand_button.clicked.connect(self.create_demand)
        self.create_demand_button.setEnabled(False)
        # Hide the Create Demand button as requested
        self.create_demand_button.setVisible(False)
        apply_button_style(self.create_demand_button, "danger")
        button_layout.addWidget(self.create_demand_button)
        
        left_layout.addLayout(button_layout)
        
        # Right side - Criteria details and editing
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        
        # Equipment details group
        details_group = QGroupBox("Equipment Details")
        details_layout = QFormLayout(details_group)
        
        # BA Number field hidden as requested
        self.detail_ba_number = QLabel("--")
        # details_layout.addRow("BA Number:", self.detail_ba_number)  # Hidden
        
        self.detail_make_type = QLabel("--")
        details_layout.addRow("Make & Type:", self.detail_make_type)
        
        self.detail_date_commission = QLabel("--")
        details_layout.addRow("Date of Commission:", self.detail_date_commission)
        
        self.detail_vintage = QLabel("--")
        details_layout.addRow("Current Vintage:", self.detail_vintage)
        
        self.detail_meterage = QLabel("--")
        details_layout.addRow("Current Meterage:", self.detail_meterage)
        
        self.detail_status = StatusLabel("--")
        details_layout.addRow("Status:", self.detail_status)
        
        right_layout.addWidget(details_group)
        
        # Criteria editing group
        criteria_group = QGroupBox("Discard Criteria")
        criteria_layout = QFormLayout(criteria_group)
        
        self.criteria_years_label = QLabel("--")
        criteria_layout.addRow("Years Criteria:", self.criteria_years_label)
        
        self.criteria_kms_label = QLabel("--")
        criteria_layout.addRow("KMs Criteria:", self.criteria_kms_label)
        
        # Auto criteria info
        self.auto_criteria_label = QLabel("--")
        self.auto_criteria_label.setStyleSheet("color: #3498db; font-style: italic;")
        criteria_layout.addRow("Auto Rules Available:", self.auto_criteria_label)
        
        # Quick edit section
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        criteria_layout.addRow(separator)
        
        quick_edit_label = QLabel("Quick Edit:")
        quick_edit_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        criteria_layout.addRow(quick_edit_label)
        
        self.edit_years_input = QLineEdit()
        self.edit_years_input.setPlaceholderText("Enter years...")
        self.edit_years_input.setEnabled(False)
        criteria_layout.addRow("Years:", self.edit_years_input)
        
        self.edit_kms_input = QLineEdit()
        self.edit_kms_input.setPlaceholderText("Enter KMs...")
        self.edit_kms_input.setEnabled(False)
        criteria_layout.addRow("KMs:", self.edit_kms_input)
        
        self.edit_hours_input = QLineEdit()
        self.edit_hours_input.setPlaceholderText("Enter Hours...")
        self.edit_hours_input.setEnabled(False)
        criteria_layout.addRow("Hours:", self.edit_hours_input)
        
        self.save_criteria_button = QPushButton("Save Changes")
        self.save_criteria_button.clicked.connect(self.save_criteria_changes)
        self.save_criteria_button.setEnabled(False)
        criteria_layout.addRow(self.save_criteria_button)

        # Add discard button
        self.discard_button = QPushButton("Mark as Discarded")
        self.discard_button.clicked.connect(self.mark_equipment_discarded)
        self.discard_button.setEnabled(False)
        self.discard_button.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #b71c1c;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        criteria_layout.addRow(self.discard_button)
        
        right_layout.addWidget(criteria_group)
        
        # Status summary
        summary_group = QGroupBox("Summary")
        summary_layout = QVBoxLayout(summary_group)
        
        self.summary_label = QLabel("Total Equipment: 0\nNormal: 0\nMeeting Discard Criteria: 0")
        self.summary_label.setStyleSheet("font-family: monospace; padding: 10px;")
        summary_layout.addWidget(self.summary_label)
        
        right_layout.addWidget(summary_group)
        
        right_layout.addStretch()
        
        # Add widgets to splitter
        content_splitter.addWidget(left_widget)
        content_splitter.addWidget(right_widget)
        content_splitter.setSizes([int(self.width() * 0.7), int(self.width() * 0.3)])
        
        main_layout.addWidget(content_splitter)
        
        # Summary information
        self.summary_label = QLabel()
        self.summary_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
                color: #495057;
            }
        """)
        main_layout.addWidget(self.summary_label)

        # Apply standardized stylesheet
        self.setStyleSheet(get_complete_stylesheet())
    
    def load_data(self, force_refresh=False):
        """Load discard criteria data into the table."""
        # Check and create discard criteria for equipment that completed OH-II + 10 years
        try:
            import overhaul_service
            if force_refresh:
                # Force regeneration of discard criteria on policy update
                logging.info("Force refreshing discard criteria after policy update")
                overhaul_service.check_and_create_discard_criteria(force_update=True)
                overhaul_service.auto_populate_discard_criteria_from_overhaul_status(force_update=True)
            else:
                overhaul_service.check_and_create_discard_criteria()
                overhaul_service.auto_populate_discard_criteria_from_overhaul_status()
        except Exception as e:
            logging.error(f"Error checking discard criteria: {e}")
            
        # Get all equipment with discard criteria
        all_criteria_list = models.DiscardCriteria.get_all()
        
        # Identify equipment that needs to be shown in discard view
        criteria_list = []
        overhaul_discard_equipment = []
        
        if POLICY_SUPPORT:
            try:
                import overhaul_service
                from models import Overhaul
                
                # Check for overhaul discard equipment (due to OH-II completion + 10 years)
                overhaul_discard_equipment = []
                
                # For now, we're disabling the automatic overhaul discard status check
                # This addresses the issue of overhaul data showing up incorrectly
                include_overhaul_discard = False
                
                try:
                    if include_overhaul_discard:
                        # Iterate through all equipment
                        for criteria in all_criteria_list:
                            equipment_id = criteria.get('equipment_id')
                            if not equipment_id:
                                continue
                            
                            # Check if this equipment has overhaul-based discard status
                            overhauls = Overhaul.get_by_equipment(equipment_id)
                            oh2_overhaul = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-II'), None)
                            
                            if oh2_overhaul and oh2_overhaul.get('done_date'):
                                oh1_overhaul = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-I'), None)
                                oh1_done_date = oh1_overhaul.get('done_date') if oh1_overhaul else None
                                
                                current_status = overhaul_service.get_overhaul_status(
                                    'OH-II',
                                    oh2_overhaul.get('due_date'),
                                    oh2_overhaul.get('done_date'),
                                    date_of_commission=criteria.get('date_of_commission') or criteria.get('date_of_induction'),
                                    oh1_done_date=oh1_done_date,
                                    meterage_km=float(criteria.get('meterage_kms') or 0)
                                )
                                
                                if current_status == "discard":
                                    overhaul_discard_equipment.append(equipment_id)
                    
                    logging.info(f"Found {len(overhaul_discard_equipment)} equipment with overhaul discard status")
                except Exception as e:
                    logging.error(f"Error checking overhaul discard status: {e}")
                
                # Get all available policies
                all_policies = policy_service.get_all_policies(force_refresh=force_refresh)
                
                if not all_policies and not overhaul_discard_equipment:
                    logging.warning("No policies found in the database and no overhaul discard equipment. Table will be empty.")
                    criteria_list = []  # No data to show
                    return
                    
                # Extract make and type from policies (already normalized by policy_service)
                policy_make_types = set()
                for policy in all_policies:
                    policy_mt = policy.get('policy', {}).get('make_and_type', '')
                    if policy_mt:
                        # Use policy_service's normalize function to ensure consistency
                        policy_make_types.add(policy_service.normalize_make_type(policy_mt))
                
                # Convert to list for easier logging and lookups
                policy_make_types_list = list(policy_make_types)
                logging.info(f"Found {len(policy_make_types_list)} unique make/types with policies: {policy_make_types_list}")
                
                # Clear list before filtering
                criteria_list = []
                
                # Log all policy make types to debug the issue
                logging.info("--------- POLICY MATCHING DIAGNOSTICS ---------")
                
                # Include equipment that either:
                # 1. Has a policy defined for its make/type, OR
                # 2. Is marked for discard by the overhaul system
                for criteria in all_criteria_list:
                    equipment_id = criteria.get('equipment_id')
                    if not equipment_id:
                        continue
                        
                    raw_make_and_type = criteria.get('make_and_type', '')
                    if not raw_make_and_type:
                        continue
                    
                    # Get BA number for logging
                    ba_number = criteria.get('ba_number', 'Unknown')
                    
                    # Use policy_service's normalize function for consistent handling
                    make_and_type = policy_service.normalize_make_type(raw_make_and_type)
                    
                    # Skip equipment with blank make_and_type
                    if not make_and_type:
                        logging.debug(f"Skipping equipment ID {equipment_id} (BA: {criteria.get('ba_number', 'Unknown')}) due to empty make_and_type")
                        continue
                    
                    included = False
                    
                    # Check for overhaul discard status first - only if we're including overhaul discard
                    if include_overhaul_discard and equipment_id in overhaul_discard_equipment:
                        logging.info(f"[INCLUDE] Equipment {ba_number} (ID: {equipment_id}, Type: {make_and_type}) due to overhaul discard status")
                        criteria_list.append(criteria)
                        included = True
                        continue
                        
                    # Check against policies
                    policy_match = False
                    matching_policy = ''
                    
                    # First, try to get policy directly from service - this uses all our enhanced matching logic
                    policy_data = policy_service.get_policy_with_conditions(make_and_type=raw_make_and_type)
                    if policy_data:
                        policy_match = True
                        matching_policy = policy_data.get('policy', {}).get('make_and_type', '')
                        logging.debug(f"Policy service found match for '{make_and_type}' with policy '{matching_policy}'")
                    else:
                        # Fallback to policy list comparison if service returns nothing
                        for policy_make_type in policy_make_types:
                            # Try exact match first
                            if make_and_type == policy_make_type:
                                policy_match = True
                                matching_policy = policy_make_type
                                logging.debug(f"Exact match found between '{make_and_type}' and policy '{policy_make_type}'")
                                break
                            # Try case-insensitive match
                            elif make_and_type.lower() == policy_make_type.lower():
                                policy_match = True
                                matching_policy = policy_make_type
                                logging.debug(f"Case-insensitive match found between '{make_and_type}' and policy '{policy_make_type}'")
                                break
                    
                    if policy_match:
                        logging.info(f"[INCLUDE] Equipment {ba_number} (ID: {equipment_id}, Type: {make_and_type}) due to policy match with '{matching_policy}'")
                        criteria_list.append(criteria)
                        included = True
                    else:
                        logging.info(f"[EXCLUDE] Equipment {ba_number} (ID: {equipment_id}, Type: {make_and_type}) - No matching policy or overhaul status")
                    
                logging.info(f"Filtered from {len(all_criteria_list)} to {len(criteria_list)} criteria based on combined policy and overhaul status")
            except Exception as e:
                logging.error(f"Error filtering criteria by policy and overhaul status: {e}")
                criteria_list = all_criteria_list  # Fallback to showing all if there's an error
        else:
            # If policy support is disabled, show all equipment
            criteria_list = all_criteria_list

        # Load equipment filter
        self.load_equipment_filter()

        # Clear table
        self.criteria_table.setRowCount(0)
        
        # Group equipment by make_and_type and filter out normal status
        grouped_data = {}
        normal_count = 0
        discard_count = 0
        policy_discard_count = 0
        
        # First pass: Evaluate each equipment and group by make_and_type
        for criteria in criteria_list:
            # Make and Type
            make_type = criteria.get('make_and_type') or 'Unknown'
            
            # Evaluate equipment status first
            equipment_id = criteria.get('equipment_id')
            current_vintage = float(criteria.get('vintage_years') or 0)
            current_meterage = float(criteria.get('meterage_kms') or 0)
            current_hours = float(criteria.get('hours_run_total') or 0)
            criteria_years = float(criteria.get('criteria_years') or 0)
            criteria_kms = float(criteria.get('criteria_kms') or 0)
            criteria_hours = float(criteria.get('criteria_hours') or 0)
            
            # Get date of commission
            date_commission = criteria.get('date_of_commission') or criteria.get('date_of_induction')
            if date_commission:
                try:
                    if isinstance(date_commission, str):
                        date_obj = datetime.strptime(date_commission, '%Y-%m-%d')
                        formatted_date = date_obj.strftime('%d/%m/%Y')
                    else:
                        formatted_date = str(date_commission)
                except:
                    formatted_date = str(date_commission)
            else:
                formatted_date = '--'
            
            # Variables for status determination
            is_auto_populated = False
            has_policy = False
            policy_status = None
            status_text = "Normal"
            status_color = QColor("#27ae60")  # Green (default)
            status_type = "normal"
            status_tooltip = ""
            
            # Equipment data for policy evaluation
            make_and_type = criteria.get('make_and_type', '')
            if make_and_type and isinstance(make_and_type, str):
                make_and_type = make_and_type.strip()
                
            equipment_data = {
                'equipment_id': equipment_id,
                'make_and_type': make_and_type,
                'vintage_years': current_vintage,
                'meterage_kms': current_meterage,
                'hours_run_total': current_hours,
                'date_of_commission': date_commission,
            }
            
            # 1. Check for policy-based status if policy support is available
            if POLICY_SUPPORT and equipment_id:
                try:
                    policy_status = policy_service.get_equipment_policy_status(equipment_data)
                    if policy_status:
                        has_policy = True
                        
                        # Check if meets discard criteria according to policy
                        if policy_status.get('conditions', {}).get(PolicyCondition.DISCARD, {}).get('met', False):
                            status_text = "Discarded due to Policy"
                            status_color = QColor("#d32f2f")  # Dark Red
                            status_type = "policy_discard"
                            policy_discard_count += 1
                            discard_count += 1
                            
                            # Get the specific reason
                            discard_condition = policy_status.get('conditions', {}).get(PolicyCondition.DISCARD, {})
                            reason_parts = []
                            
                            if discard_condition.get('meets_years'):
                                reason_parts.append(
                                    f"Age: {current_vintage:.1f} >= {discard_condition.get('years_threshold')} years"
                                )
                            if discard_condition.get('meets_kms'):
                                reason_parts.append(
                                    f"KMs: {current_meterage:.0f} >= {discard_condition.get('km_threshold')} km"
                                )
                            if discard_condition.get('meets_hours'):
                                reason_parts.append(
                                    f"Hours: {current_hours:.0f} >= {discard_condition.get('hours_threshold')} hrs"
                                )
                                
                            status_tooltip = f"Policy: {policy_status.get('policy_name', 'Unknown')}\n" + \
                                           f"Reason: {', '.join(reason_parts)}"
                            
                            # Log successful policy discard match for debugging
                            logging.debug(f"Equipment {equipment_id} ({make_type}) marked for discard by policy: {status_tooltip}")
                except Exception as e:
                    logging.error(f"Error evaluating policy for equipment {equipment_id}: {e}")
            
            # 2. Always check overhaul system status regardless of policy
            # If equipment has a policy-based discard, it takes precedence
            # but we still want to record overhaul status for completeness
            try:
                import overhaul_service
                from models import Overhaul

                # Get overhauls for this equipment
                overhauls = Overhaul.get_by_equipment(equipment_id)
                oh2_overhaul = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-II'), None)

                if oh2_overhaul and oh2_overhaul.get('done_date'):
                    oh1_overhaul = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-I'), None)
                    oh1_done_date = oh1_overhaul.get('done_date') if oh1_overhaul else None

                    current_status = overhaul_service.get_overhaul_status(
                        'OH-II',
                        oh2_overhaul.get('due_date'),
                        oh2_overhaul.get('done_date'),
                        date_of_commission=criteria.get('date_of_commission') or criteria.get('date_of_induction'),
                        oh1_done_date=oh1_done_date,
                        meterage_km=current_meterage
                    )

                    if current_status == "discard" and (not has_policy or status_type == "normal"):
                        # Only change status if no policy discard is present
                        # or if policy evaluation resulted in normal status
                        is_auto_populated = True
                        status_text = "Discarded from Overhaul"
                        status_color = QColor("#d32f2f")  # Dark Red
                        status_type = "auto_discard"
                        discard_count += 1
                        status_tooltip = "Automatically discarded based on overhaul status"
                    elif current_status == "discard" and status_type == "policy_discard":
                        # If already marked for policy discard, add overhaul info to tooltip
                        status_tooltip += "\n\nNote: Also meets overhaul-based discard criteria"
            except Exception as e:
                logging.error(f"Error evaluating overhaul status for equipment {equipment_id}: {e}")

            # 3. If no policy discard or overhaul discard, check manual criteria
            if not has_policy and not is_auto_populated:
                # Determine status based on criteria thresholds
                meets_discard = False
                
                # Check individual criteria
                meets_years = criteria_years > 0 and current_vintage >= criteria_years
                meets_kms = criteria_kms > 0 and current_meterage >= criteria_kms
                meets_hours = criteria_hours > 0 and current_hours >= criteria_hours
                
                # Equipment meets discard if ANY criteria is met (OR logic)
                meets_discard = meets_years or meets_kms or meets_hours

                if meets_discard:
                    status_text = "Meeting Discard Criteria"
                    status_color = QColor("#e74c3c")  # Red
                    status_type = "discard"
                    discard_count += 1
                    
                    # Build tooltip text
                    reason_parts = []
                    if meets_years:
                        reason_parts.append(f"Age: {current_vintage:.1f} >= {criteria_years} years")
                    if meets_kms:
                        reason_parts.append(f"KMs: {current_meterage:.0f} >= {criteria_kms} km")
                    if meets_hours:
                        reason_parts.append(f"Hours: {current_hours:.0f} >= {criteria_hours} hrs")
                    
                    status_tooltip = f"Manual criteria:\n{', '.join(reason_parts)}"
                else:
                    normal_count += 1

            # FILTER OUT NORMAL STATUS EQUIPMENT (as requested)
            if status_type == "normal":
                continue  # Skip normal status equipment
            
            # Add equipment data with full details for the group
            equipment_details = {
                'ba_number': criteria.get('ba_number', '--'),
                'equipment_id': equipment_id,
                'discard_criteria_id': criteria.get('discard_criteria_id'),
                'date_of_commission': date_commission,
                'formatted_date': formatted_date,
                'vintage_years': current_vintage,
                'meterage_kms': current_meterage,
                'hours_run_total': current_hours,
                'criteria_years': criteria_years,
                'criteria_kms': criteria_kms,
                'criteria_hours': criteria_hours,
                'status_text': status_text,
                'status_color': status_color,
                'status_type': status_type,
                'status_tooltip': status_tooltip,
                'policy_status': policy_status
            }
            
            # Group by make_and_type
            if make_type not in grouped_data:
                grouped_data[make_type] = {
                    'make_and_type': make_type,
                    'equipment_list': [],
                    'count': 0,
                    'status_summary': {},
                    'date_range': {'min_date': None, 'max_date': None},
                    'criteria_summary': set()
                }
            
            grouped_data[make_type]['equipment_list'].append(equipment_details)
            grouped_data[make_type]['count'] += 1
            
            # Update status summary
            if status_type not in grouped_data[make_type]['status_summary']:
                grouped_data[make_type]['status_summary'][status_type] = 0
            grouped_data[make_type]['status_summary'][status_type] += 1
            
            # Update date range
            if date_commission:
                try:
                    if isinstance(date_commission, str):
                        date_obj = datetime.strptime(date_commission, '%Y-%m-%d')
                    else:
                        date_obj = date_commission
                    
                    if grouped_data[make_type]['date_range']['min_date'] is None or date_obj < grouped_data[make_type]['date_range']['min_date']:
                        grouped_data[make_type]['date_range']['min_date'] = date_obj
                    if grouped_data[make_type]['date_range']['max_date'] is None or date_obj > grouped_data[make_type]['date_range']['max_date']:
                        grouped_data[make_type]['date_range']['max_date'] = date_obj
                except:
                    pass
            
            # Update criteria summary
            if criteria_years > 0:
                grouped_data[make_type]['criteria_summary'].add(f"{int(criteria_years)} Years")
            if criteria_kms > 0:
                grouped_data[make_type]['criteria_summary'].add(f"{int(criteria_kms)} KMs")
            if criteria_hours > 0:
                grouped_data[make_type]['criteria_summary'].add(f"{int(criteria_hours)} Hours")
        
        # Second pass: Populate table with grouped data (filter out groups with count=0)
        row = 0
        for make_type, group_data in grouped_data.items():
            # Skip groups with count 0
            if group_data['count'] == 0:
                continue
                
            self.criteria_table.insertRow(row)
            
            # Column 0: Make and Type
            make_type_item = QTableWidgetItem(str(make_type))
            # Store group data in the item for detail dialog
            make_type_item.setData(Qt.ItemDataRole.UserRole, group_data)
            self.criteria_table.setItem(row, 0, make_type_item)
            
            # Column 1: Count
            count_item = QTableWidgetItem(str(group_data['count']))
            self.criteria_table.setItem(row, 1, count_item)
            
            # Column 2: Status Summary
            status_summary = group_data['status_summary']
            summary_text = []
            if 'policy_discard' in status_summary:
                summary_text.append(f"{status_summary['policy_discard']} Policy Discard")
            if 'auto_discard' in status_summary:
                summary_text.append(f"{status_summary['auto_discard']} Overhaul Discard")
            if 'discard' in status_summary:
                summary_text.append(f"{status_summary['discard']} Manual Discard")
            
            status_item = QTableWidgetItem(", ".join(summary_text) if summary_text else "Unknown")
            status_item.setForeground(QColor("#d32f2f"))  # Red for discard
            self.criteria_table.setItem(row, 2, status_item)
            
            # Column 3: Date Range
            date_range = group_data['date_range']
            if date_range['min_date'] and date_range['max_date']:
                if date_range['min_date'] == date_range['max_date']:
                    date_text = date_range['min_date'].strftime('%Y')
                else:
                    date_text = f"{date_range['min_date'].strftime('%Y')}-{date_range['max_date'].strftime('%Y')}"
            else:
                date_text = "--"
            self.criteria_table.setItem(row, 3, QTableWidgetItem(date_text))
            
            # Column 4: Criteria
            criteria_text = ", ".join(sorted(group_data['criteria_summary'])) if group_data['criteria_summary'] else "--"
            self.criteria_table.setItem(row, 4, QTableWidgetItem(criteria_text))
            
            row += 1
        
        # Count entries by type
        auto_discard_count = 0
        manual_discard_count = 0
        policy_entries = set()
        
        for row in range(self.criteria_table.rowCount()):
            # Get equipment details for logging (adjusted for BA Number column removal)
            make_type_item = self.criteria_table.item(row, 0)  # Make and Type is now in column 0
            
            make_type = make_type_item.text() if make_type_item else "Unknown"
            equipment_id = make_type_item.data(Qt.UserRole + 1) if make_type_item else None
            
            # Get status type (moved to column 2)
            status_item = self.criteria_table.item(row, 2)
            if status_item:
                status_type = status_item.data(Qt.UserRole)
                status_text = status_item.text()
                
                if status_type == "auto_discard":
                    auto_discard_count += 1
                elif status_type == "discard":
                    manual_discard_count += 1
                elif status_type == "policy_discard":
                    policy_entries.add(f"{make_type}")
                    
                logging.debug(f"Row {row}: {make_type} - Status: {status_text} ({status_type})")
        
        # Log policy-based entries for debugging
        if policy_entries:
            logging.info(f"Policy-based entries in table: {policy_entries}")

        # Update summary - show grouped counts
        total_groups = len(grouped_data)
        total_equipment_with_discard = sum(group['count'] for group in grouped_data.values())
        
        self.summary_label.setText(
            f"Equipment Groups: {total_groups}\n"
            f"Total Equipment meeting Discard Criteria: {total_equipment_with_discard}\n"
            f"Normal Equipment: {normal_count} (hidden)\n"
            f"Policy Discard: {policy_discard_count}\n"
            f"Manual Discard: {discard_count - policy_discard_count}\n"
            f"Note: Only equipment meeting discard criteria is shown"
        )
        
        # Resize columns to content
        self.criteria_table.resizeColumnsToContents()
        
        # Clear details
        self.clear_criteria_details()
    
    def load_equipment_filter(self):
        """Load equipment filter dropdown."""
        self.equipment_filter.clear()
        self.equipment_filter.addItem("All Equipment", None)
        
        equipment_list = models.Equipment.get_active()
        
        for equipment in equipment_list:
            ba_number = equipment.get('ba_number') or ''
            make_type = equipment.get('make_and_type') or ''
            display_text = f"{ba_number} - {make_type}" if ba_number else make_type
            equipment_id = equipment.get('equipment_id')
            self.equipment_filter.addItem(display_text, equipment_id)
    
    def filter_criteria(self):
        """Filter criteria list based on search text and filters."""
        search_text = self.search_edit.text().lower()
        equipment_id = self.equipment_filter.currentData()
        status_filter = self.status_filter.currentData()
        
        for row in range(self.criteria_table.rowCount()):
            show_row = True
            
            if search_text:
                row_text = ""
                for col in range(self.criteria_table.columnCount()):
                    item = self.criteria_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "
                
                if search_text not in row_text:
                    show_row = False
            
            if equipment_id is not None:
                item = self.criteria_table.item(row, 0)
                item_equipment_id = item.data(Qt.UserRole + 1)
                if item_equipment_id != equipment_id:
                    show_row = False
            
            if status_filter is not None:
                status_item = self.criteria_table.item(row, 3)
                item_status = status_item.data(Qt.UserRole)
                if item_status != status_filter:
                    show_row = False
            
            self.criteria_table.setRowHidden(row, not show_row)
    
    def criteria_selected(self, row):
        """Handle criteria selection - opens detail dialog for grouped data."""
        if row < 0 or row >= self.criteria_table.rowCount():
            self.clear_criteria_details()
            return
        
        # Get the group data from the first column
        make_type_item = self.criteria_table.item(row, 0)
        if not make_type_item:
            self.clear_criteria_details()
            return
        
        # Get the group data stored in the item
        group_data = make_type_item.data(Qt.ItemDataRole.UserRole)
        if not group_data:
            self.clear_criteria_details()
            return
        
        # Open the detail dialog to show individual equipment in this group
        detail_dialog = GroupDetailDialog(group_data, self)
        detail_dialog.exec_()
        
        # Disable quick edit fields for grouped data (Edit/Delete now in dialog)
        self.save_criteria_button.setEnabled(False)  # Not used for group operations
        if hasattr(self, 'edit_years_input'):
            self.edit_years_input.setEnabled(False)  # Quick edit disabled for groups
        if hasattr(self, 'edit_kms_input'):
            self.edit_kms_input.setEnabled(False)  # Quick edit disabled for groups
        if hasattr(self, 'edit_hours_input'):
            self.edit_hours_input.setEnabled(False)  # Quick edit disabled for groups
        
        # Enable demand button for groups (all equipment in group meets discard criteria)
        self.create_demand_button.setEnabled(True)
        if hasattr(self, 'discard_button'):
            self.discard_button.setEnabled(True)  
        
        # Update the details panel to show group summary information
        make_and_type = group_data.get('make_and_type', 'Unknown')
        count = group_data.get('count', 0)
        equipment_list = group_data.get('equipment_list', [])
        
        # Show group summary in details panel
        self.detail_make_type.setText(f"{make_and_type} (Group of {count})")
        
        # Show date range
        date_range = group_data.get('date_range', {})
        if date_range.get('min_date') and date_range.get('max_date'):
            if date_range['min_date'] == date_range['max_date']:
                date_text = date_range['min_date'].strftime('%d/%m/%Y')
            else:
                date_text = f"{date_range['min_date'].strftime('%d/%m/%Y')} - {date_range['max_date'].strftime('%d/%m/%Y')}"
        else:
            date_text = "--"
        self.detail_date_commission.setText(date_text)
        
        # Show group vintage range
        vintage_values = [eq.get('vintage_years', 0) for eq in equipment_list]
        if vintage_values:
            min_vintage = min(vintage_values)
            max_vintage = max(vintage_values)
            if min_vintage == max_vintage:
                vintage_text = f"{utils.format_decimal(min_vintage, 1)} years"
            else:
                vintage_text = f"{utils.format_decimal(min_vintage, 1)} - {utils.format_decimal(max_vintage, 1)} years"
        else:
            vintage_text = "--"
        self.detail_vintage.setText(vintage_text)
        
        # Show group meterage range
        meterage_values = [eq.get('meterage_kms', 0) for eq in equipment_list]
        if meterage_values:
            min_meterage = min(meterage_values)
            max_meterage = max(meterage_values)
            if min_meterage == max_meterage:
                meterage_text = f"{utils.format_decimal(min_meterage, 0)} KMs"
            else:
                meterage_text = f"{utils.format_decimal(min_meterage, 0)} - {utils.format_decimal(max_meterage, 0)} KMs"
        else:
            meterage_text = "--"
        self.detail_meterage.setText(meterage_text)
        
        # Show group status summary
        status_summary = group_data.get('status_summary', {})
        summary_text = []
        if 'policy_discard' in status_summary:
            summary_text.append(f"{status_summary['policy_discard']} Policy Discard")
        if 'auto_discard' in status_summary:
            summary_text.append(f"{status_summary['auto_discard']} Overhaul Discard")
        if 'discard' in status_summary:
            summary_text.append(f"{status_summary['discard']} Manual Discard")
        
        status_text = ", ".join(summary_text) if summary_text else "Unknown"
        status = "critical"  # All grouped items meet discard criteria
        
        # Show criteria summary
        criteria_summary = group_data.get('criteria_summary', set())
        criteria_text = ", ".join(sorted(criteria_summary)) if criteria_summary else "--"
        if hasattr(self, 'criteria_years_label'):
            self.criteria_years_label.setText(criteria_text)
        if hasattr(self, 'criteria_kms_label'):
            self.criteria_kms_label.setText("See individual details")
        if hasattr(self, 'auto_criteria_label'):
            self.auto_criteria_label.setText("--")
        
        self.detail_status.setText(status_text)
        self.detail_status.setStatus(status)
        
        # Clear the edit fields since we're showing grouped data
        if hasattr(self, 'edit_years_input'):
            self.edit_years_input.setText("")
        if hasattr(self, 'edit_kms_input'):
            self.edit_kms_input.setText("")
        if hasattr(self, 'edit_hours_input'):
            self.edit_hours_input.setText("")
    
    def clear_criteria_details(self):
        """Clear criteria details."""
        # BA Number field is hidden, so no need to clear it
        # self.detail_ba_number.setText("--")  # Hidden
        self.detail_make_type.setText("--")
        self.detail_date_commission.setText("--")
        self.detail_vintage.setText("--")
        self.detail_meterage.setText("--")
        self.detail_status.setText("--")
        self.detail_status.setStatus("normal")
        
        self.criteria_years_label.setText("--")
        self.criteria_kms_label.setText("--")
        self.auto_criteria_label.setText("--")
        
        self.edit_years_input.setText("")
        self.edit_kms_input.setText("")
        self.edit_hours_input.setText("")
        self.edit_years_input.setEnabled(False)
        self.edit_kms_input.setEnabled(False)
        self.edit_hours_input.setEnabled(False)
        
        self.create_demand_button.setEnabled(False)
        self.save_criteria_button.setEnabled(False)
        if hasattr(self, 'discard_button'):
            self.discard_button.setEnabled(False)
    
    def save_criteria_changes(self):
        """Save criteria changes from quick edit."""
        selected_row = self.criteria_table.currentRow()
        if selected_row < 0:
            return
        
        criteria_id = self.criteria_table.item(selected_row, 0).data(Qt.UserRole)
        equipment_id = self.criteria_table.item(selected_row, 0).data(Qt.UserRole + 1)
        
        try:
            years_text = self.edit_years_input.text().strip()
            kms_text = self.edit_kms_input.text().strip()
            hours_text = self.edit_hours_input.text().strip()
            
            years = int(years_text) if years_text else 0
            kms = int(kms_text) if kms_text else 0
            hours = int(hours_text) if hours_text else 0
            
            if years == 0 and kms == 0 and hours == 0:
                QMessageBox.warning(self, "Validation Error", 
                                  "At least one criteria (Years, KMs, or Hours) must be specified.")
                return
            
            criteria_model = models.DiscardCriteria(
                discard_criteria_id=criteria_id,
                equipment_id=equipment_id,
                criteria_years=years,
                criteria_kms=kms,
                criteria_hours=hours
            )
            
            result = criteria_model.save()
            
            if result:
                self.load_data()
                
                for row in range(self.criteria_table.rowCount()):
                    item = self.criteria_table.item(row, 0)
                    if item and item.data(Qt.UserRole) == criteria_id:
                        self.criteria_table.selectRow(row)
                        self.criteria_selected(row)
                        break
                
                QMessageBox.information(self, "Success", 
                                      "Discard criteria updated successfully.")
            else:
                QMessageBox.critical(self, "Error", 
                                   "Failed to update discard criteria.")
                
        except ValueError:
            QMessageBox.warning(self, "Validation Error", 
                              "Please enter valid numbers for Years and KMs.")
        except Exception as e:
            QMessageBox.critical(self, "Error", 
                               f"Error updating discard criteria: {str(e)}")
    
    def add_criteria(self):
        """Add new discard criteria."""
        equipment_list = models.Equipment.get_active()
        
        dialog = DiscardCriteriaDialog(equipment_list=equipment_list, parent=self)
        if dialog.exec_():
            criteria_data = dialog.get_criteria_data()
            
            criteria = models.DiscardCriteria(
                equipment_id=criteria_data.get('EquipmentID'),
                criteria_years=criteria_data.get('CriteriaYears'),
                criteria_kms=criteria_data.get('CriteriaKMs'),
                criteria_hours=criteria_data.get('CriteriaHours')
            )
            
            try:
                criteria.save()
                self.load_data()
                QMessageBox.information(self, "Success", 
                                      "Discard criteria added successfully.")
            except Exception as e:
                QMessageBox.critical(self, "Error", 
                                   f"Error adding discard criteria: {str(e)}")
    
    def edit_criteria(self):
        """Edit discard criteria for selected equipment group."""
        selected_row = self.criteria_table.currentRow()
        if selected_row < 0:
            return
        
        # Get the group data from the selected row
        make_type_item = self.criteria_table.item(selected_row, 0)
        if not make_type_item:
            return
        
        group_data = make_type_item.data(Qt.ItemDataRole.UserRole)
        if not group_data:
            return
        
        # Get equipment list from the group
        equipment_list = group_data.get('equipment_list', [])
        if not equipment_list:
            QMessageBox.warning(self, "No Equipment", "No equipment found in selected group.")
            return
        
        make_and_type = group_data.get('make_and_type', 'Unknown')
        count = len(equipment_list)
        
        # Show confirmation dialog for group editing
        confirm = QMessageBox.question(
            self, "Edit Group Criteria", 
            f"Do you want to edit discard criteria for all {count} equipment in the group:\n\n"
            f"Make and Type: {make_and_type}\n\n"
            f"This will open the criteria editor for the first equipment. "
            f"You can apply the same criteria to all equipment in the group.",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes
        )
        
        if confirm != QMessageBox.Yes:
            return
        
        # Get the first equipment that has criteria or create for first equipment
        first_equipment = equipment_list[0]
        equipment_id = first_equipment.get('equipment_id')
        
        # Try to find existing criteria for this equipment
        existing_criteria = None
        for eq in equipment_list:
            criteria_id = eq.get('criteria_id')
            if criteria_id:
                existing_criteria = models.DiscardCriteria.get_by_id(criteria_id)
                if existing_criteria:
                    break
        
        # If no existing criteria, create a template
        if not existing_criteria:
            existing_criteria = {
                'equipment_id': equipment_id,
                'criteria_years': 0,
                'criteria_kms': 0,
                'criteria_hours': 0
            }
        
        all_equipment = models.Equipment.get_active()
        
        dialog = DiscardCriteriaDialog(existing_criteria, all_equipment, parent=self)
        if dialog.exec_():
            criteria_data = dialog.get_criteria_data()
            
            # Apply criteria to all equipment in the group
            success_count = 0
            error_count = 0
            
            for equipment in equipment_list:
                try:
                    eq_id = equipment.get('equipment_id')
                    existing_criteria_id = equipment.get('criteria_id')
                    
                    criteria_model = models.DiscardCriteria(
                        discard_criteria_id=existing_criteria_id,  # Use existing ID if available
                        equipment_id=eq_id,
                        criteria_years=criteria_data.get('CriteriaYears'),
                        criteria_kms=criteria_data.get('CriteriaKMs'),
                        criteria_hours=criteria_data.get('CriteriaHours')
                    )
                    
                    result = criteria_model.save()
                    if result:
                        success_count += 1
                    else:
                        error_count += 1
                        
                except Exception as e:
                    logging.error(f"Error saving criteria for equipment {eq_id}: {e}")
                    error_count += 1
            
            # Reload data and show results
            self.load_data()
            
            if error_count == 0:
                QMessageBox.information(self, "Success", 
                                      f"Discard criteria updated successfully for all {success_count} equipment in the group.")
            else:
                QMessageBox.warning(self, "Partial Success", 
                                  f"Updated criteria for {success_count} equipment.\n"
                                  f"Failed to update {error_count} equipment.\n"
                                  f"Check the log for details.")
                
            # Reselect the group
            for row in range(self.criteria_table.rowCount()):
                item = self.criteria_table.item(row, 0)
                if item:
                    row_group_data = item.data(Qt.ItemDataRole.UserRole)
                    if row_group_data and row_group_data.get('make_and_type') == make_and_type:
                        self.criteria_table.selectRow(row)
                        self.criteria_selected(row)
                        break
    
    def delete_criteria(self):
        """Delete discard criteria for selected equipment group."""
        selected_row = self.criteria_table.currentRow()
        if selected_row < 0:
            return
        
        # Get the group data from the selected row
        make_type_item = self.criteria_table.item(selected_row, 0)
        if not make_type_item:
            return
        
        group_data = make_type_item.data(Qt.ItemDataRole.UserRole)
        if not group_data:
            return
        
        # Get equipment list from the group
        equipment_list = group_data.get('equipment_list', [])
        if not equipment_list:
            QMessageBox.warning(self, "No Equipment", "No equipment found in selected group.")
            return
        
        make_and_type = group_data.get('make_and_type', 'Unknown')
        count = len(equipment_list)
        
        # Count how many equipment have criteria to delete
        criteria_count = sum(1 for eq in equipment_list if eq.get('criteria_id'))
        
        if criteria_count == 0:
            QMessageBox.information(self, "No Criteria", 
                                  f"No discard criteria found for equipment in the group:\n{make_and_type}")
            return
        
        # Show confirmation dialog for group deletion
        confirm = QMessageBox.question(
            self, "Confirm Group Deletion", 
            f"Are you sure you want to delete discard criteria for the group:\n\n"
            f"Make and Type: {make_and_type}\n"
            f"Total Equipment: {count}\n"
            f"Equipment with Criteria: {criteria_count}\n\n"
            f"This will remove custom discard criteria for all equipment in this group.\n"
            f"Equipment may still appear if they meet automatic policy criteria.",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if confirm != QMessageBox.Yes:
            return
        
        # Delete criteria for all equipment in the group
        success_count = 0
        error_count = 0
        
        try:
            for equipment in equipment_list:
                criteria_id = equipment.get('criteria_id')
                if criteria_id:
                    try:
                        models.DiscardCriteria.delete(criteria_id)
                        success_count += 1
                    except Exception as e:
                        logging.error(f"Error deleting criteria {criteria_id}: {e}")
                        error_count += 1
            
            # Reload data
            self.load_data()
            
            if error_count == 0:
                QMessageBox.information(self, "Success", 
                                      f"Discard criteria deleted successfully for {success_count} equipment.")
            else:
                QMessageBox.warning(self, "Partial Success", 
                                  f"Deleted criteria for {success_count} equipment.\n"
                                  f"Failed to delete {error_count} criteria.\n"
                                  f"Check the log for details.")
                
        except Exception as e:
            QMessageBox.critical(self, "Error", 
                               f"Error deleting discard criteria: {str(e)}")
    
    def create_demand(self):
        """Create equipment demand forecast for discarded equipment."""
        selected_row = self.criteria_table.currentRow()
        if selected_row < 0:
            return
        
        equipment_id = self.criteria_table.item(selected_row, 0).data(Qt.UserRole + 1)
        
        equipment = models.Equipment.get_by_id(equipment_id)
        if not equipment:
            QMessageBox.critical(self, "Error", "Equipment not found.")
            return
        
        try:
            current_year = datetime.now().year
            fiscal_year = f"{current_year}-{current_year + 1}"
            
            forecast = models.EquipmentForecast(
                equipment_id=equipment_id,
                fiscal_year=fiscal_year,
                equipment_type=getattr(equipment, 'make_and_type', ''),
                replacement_reason="Discarded - Meeting Discard Criteria",
                quantity_required=1,
                total_requirement=1.0,
                remarks="Equipment no longer in service"
            )
            
            forecast_id = forecast.save()
            
            if forecast_id:
                ba_number = getattr(equipment, 'ba_number', '--')
                make_type = getattr(equipment, 'make_and_type', '--')
                
                result = QMessageBox.question(
                    self, "Demand Created Successfully", 
                    f"Equipment demand forecast has been created for:\n\n"
                    f"BA Number: {ba_number}\n"
                    f"Equipment: {make_type}\n"
                    f"Fiscal Year: {fiscal_year}\n\n"
                    f"Would you like to view the Equipment Forecast tab?",
                    QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes
                )
                
                if result == QMessageBox.Yes and self.main_window:
                    # Navigate to demand forecast tab
                    for i in range(self.main_window.tab_widget.count()):
                        widget = self.main_window.tab_widget.widget(i)
                        if hasattr(widget, '__class__') and 'DemandForecast' in widget.__class__.__name__:
                            self.main_window.tab_widget.setCurrentIndex(i)
                            if hasattr(widget, 'tab_widget'):
                                for j in range(widget.tab_widget.count()):
                                    if 'Equipment' in widget.tab_widget.tabText(j):
                                        widget.tab_widget.setCurrentIndex(j)
                                        break
                            break
            else:
                QMessageBox.critical(self, "Error", "Failed to create equipment demand forecast.")
                
        except Exception as e:
            QMessageBox.critical(self, "Error", 
                               f"Error creating demand forecast: {str(e)}")
    
    def open_policy_management(self):
        """Open the comprehensive policy management dialog."""
        try:
            from ui.policy_management_dialog import PolicyManagementDialog
            
            # Open policy management dashboard
            dialog = PolicyManagementDialog(parent=self)
            result = dialog.exec_()
            
            if result == QDialog.Accepted:
                logging.info("Policy management dialog closed with Accept - refreshing discard criteria data")
                
                # Force update any discard criteria that might be affected by policy changes
                try:
                    logging.info("Forcing update of discard criteria after policy changes")
                    import policy_service
                    policy_service.clear_policy_cache()
                    
                    # Force refresh of all criteria
                    import overhaul_service
                    overhaul_service.check_and_create_discard_criteria(force_update=True)
                    overhaul_service.auto_populate_discard_criteria_from_overhaul_status(force_update=True)
                except Exception as e:
                    logging.error(f"Error updating criteria after policy change: {e}")
                
                # Reload data with forced policy refresh
                self.load_data(force_refresh=True)
                
                # Show success message
                QMessageBox.information(self, "Success", "Policy operations completed successfully.")
                
        except Exception as e:
            logging.error(f"Error opening policy management: {e}")
            QMessageBox.critical(self, "Error", f"Failed to open policy management: {e}")
            
        # Final forced reload to ensure everything is in sync
        self.load_data(force_refresh=True)
    
    def refresh_on_overhaul_change(self, equipment_id):
        """Callback for overhaul status changes.
        
        This method is called by the overhaul_service when the status of an overhaul changes,
        which might affect discard criteria or policy evaluation.
        
        Args:
            equipment_id: The ID of the equipment whose overhaul status changed
        """
        try:
            logging.info(f"Received notification of overhaul status change for equipment {equipment_id}")
            
            # Ensure the overhaul-based discard criteria is up to date
            import overhaul_service
            overhaul_service.check_and_create_discard_criteria(equipment_id, force_update=True)
            
            # Only reload the widget data if it's visible to avoid unnecessary processing
            if self.isVisible():
                logging.info("Discard criteria widget is visible, refreshing data")
                # Use Qt's queued connection to avoid refreshing during another operation
                QTimer.singleShot(100, lambda: self.load_data(force_refresh=True))
            else:
                logging.info("Discard criteria widget is not visible, deferring refresh")
        except Exception as e:
            logging.error(f"Error refreshing discard criteria on overhaul change: {e}")
    
    def mark_equipment_discarded(self):
        """Mark selected equipment as discarded."""
        selected_row = self.criteria_table.currentRow()
        if selected_row < 0:
            QMessageBox.warning(self, "Warning", "Please select equipment to mark as discarded.")
            return
        
        equipment_id = self.criteria_table.item(selected_row, 0).data(Qt.UserRole + 1)

        # Get equipment details for confirmation
        equipment = models.Equipment.get_by_id(equipment_id)
        if not equipment:
            QMessageBox.critical(self, "Error", "Equipment not found.")
            return

        ba_number = getattr(equipment, 'ba_number', '--')
        make_type = getattr(equipment, 'make_and_type', '--')

        # Confirm discard action
        confirm = QMessageBox.question(
            self, "Confirm Equipment Discard",
            f"Are you sure you want to mark this equipment as DISCARDED?\n\n"
            f"BA Number: {ba_number}\n"
            f"Equipment: {make_type}\n\n"
            f"This action will:\n"
            f"• Remove equipment from overhaul tables\n"
            f"• Mark equipment status as 'discarded'\n"
            f"• Equipment will only appear in discard criteria tab\n\n"
            f"This action cannot be easily undone.",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if confirm != QMessageBox.Yes:
            return

        try:
            from datetime import date
            discard_date = date.today().isoformat()
            discard_reason = "Meeting discard criteria - manually discarded"

            # Mark equipment as discarded
            success = models.Equipment.mark_as_discarded(equipment_id, discard_date, discard_reason)

            if success:
                # Refresh the data to reflect changes
                self.load_data()

                # Notify other tabs to refresh their data
                if hasattr(self, 'main_window') and self.main_window:
                    # Refresh overhaul tab to remove discarded equipment
                    for i in range(self.main_window.tab_widget.count()):
                        widget = self.main_window.tab_widget.widget(i)
                        if hasattr(widget, 'load_data') and 'repairs' in widget.__class__.__name__.lower():
                            widget.load_data()
                            break

                QMessageBox.information(
                    self, "Equipment Discarded Successfully",
                    f"Equipment has been marked as discarded:\n\n"
                    f"BA Number: {ba_number}\n"
                    f"Equipment: {make_type}\n\n"
                    f"The equipment will no longer appear in overhaul tables."
                )
            else:
                QMessageBox.critical(self, "Error", "Failed to mark equipment as discarded.")

        except Exception as e:
            QMessageBox.critical(self, "Error",
                               f"Error marking equipment as discarded: {str(e)}")

    def select_discard_criteria(self, criteria_id):
        """Select discard criteria with the given ID."""
        for row in range(self.criteria_table.rowCount()):
            item = self.criteria_table.item(row, 0)
            if item and item.data(Qt.UserRole) == criteria_id:
                self.criteria_table.selectRow(row)
                self.criteria_selected(row)
                break
    
    def update_equipment_details_with_auto_info(self, equipment_data):
        """Update equipment details including automatic criteria information."""
        # Get automatic criteria rules for this equipment
        auto_rules = match_equipment_to_criteria(equipment_data.get('make_and_type', ''))
        
        if auto_rules:
            auto_text = ""
            if auto_rules.get('years'):
                auto_text += f"{auto_rules['years']} years"
            if auto_rules.get('kms'):
                if auto_text:
                    auto_text += ", "
                auto_text += f"{auto_rules['kms']:,} km"
            if auto_rules.get('hours'):
                if auto_text:
                    auto_text += ", "
                auto_text += f"{auto_rules['hours']:,} hours"
            
            self.auto_criteria_label.setText(auto_text)
            self.auto_criteria_label.setToolTip(f"Automatic rules for {equipment_data.get('make_and_type', '')}")
        else:
            self.auto_criteria_label.setText("No automatic rules found")
            self.auto_criteria_label.setToolTip("No automatic discard criteria rules match this equipment type")

class GroupDetailDialog(QDialog):
    """Dialog to show details of individual equipment in a selected group."""
    
    def __init__(self, group_data, parent=None):
        super().__init__(parent)
        self.group_data = group_data
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the detail dialog UI."""
        from ui.window_utils import DPIScaler, DialogManager
        
        # Get the group's make and type
        make_and_type = self.group_data.get('make_and_type', 'Unknown')
        equipment_list = self.group_data.get('equipment_list', [])
        count = len(equipment_list)
        
        self.setWindowTitle(f"{make_and_type} - Individual Vehicles ({count} vehicles)")
        self.setModal(True)
        
        # Set responsive dialog size
        dialog_width, dialog_height = DPIScaler.get_responsive_dialog_size(900, 600)
        self.resize(dialog_width, dialog_height)
        
        # Main layout
        layout = QVBoxLayout(self)
        
        # Header info
        header_layout = QHBoxLayout()
        
        info_label = QLabel(f"<b>{make_and_type}</b> - {count} vehicle(s) meeting discard criteria")
        info_label.setFont(DPIScaler.create_scaled_font(12, bold=True))
        header_layout.addWidget(info_label)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Equipment details table
        self.detail_table = ReadOnlyTableWidget()
        self.detail_table.setColumnCount(6)
        self.detail_table.setHorizontalHeaderLabels([
            "BA Number", "Commission Date", "Status", "Years", "KMs", "Discard Reason"
        ])
        self.detail_table.verticalHeader().setVisible(False)
        self.detail_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.detail_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.detail_table.setSortingEnabled(True)
        
        # Set responsive column widths
        base_widths = [100, 120, 180, 80, 80, 300]  # BA Number, Commission Date, Status, Years, KMs, Discard Reason
        self.detail_table.set_responsive_column_widths(base_widths)
        
        # Populate the table with equipment data
        self.populate_detail_table(equipment_list)
        
        layout.addWidget(self.detail_table)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        # Edit button
        self.edit_button = QPushButton("Edit Criteria")
        self.edit_button.clicked.connect(self.edit_selected_criteria)
        self.edit_button.setEnabled(False)  # Enabled when row is selected
        apply_button_style(self.edit_button, "primary")
        button_layout.addWidget(self.edit_button)
        
        # Delete button
        self.delete_button = QPushButton("Delete Criteria")
        self.delete_button.clicked.connect(self.delete_selected_criteria)
        self.delete_button.setEnabled(False)  # Enabled when row is selected
        apply_button_style(self.delete_button, "danger")
        button_layout.addWidget(self.delete_button)
        
        button_layout.addStretch()
        
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.accept)
        apply_button_style(close_button, "default")
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        
        # Connect table selection to enable/disable buttons
        self.detail_table.itemSelectionChanged.connect(self.on_selection_changed)
        
    def populate_detail_table(self, equipment_list):
        """Populate the detail table with individual equipment data."""
        self.detail_table.setRowCount(len(equipment_list))
        
        for row, equipment in enumerate(equipment_list):
            # BA Number
            ba_number = equipment.get('ba_number', '--')
            self.detail_table.setItem(row, 0, QTableWidgetItem(str(ba_number)))
            
            # Commission Date
            date_commission = equipment.get('date_of_commission') or equipment.get('date_of_induction')
            if date_commission:
                try:
                    if isinstance(date_commission, str):
                        date_obj = datetime.strptime(date_commission, '%Y-%m-%d')
                        formatted_date = date_obj.strftime('%d/%m/%Y')
                    else:
                        formatted_date = str(date_commission)
                except:
                    formatted_date = str(date_commission)
            else:
                formatted_date = '--'
            self.detail_table.setItem(row, 1, QTableWidgetItem(formatted_date))
            
            # Status (use the status_text from the equipment data)
            status_text = equipment.get('status_text', 'Unknown')
            status_color = equipment.get('status_color', QColor("#27ae60"))
            status_item = QTableWidgetItem(status_text)
            status_item.setForeground(status_color)
            if equipment.get('status_tooltip'):
                status_item.setToolTip(equipment.get('status_tooltip'))
            self.detail_table.setItem(row, 2, status_item)
            
            # Years
            current_vintage = equipment.get('vintage_years', 0)
            self.detail_table.setItem(row, 3, QTableWidgetItem(f"{current_vintage:.1f}"))
            
            # KMs
            current_meterage = equipment.get('meterage_kms', 0)
            self.detail_table.setItem(row, 4, QTableWidgetItem(f"{current_meterage:.0f}"))
            
            # Discard Reason
            reason = equipment.get('status_tooltip', '--')
            # Clean up tooltip text for display in table
            if reason and reason != '--':
                # Remove extra whitespace and make more compact
                reason = reason.replace('\n\n', ' | ').replace('\n', ' | ')
                if len(reason) > 100:
                    reason = reason[:97] + '...'
            self.detail_table.setItem(row, 5, QTableWidgetItem(reason))
        
        # Resize columns to content
        self.detail_table.resizeColumnsToContents()
        
    def on_selection_changed(self):
        """Handle table selection change to enable/disable buttons."""
        has_selection = len(self.detail_table.selectedItems()) > 0
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
        
    def get_selected_equipment(self):
        """Get the selected equipment data from the table."""
        current_row = self.detail_table.currentRow()
        if current_row < 0 or current_row >= len(self.group_data.get('equipment_list', [])):
            return None
        return self.group_data['equipment_list'][current_row]
        
    def edit_selected_criteria(self):
        """Edit the selected equipment's discard criteria."""
        equipment = self.get_selected_equipment()
        if not equipment:
            QMessageBox.warning(self, "No Selection", "Please select an equipment to edit.")
            return
            
        equipment_id = equipment.get('equipment_id')
        criteria_id = equipment.get('discard_criteria_id')
        
        if not criteria_id:
            QMessageBox.warning(self, "No Criteria", "No discard criteria found for this equipment.")
            return
            
        # Get the criteria data
        criteria = models.DiscardCriteria.get_by_id(criteria_id)
        if not criteria:
            QMessageBox.warning(self, "Error", "Could not load criteria data.")
            return
            
        # Get equipment list for dialog
        equipment_list = models.Equipment.get_active()
        
        # Open the edit dialog
        dialog = DiscardCriteriaDialog(criteria, equipment_list, parent=self)
        if dialog.exec_():
            criteria_data = dialog.get_criteria_data()
            
            # Update the criteria
            criteria_model = models.DiscardCriteria(
                discard_criteria_id=criteria_id,
                equipment_id=criteria_data.get('EquipmentID'),
                criteria_years=criteria_data.get('CriteriaYears'),
                criteria_kms=criteria_data.get('CriteriaKMs'),
                criteria_hours=criteria_data.get('CriteriaHours')
            )
            
            result = criteria_model.save()
            
            if result:
                QMessageBox.information(self, "Success", "Discard criteria updated successfully.")
                # Refresh the parent widget
                if hasattr(self.parent(), 'load_data'):
                    self.parent().load_data()
                
                # Refresh the dialog data to show updated information
                self.refresh_dialog_data()
            else:
                QMessageBox.critical(self, "Error", "Failed to update discard criteria.")
                
    def delete_selected_criteria(self):
        """Delete the selected equipment's discard criteria."""
        equipment = self.get_selected_equipment()
        if not equipment:
            QMessageBox.warning(self, "No Selection", "Please select an equipment to delete.")
            return
            
        criteria_id = equipment.get('discard_criteria_id')
        ba_number = equipment.get('ba_number', 'Unknown')
        make_type = equipment.get('make_and_type', 'Unknown')
        
        if not criteria_id:
            QMessageBox.warning(self, "No Criteria", "No discard criteria found for this equipment.")
            return
            
        # Confirm deletion
        confirm = QMessageBox.question(
            self, "Confirm Deletion", 
            f"Are you sure you want to delete discard criteria for:\\n\\n"
            f"BA Number: {ba_number}\\n"
            f"Equipment: {make_type}",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if confirm != QMessageBox.Yes:
            return
            
        # Delete the criteria
        try:
            success = models.DiscardCriteria.delete(criteria_id)
            
            if success:
                QMessageBox.information(self, "Success", "Discard criteria deleted successfully.")
                # Refresh the parent widget
                if hasattr(self.parent(), 'load_data'):
                    self.parent().load_data()
                
                # Remove the deleted equipment from our local group data and refresh the dialog
                self.refresh_dialog_data()
            else:
                QMessageBox.critical(self, "Error", "Failed to delete discard criteria.")
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error deleting discard criteria: {str(e)}")
            
    def refresh_dialog_data(self):
        """Refresh the dialog data by fetching updated group information from the parent."""
        try:
            # Get the updated group data from the parent widget
            if hasattr(self.parent(), 'criteria_table'):
                parent_widget = self.parent()
                
                # Find the current group in the parent's table data
                make_and_type = self.group_data.get('make_and_type', 'Unknown')
                
                # Look through the parent's table to find the updated group data
                for row in range(parent_widget.criteria_table.rowCount()):
                    make_type_item = parent_widget.criteria_table.item(row, 0)
                    if make_type_item and make_type_item.text() == make_and_type:
                        updated_group_data = make_type_item.data(Qt.ItemDataRole.UserRole)
                        if updated_group_data:
                            self.group_data = updated_group_data
                            break
                
                # Check if the group still has equipment
                equipment_list = self.group_data.get('equipment_list', [])
                if not equipment_list:
                    # No equipment left in this group, close the dialog
                    QMessageBox.information(self, "Group Empty", 
                                          f"No equipment remaining in group '{make_and_type}'. Closing dialog.")
                    self.accept()
                    return
                
                # Update dialog title and header
                count = len(equipment_list)
                self.setWindowTitle(f"{make_and_type} - Individual Vehicles ({count} vehicles)")
                
                # Find and update the header info label
                for child in self.findChildren(QLabel):
                    if make_and_type in child.text() and "vehicle(s) meeting discard criteria" in child.text():
                        child.setText(f"<b>{make_and_type}</b> - {count} vehicle(s) meeting discard criteria")
                        break
                
                # Repopulate the table with updated data
                self.populate_detail_table(equipment_list)
                
                # Clear selection and disable buttons
                self.detail_table.clearSelection()
                self.edit_button.setEnabled(False)
                self.delete_button.setEnabled(False)
                
        except Exception as e:
            QMessageBox.warning(self, "Refresh Error", 
                              f"Could not refresh dialog data: {str(e)}")
            # If refresh fails, just close the dialog
            self.accept()