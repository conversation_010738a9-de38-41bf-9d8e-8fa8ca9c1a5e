#!/usr/bin/env python3
"""
Comprehensive test to verify that the dashboard properly aggregates 
overdue counts from both OH-I and OH-II overhaul types.
"""

import sys
import os
sys.path.append('.')

import database
import utils
from models import Overhaul, Equipment
from datetime import date, datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_overhaul_type_breakdown():
    """Test breakdown of overhauls by type and status."""
    print('Testing Overhaul Type and Status Breakdown')
    print('=' * 60)
    
    try:
        overhauls = Overhaul.get_all()
        
        # Initialize counters
        oh1_counts = {'scheduled': 0, 'warning': 0, 'critical': 0, 'overdue': 0, 'completed': 0, 'cancelled': 0, 'discard': 0, 'unknown': 0}
        oh2_counts = {'scheduled': 0, 'warning': 0, 'critical': 0, 'overdue': 0, 'completed': 0, 'cancelled': 0, 'discard': 0, 'unknown': 0}
        other_counts = {'scheduled': 0, 'warning': 0, 'critical': 0, 'overdue': 0, 'completed': 0, 'cancelled': 0, 'discard': 0, 'unknown': 0}
        
        print(f"Total overhauls found: {len(overhauls) if overhauls else 0}")
        
        for oh in overhauls:
            equipment_id = oh.get('equipment_id')
            equipment = Equipment.get_by_id(equipment_id) if equipment_id else None
            overhaul_type = oh.get('overhaul_type', 'Unknown')
            
            # Calculate status using centralized logic
            status = utils.calculate_overhaul_status(
                oh.get('overhaul_type'),
                oh.get('due_date'),
                oh.get('done_date'),
                equipment.date_of_commission if equipment else None,
                None,  # oh1_done_date
                None,  # custom_intervals
                equipment.meterage_kms if equipment else None
            )
            
            # Categorize by overhaul type
            if overhaul_type == 'OH-I':
                if status in oh1_counts:
                    oh1_counts[status] += 1
                else:
                    oh1_counts['unknown'] += 1
            elif overhaul_type == 'OH-II':
                if status in oh2_counts:
                    oh2_counts[status] += 1
                else:
                    oh2_counts['unknown'] += 1
            else:
                if status in other_counts:
                    other_counts[status] += 1
                else:
                    other_counts['unknown'] += 1
        
        # Display results
        print("\nOH-I (First Overhaul) Status Breakdown:")
        total_oh1 = sum(oh1_counts.values())
        for status, count in oh1_counts.items():
            if count > 0:
                print(f"  {status.title()}: {count}")
        print(f"  Total OH-I: {total_oh1}")
        
        print("\nOH-II (Second Overhaul) Status Breakdown:")
        total_oh2 = sum(oh2_counts.values())
        for status, count in oh2_counts.items():
            if count > 0:
                print(f"  {status.title()}: {count}")
        print(f"  Total OH-II: {total_oh2}")
        
        if sum(other_counts.values()) > 0:
            print("\nOther Overhaul Types:")
            for status, count in other_counts.items():
                if count > 0:
                    print(f"  {status.title()}: {count}")
        
        # Calculate overdue totals
        oh1_overdue = oh1_counts['overdue']
        oh2_overdue = oh2_counts['overdue']
        other_overdue = other_counts['overdue']
        total_overdue = oh1_overdue + oh2_overdue + other_overdue
        
        print(f"\nOVERDUE SUMMARY:")
        print(f"  OH-I Overdue: {oh1_overdue}")
        print(f"  OH-II Overdue: {oh2_overdue}")
        if other_overdue > 0:
            print(f"  Other Types Overdue: {other_overdue}")
        print(f"  TOTAL OVERDUE: {total_overdue}")
        
        return {
            'oh1_overdue': oh1_overdue,
            'oh2_overdue': oh2_overdue,
            'other_overdue': other_overdue,
            'total_overdue': total_overdue,
            'oh1_counts': oh1_counts,
            'oh2_counts': oh2_counts
        }
        
    except Exception as e:
        logger.error(f"Error testing overhaul type breakdown: {e}")
        return None

def test_dashboard_aggregation():
    """Test that dashboard method aggregates both OH-I and OH-II overdue counts."""
    print('\nTesting Dashboard Aggregation Logic')
    print('=' * 50)
    
    try:
        # Simulate dashboard method
        overhauls = Overhaul.get_all()
        overdue_count = 0
        oh1_overdue = 0
        oh2_overdue = 0
        
        for oh in overhauls:
            equipment_id = oh.get('equipment_id')
            equipment = Equipment.get_by_id(equipment_id) if equipment_id else None
            overhaul_type = oh.get('overhaul_type')
            
            # Calculate status using centralized logic (same as dashboard)
            status = utils.calculate_overhaul_status(
                oh.get('overhaul_type'),
                oh.get('due_date'),
                oh.get('done_date'),
                equipment.date_of_commission if equipment else None,
                None,  # oh1_done_date
                None,  # custom_intervals
                equipment.meterage_kms if equipment else None
            )
            
            # Count only overdue overhauls
            if status == "overdue":
                overdue_count += 1
                if overhaul_type == 'OH-I':
                    oh1_overdue += 1
                elif overhaul_type == 'OH-II':
                    oh2_overdue += 1
        
        print(f"Dashboard Aggregation Results:")
        print(f"  OH-I Overdue: {oh1_overdue}")
        print(f"  OH-II Overdue: {oh2_overdue}")
        print(f"  Total Overdue (Dashboard): {overdue_count}")
        
        return {
            'dashboard_total': overdue_count,
            'dashboard_oh1': oh1_overdue,
            'dashboard_oh2': oh2_overdue
        }
        
    except Exception as e:
        logger.error(f"Error testing dashboard aggregation: {e}")
        return None

def test_individual_tab_simulation():
    """Simulate individual OH-I and OH-II tab counting."""
    print('\nTesting Individual Tab Simulation')
    print('=' * 40)
    
    try:
        overhauls = Overhaul.get_all()
        
        # Simulate OH-I tab
        oh1_overdue = 0
        oh1_overhauls = [oh for oh in overhauls if oh.get('overhaul_type') == 'OH-I']
        
        for oh in oh1_overhauls:
            equipment_id = oh.get('equipment_id')
            equipment = Equipment.get_by_id(equipment_id) if equipment_id else None
            
            status = utils.calculate_overhaul_status(
                'OH-I',
                oh.get('due_date'),
                oh.get('done_date'),
                equipment.date_of_commission if equipment else None,
                None,
                None,
                equipment.meterage_kms if equipment else None
            )
            
            if status == "overdue":
                oh1_overdue += 1
        
        # Simulate OH-II tab
        oh2_overdue = 0
        oh2_overhauls = [oh for oh in overhauls if oh.get('overhaul_type') == 'OH-II']
        
        for oh in oh2_overhauls:
            equipment_id = oh.get('equipment_id')
            equipment = Equipment.get_by_id(equipment_id) if equipment_id else None
            
            status = utils.calculate_overhaul_status(
                'OH-II',
                oh.get('due_date'),
                oh.get('done_date'),
                equipment.date_of_commission if equipment else None,
                None,
                None,
                equipment.meterage_kms if equipment else None
            )
            
            if status == "overdue":
                oh2_overdue += 1
        
        tab_total = oh1_overdue + oh2_overdue
        
        print(f"Individual Tab Results:")
        print(f"  OH-I Tab Overdue: {oh1_overdue}")
        print(f"  OH-II Tab Overdue: {oh2_overdue}")
        print(f"  Sum of Tabs: {tab_total}")
        
        return {
            'tab_oh1': oh1_overdue,
            'tab_oh2': oh2_overdue,
            'tab_total': tab_total
        }
        
    except Exception as e:
        logger.error(f"Error testing individual tab simulation: {e}")
        return None

def main():
    """Main test function."""
    print("Overhaul Aggregation Verification Test")
    print("=" * 70)
    
    # Test 1: Breakdown by type
    breakdown = test_overhaul_type_breakdown()
    
    # Test 2: Dashboard aggregation
    dashboard = test_dashboard_aggregation()
    
    # Test 3: Individual tab simulation
    tabs = test_individual_tab_simulation()
    
    # Verification
    print('\nVERIFICATION RESULTS')
    print('=' * 30)
    
    if breakdown and dashboard and tabs:
        breakdown_total = breakdown['total_overdue']
        dashboard_total = dashboard['dashboard_total']
        tab_total = tabs['tab_total']
        
        print(f"Breakdown Analysis Total: {breakdown_total}")
        print(f"Dashboard Method Total: {dashboard_total}")
        print(f"Individual Tabs Sum: {tab_total}")
        
        # Check consistency
        all_match = (breakdown_total == dashboard_total == tab_total)
        
        if all_match:
            print(f"✅ ALL METHODS CONSISTENT: {dashboard_total} overdue overhauls")
            print(f"✅ Dashboard properly aggregates OH-I and OH-II overdue counts")
        else:
            print(f"❌ INCONSISTENCY DETECTED:")
            print(f"   Breakdown: {breakdown_total}")
            print(f"   Dashboard: {dashboard_total}")
            print(f"   Tab Sum: {tab_total}")
        
        # Detailed breakdown
        print(f"\nDETAILED BREAKDOWN:")
        print(f"  OH-I Overdue: {breakdown['oh1_overdue']}")
        print(f"  OH-II Overdue: {breakdown['oh2_overdue']}")
        if breakdown['other_overdue'] > 0:
            print(f"  Other Types: {breakdown['other_overdue']}")
        
        return all_match
    else:
        print("❌ TEST FAILED: Unable to complete verification")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n🎉 SUCCESS: Dashboard correctly aggregates both OH-I and OH-II overdue counts!")
    else:
        print(f"\n⚠️ ISSUE: Dashboard aggregation needs attention.")
