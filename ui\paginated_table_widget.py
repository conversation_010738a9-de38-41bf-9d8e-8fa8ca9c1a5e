"""
Paginated Table Widget for PROJECT-ALPHA
Prevents memory explosion and UI freezing with large datasets
"""

import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLabel, QSpinBox,
                             QComboBox, QHeaderView, QAbstractItemView, QLineEdit)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QBrush

logger = logging.getLogger('paginated_table')

class PaginatedTableWidget(QWidget):
    """Table widget with pagination to handle large datasets safely."""
    
    # Signals
    row_selected = pyqtSignal(dict)  # Emitted when a row is selected
    data_changed = pyqtSignal()      # Emitted when data is modified
    
    def __init__(self, page_size=100, max_total_rows=50000, enable_ba_filter=True, enable_ba_grouping=True, show_vintage_button=False):
        """
        Initialize paginated table widget.

        Args:
            page_size: Number of rows per page (default: 100)
            max_total_rows: Maximum total rows to prevent memory issues (default: 50,000)
            enable_ba_filter: Whether to show BA number filter (default: True)
            enable_ba_grouping: Whether to enable BA number visual grouping (default: True)
            show_vintage_button: Whether to show vintage age button (default: False, only for equipment table)
        """
        super().__init__()
        self.page_size = page_size
        self.max_total_rows = max_total_rows
        self.enable_ba_filter = enable_ba_filter
        self.enable_ba_grouping = enable_ba_grouping
        self.show_vintage_button = show_vintage_button
        self.current_page = 0
        self.total_pages = 0
        self.total_rows = 0
        self.all_data = []  # Store all data
        self.filtered_data = []  # Store filtered data
        self.headers = []
        self.id_column = None
        self.ba_column = None  # Track BA number column
        self.ba_column_index = None  # Track BA number column index
        self.ba_groups = {}  # Track BA number groups for styling

        self.setup_ui()
        self.setup_table()
    
    def setup_ui(self):
        """Setup the user interface."""
        layout = QVBoxLayout(self)

        # Add BA number filter if enabled
        if self.enable_ba_filter:
            filter_layout = QHBoxLayout()

            # Make & Type filter (existing pattern)
            filter_layout.addWidget(QLabel("Make & Type:"))
            self.make_type_filter = QComboBox()
            self.make_type_filter.addItem("All Make & Types")
            self.make_type_filter.setMinimumWidth(200)
            self.make_type_filter.setMaximumWidth(350)
            self.make_type_filter.setMaxVisibleItems(8)
            self.make_type_filter.currentTextChanged.connect(self.apply_filters)
            # Apply styling fix for dropdown
            from ui.common_styles import apply_combobox_hover_fix
            apply_combobox_hover_fix(self.make_type_filter)
            filter_layout.addWidget(self.make_type_filter)

            # BA Number filter (new)
            filter_layout.addWidget(QLabel("BA Number:"))
            self.ba_filter = QComboBox()
            self.ba_filter.addItem("All BA Numbers")
            self.ba_filter.setMinimumWidth(150)
            self.ba_filter.setMaximumWidth(250)
            self.ba_filter.setMaxVisibleItems(8)
            self.ba_filter.setEditable(True)
            self.ba_filter.setInsertPolicy(QComboBox.InsertPolicy.NoInsert)
            self.ba_filter.currentTextChanged.connect(self.apply_filters)
            self.ba_filter.lineEdit().textChanged.connect(self.apply_filters)
            # Apply styling fix for dropdown
            apply_combobox_hover_fix(self.ba_filter)
            filter_layout.addWidget(self.ba_filter)

            # Clear filters button
            self.clear_filters_button = QPushButton("Clear Filters")
            self.clear_filters_button.clicked.connect(self.clear_filters)
            filter_layout.addWidget(self.clear_filters_button)
            
            # Vintage Age button (only show for equipment table)
            if self.show_vintage_button:
                self.vintage_age_button = QPushButton("Vintage Age")
                self.vintage_age_button.clicked.connect(self.recalculate_vintage_years)
                self.vintage_age_button.setToolTip("Recalculate vintage years for all equipment")
                filter_layout.addWidget(self.vintage_age_button)

            filter_layout.addStretch()  # Push everything to the left
            layout.addLayout(filter_layout)

        # Table
        self.table = QTableWidget()
        layout.addWidget(self.table)
        
        # Pagination controls
        pagination_layout = QHBoxLayout()
        
        # Page size selector with higher limits
        pagination_layout.addWidget(QLabel("Rows per page:"))
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["50", "100", "200", "500", "1000", "All"])
        self.page_size_combo.setCurrentText(str(self.page_size))
        self.page_size_combo.currentTextChanged.connect(self.change_page_size)
        pagination_layout.addWidget(self.page_size_combo)
        
        pagination_layout.addStretch()
        
        # Navigation buttons
        self.first_button = QPushButton("First")
        self.first_button.clicked.connect(self.go_to_first_page)
        pagination_layout.addWidget(self.first_button)
        
        self.prev_button = QPushButton("Previous")
        self.prev_button.clicked.connect(self.go_to_previous_page)
        pagination_layout.addWidget(self.prev_button)
        
        # Page info
        self.page_info_label = QLabel("Page 0 of 0")
        pagination_layout.addWidget(self.page_info_label)
        
        # Page selector
        self.page_spinbox = QSpinBox()
        self.page_spinbox.setMinimum(1)
        self.page_spinbox.valueChanged.connect(self.go_to_page)
        pagination_layout.addWidget(self.page_spinbox)
        
        self.next_button = QPushButton("Next")
        self.next_button.clicked.connect(self.go_to_next_page)
        pagination_layout.addWidget(self.next_button)
        
        self.last_button = QPushButton("Last")
        self.last_button.clicked.connect(self.go_to_last_page)
        pagination_layout.addWidget(self.last_button)
        
        pagination_layout.addStretch()
        
        # Row count info
        self.row_count_label = QLabel("0 rows")
        pagination_layout.addWidget(self.row_count_label)
        
        layout.addLayout(pagination_layout)
    
    def setup_table(self):
        """Setup table properties."""
        # Table selection and behavior
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.table.setAlternatingRowColors(True)
        
        # Hide vertical header (row numbers/serial numbers)
        self.table.verticalHeader().setVisible(False)
        
        # Connect signals
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        
        # Apply DPI-aware styling
        try:
            from ui.window_utils import DPIScaler
            
            # Scale table row height
            row_height = DPIScaler.scale_size(25, min_size=20, max_size=40)
            self.table.verticalHeader().setDefaultSectionSize(row_height)
            
            # Scale font
            font = DPIScaler.create_scaled_font(9)
            self.table.setFont(font)
            
        except ImportError:
            logger.warning("DPI scaling not available for table")
    
    def set_data(self, headers, data, id_column=None):
        """
        Set data for the table with memory safety checks.

        Args:
            headers: List of column headers
            data: List of dictionaries containing row data
            id_column: Name of the ID column for row identification
        """
        try:
            # Memory safety check
            if len(data) > self.max_total_rows:
                logger.warning(f"Dataset too large ({len(data)} rows). Truncating to {self.max_total_rows} rows.")
                data = data[:self.max_total_rows]

            self.headers = headers
            self.all_data = data
            self.filtered_data = data.copy()  # Initially no filter
            self.id_column = id_column
            self.total_rows = len(data)

            # Find BA number column
            self.ba_column = None
            self.ba_column_index = None
            for i, header in enumerate(headers):
                if 'ba' in header.lower() and 'number' in header.lower():
                    self.ba_column = header
                    self.ba_column_index = i
                    break

            # Sort by BA number first if BA grouping is enabled and BA column exists
            if self.enable_ba_grouping and self.ba_column_index is not None:
                self.sort_by_ba_number()
                self.create_ba_groups()

            # Populate filter dropdowns if enabled
            if self.enable_ba_filter:
                self.populate_filter_dropdowns()

            # Calculate pagination
            self.total_pages = max(1, (self.total_rows + self.page_size - 1) // self.page_size)
            self.current_page = 0

            # Setup table headers
            self.table.setColumnCount(len(headers))
            self.table.setHorizontalHeaderLabels(headers)

            # Load first page
            self.load_current_page()
            self.update_pagination_controls()

            logger.info(f"Table data loaded: {self.total_rows} rows, {self.total_pages} pages")

        except Exception as e:
            logger.error(f"Error setting table data: {e}")

    def sort_by_ba_number(self):
        """Sort data by BA number first, then by other criteria."""
        try:
            if not self.ba_column or not self.all_data:
                return

            def sort_key(row):
                # Convert BA number to string safely
                ba_number = row.get(self.ba_column, '') or ''
                ba_number = str(ba_number) if ba_number is not None else ''

                # Convert other fields to string safely
                make_type = row.get('Make & Type', '') or row.get('make_and_type', '') or ''
                make_type = str(make_type) if make_type is not None else ''

                serial = row.get('Serial Number', '') or row.get('serial_number', '') or ''
                serial = str(serial) if serial is not None else ''

                return (ba_number.lower(), make_type.lower(), serial.lower())

            self.all_data.sort(key=sort_key)
            self.filtered_data = self.all_data.copy()

            logger.info("Data sorted by BA number")

        except Exception as e:
            logger.error(f"Error sorting by BA number: {e}")

    def create_ba_groups(self):
        """Create BA number groups for styling."""
        try:
            if not self.ba_column or not self.all_data:
                return

            self.ba_groups = {}
            current_ba = None
            group_index = 0

            for i, row in enumerate(self.all_data):
                ba_number = row.get(self.ba_column, '') or ''

                if ba_number != current_ba:
                    current_ba = ba_number
                    group_index += 1
                    self.ba_groups[ba_number] = {
                        'group_index': group_index,
                        'start_row': i,
                        'rows': []
                    }

                if ba_number in self.ba_groups:
                    self.ba_groups[ba_number]['rows'].append(i)

            logger.info(f"Created {len(self.ba_groups)} BA number groups")

        except Exception as e:
            logger.error(f"Error creating BA groups: {e}")

    def create_ba_groups_for_filtered_data(self):
        """Create BA number groups for filtered data."""
        try:
            if not self.ba_column or not self.filtered_data:
                return

            self.ba_groups = {}
            current_ba = None
            group_index = 0

            for i, row in enumerate(self.filtered_data):
                ba_number = row.get(self.ba_column, '') or ''

                if ba_number != current_ba:
                    current_ba = ba_number
                    group_index += 1
                    self.ba_groups[ba_number] = {
                        'group_index': group_index,
                        'start_row': i,
                        'rows': []
                    }

                if ba_number in self.ba_groups:
                    self.ba_groups[ba_number]['rows'].append(i)

            logger.info(f"Created {len(self.ba_groups)} BA number groups for filtered data")

        except Exception as e:
            logger.error(f"Error creating BA groups for filtered data: {e}")

    def load_current_page(self):
        """Load data for the current page."""
        try:
            # Calculate page bounds
            start_idx = self.current_page * self.page_size
            end_idx = min(start_idx + self.page_size, len(self.filtered_data))
            
            # Get page data
            page_data = self.filtered_data[start_idx:end_idx]
            
            # Set table row count
            self.table.setRowCount(len(page_data))
            
            # Populate table with BA grouping styling
            for row_idx, row_data in enumerate(page_data):
                # Calculate actual data index for styling
                actual_data_idx = start_idx + row_idx

                for col_idx, header in enumerate(self.headers):
                    value = row_data.get(header, '')

                    # Convert value to string safely
                    if value is None:
                        display_value = ''
                    else:
                        display_value = str(value)

                    item = QTableWidgetItem(display_value)
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # Read-only

                    # Add tooltip for long text to show full content on hover
                    if len(display_value) > 20:  # Show tooltip for longer text
                        item.setToolTip(display_value)

                    # Apply BA grouping styling first (but not for Status column)
                    if self.enable_ba_grouping and self.ba_column and header.lower() != 'status':
                        self.apply_ba_grouping_style(item, row_data, col_idx, actual_data_idx)

                    # Apply status color coding (takes absolute priority for Status column)
                    if header.lower() == 'status' and display_value:
                        self.apply_status_color_coding(item, display_value.lower())

                    self.table.setItem(row_idx, col_idx, item)
            
            # Apply BA number cell merging if enabled
            if self.enable_ba_grouping and self.ba_column_index is not None:
                self.apply_ba_cell_merging(page_data, start_idx)

            # Auto-resize columns
            self.table.resizeColumnsToContents()

            # Apply intelligent column width limits based on content type
            header = self.table.horizontalHeader()
            for col in range(len(self.headers)):
                header_text = self.headers[col].lower()
                width = header.sectionSize(col)

                # Set different max widths based on column content
                if 'make' in header_text and 'type' in header_text:
                    # Make & Type columns need more space for full equipment names
                    max_width = 300
                elif 'equipment' in header_text or 'ba' in header_text:
                    # Equipment and BA Number columns
                    max_width = 250
                elif 'remarks' in header_text or 'notes' in header_text or 'description' in header_text:
                    # Text fields need more space
                    max_width = 350
                else:
                    # Other columns (dates, numbers, status)
                    max_width = 200

                # Apply the limit only if current width exceeds it
                if width > max_width:
                    header.resizeSection(col, max_width)

                # Ensure minimum width for readability
                min_width = 80
                if width < min_width:
                    header.resizeSection(col, min_width)
            
        except Exception as e:
            logger.error(f"Error loading page data: {e}")

    def apply_status_color_coding(self, item, status):
        """Apply color coding to status cells based on maintenance status."""
        try:
            # Use centralized status color mapping from utils
            from utils import get_status_color
            color = get_status_color(status)
            
            # Color is already retrieved from get_status_color()
            
            # Apply background color with QColor directly
            item.setBackground(QColor(color))
            item.setForeground(QColor('white'))
            
            # Make status text bold
            font = item.font()
            font.setBold(True)
            item.setFont(font)
            
            # Center align status text
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignVCenter)
            
        except Exception as e:
            logger.error(f"Error applying status color coding: {e}")

    def apply_ba_grouping_style(self, item, row_data, col_idx, actual_data_idx):
        """Apply visual styling for BA number grouping."""
        try:
            ba_number = row_data.get(self.ba_column, '') or ''

            if ba_number and ba_number in self.ba_groups:
                group_info = self.ba_groups[ba_number]
                group_index = group_info['group_index']

                # Alternate background colors for different BA groups
                if group_index % 2 == 0:
                    # Light blue background for even groups
                    item.setBackground(QBrush(QColor(240, 248, 255)))
                else:
                    # Light gray background for odd groups
                    item.setBackground(QBrush(QColor(248, 248, 248)))

                # Bold text for BA number column on first occurrence
                if col_idx == self.ba_column_index:
                    is_first_in_group = actual_data_idx == group_info['start_row']
                    if is_first_in_group:
                        font = item.font()
                        font.setBold(True)
                        item.setFont(font)
                        # Darker background for first BA occurrence
                        if group_index % 2 == 0:
                            item.setBackground(QBrush(QColor(220, 235, 255)))
                        else:
                            item.setBackground(QBrush(QColor(235, 235, 235)))

        except Exception as e:
            logger.error(f"Error applying BA grouping style: {e}")

    def apply_ba_cell_merging(self, page_data, start_idx):
        """Apply cell merging for consecutive rows with same BA number."""
        try:
            if not page_data or self.ba_column_index is None:
                return

            # Track consecutive BA numbers for merging
            merge_groups = {}
            current_ba = None
            current_group_start = 0

            for row_idx, row_data in enumerate(page_data):
                ba_number = row_data.get(self.ba_column, '') or ''

                if ba_number != current_ba:
                    # Finish previous group if it had multiple rows
                    if current_ba and row_idx > current_group_start + 1:
                        merge_groups[current_ba] = {
                            'start_row': current_group_start,
                            'row_count': row_idx - current_group_start
                        }

                    # Start new group
                    current_ba = ba_number
                    current_group_start = row_idx

            # Handle last group
            if current_ba and len(page_data) > current_group_start + 1:
                merge_groups[current_ba] = {
                    'start_row': current_group_start,
                    'row_count': len(page_data) - current_group_start
                }

            # Apply cell merging
            for ba_number, merge_info in merge_groups.items():
                if merge_info['row_count'] > 1:
                    self.table.setSpan(
                        merge_info['start_row'],
                        self.ba_column_index,
                        merge_info['row_count'],
                        1
                    )

                    # Set the merged cell text to the BA number
                    item = self.table.item(merge_info['start_row'], self.ba_column_index)
                    if item:
                        item.setText(ba_number)
                        # Center align merged cells
                        item.setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)

        except Exception as e:
            logger.error(f"Error applying BA cell merging: {e}")

    def update_pagination_controls(self):
        """Update pagination control states and labels."""
        try:
            # Update page info
            if self.total_pages > 0:
                self.page_info_label.setText(f"Page {self.current_page + 1} of {self.total_pages}")
                self.page_spinbox.setMaximum(self.total_pages)
                self.page_spinbox.setValue(self.current_page + 1)
            else:
                self.page_info_label.setText("Page 0 of 0")
                self.page_spinbox.setMaximum(1)
                self.page_spinbox.setValue(1)
            
            # Update row count
            start_row = self.current_page * self.page_size + 1 if self.total_rows > 0 else 0
            end_row = min((self.current_page + 1) * self.page_size, self.total_rows)
            self.row_count_label.setText(f"Showing {start_row}-{end_row} of {self.total_rows} rows")
            
            # Update button states
            self.first_button.setEnabled(self.current_page > 0)
            self.prev_button.setEnabled(self.current_page > 0)
            self.next_button.setEnabled(self.current_page < self.total_pages - 1)
            self.last_button.setEnabled(self.current_page < self.total_pages - 1)
            
        except Exception as e:
            logger.error(f"Error updating pagination controls: {e}")
    
    def change_page_size(self, new_size_text):
        """Change the page size and reload data."""
        try:
            if new_size_text == "All":
                new_size = len(self.filtered_data) if self.filtered_data else 1000
            else:
                new_size = int(new_size_text)

            if new_size != self.page_size:
                self.page_size = new_size

                # Recalculate pagination
                self.total_pages = max(1, (self.total_rows + self.page_size - 1) // self.page_size)

                # Adjust current page if necessary
                if self.current_page >= self.total_pages:
                    self.current_page = max(0, self.total_pages - 1)

                self.load_current_page()
                self.update_pagination_controls()

        except ValueError:
            logger.error(f"Invalid page size: {new_size_text}")
    
    def go_to_first_page(self):
        """Go to the first page."""
        if self.current_page != 0:
            self.current_page = 0
            self.load_current_page()
            self.update_pagination_controls()
    
    def go_to_previous_page(self):
        """Go to the previous page."""
        if self.current_page > 0:
            self.current_page -= 1
            self.load_current_page()
            self.update_pagination_controls()
    
    def go_to_next_page(self):
        """Go to the next page."""
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            self.load_current_page()
            self.update_pagination_controls()
    
    def go_to_last_page(self):
        """Go to the last page."""
        if self.current_page != self.total_pages - 1:
            self.current_page = max(0, self.total_pages - 1)
            self.load_current_page()
            self.update_pagination_controls()
    
    def go_to_page(self, page_number):
        """Go to a specific page (1-based)."""
        page_index = page_number - 1
        if 0 <= page_index < self.total_pages and page_index != self.current_page:
            self.current_page = page_index
            self.load_current_page()
            self.update_pagination_controls()
    
    def filter_data(self, filter_func):
        """
        Filter data based on a filter function.

        Args:
            filter_func: Function that takes a row dict and returns True/False
        """
        try:
            if filter_func is None:
                self.filtered_data = self.all_data.copy()
            else:
                self.filtered_data = [row for row in self.all_data if filter_func(row)]

            # Recreate BA groups for filtered data if BA grouping is enabled
            if self.enable_ba_grouping and self.ba_column_index is not None:
                self.create_ba_groups_for_filtered_data()

            # Update pagination for filtered data
            self.total_rows = len(self.filtered_data)
            self.total_pages = max(1, (self.total_rows + self.page_size - 1) // self.page_size)
            self.current_page = 0

            self.load_current_page()
            self.update_pagination_controls()

            logger.info(f"Filter applied: {self.total_rows} rows match")

        except Exception as e:
            logger.error(f"Error filtering data: {e}")
    
    def clear_filter(self):
        """Clear any applied filters."""
        self.filter_data(None)
    
    def get_selected_row_data(self):
        """Get the data for the currently selected row."""
        try:
            current_row = self.table.currentRow()
            if current_row >= 0:
                # Calculate actual data index
                data_index = self.current_page * self.page_size + current_row
                if data_index < len(self.filtered_data):
                    return self.filtered_data[data_index]
            return None
            
        except Exception as e:
            logger.error(f"Error getting selected row data: {e}")
            return None
    
    def get_selected_id(self):
        """Get the ID of the selected row (compatibility method for ReadOnlyTableWidget)."""
        try:
            row_data = self.get_selected_row_data()
            if row_data and self.id_column is not None:
                # Get ID from the specified ID column
                if isinstance(self.id_column, int) and self.id_column < len(self.headers):
                    id_header = self.headers[self.id_column]
                    return row_data.get(id_header)
                elif isinstance(self.id_column, str):
                    return row_data.get(self.id_column)
                else:
                    # Try common ID field names
                    for id_field in ['ID', 'id', 'equipment_id', 'overhaul_id', 'maintenance_id']:
                        if id_field in row_data:
                            return row_data[id_field]
            return None
            
        except Exception as e:
            logger.error(f"Error getting selected ID: {e}")
            return None
    
    def on_selection_changed(self):
        """Handle table selection changes."""
        try:
            row_data = self.get_selected_row_data()
            if row_data:
                self.row_selected.emit(row_data)
                
        except Exception as e:
            logger.error(f"Error handling selection change: {e}")
    
    def refresh_data(self):
        """Refresh the current page data."""
        self.load_current_page()
    
    def get_total_row_count(self):
        """Get the total number of rows (including filtered)."""
        return self.total_rows
    
    def get_all_data(self):
        """Get all data (filtered)."""
        return self.filtered_data.copy()
    
    def clear_data(self):
        """Clear all data from the table."""
        self.all_data = []
        self.filtered_data = []
        self.total_rows = 0
        self.total_pages = 0
        self.current_page = 0
        self.table.setRowCount(0)
        self.update_pagination_controls()

    def setColumnHidden(self, column, hidden):
        """Hide or show a column (compatibility method)."""
        try:
            self.table.setColumnHidden(column, hidden)
        except Exception as e:
            logger.error(f"Error setting column hidden: {e}")

    def setHorizontalHeaderLabels(self, labels):
        """Set horizontal header labels (compatibility method)."""
        try:
            self.table.setHorizontalHeaderLabels(labels)
            self.headers = labels
        except Exception as e:
            logger.error(f"Error setting header labels: {e}")

    def horizontalHeader(self):
        """Get horizontal header (compatibility method)."""
        return self.table.horizontalHeader()

    def setColumnWidth(self, column, width):
        """Set column width (compatibility method)."""
        try:
            self.table.setColumnWidth(column, width)
        except Exception as e:
            logger.error(f"Error setting column width: {e}")

    def resizeColumnsToContents(self):
        """Resize columns to contents (compatibility method)."""
        try:
            self.table.resizeColumnsToContents()
        except Exception as e:
            logger.error(f"Error resizing columns: {e}")

    def rowCount(self):
        """Get row count (compatibility method)."""
        try:
            return self.table.rowCount()
        except Exception as e:
            logger.error(f"Error getting row count: {e}")
            return 0

    def setRowCount(self, rows):
        """Set row count (compatibility method)."""
        try:
            self.table.setRowCount(rows)
        except Exception as e:
            logger.error(f"Error setting row count: {e}")

    def columnCount(self):
        """Get column count (compatibility method)."""
        try:
            return self.table.columnCount()
        except Exception as e:
            logger.error(f"Error getting column count: {e}")
            return 0

    def setColumnCount(self, columns):
        """Set column count (compatibility method)."""
        try:
            self.table.setColumnCount(columns)
        except Exception as e:
            logger.error(f"Error setting column count: {e}")

    def item(self, row, column):
        """Get table item (compatibility method)."""
        try:
            return self.table.item(row, column)
        except Exception as e:
            logger.error(f"Error getting table item: {e}")
            return None

    def setItem(self, row, column, item):
        """Set table item (compatibility method)."""
        try:
            self.table.setItem(row, column, item)
        except Exception as e:
            logger.error(f"Error setting table item: {e}")

    def currentRow(self):
        """Get current row (compatibility method)."""
        try:
            return self.table.currentRow()
        except Exception as e:
            logger.error(f"Error getting current row: {e}")
            return -1

    def selectRow(self, row):
        """Select a row (compatibility method)."""
        try:
            self.table.selectRow(row)
        except Exception as e:
            logger.error(f"Error selecting row: {e}")

    def clearSelection(self):
        """Clear selection (compatibility method)."""
        try:
            self.table.clearSelection()
        except Exception as e:
            logger.error(f"Error clearing selection: {e}")

    def setSelectionBehavior(self, behavior):
        """Set selection behavior (compatibility method)."""
        try:
            self.table.setSelectionBehavior(behavior)
        except Exception as e:
            logger.error(f"Error setting selection behavior: {e}")

    def setSelectionMode(self, mode):
        """Set selection mode (compatibility method)."""
        try:
            self.table.setSelectionMode(mode)
        except Exception as e:
            logger.error(f"Error setting selection mode: {e}")

    def setAlternatingRowColors(self, enable):
        """Set alternating row colors (compatibility method)."""
        try:
            self.table.setAlternatingRowColors(enable)
        except Exception as e:
            logger.error(f"Error setting alternating row colors: {e}")

    def setSortingEnabled(self, enable):
        """Set sorting enabled (compatibility method)."""
        try:
            self.table.setSortingEnabled(enable)
        except Exception as e:
            logger.error(f"Error setting sorting enabled: {e}")

    def sortByColumn(self, column, order):
        """Sort by column (compatibility method)."""
        try:
            self.table.sortByColumn(column, order)
        except Exception as e:
            logger.error(f"Error sorting by column: {e}")

    def verticalHeader(self):
        """Get vertical header (compatibility method)."""
        try:
            return self.table.verticalHeader()
        except Exception as e:
            logger.error(f"Error getting vertical header: {e}")
            return None

    def setSpan(self, row, column, rowSpan, columnSpan):
        """Set cell span (compatibility method)."""
        try:
            self.table.setSpan(row, column, rowSpan, columnSpan)
        except Exception as e:
            logger.error(f"Error setting cell span: {e}")

    def itemSelectionChanged(self):
        """Get item selection changed signal (compatibility method)."""
        try:
            return self.table.itemSelectionChanged
        except Exception as e:
            logger.error(f"Error getting item selection changed signal: {e}")
            return None

    def populate_filter_dropdowns(self):
        """Populate the filter dropdowns with unique values from data."""
        if not self.enable_ba_filter or not self.all_data:
            return

        try:
            # Get unique Make & Type values
            make_types = set()
            ba_numbers = set()

            for row in self.all_data:
                # Make & Type
                make_type = row.get('Make & Type', '') or row.get('make_and_type', '')
                if make_type and make_type.strip():
                    make_types.add(make_type.strip())

                # BA Number
                ba_number = row.get('BA Number', '') or row.get('ba_number', '')
                if ba_number and ba_number.strip() and ba_number.strip().lower() != 'not assigned':
                    ba_numbers.add(ba_number.strip())

            # Update Make & Type dropdown
            self.make_type_filter.clear()
            self.make_type_filter.addItem("All Make & Types")
            for make_type in sorted(make_types):
                self.make_type_filter.addItem(make_type)

            # Update BA Number dropdown
            self.ba_filter.clear()
            self.ba_filter.addItem("All BA Numbers")
            for ba_number in sorted(ba_numbers):
                self.ba_filter.addItem(ba_number)

        except Exception as e:
            logger.error(f"Error populating filter dropdowns: {e}")

    def apply_filters(self):
        """Apply the current filter settings."""
        if not self.enable_ba_filter:
            return

        try:
            make_type_filter = self.make_type_filter.currentText()
            ba_filter_text = self.ba_filter.currentText()

            # If using editable combo, get the actual text
            if self.ba_filter.lineEdit():
                ba_filter_text = self.ba_filter.lineEdit().text()

            def filter_func(row):
                # Make & Type filter
                if make_type_filter != "All Make & Types":
                    row_make_type = row.get('Make & Type', '') or row.get('make_and_type', '')
                    if row_make_type != make_type_filter:
                        return False

                # BA Number filter (case-insensitive partial match)
                if ba_filter_text and ba_filter_text != "All BA Numbers":
                    row_ba_number = row.get('BA Number', '') or row.get('ba_number', '')
                    if not row_ba_number or ba_filter_text.lower() not in row_ba_number.lower():
                        return False

                return True

            # Apply filter
            if make_type_filter == "All Make & Types" and (not ba_filter_text or ba_filter_text == "All BA Numbers"):
                self.filter_data(None)  # Clear filter
            else:
                self.filter_data(filter_func)

        except Exception as e:
            logger.error(f"Error applying filters: {e}")

    def clear_filters(self):
        """Clear all filters."""
        if not self.enable_ba_filter:
            return

        try:
            self.make_type_filter.setCurrentText("All Make & Types")
            self.ba_filter.setCurrentText("All BA Numbers")
            if self.ba_filter.lineEdit():
                self.ba_filter.lineEdit().clear()
            self.filter_data(None)

        except Exception as e:
            logger.error(f"Error clearing filters: {e}")
    
    def recalculate_vintage_years(self):
        """Recalculate vintage years for all equipment using the Equipment model method."""
        try:
            from PyQt5.QtWidgets import QMessageBox, QProgressDialog, QApplication
            from PyQt5.QtCore import Qt
            import database
            from models import Equipment
            
            # Show confirmation dialog
            reply = QMessageBox.question(
                self,
                "Recalculate Vintage Years",
                "This will recalculate vintage years for all equipment based on their commission dates.\n\n"
                "This operation may take a few moments. Continue?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply != QMessageBox.StandardButton.Yes:
                return
            
            # Create progress dialog
            progress = QProgressDialog("Recalculating vintage years...", "Cancel", 0, 0, self)
            progress.setWindowTitle("Processing")
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.show()
            QApplication.processEvents()
            
            # Get all equipment records
            query = """
                SELECT equipment_id, date_of_commission, vintage_years
                FROM equipment
                WHERE date_of_commission IS NOT NULL AND date_of_commission != ''
                ORDER BY equipment_id
            """
            equipment_list = database.execute_query(query)
            
            if not equipment_list or not isinstance(equipment_list, list):
                progress.close()
                QMessageBox.warning(self, "No Data", "No equipment records found with commission dates.")
                return
            
            updated_count = 0
            skipped_count = 0
            
            for equipment in equipment_list:
                if progress.wasCanceled():
                    break
                
                equipment_id = equipment.get('equipment_id')
                date_of_commission = equipment.get('date_of_commission')
                current_vintage = equipment.get('vintage_years', 0)
                
                if not equipment_id or not date_of_commission:
                    skipped_count += 1
                    continue
                
                # Create Equipment instance and calculate vintage years
                equipment_obj = Equipment(
                    equipment_id=equipment_id,
                    date_of_commission=date_of_commission
                )
                
                new_vintage_years = equipment_obj.calculate_vintage_years()
                
                # Log the calculation for debugging
                logger.info(f"Equipment {equipment_id}: '{date_of_commission}' -> {new_vintage_years:.2f} years (was {current_vintage:.2f})")
                
                # Only update if there's a significant difference
                if abs(float(new_vintage_years) - float(current_vintage or 0)) > 0.01:
                    # Update the database
                    update_query = """
                        UPDATE equipment 
                        SET vintage_years = ? 
                        WHERE equipment_id = ?
                    """
                    database.execute_query(update_query, (new_vintage_years, equipment_id), fetchall=False)
                    updated_count += 1
                else:
                    skipped_count += 1
                
                QApplication.processEvents()
            
            progress.close()
            
            # Show completion message
            QMessageBox.information(
                self,
                "Vintage Years Updated",
                f"Vintage years recalculation completed!\n\n"
                f"✅ Records updated: {updated_count}\n"
                f"⏭️ Records already correct: {skipped_count}\n\n"
                f"The equipment table will now refresh to show updated values."
            )
            
            # Emit data changed signal to refresh the table
            self.data_changed.emit()
            
        except Exception as e:
            logger.error(f"Error recalculating vintage years: {e}")
            if 'progress' in locals():
                progress.close()
            QMessageBox.critical(
                self,
                "Error",
                f"An error occurred while recalculating vintage years:\n{str(e)}"
            )
