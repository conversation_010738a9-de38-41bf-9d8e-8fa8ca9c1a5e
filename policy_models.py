"""Policy data models for the equipment inventory application."""
import logging
from datetime import datetime

import database
import utils

# Configure logger
logger = logging.getLogger('policy_models')

class VehicleClassPolicy:
    """Model for vehicle class policy."""
    
    def __init__(self, policy_id=None, make_and_type=None, created_date=None, 
                modified_date=None, created_by=None, is_active=1):
        self.policy_id = policy_id
        self.make_and_type = make_and_type
        self.created_date = created_date
        self.modified_date = modified_date
        self.created_by = created_by
        self.is_active = is_active
    
    @staticmethod
    def get_all():
        """Get all active policies."""
        with database.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM vehicle_class_policy WHERE is_active = 1"
            )
            policies = cursor.fetchall()
            return policies
    
    @staticmethod
    def normalize_make_type(make_and_type):
        """Normalize make and type string by removing newlines and extra spaces."""
        if not make_and_type:
            return ''
        # Remove leading/trailing whitespace and replace newlines with spaces
        return ' '.join(make_and_type.strip().splitlines())
    
    @staticmethod
    def get_by_make_type(make_and_type):
        """Get policy by make and type."""
        if not make_and_type:
            return None
            
        # First try exact match
        with database.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM vehicle_class_policy WHERE make_and_type = ? AND is_active = 1",
                (make_and_type,)
            )
            policy = cursor.fetchone()
            if policy:
                return policy

            # If no exact match, try with normalized make_and_type
            normalized = VehicleClassPolicy.normalize_make_type(make_and_type)
            if normalized != make_and_type:
                logger.debug(f"No exact match for '{make_and_type}', trying normalized version '{normalized}'")
                cursor.execute(
                    "SELECT * FROM vehicle_class_policy WHERE make_and_type = ? AND is_active = 1",
                    (normalized,)
                )
                policy = cursor.fetchone()
                if policy:
                    return policy

            # Fall back to LIKE comparison for partial matches
            cursor.execute(
                "SELECT * FROM vehicle_class_policy WHERE make_and_type LIKE ? AND is_active = 1 LIMIT 1",
                (f"%{normalized}%",)
            )
            policy = cursor.fetchone()
            return policy
    
    @staticmethod
    def get_by_id(policy_id):
        """Get policy by ID."""
        with database.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM vehicle_class_policy WHERE policy_id = ?",
                (policy_id,)
            )
            policy = cursor.fetchone()
            return policy
    
    def save(self):
        """Save or update policy."""
        with database.get_db_connection() as conn:
            # Normalize make_and_type before saving
            if self.make_and_type:
                self.make_and_type = VehicleClassPolicy.normalize_make_type(self.make_and_type)

            cursor = conn.cursor()
            if self.policy_id is None:
                # Insert new policy
                cursor.execute(
                    """
                    INSERT INTO vehicle_class_policy
                    (make_and_type, created_by, is_active)
                    VALUES (?, ?, ?)
                    """,
                    (self.make_and_type, self.created_by, self.is_active)
                )
                self.policy_id = cursor.lastrowid
            else:
                # Update existing policy
                cursor.execute(
                    """
                    UPDATE vehicle_class_policy
                    SET make_and_type = ?, modified_date = CURRENT_TIMESTAMP,
                    is_active = ?
                    WHERE policy_id = ?
                    """,
                    (self.make_and_type, self.is_active, self.policy_id)
                )
            conn.commit()
            return self.policy_id
    
    @staticmethod
    def delete(policy_id):
        """Delete policy by marking as inactive."""
        with database.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "UPDATE vehicle_class_policy SET is_active = 0 WHERE policy_id = ?",
                (policy_id,)
            )
            conn.commit()
            return True

    @staticmethod
    def create_tables():
        """Create necessary tables if they don't exist."""
        try:
            with database.get_db_connection() as conn:
                cursor = conn.cursor()

                # Create vehicle class policy table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS vehicle_class_policy (
                        policy_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        make_and_type TEXT NOT NULL,
                        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_by TEXT,
                        is_active INTEGER NOT NULL DEFAULT 1
                    )
                ''')

                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error creating vehicle_class_policy table: {str(e)}")
            return False


class PolicyCondition:
    """Model for policy condition."""
    
    # Constants for condition types
    FIRST_OH = "FIRST_OH"
    SECOND_OH = "SECOND_OH"
    MR = "MR"  # Medium Reset
    DISCARD = "DISCARD"

    # Logic types
    EARLIER = "EARLIER"
    LATER = "LATER"
    EXACT = "EXACT"
    
    def __init__(self, condition_id=None, policy_id=None, condition_type=None, 
                years_threshold=None, km_threshold=None, hours_threshold=None,
                logic_type='EARLIER', reference_condition_id=None, is_relative=0):
        self.condition_id = condition_id
        self.policy_id = policy_id
        self.condition_type = condition_type
        self.years_threshold = years_threshold
        self.km_threshold = km_threshold
        self.hours_threshold = hours_threshold
        self.logic_type = logic_type
        self.reference_condition_id = reference_condition_id
        self.is_relative = is_relative
    
    @staticmethod
    def get_by_policy(policy_id):
        """Get all conditions for a policy."""
        with database.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM policy_condition WHERE policy_id = ?",
                (policy_id,)
            )
            conditions = cursor.fetchall()
            return conditions
    
    def save(self):
        """Save or update condition."""
        with database.get_db_connection() as conn:
            cursor = conn.cursor()
            if self.condition_id is None:
                # Insert new condition
                cursor.execute(
                    """
                    INSERT INTO policy_condition
                    (policy_id, condition_type, years_threshold, km_threshold,
                    hours_threshold, logic_type, reference_condition_id, is_relative)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    (self.policy_id, self.condition_type, self.years_threshold,
                    self.km_threshold, self.hours_threshold, self.logic_type,
                    self.reference_condition_id, self.is_relative)
                )
                self.condition_id = cursor.lastrowid
            else:
                # Update existing condition
                cursor.execute(
                    """
                    UPDATE policy_condition
                    SET policy_id = ?, condition_type = ?, years_threshold = ?,
                    km_threshold = ?, hours_threshold = ?, logic_type = ?,
                    reference_condition_id = ?, is_relative = ?
                    WHERE condition_id = ?
                    """,
                    (self.policy_id, self.condition_type, self.years_threshold,
                    self.km_threshold, self.hours_threshold, self.logic_type,
                    self.reference_condition_id, self.is_relative, self.condition_id)
                )
            conn.commit()
            return self.condition_id
    
    @staticmethod
    def delete_by_policy(policy_id):
        """Delete all conditions for a policy."""
        with database.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "DELETE FROM policy_condition WHERE policy_id = ?",
                (policy_id,)
            )
            conn.commit()
            return True

    @staticmethod
    def create_tables():
        """Create necessary tables if they don't exist."""
        try:
            with database.get_db_connection() as conn:
                cursor = conn.cursor()

                # Create policy condition table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS policy_condition (
                        condition_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        policy_id INTEGER NOT NULL,
                        condition_type TEXT NOT NULL,
                        years_threshold INTEGER,
                        km_threshold INTEGER,
                        hours_threshold INTEGER,
                        logic_type TEXT DEFAULT 'EARLIER',
                        reference_condition_id INTEGER,
                        is_relative INTEGER DEFAULT 0,
                        FOREIGN KEY (policy_id) REFERENCES vehicle_class_policy (policy_id) ON DELETE CASCADE,
                        FOREIGN KEY (reference_condition_id) REFERENCES policy_condition (condition_id)
                    )
                ''')

                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error creating policy_condition table: {str(e)}")
            return False
