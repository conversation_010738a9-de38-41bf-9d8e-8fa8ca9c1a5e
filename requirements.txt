# PROJECT-ALPHA Equipment Inventory Management System
# Distribution Build Requirements
# Optimized for Windows deployment with low-resolution display support

# Core GUI Framework
PyQt5>=5.15.0,<5.16.0

# Data Processing and Analysis
pandas>=1.3.0,<2.1.0
numpy>=1.19.0,<1.25.0
openpyxl>=3.0.0,<3.2.0
xlrd>=2.0.0,<2.1.0

# Date and Time Handling
python-dateutil>=2.8.0,<2.9.0

# Visualization and Plotting
matplotlib>=3.3.0,<3.8.0

# PDF Generation and Reports
reportlab>=3.6.0,<4.1.0

# System and Process Utilities
psutil>=5.8.0,<5.10.0

# Unit Handling and Conversions
pint>=0.20,<0.24.0

# String Matching and Processing
fuzzywuzzy>=0.18,<0.19.0
python-Levenshtein>=0.12.0,<0.21.0  # For better fuzzywuzzy performance

# Build and Distribution Tools
pyinstaller>=5.0.0,<6.0.0
cx-Freeze>=6.0,<6.16.0

# Optional: Image Processing for Icon Creation
Pillow>=8.0.0,<10.1.0

# Development and Testing (optional)
pytest>=6.0.0,<7.5.0
pytest-qt>=4.0.0,<4.3.0

# Additional utilities for robust operation
setuptools>=60.0.0,<69.0.0
wheel>=0.37.0,<0.42.0

# Security and compatibility
certifi>=2021.10.8  # For secure connections
urllib3>=1.26.0,<2.0.0  # HTTP library with security fixes
