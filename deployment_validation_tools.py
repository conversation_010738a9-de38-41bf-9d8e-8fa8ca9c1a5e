#!/usr/bin/env python3
"""
Deployment Validation Tools for PROJECT-ALPHA
Validates Excel import functionality across different system configurations
to ensure reliable operation in military deployment environments.
"""

import os
import sys
import logging
import tempfile
import json
import time
import traceback
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import subprocess

logger = logging.getLogger('deployment_validation')

class DeploymentValidator:
    """Validates system readiness for PROJECT-ALPHA deployment."""
    
    def __init__(self):
        self.validation_results = {}
        self.test_data_dir = None
        self.setup_test_environment()
    
    def setup_test_environment(self):
        """Setup test environment for validation."""
        try:
            self.test_data_dir = tempfile.mkdtemp(prefix='PROJECT_ALPHA_VALIDATION_')
            logger.info(f"Test environment created: {self.test_data_dir}")
        except Exception as e:
            logger.error(f"Failed to setup test environment: {e}")
            raise
    
    def run_comprehensive_validation(self) -> Dict[str, Any]:
        """Run comprehensive deployment validation."""
        logger.info("Starting comprehensive deployment validation...")
        
        validation_start = time.time()
        
        # System compatibility validation
        system_validation = self.validate_system_compatibility()
        
        # Dependency validation
        dependency_validation = self.validate_dependencies()
        
        # Excel import functionality validation
        excel_validation = self.validate_excel_import_functionality()
        
        # Performance validation
        performance_validation = self.validate_performance_characteristics()
        
        # Resource management validation
        resource_validation = self.validate_resource_management()
        
        # Cross-system compatibility validation
        cross_system_validation = self.validate_cross_system_compatibility()
        
        validation_duration = time.time() - validation_start
        
        # Compile overall results
        overall_results = {
            'validation_timestamp': datetime.now().isoformat(),
            'validation_duration_seconds': validation_duration,
            'system_compatibility': system_validation,
            'dependencies': dependency_validation,
            'excel_import': excel_validation,
            'performance': performance_validation,
            'resource_management': resource_validation,
            'cross_system': cross_system_validation,
            'overall_status': self._determine_overall_status([
                system_validation, dependency_validation, excel_validation,
                performance_validation, resource_validation, cross_system_validation
            ])
        }
        
        self.validation_results = overall_results
        logger.info(f"Validation completed in {validation_duration:.2f} seconds")
        
        return overall_results
    
    def validate_system_compatibility(self) -> Dict[str, Any]:
        """Validate system compatibility requirements."""
        logger.info("Validating system compatibility...")
        
        try:
            from system_environment_detector import get_system_detector
            detector = get_system_detector()
            
            system_profile = detector.get_system_profile()
            compatibility_issues = detector.get_compatibility_issues()
            
            return {
                'status': 'PASS' if detector.is_system_compatible() else 'FAIL',
                'system_info': system_profile['system'],
                'compatibility_issues': compatibility_issues,
                'recommendations': system_profile['recommendations'],
                'details': 'System compatibility check completed'
            }
        except Exception as e:
            logger.error(f"System compatibility validation failed: {e}")
            return {
                'status': 'ERROR',
                'error': str(e),
                'details': 'Failed to validate system compatibility'
            }
    
    def validate_dependencies(self) -> Dict[str, Any]:
        """Validate required dependencies."""
        logger.info("Validating dependencies...")
        
        try:
            from system_compatibility_layer import get_system_compatibility_status
            status = get_system_compatibility_status()
            
            return {
                'status': 'PASS' if status['compatible'] else 'FAIL',
                'dependency_status': status['dependency_status'],
                'recommendations': status['recommendations'],
                'details': 'Dependency validation completed'
            }
        except Exception as e:
            logger.error(f"Dependency validation failed: {e}")
            return {
                'status': 'ERROR',
                'error': str(e),
                'details': 'Failed to validate dependencies'
            }
    
    def validate_excel_import_functionality(self) -> Dict[str, Any]:
        """Validate Excel import functionality with test data."""
        logger.info("Validating Excel import functionality...")
        
        try:
            # Create test Excel file
            test_excel_file = self._create_test_excel_file()
            
            # Test different import strategies
            import_results = {}
            
            # Test cross-system compatible import
            try:
                from cross_system_excel_importer import import_excel_cross_system_compatible
                result = import_excel_cross_system_compatible(test_excel_file)
                import_results['cross_system'] = {
                    'status': 'PASS' if result['success'] else 'FAIL',
                    'strategy': result.get('strategy', 'unknown'),
                    'details': result
                }
            except Exception as e:
                import_results['cross_system'] = {
                    'status': 'ERROR',
                    'error': str(e),
                    'details': 'Cross-system import failed'
                }
            
            # Test standard import (if available)
            try:
                from excel_importer import import_from_excel
                result = import_from_excel(test_excel_file)
                import_results['standard'] = {
                    'status': 'PASS' if isinstance(result, dict) else 'FAIL',
                    'details': result
                }
            except Exception as e:
                import_results['standard'] = {
                    'status': 'ERROR',
                    'error': str(e),
                    'details': 'Standard import failed'
                }
            
            # Determine overall Excel import status
            any_success = any(r['status'] == 'PASS' for r in import_results.values())
            
            return {
                'status': 'PASS' if any_success else 'FAIL',
                'import_strategies': import_results,
                'test_file': test_excel_file,
                'details': 'Excel import functionality validation completed'
            }
            
        except Exception as e:
            logger.error(f"Excel import validation failed: {e}")
            return {
                'status': 'ERROR',
                'error': str(e),
                'details': 'Failed to validate Excel import functionality'
            }
    
    def validate_performance_characteristics(self) -> Dict[str, Any]:
        """Validate performance characteristics."""
        logger.info("Validating performance characteristics...")
        
        try:
            from memory_resource_manager import get_memory_status, force_memory_cleanup
            
            # Get initial memory status
            initial_memory = get_memory_status()
            
            # Perform memory stress test
            start_time = time.time()
            
            # Create some data to test memory handling
            test_data = []
            for i in range(1000):
                test_data.append([j for j in range(100)])
            
            # Test memory cleanup
            force_memory_cleanup()
            cleanup_time = time.time() - start_time
            
            # Get final memory status
            final_memory = get_memory_status()
            
            # Clean up test data
            del test_data
            
            return {
                'status': 'PASS',
                'initial_memory': initial_memory,
                'final_memory': final_memory,
                'cleanup_time_seconds': cleanup_time,
                'memory_management': 'functional',
                'details': 'Performance characteristics validation completed'
            }
            
        except Exception as e:
            logger.error(f"Performance validation failed: {e}")
            return {
                'status': 'ERROR',
                'error': str(e),
                'details': 'Failed to validate performance characteristics'
            }
    
    def validate_resource_management(self) -> Dict[str, Any]:
        """Validate resource management capabilities."""
        logger.info("Validating resource management...")
        
        try:
            from memory_resource_manager import managed_resources
            
            with managed_resources() as rm:
                # Test temp file creation
                temp_file = rm.create_temp_file(suffix='.test')
                temp_dir = rm.create_temp_dir(suffix='_test')
                
                # Verify files exist
                temp_file_exists = os.path.exists(temp_file)
                temp_dir_exists = os.path.exists(temp_dir)
                
                # Test resource status
                status = rm.get_resource_status()
                
                # Test cleanup
                rm.full_cleanup()
                
                return {
                    'status': 'PASS',
                    'temp_file_creation': temp_file_exists,
                    'temp_dir_creation': temp_dir_exists,
                    'resource_tracking': status,
                    'cleanup_functional': True,
                    'details': 'Resource management validation completed'
                }
                
        except Exception as e:
            logger.error(f"Resource management validation failed: {e}")
            return {
                'status': 'ERROR',
                'error': str(e),
                'details': 'Failed to validate resource management'
            }
    
    def validate_cross_system_compatibility(self) -> Dict[str, Any]:
        """Validate cross-system compatibility features."""
        logger.info("Validating cross-system compatibility...")
        
        try:
            from locale_compatibility_handler import (
                parse_date_locale_safe, parse_number_locale_safe,
                get_locale_compatibility_info, setup_safe_locale
            )
            
            # Test locale handling
            locale_info = get_locale_compatibility_info()
            locale_setup = setup_safe_locale()
            
            # Test date parsing with various formats
            test_dates = ["2023-12-25", "25/12/2023", "Dec 25, 2023"]
            date_parsing_results = {}
            for test_date in test_dates:
                parsed = parse_date_locale_safe(test_date)
                date_parsing_results[test_date] = parsed is not None
            
            # Test number parsing with various formats
            test_numbers = ["1234.56", "1,234.56", "1.234,56"]
            number_parsing_results = {}
            for test_number in test_numbers:
                parsed = parse_number_locale_safe(test_number)
                number_parsing_results[test_number] = parsed > 0
            
            return {
                'status': 'PASS',
                'locale_info': locale_info,
                'locale_setup': locale_setup,
                'date_parsing': date_parsing_results,
                'number_parsing': number_parsing_results,
                'details': 'Cross-system compatibility validation completed'
            }
            
        except Exception as e:
            logger.error(f"Cross-system compatibility validation failed: {e}")
            return {
                'status': 'ERROR',
                'error': str(e),
                'details': 'Failed to validate cross-system compatibility'
            }
    
    def _create_test_excel_file(self) -> str:
        """Create test Excel file for validation."""
        try:
            import pandas as pd
            
            # Create test data
            test_data = {
                'SER NO': ['TEST001', 'TEST002', 'TEST003'],
                'MAKE & TYPE': ['Test Equipment A', 'Test Equipment B', 'Test Equipment C'],
                'BA NO': ['BA001', 'BA002', 'BA003'],
                'VINTAGE': [5, 8, 12],
                'KM RUN': [15000, 25000, 30000],
                'HRS RUN': [1500, 2500, 800],
                'DATE OF REL': ['2019-01-15', '2016-06-20', '2012-03-10'],
                'ENG OIL -> CAPACITY': [25, 18, 22],
                'ENG OIL -> GRADE': ['SAE 15W-40', 'SAE 10W-30', 'SAE 20W-50'],
                'Hyd Oil -> CAPACITY': [12, 15, 10],  # Test case-insensitive detection
                'Hyd Oil -> GRADE': ['ISO 46', 'ISO 32', 'ISO 68'],
                'REMARKS': ['Test equipment for validation', 'Test equipment for validation', 'Test equipment for validation']
            }
            
            df = pd.DataFrame(test_data)
            
            # Save to Excel file
            test_file = os.path.join(self.test_data_dir, 'validation_test.xlsx')
            df.to_excel(test_file, index=False)
            
            logger.info(f"Test Excel file created: {test_file}")
            return test_file
            
        except Exception as e:
            logger.error(f"Failed to create test Excel file: {e}")
            raise
    
    def _determine_overall_status(self, validation_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Determine overall validation status."""
        statuses = [result.get('status', 'ERROR') for result in validation_results]
        
        error_count = statuses.count('ERROR')
        fail_count = statuses.count('FAIL')
        pass_count = statuses.count('PASS')
        
        if error_count > 0:
            overall_status = 'ERROR'
            message = f"Validation errors detected ({error_count} errors, {fail_count} failures)"
        elif fail_count > 0:
            overall_status = 'FAIL'
            message = f"Validation failures detected ({fail_count} failures)"
        else:
            overall_status = 'PASS'
            message = f"All validations passed ({pass_count} tests)"
        
        return {
            'status': overall_status,
            'message': message,
            'summary': {
                'total_tests': len(validation_results),
                'passed': pass_count,
                'failed': fail_count,
                'errors': error_count
            }
        }
    
    def generate_validation_report(self, output_file: Optional[str] = None) -> str:
        """Generate comprehensive validation report."""
        if not self.validation_results:
            raise ValueError("No validation results available. Run validation first.")
        
        # Generate report content
        report_lines = [
            "=" * 80,
            "PROJECT-ALPHA DEPLOYMENT VALIDATION REPORT",
            "=" * 80,
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Duration: {self.validation_results['validation_duration_seconds']:.2f} seconds",
            "",
            "OVERALL STATUS:",
            f"Status: {self.validation_results['overall_status']['status']}",
            f"Message: {self.validation_results['overall_status']['message']}",
            "",
            "DETAILED RESULTS:",
            ""
        ]
        
        # Add detailed results for each validation category
        categories = [
            ('System Compatibility', 'system_compatibility'),
            ('Dependencies', 'dependencies'),
            ('Excel Import', 'excel_import'),
            ('Performance', 'performance'),
            ('Resource Management', 'resource_management'),
            ('Cross-System Compatibility', 'cross_system')
        ]
        
        for category_name, category_key in categories:
            result = self.validation_results.get(category_key, {})
            status = result.get('status', 'UNKNOWN')
            
            report_lines.extend([
                f"{category_name}:",
                f"  Status: {status}",
                f"  Details: {result.get('details', 'No details available')}",
                ""
            ])
            
            if status in ['FAIL', 'ERROR'] and 'error' in result:
                report_lines.extend([
                    f"  Error: {result['error']}",
                    ""
                ])
        
        # Add recommendations if any
        if 'system_compatibility' in self.validation_results:
            recommendations = self.validation_results['system_compatibility'].get('recommendations', {})
            if recommendations:
                report_lines.extend([
                    "RECOMMENDATIONS:",
                    f"  Processing Strategy: {recommendations.get('strategy', 'unknown')}",
                    f"  Chunk Size: {recommendations.get('chunk_size', 'unknown')}",
                    f"  Max Memory: {recommendations.get('max_memory_mb', 'unknown')}MB",
                    ""
                ])
        
        report_content = "\n".join(report_lines)
        
        # Save to file if specified
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                logger.info(f"Validation report saved to: {output_file}")
            except Exception as e:
                logger.error(f"Failed to save validation report: {e}")
        
        return report_content
    
    def cleanup(self):
        """Cleanup test environment."""
        if self.test_data_dir and os.path.exists(self.test_data_dir):
            try:
                import shutil
                shutil.rmtree(self.test_data_dir)
                logger.info("Test environment cleaned up")
            except Exception as e:
                logger.warning(f"Failed to cleanup test environment: {e}")

def run_deployment_validation(output_file: Optional[str] = None) -> Dict[str, Any]:
    """
    Run comprehensive deployment validation.
    
    Args:
        output_file: Optional file path to save validation report
        
    Returns:
        dict: Validation results
    """
    validator = DeploymentValidator()
    
    try:
        # Run validation
        results = validator.run_comprehensive_validation()
        
        # Generate report
        if output_file:
            validator.generate_validation_report(output_file)
        
        return results
        
    finally:
        validator.cleanup()

def validate_excel_import_readiness() -> bool:
    """
    Quick validation of Excel import readiness.
    
    Returns:
        bool: True if system is ready for Excel import operations
    """
    try:
        validator = DeploymentValidator()
        
        # Run essential validations only
        system_validation = validator.validate_system_compatibility()
        dependency_validation = validator.validate_dependencies()
        
        validator.cleanup()
        
        return (system_validation['status'] == 'PASS' and 
                dependency_validation['status'] == 'PASS')
        
    except Exception as e:
        logger.error(f"Excel import readiness validation failed: {e}")
        return False

if __name__ == "__main__":
    # Command-line interface
    import argparse
    
    parser = argparse.ArgumentParser(description='PROJECT-ALPHA Deployment Validation')
    parser.add_argument('--output', '-o', help='Output file for validation report')
    parser.add_argument('--quick', '-q', action='store_true', help='Quick validation only')
    
    args = parser.parse_args()
    
    if args.quick:
        print("Running quick Excel import readiness validation...")
        ready = validate_excel_import_readiness()
        print(f"Excel Import Ready: {'YES' if ready else 'NO'}")
        sys.exit(0 if ready else 1)
    else:
        print("Running comprehensive deployment validation...")
        results = run_deployment_validation(args.output)
        
        print("\n" + "=" * 60)
        print("VALIDATION SUMMARY")
        print("=" * 60)
        print(f"Overall Status: {results['overall_status']['status']}")
        print(f"Message: {results['overall_status']['message']}")
        
        if args.output:
            print(f"Detailed report saved to: {args.output}")
        
        # Exit with appropriate code
        sys.exit(0 if results['overall_status']['status'] == 'PASS' else 1)
