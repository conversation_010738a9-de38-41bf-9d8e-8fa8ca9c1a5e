#!/usr/bin/env python3
"""
Locale Compatibility Handler for PROJECT-ALPHA
Addresses regional settings, date formats, and locale-specific configurations
to ensure consistent Excel import behavior across different deployment environments.
"""

import os
import sys
import locale
import logging
import re
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any, Tuple
import calendar

logger = logging.getLogger('locale_compatibility')

class LocaleCompatibilityHandler:
    """Handles locale-specific compatibility issues for Excel import."""
    
    def __init__(self):
        self.original_locale = None
        self.current_locale = None
        self.date_formats = []
        self.number_formats = {}
        self.encoding_info = {}
        
        self._detect_system_locale()
        self._setup_date_formats()
        self._setup_number_formats()
        self._setup_encoding_handling()
    
    def _detect_system_locale(self):
        """Detect and store current system locale information."""
        try:
            self.original_locale = locale.getlocale()
            self.current_locale = locale.getdefaultlocale()
            
            logger.info(f"System locale: {self.current_locale}")
            logger.info(f"Current locale: {self.original_locale}")
            
        except Exception as e:
            logger.warning(f"Could not detect system locale: {e}")
            self.original_locale = ('en_US', 'UTF-8')
            self.current_locale = ('en_US', 'UTF-8')
    
    def _setup_date_formats(self):
        """Setup comprehensive date format handling for different locales."""
        # Common date formats across different locales
        self.date_formats = [
            # ISO format (universal)
            '%Y-%m-%d',
            '%Y/%m/%d',
            
            # US formats
            '%m/%d/%Y',
            '%m-%d-%Y',
            '%m.%d.%Y',
            
            # European formats
            '%d/%m/%Y',
            '%d-%m-%Y',
            '%d.%m.%Y',
            
            # UK formats
            '%d/%m/%y',
            '%d-%m-%y',
            
            # Alternative formats
            '%Y%m%d',
            '%d%m%Y',
            '%m%d%Y',
            
            # With time
            '%Y-%m-%d %H:%M:%S',
            '%d/%m/%Y %H:%M:%S',
            '%m/%d/%Y %H:%M:%S',
            
            # Month names (English)
            '%d %B %Y',
            '%B %d, %Y',
            '%d %b %Y',
            '%b %d, %Y',
            
            # Excel-specific formats
            '%d-%b-%Y',
            '%d-%b-%y',
            '%d/%b/%Y',
            '%d/%b/%y'
        ]
        
        # Add locale-specific formats if available
        try:
            # Try to get locale-specific date format
            locale_format = locale.nl_langinfo(locale.D_FMT)
            if locale_format and locale_format not in self.date_formats:
                self.date_formats.insert(0, locale_format)
        except (AttributeError, locale.Error):
            pass
    
    def _setup_number_formats(self):
        """Setup number format handling for different locales."""
        try:
            # Get locale-specific number formatting
            self.number_formats = {
                'decimal_point': locale.localeconv().get('decimal_point', '.'),
                'thousands_sep': locale.localeconv().get('thousands_sep', ','),
                'grouping': locale.localeconv().get('grouping', [3, 0])
            }
        except Exception as e:
            logger.warning(f"Could not get locale number formats: {e}")
            # Default to US format
            self.number_formats = {
                'decimal_point': '.',
                'thousands_sep': ',',
                'grouping': [3, 0]
            }
    
    def _setup_encoding_handling(self):
        """Setup encoding detection and handling."""
        self.encoding_info = {
            'default_encoding': sys.getdefaultencoding(),
            'filesystem_encoding': sys.getfilesystemencoding(),
            'preferred_encoding': locale.getpreferredencoding(),
            'supported_encodings': ['utf-8', 'utf-16', 'cp1252', 'iso-8859-1', 'ascii']
        }
        
        logger.info(f"Encoding info: {self.encoding_info}")
    
    def set_safe_locale(self) -> bool:
        """Set a safe locale for consistent data processing."""
        safe_locales = [
            'C',  # POSIX locale
            'en_US.UTF-8',
            'en_US',
            'English_United States.1252',  # Windows
            'English'
        ]
        
        for safe_locale in safe_locales:
            try:
                locale.setlocale(locale.LC_ALL, safe_locale)
                logger.info(f"Set safe locale: {safe_locale}")
                return True
            except locale.Error:
                continue
        
        logger.warning("Could not set any safe locale, using system default")
        return False
    
    def restore_original_locale(self):
        """Restore original locale settings."""
        try:
            if self.original_locale:
                locale.setlocale(locale.LC_ALL, self.original_locale)
                logger.info("Restored original locale")
        except locale.Error as e:
            logger.warning(f"Could not restore original locale: {e}")
    
    def parse_date_robust(self, date_value: Any) -> Optional[str]:
        """
        Parse date value robustly across different locale formats.
        
        Args:
            date_value: Date value from Excel (string, number, datetime object)
            
        Returns:
            str: ISO format date string (YYYY-MM-DD) or None if parsing fails
        """
        if not date_value or str(date_value).strip() in ['', '-', 'NA', 'N/A']:
            return None
        
        # Handle datetime objects
        if isinstance(date_value, (datetime, date)):
            return date_value.strftime('%Y-%m-%d')
        
        # Handle numeric Excel dates
        if isinstance(date_value, (int, float)):
            return self._parse_excel_numeric_date(date_value)
        
        # Handle string dates
        if isinstance(date_value, str):
            return self._parse_string_date(date_value.strip())
        
        return None
    
    def _parse_excel_numeric_date(self, numeric_value: float) -> Optional[str]:
        """Parse Excel numeric date values."""
        try:
            # Excel date origins
            excel_origins = [
                datetime(1899, 12, 30),  # Windows Excel
                datetime(1904, 1, 1)     # Mac Excel
            ]
            
            for origin in excel_origins:
                try:
                    # Convert Excel serial date to datetime
                    excel_date = origin + timedelta(days=numeric_value)
                    
                    # Validate reasonable date range
                    if 1900 <= excel_date.year <= 2100:
                        return excel_date.strftime('%Y-%m-%d')
                except (ValueError, OverflowError):
                    continue
            
            # Try pandas conversion if available
            try:
                import pandas as pd
                for origin in ['1899-12-30', '1904-01-01']:
                    try:
                        result = pd.to_datetime(numeric_value, unit='D', origin=origin)
                        return result.strftime('%Y-%m-%d')
                    except Exception:
                        continue
            except ImportError:
                pass
            
        except Exception as e:
            logger.debug(f"Could not parse numeric date {numeric_value}: {e}")
        
        return None
    
    def _parse_string_date(self, date_str: str) -> Optional[str]:
        """Parse string date values using multiple format attempts."""
        # Clean the string
        date_str = re.sub(r'[^\w\s\-/.:,]', '', date_str)
        
        # Try each date format
        for fmt in self.date_formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                return parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                continue
        
        # Try fuzzy parsing for month names
        return self._parse_fuzzy_date(date_str)
    
    def _parse_fuzzy_date(self, date_str: str) -> Optional[str]:
        """Attempt fuzzy date parsing for complex formats."""
        try:
            # Extract numbers and month names
            numbers = re.findall(r'\d+', date_str)
            month_names = re.findall(r'[A-Za-z]+', date_str)
            
            if len(numbers) >= 2 and month_names:
                # Try to identify month
                month_num = self._parse_month_name(month_names[0])
                if month_num:
                    # Determine day and year from numbers
                    if len(numbers) == 2:
                        day, year = numbers
                    elif len(numbers) >= 3:
                        # Guess based on magnitude
                        nums = [int(n) for n in numbers]
                        year = max(nums)  # Largest number is likely year
                        day = min(n for n in nums if n != year and n <= 31)
                    else:
                        return None
                    
                    # Validate and construct date
                    try:
                        day, year = int(day), int(year)
                        
                        # Handle 2-digit years
                        if year < 100:
                            year += 2000 if year < 50 else 1900
                        
                        # Validate ranges
                        if 1900 <= year <= 2100 and 1 <= day <= 31:
                            parsed_date = datetime(year, month_num, day)
                            return parsed_date.strftime('%Y-%m-%d')
                    except ValueError:
                        pass
        
        except Exception as e:
            logger.debug(f"Fuzzy date parsing failed for '{date_str}': {e}")
        
        return None
    
    def _parse_month_name(self, month_str: str) -> Optional[int]:
        """Parse month name to number."""
        month_str = month_str.lower()
        
        # Full month names
        month_names = {
            'january': 1, 'february': 2, 'march': 3, 'april': 4,
            'may': 5, 'june': 6, 'july': 7, 'august': 8,
            'september': 9, 'october': 10, 'november': 11, 'december': 12
        }
        
        # Abbreviated month names
        month_abbrevs = {
            'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4,
            'may': 5, 'jun': 6, 'jul': 7, 'aug': 8,
            'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
        }
        
        # Check full names first
        if month_str in month_names:
            return month_names[month_str]
        
        # Check abbreviations
        if month_str in month_abbrevs:
            return month_abbrevs[month_str]
        
        # Check partial matches
        for name, num in month_names.items():
            if month_str.startswith(name[:3]) or name.startswith(month_str):
                return num
        
        return None
    
    def parse_number_robust(self, number_value: Any) -> float:
        """
        Parse numeric value robustly across different locale formats.
        
        Args:
            number_value: Numeric value from Excel
            
        Returns:
            float: Parsed number or 0.0 if parsing fails
        """
        if number_value is None or str(number_value).strip() == '':
            return 0.0
        
        # Handle already numeric values
        if isinstance(number_value, (int, float)):
            return float(number_value)
        
        # Handle string numbers
        if isinstance(number_value, str):
            return self._parse_string_number(number_value.strip())
        
        return 0.0
    
    def _parse_string_number(self, number_str: str) -> float:
        """Parse string number with locale-aware formatting."""
        try:
            # Remove common non-numeric characters
            cleaned = re.sub(r'[^\d\-+.,]', '', number_str)
            
            if not cleaned:
                return 0.0
            
            # Handle different decimal separators
            decimal_point = self.number_formats['decimal_point']
            thousands_sep = self.number_formats['thousands_sep']
            
            # If locale uses comma as decimal separator
            if decimal_point == ',' and thousands_sep == '.':
                # European format: 1.234,56
                if ',' in cleaned and '.' in cleaned:
                    # Last comma is decimal separator
                    parts = cleaned.rsplit(',', 1)
                    if len(parts) == 2:
                        integer_part = parts[0].replace('.', '')
                        decimal_part = parts[1]
                        cleaned = f"{integer_part}.{decimal_part}"
                elif ',' in cleaned:
                    # Only comma - could be thousands or decimal
                    comma_pos = cleaned.rfind(',')
                    after_comma = cleaned[comma_pos + 1:]
                    if len(after_comma) <= 2:  # Likely decimal
                        cleaned = cleaned.replace(',', '.')
                    else:  # Likely thousands
                        cleaned = cleaned.replace(',', '')
            else:
                # US format: 1,234.56
                if ',' in cleaned and '.' in cleaned:
                    # Remove thousands separators (commas)
                    parts = cleaned.split('.')
                    if len(parts) == 2:
                        integer_part = parts[0].replace(',', '')
                        decimal_part = parts[1]
                        cleaned = f"{integer_part}.{decimal_part}"
                elif ',' in cleaned:
                    # Only comma - likely thousands separator
                    cleaned = cleaned.replace(',', '')
            
            return float(cleaned)
            
        except (ValueError, TypeError) as e:
            logger.debug(f"Could not parse number '{number_str}': {e}")
            return 0.0
    
    def normalize_text_encoding(self, text: str) -> str:
        """Normalize text encoding for consistent processing."""
        if not isinstance(text, str):
            text = str(text)
        
        try:
            # Try to encode/decode to ensure clean UTF-8
            return text.encode('utf-8', errors='ignore').decode('utf-8')
        except Exception:
            # Fallback to ASCII-safe version
            return ''.join(char for char in text if ord(char) < 128)
    
    def get_locale_info(self) -> Dict[str, Any]:
        """Get comprehensive locale information for debugging."""
        return {
            'original_locale': self.original_locale,
            'current_locale': self.current_locale,
            'date_formats_count': len(self.date_formats),
            'number_formats': self.number_formats,
            'encoding_info': self.encoding_info,
            'system_locale': locale.getlocale(),
            'default_locale': locale.getdefaultlocale()
        }

# Global locale handler instance
locale_handler = LocaleCompatibilityHandler()

# Convenience functions for easy integration
def parse_date_locale_safe(date_value: Any) -> Optional[str]:
    """Parse date value with locale compatibility."""
    return locale_handler.parse_date_robust(date_value)

def parse_number_locale_safe(number_value: Any) -> float:
    """Parse number value with locale compatibility."""
    return locale_handler.parse_number_robust(number_value)

def normalize_text_safe(text: str) -> str:
    """Normalize text encoding safely."""
    return locale_handler.normalize_text_encoding(text)

def setup_safe_locale() -> bool:
    """Setup safe locale for data processing."""
    return locale_handler.set_safe_locale()

def restore_locale():
    """Restore original locale settings."""
    locale_handler.restore_original_locale()

def get_locale_compatibility_info() -> Dict[str, Any]:
    """Get locale compatibility information."""
    return locale_handler.get_locale_info()

# Context manager for safe locale operations
class SafeLocaleContext:
    """Context manager for safe locale operations."""
    
    def __enter__(self):
        self.original_locale = locale.getlocale()
        setup_safe_locale()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        try:
            locale.setlocale(locale.LC_ALL, self.original_locale)
        except locale.Error:
            pass

if __name__ == "__main__":
    # Test locale compatibility
    print("=" * 60)
    print("PROJECT-ALPHA LOCALE COMPATIBILITY TEST")
    print("=" * 60)
    
    info = get_locale_compatibility_info()
    print(f"Current Locale: {info['current_locale']}")
    print(f"Date Formats Available: {info['date_formats_count']}")
    print(f"Decimal Point: '{info['number_formats']['decimal_point']}'")
    print(f"Thousands Separator: '{info['number_formats']['thousands_sep']}'")
    
    # Test date parsing
    test_dates = [
        "2023-12-25",
        "25/12/2023",
        "12/25/2023",
        "25-Dec-2023",
        "Dec 25, 2023",
        "25.12.2023"
    ]
    
    print("\nDate Parsing Tests:")
    for test_date in test_dates:
        parsed = parse_date_locale_safe(test_date)
        print(f"'{test_date}' -> {parsed}")
    
    # Test number parsing
    test_numbers = [
        "1234.56",
        "1,234.56",
        "1.234,56",
        "1 234,56",
        "1234"
    ]
    
    print("\nNumber Parsing Tests:")
    for test_number in test_numbers:
        parsed = parse_number_locale_safe(test_number)
        print(f"'{test_number}' -> {parsed}")
