"""Window utilities for responsive design and DPI-aware state management."""

import logging
from PyQt5.QtWidgets import QApplication, QDesktopWidget
from PyQt5.QtCore import QSettings, QRect, QSize
from PyQt5.QtGui import QScreen, QFont
import config

logger = logging.getLogger('window_utils')
import math

# Font size multiplier for global font size increase (1.0 = normal, 1.2 = 20% larger)
FONT_SIZE_MULTIPLIER = 1.2



class DPIScaler:
    """Comprehensive DPI scaling utility for all UI components."""
    
    _cached_scale_factor = None
    _cached_screen_info = None
    
    @classmethod
    def get_screen_info(cls):
        """Get comprehensive screen information."""
        if cls._cached_screen_info is None:
            app = QApplication.instance()
            if app:
                try:
                    screen = app.primaryScreen()
                    if screen:
                        geometry = screen.availableGeometry()
                        dpi_x = screen.logicalDotsPerInchX()
                        dpi_y = screen.logicalDotsPerInchY()
                        device_pixel_ratio = screen.devicePixelRatio()
                        
                        cls._cached_screen_info = {
                            'width': geometry.width(),
                            'height': geometry.height(),
                            'dpi_x': dpi_x,
                            'dpi_y': dpi_y,
                            'device_ratio': device_pixel_ratio,
                            'scale_factor': max(dpi_x, dpi_y) / 96.0  # 96 DPI is standard
                        }
                except:
                    pass
            
            # Fallback for older PyQt5 or if screen detection fails
            if cls._cached_screen_info is None:
                cls._cached_screen_info = {
                    'width': 1920,
                    'height': 1080,
                    'dpi_x': 96,
                    'dpi_y': 96,
                    'device_ratio': 1.0,
                    'scale_factor': 1.0
                }
        
        return cls._cached_screen_info
    
    @classmethod
    def get_scale_factor(cls):
        """Get the overall DPI scale factor."""
        if cls._cached_scale_factor is None:
            screen_info = cls.get_screen_info()
            cls._cached_scale_factor = screen_info['scale_factor']
        return cls._cached_scale_factor
    
    @classmethod
    def scale_size(cls, size, min_size=None, max_size=None):
        """Scale a size value by DPI factor."""
        scale_factor = cls.get_scale_factor()
        scaled = int(size * scale_factor)
        
        if min_size is not None:
            scaled = max(scaled, min_size)
        if max_size is not None:
            scaled = min(scaled, max_size)
            
        return scaled
    
    @classmethod
    def scale_font_size(cls, base_size):
        """Scale font size by DPI factor and global font multiplier."""
        scale_factor = cls.get_scale_factor()
        # Always apply global font multiplier first to ensure it's visible
        base_with_multiplier = base_size * FONT_SIZE_MULTIPLIER
        
        # Then apply DPI scaling (less conservative to preserve multiplier effect)
        if scale_factor > 1.0:
            font_scale = 1.0 + (scale_factor - 1.0) * 0.5  # Less conservative
            scaled_size = base_with_multiplier * font_scale
        else:
            # For normal/low DPI screens, just use the multiplier
            scaled_size = base_with_multiplier
            
        return max(8, int(scaled_size + 0.5))  # Round instead of truncate
    
    @classmethod
    def scale_margins(cls, margin_tuple):
        """Scale margin tuple (left, top, right, bottom)."""
        scale_factor = cls.get_scale_factor()
        return tuple(int(margin * scale_factor) for margin in margin_tuple)
    
    @classmethod
    def get_responsive_tile_size(cls, base_width=240, base_height=120):
        """Get responsive tile size based on screen size and DPI."""
        screen_info = cls.get_screen_info()
        scale_factor = cls.get_scale_factor()

        # Scale by DPI first
        width = int(base_width * scale_factor)
        height = int(base_height * scale_factor)

        # Additional scaling based on screen resolution
        screen_width = screen_info['width']
        screen_height = screen_info['height']

        # Enhanced low-resolution support
        if screen_width <= 1366:  # Low resolution screens (1366x768, 1280x720, etc.)
            if screen_width <= 1280:  # Very small screens
                width = int(width * 0.75)
                height = int(height * 0.75)
            else:  # 1366x768
                width = int(width * 0.85)
                height = int(height * 0.85)
        elif screen_width <= 1600:  # Medium screens
            width = int(width * 0.95)
            height = int(height * 0.95)
        elif screen_width > 2560:  # Very large screens
            width = int(width * 1.2)
            height = int(height * 1.2)

        # Ensure minimum usable size
        width = max(width, 180)
        height = max(height, 90)

        return width, height
    
    @classmethod
    def get_responsive_dialog_size(cls, base_width=800, base_height=600, 
                                 screen_percent_width=0.8, screen_percent_height=0.8):
        """Get responsive dialog size."""
        screen_info = cls.get_screen_info()
        scale_factor = cls.get_scale_factor()
        
        # Calculate based on screen percentage
        screen_based_width = int(screen_info['width'] * screen_percent_width)
        screen_based_height = int(screen_info['height'] * screen_percent_height)
        
        # Scale base size by DPI
        dpi_scaled_width = int(base_width * scale_factor)
        dpi_scaled_height = int(base_height * scale_factor)
        
        # Use the larger of the two approaches
        width = max(screen_based_width, dpi_scaled_width)
        height = max(screen_based_height, dpi_scaled_height)
        
        # Ensure we don't exceed screen
        width = min(width, screen_info['width'] - 100)
        height = min(height, screen_info['height'] - 100)
        
        return width, height
    
    @classmethod
    def get_scaled_icon_size(cls, base_size=16):
        """Get scaled icon size."""
        return cls.scale_size(base_size, min_size=12, max_size=32)
    
    @classmethod
    def create_scaled_font(cls, base_size=10, bold=False, family=None):
        """Create a QFont with DPI-appropriate scaling and global font multiplier."""
        font = QFont()
        if family:
            font.setFamily(family)
        
        scaled_size = cls.scale_font_size(base_size)
        font.setPointSize(scaled_size)
        font.setBold(bold)
        
        return font


class WindowManager:
    """Enhanced window manager with DPI awareness."""
    
    @staticmethod
    def get_screen_geometry():
        """Get the primary screen geometry with safe fallback detection."""
        try:
            app = QApplication.instance()
            if app and hasattr(app, 'primaryScreen'):
                screen = app.primaryScreen()
                if screen:
                    geometry = screen.availableGeometry()
                    # Validate geometry is reasonable
                    if geometry.width() > 0 and geometry.height() > 0:
                        return geometry

            # Alternative method for older PyQt5 versions
            from PyQt5.QtWidgets import QDesktopWidget
            desktop = QDesktopWidget()
            geometry = desktop.availableGeometry()
            if geometry.width() > 0 and geometry.height() > 0:
                return geometry

        except Exception as e:
            logger.warning(f"Screen geometry detection failed: {e}")

        # Safe fallback based on common military system resolutions
        from PyQt5.QtCore import QRect
        try:
            # Try to detect actual screen size using system calls
            import tkinter as tk
            root = tk.Tk()
            width = root.winfo_screenwidth()
            height = root.winfo_screenheight()
            root.destroy()

            if width > 0 and height > 0:
                logger.info(f"Detected screen resolution via tkinter: {width}x{height}")
                return QRect(0, 0, width, height)

        except Exception as e:
            logger.warning(f"Tkinter screen detection failed: {e}")

        # Final safe fallback - use minimum viable resolution for military systems
        logger.warning("Using safe fallback resolution: 1024x768")
        return QRect(0, 0, 1024, 768)
    
    @staticmethod
    def setup_responsive_window(window, width_percent=0.75, height_percent=0.8,
                              min_width_percent=0.6, min_height_percent=0.6):
        """Setup a window with responsive sizing and DPI awareness."""
        # Get DPI-aware dimensions
        screen_info = DPIScaler.get_screen_info()
        screen_width = screen_info['width']
        screen_height = screen_info['height']

        # Adjust percentages for low-resolution screens
        if screen_width <= 1366:  # Low resolution optimization
            # Use more screen real estate on small screens
            width_percent = min(0.95, width_percent + 0.1)  # Use up to 95% width
            height_percent = min(0.9, height_percent + 0.05)  # Use up to 90% height
            min_width_percent = max(0.85, min_width_percent + 0.15)  # Higher minimum
            min_height_percent = max(0.75, min_height_percent + 0.1)  # Higher minimum

        # Target size with DPI scaling
        base_width = int(screen_width * width_percent)
        base_height = int(screen_height * height_percent)

        # Minimum size with DPI scaling - adjusted for low-res
        if screen_width <= 1366:
            # Lower absolute minimums for small screens
            min_width = max(int(screen_width * min_width_percent),
                           DPIScaler.scale_size(1000))  # Reduced from 800
            min_height = max(int(screen_height * min_height_percent),
                            DPIScaler.scale_size(650))   # Reduced from 600
        else:
            min_width = max(int(screen_width * min_width_percent),
                           DPIScaler.scale_size(800))
            min_height = max(int(screen_height * min_height_percent),
                            DPIScaler.scale_size(600))

        # Ensure target meets minimum
        width = max(base_width, min_width)
        height = max(base_height, min_height)

        # Ensure we don't exceed screen bounds (leave some margin)
        max_width = screen_width - 50
        max_height = screen_height - 100  # Account for taskbar
        width = min(width, max_width)
        height = min(height, max_height)

        # Set window properties
        window.resize(width, height)
        window.setMinimumSize(min_width, min_height)

        # Center the window
        WindowManager.center_window(window)
    
    @staticmethod
    def center_window(window):
        """Center a window on the screen."""
        screen_rect = WindowManager.get_screen_geometry()
        window_rect = window.geometry()
        
        x = (screen_rect.width() - window_rect.width()) // 2
        y = (screen_rect.height() - window_rect.height()) // 2
        
        window.move(x, y)
    
    @staticmethod
    def save_window_state(window, settings_key="window_state"):
        """Save window state to QSettings."""
        settings = QSettings()
        settings.setValue(f"{settings_key}/geometry", window.saveGeometry())
        settings.setValue(f"{settings_key}/state", window.saveState())
    
    @staticmethod
    def restore_window_state(window, settings_key="window_state"):
        """Restore window state from QSettings."""
        settings = QSettings()
        geometry = settings.value(f"{settings_key}/geometry")
        state = settings.value(f"{settings_key}/state")
        
        if geometry:
            window.restoreGeometry(geometry)
        else:
            # If no saved state, use responsive sizing and center
            WindowManager.setup_responsive_window(window)
        
        if state:
            window.restoreState(state)


class DialogManager:
    """Enhanced dialog manager with DPI awareness."""
    
    @staticmethod
    def setup_responsive_dialog(dialog, width_percent=0.5, height_percent=0.6,
                              min_width=400, min_height=300):
        """Setup a dialog with responsive sizing and DPI awareness."""
        # Get DPI-aware dimensions
        width, height = DPIScaler.get_responsive_dialog_size(
            base_width=DPIScaler.scale_size(min_width),
            base_height=DPIScaler.scale_size(min_height),
            screen_percent_width=width_percent,
            screen_percent_height=height_percent
        )
        
        # Scale minimum sizes
        scaled_min_width = DPIScaler.scale_size(min_width)
        scaled_min_height = DPIScaler.scale_size(min_height)
        
        dialog.resize(width, height)
        dialog.setMinimumSize(scaled_min_width, scaled_min_height)
        
        # Center the dialog
        WindowManager.center_window(dialog)


class LayoutManager:
    """Manager for DPI-aware layouts and spacing."""
    
    @staticmethod
    def apply_responsive_margins(layout, base_margins=(10, 10, 10, 10)):
        """Apply DPI-scaled margins to a layout."""
        scaled_margins = DPIScaler.scale_margins(base_margins)
        layout.setContentsMargins(*scaled_margins)
    
    @staticmethod
    def apply_responsive_spacing(layout, base_spacing=5):
        """Apply DPI-scaled spacing to a layout."""
        scaled_spacing = DPIScaler.scale_size(base_spacing)
        layout.setSpacing(scaled_spacing)
    
    @staticmethod
    def setup_responsive_layout(layout, margins=(10, 10, 10, 10), spacing=5):
        """Setup a layout with responsive margins and spacing."""
        LayoutManager.apply_responsive_margins(layout, margins)
        LayoutManager.apply_responsive_spacing(layout, spacing)


class FormManager:
    """Manager for DPI-aware form layouts and widgets."""
    
    @staticmethod
    def setup_responsive_form_layout(form_layout, horizontal_spacing=None, vertical_spacing=None):
        """Setup a QFormLayout with DPI-aware spacing."""
        if horizontal_spacing is None:
            horizontal_spacing = 10
        if vertical_spacing is None:
            vertical_spacing = 6
            
        # Scale spacing
        h_spacing = DPIScaler.scale_size(horizontal_spacing, min_size=5, max_size=20)
        v_spacing = DPIScaler.scale_size(vertical_spacing, min_size=3, max_size=15)
        
        form_layout.setHorizontalSpacing(h_spacing)
        form_layout.setVerticalSpacing(v_spacing)
        
        # Scale form margins
        LayoutManager.apply_responsive_margins(form_layout, (10, 5, 10, 5))
    
    @staticmethod
    def create_responsive_input_widget(widget_type, **kwargs):
        """Create an input widget with DPI-aware sizing."""
        widget = widget_type(**kwargs)
        
        # Apply scaled font
        font = DPIScaler.create_scaled_font(10)
        widget.setFont(font)
        
        # Scale minimum height for input widgets
        if hasattr(widget, 'setMinimumHeight'):
            min_height = DPIScaler.scale_size(25, min_size=20, max_size=40)
            widget.setMinimumHeight(min_height)
        
        return widget
    
    @staticmethod
    def apply_responsive_widget_sizing(widget, base_width=None, base_height=None):
        """Apply DPI-aware sizing to a widget."""
        if base_width is not None:
            scaled_width = DPIScaler.scale_size(base_width, min_size=100, max_size=600)
            widget.setMinimumWidth(scaled_width)
            
        if base_height is not None:
            scaled_height = DPIScaler.scale_size(base_height, min_size=20, max_size=100)
            widget.setMinimumHeight(scaled_height)


class ChartManager:
    """Manager for DPI-aware matplotlib charts."""

    @staticmethod
    def get_responsive_figure_size(base_width=8, base_height=6):
        """Get DPI-aware figure size for matplotlib charts."""
        scale_factor = DPIScaler.get_scale_factor()
        screen_info = DPIScaler.get_screen_info()

        # Scale figure size more conservatively for charts
        chart_scale = 1.0 + (scale_factor - 1.0) * 0.5

        width = base_width * chart_scale
        height = base_height * chart_scale

        # Additional scaling for low-resolution screens
        if screen_info['width'] <= 1366:
            # Smaller charts for low-res screens
            width *= 0.8
            height *= 0.8

        # Ensure reasonable bounds
        width = max(min(width, 16), 3)  # Reduced minimum from 4 to 3
        height = max(min(height, 12), 2.5)  # Reduced minimum from 3 to 2.5

        return width, height
    
    @staticmethod
    def get_responsive_font_sizes():
        """Get DPI-aware font sizes for chart elements with global font multiplier."""
        scale_factor = DPIScaler.get_scale_factor()
        
        # Apply global font multiplier first to ensure it's always visible
        base_sizes = {
            'title': 14 * FONT_SIZE_MULTIPLIER,
            'label': 12 * FONT_SIZE_MULTIPLIER,
            'tick': 10 * FONT_SIZE_MULTIPLIER,
            'legend': 10 * FONT_SIZE_MULTIPLIER
        }
        
        # Apply DPI scaling only if needed (less conservative)
        if scale_factor > 1.0:
            font_scale = 1.0 + (scale_factor - 1.0) * 0.4  # Less conservative
            for key in base_sizes:
                base_sizes[key] *= font_scale
        
        # Round and ensure minimums
        return {
            'title': max(int(base_sizes['title'] + 0.5), 10),
            'label': max(int(base_sizes['label'] + 0.5), 8), 
            'tick': max(int(base_sizes['tick'] + 0.5), 7),
            'legend': max(int(base_sizes['legend'] + 0.5), 7)
        }
    
    @staticmethod
    def apply_responsive_chart_styling(fig, ax):
        """Apply DPI-aware styling to a matplotlib chart."""
        font_sizes = ChartManager.get_responsive_font_sizes()
        
        # Update title font size
        if ax.get_title():
            ax.title.set_fontsize(font_sizes['title'])
        
        # Update axis labels
        ax.xaxis.label.set_fontsize(font_sizes['label'])
        ax.yaxis.label.set_fontsize(font_sizes['label'])
        
        # Update tick labels
        ax.tick_params(axis='both', which='major', labelsize=font_sizes['tick'])
        
        # Update legend if present
        legend = ax.get_legend()
        if legend:
            for text in legend.get_texts():
                text.set_fontsize(font_sizes['legend'])
        
        # Adjust layout to prevent clipping
        fig.tight_layout()
    
    @staticmethod
    def create_responsive_figure():
        """Create a matplotlib figure with DPI-aware sizing."""
        try:
            import matplotlib.pyplot as plt
            width, height = ChartManager.get_responsive_figure_size()
            
            # Get system DPI for matplotlib
            dpi = DPIScaler.get_screen_info()['dpi_x']
            
            fig = plt.figure(figsize=(width, height), dpi=min(dpi, 150))  # Cap DPI for performance
            return fig
        except ImportError:
            # Fallback if matplotlib not available
            return None


class LowResolutionManager:
    """Manager for optimizing layouts on low-resolution displays (1366x768 and below)."""

    @staticmethod
    def is_low_resolution():
        """Check if current screen is low resolution."""
        screen_info = DPIScaler.get_screen_info()
        return screen_info['width'] <= 1366 or screen_info['height'] <= 768

    @staticmethod
    def get_optimized_dashboard_layout():
        """Get optimized dashboard layout parameters for low-resolution screens."""
        if not LowResolutionManager.is_low_resolution():
            return {
                'stats_frame_width': 360,
                'chart_min_height': 120,
                'tile_spacing': 10,
                'use_compact_tiles': False,
                'single_column_alerts': False
            }

        # Low-resolution optimizations
        return {
            'stats_frame_width': 320,  # Reduced from 360
            'chart_min_height': 100,   # Reduced from 120
            'tile_spacing': 8,         # Reduced from 10
            'use_compact_tiles': True,
            'single_column_alerts': True
        }

    @staticmethod
    def get_optimized_table_settings():
        """Get optimized table settings for low-resolution screens."""
        if not LowResolutionManager.is_low_resolution():
            return {
                'row_height': 30,
                'header_height': 35,
                'font_size': 10,
                'show_grid': True,
                'column_resize_mode': 'interactive'
            }

        # Low-resolution optimizations
        return {
            'row_height': 26,          # Reduced row height
            'header_height': 30,       # Reduced header height
            'font_size': 9,            # Smaller font
            'show_grid': False,        # Hide grid for cleaner look
            'column_resize_mode': 'stretch'  # Auto-stretch columns
        }

    @staticmethod
    def get_optimized_dialog_size(base_width=800, base_height=600):
        """Get optimized dialog size with safe bounds validation."""
        try:
            screen_info = DPIScaler.get_screen_info()

            # Validate screen info
            if not screen_info or screen_info.get('width', 0) <= 0 or screen_info.get('height', 0) <= 0:
                logger.warning("Invalid screen info, using safe defaults")
                return min(base_width, 1024), min(base_height, 768)

            screen_width = screen_info['width']
            screen_height = screen_info['height']

            # Safety checks for screen dimensions
            if screen_width < 800 or screen_height < 600:
                logger.warning(f"Very small screen detected: {screen_width}x{screen_height}")
                # Use smaller base sizes for very small screens
                base_width = min(base_width, screen_width - 50)
                base_height = min(base_height, screen_height - 100)

            if not LowResolutionManager.is_low_resolution():
                # For normal resolution screens, ensure dialog fits
                max_width = int(screen_width * 0.8)
                max_height = int(screen_height * 0.8)
                return min(base_width, max_width), min(base_height, max_height)

            # Use more screen real estate on small screens
            max_width = int(screen_width * 0.9)
            max_height = int(screen_height * 0.85)

            # Ensure minimum viable sizes
            min_width = 400
            min_height = 300

            width = max(min_width, min(base_width, max_width))
            height = max(min_height, min(base_height, max_height))

            # Final safety check
            if width > screen_width:
                width = screen_width - 50
            if height > screen_height:
                height = screen_height - 100

            return width, height

        except Exception as e:
            logger.error(f"Error calculating dialog size: {e}")
            # Safe fallback
            return 800, 600

    @staticmethod
    def apply_low_res_optimizations(widget):
        """Apply low-resolution optimizations to a widget."""
        if not LowResolutionManager.is_low_resolution():
            return

        # Apply compact styling
        widget.setStyleSheet(widget.styleSheet() + """
            QWidget {
                font-size: 9px;
            }
            QPushButton {
                padding: 4px 8px;
                font-size: 9px;
            }
            QLabel {
                font-size: 9px;
            }
            QLineEdit, QComboBox, QSpinBox {
                font-size: 9px;
                padding: 2px;
            }
        """)


# Legacy functions for backward compatibility
def apply_responsive_layout_margins(layout, use_config=True):
    """Apply responsive margins to a layout (legacy function)."""
    if use_config and hasattr(config, 'TAB_CONTENT_MARGINS'):
        margins = config.TAB_CONTENT_MARGINS
        LayoutManager.apply_responsive_margins(layout, margins)
    else:
        LayoutManager.apply_responsive_margins(layout)

def get_scaled_font_size(base_size=10):
    """Get a font size scaled to screen DPI (legacy function)."""
    return DPIScaler.scale_font_size(base_size) 