"""
Comprehensive Audit Logging System for PROJECT-ALPHA
Tracks all CRUD operations with user information, timestamps, and change details.
"""

import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional, List
import database

logger = logging.getLogger('audit_logging')

class AuditLogger:
    """
    Centralized audit logging system for tracking all data changes.
    Provides comprehensive audit trails for military compliance.
    """
    
    @staticmethod
    def initialize_audit_tables():
        """Initialize audit logging tables in the database."""
        try:
            with database.get_db_connection() as conn:
                # Create audit_log table
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS audit_log (
                        audit_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        table_name TEXT NOT NULL,
                        record_id TEXT NOT NULL,
                        operation_type TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        old_values TEXT,
                        new_values TEXT,
                        changes_summary TEXT,
                        ip_address TEXT,
                        user_agent TEXT,
                        session_id TEXT,
                        notes TEXT
                    )
                ''')
                
                # Create audit_log index for performance
                conn.execute('''
                    CREATE INDEX IF NOT EXISTS idx_audit_log_table_record 
                    ON audit_log(table_name, record_id)
                ''')
                
                conn.execute('''
                    CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp 
                    ON audit_log(timestamp)
                ''')
                
                conn.execute('''
                    CREATE INDEX IF NOT EXISTS idx_audit_log_user 
                    ON audit_log(user_id)
                ''')
                
                # Create user_sessions table for session tracking
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS user_sessions (
                        session_id TEXT PRIMARY KEY,
                        user_id TEXT NOT NULL,
                        login_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                        last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
                        ip_address TEXT,
                        user_agent TEXT,
                        is_active BOOLEAN DEFAULT 1
                    )
                ''')
                
                logger.info("Audit logging tables initialized successfully")
                
        except Exception as e:
            logger.error(f"Error initializing audit tables: {e}")
            raise
            
    @staticmethod
    def log_operation(
        table_name: str,
        record_id: str,
        operation_type: str,
        user_id: str = "system",
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        notes: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> int:
        """
        Log a CRUD operation to the audit trail.
        
        Args:
            table_name: Name of the table being modified
            record_id: ID of the record being modified
            operation_type: Type of operation (CREATE, READ, UPDATE, DELETE)
            user_id: ID of the user performing the operation
            old_values: Previous values (for UPDATE and DELETE operations)
            new_values: New values (for CREATE and UPDATE operations)
            notes: Additional notes about the operation
            ip_address: IP address of the user
            user_agent: User agent string
            session_id: Session ID
            
        Returns:
            audit_id: ID of the created audit log entry
        """
        try:
            # Generate changes summary
            changes_summary = AuditLogger._generate_changes_summary(
                operation_type, old_values, new_values
            )
            
            # Serialize values to JSON
            old_values_json = json.dumps(old_values) if old_values else None
            new_values_json = json.dumps(new_values) if new_values else None
            
            with database.get_db_connection() as conn:
                cursor = conn.execute('''
                    INSERT INTO audit_log (
                        table_name, record_id, operation_type, user_id,
                        old_values, new_values, changes_summary,
                        ip_address, user_agent, session_id, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    table_name, str(record_id), operation_type.upper(), user_id,
                    old_values_json, new_values_json, changes_summary,
                    ip_address, user_agent, session_id, notes
                ))
                
                audit_id = cursor.lastrowid
                logger.info(f"Audit log created: {audit_id} - {operation_type} on {table_name}#{record_id}")
                return audit_id
                
        except Exception as e:
            logger.error(f"Error logging audit operation: {e}")
            # Don't raise exception to avoid breaking the main operation
            return -1
            
    @staticmethod
    def _generate_changes_summary(
        operation_type: str,
        old_values: Optional[Dict[str, Any]],
        new_values: Optional[Dict[str, Any]]
    ) -> str:
        """Generate a human-readable summary of changes."""
        try:
            if operation_type.upper() == "CREATE":
                if new_values:
                    key_fields = ['name', 'title', 'ba_number', 'make_and_type', 'equipment_id']
                    for field in key_fields:
                        if field in new_values and new_values[field]:
                            return f"Created new record: {field}={new_values[field]}"
                return "Created new record"
                
            elif operation_type.upper() == "DELETE":
                if old_values:
                    key_fields = ['name', 'title', 'ba_number', 'make_and_type', 'equipment_id']
                    for field in key_fields:
                        if field in old_values and old_values[field]:
                            return f"Deleted record: {field}={old_values[field]}"
                return "Deleted record"
                
            elif operation_type.upper() == "UPDATE":
                if old_values and new_values:
                    changes = []
                    for key in new_values:
                        if key in old_values and old_values[key] != new_values[key]:
                            changes.append(f"{key}: {old_values[key]} → {new_values[key]}")
                    
                    if changes:
                        return f"Updated: {', '.join(changes[:3])}" + ("..." if len(changes) > 3 else "")
                    else:
                        return "No changes detected"
                        
                return "Updated record"
                
            else:
                return f"{operation_type.upper()} operation"
                
        except Exception as e:
            logger.error(f"Error generating changes summary: {e}")
            return f"{operation_type.upper()} operation"
            
    @staticmethod
    def get_audit_trail(
        table_name: Optional[str] = None,
        record_id: Optional[str] = None,
        user_id: Optional[str] = None,
        operation_type: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 1000
    ) -> List[Dict[str, Any]]:
        """
        Retrieve audit trail records based on filters.
        
        Args:
            table_name: Filter by table name
            record_id: Filter by record ID
            user_id: Filter by user ID
            operation_type: Filter by operation type
            start_date: Filter by start date
            end_date: Filter by end date
            limit: Maximum number of records to return
            
        Returns:
            List of audit log records
        """
        try:
            query = "SELECT * FROM audit_log WHERE 1=1"
            params = []
            
            if table_name:
                query += " AND table_name = ?"
                params.append(table_name)
                
            if record_id:
                query += " AND record_id = ?"
                params.append(str(record_id))
                
            if user_id:
                query += " AND user_id = ?"
                params.append(user_id)
                
            if operation_type:
                query += " AND operation_type = ?"
                params.append(operation_type.upper())
                
            if start_date:
                query += " AND timestamp >= ?"
                params.append(start_date.isoformat())
                
            if end_date:
                query += " AND timestamp <= ?"
                params.append(end_date.isoformat())
                
            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)
            
            with database.get_db_connection() as conn:
                cursor = conn.execute(query, params)
                columns = [description[0] for description in cursor.description]
                
                audit_records = []
                for row in cursor.fetchall():
                    record = dict(zip(columns, row))
                    
                    # Parse JSON values
                    if record['old_values']:
                        try:
                            record['old_values'] = json.loads(record['old_values'])
                        except json.JSONDecodeError:
                            record['old_values'] = None
                            
                    if record['new_values']:
                        try:
                            record['new_values'] = json.loads(record['new_values'])
                        except json.JSONDecodeError:
                            record['new_values'] = None
                            
                    audit_records.append(record)
                    
                return audit_records
                
        except Exception as e:
            logger.error(f"Error retrieving audit trail: {e}")
            return []
            
    @staticmethod
    def get_record_history(table_name: str, record_id: str) -> List[Dict[str, Any]]:
        """Get complete history for a specific record."""
        return AuditLogger.get_audit_trail(
            table_name=table_name,
            record_id=record_id
        )
        
    @staticmethod
    def get_user_activity(user_id: str, days: int = 30) -> List[Dict[str, Any]]:
        """Get user activity for the specified number of days."""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        return AuditLogger.get_audit_trail(
            user_id=user_id,
            start_date=start_date,
            end_date=end_date
        )
        
    @staticmethod
    def create_session(user_id: str, ip_address: str = None, user_agent: str = None) -> str:
        """Create a new user session."""
        try:
            import uuid
            session_id = str(uuid.uuid4())
            
            with database.get_db_connection() as conn:
                conn.execute('''
                    INSERT INTO user_sessions (session_id, user_id, ip_address, user_agent)
                    VALUES (?, ?, ?, ?)
                ''', (session_id, user_id, ip_address, user_agent))
                
            logger.info(f"Session created for user {user_id}: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            return ""
            
    @staticmethod
    def update_session_activity(session_id: str):
        """Update last activity time for a session."""
        try:
            with database.get_db_connection() as conn:
                conn.execute('''
                    UPDATE user_sessions 
                    SET last_activity = CURRENT_TIMESTAMP 
                    WHERE session_id = ?
                ''', (session_id,))
                
        except Exception as e:
            logger.error(f"Error updating session activity: {e}")
            
    @staticmethod
    def close_session(session_id: str):
        """Close a user session."""
        try:
            with database.get_db_connection() as conn:
                conn.execute('''
                    UPDATE user_sessions 
                    SET is_active = 0 
                    WHERE session_id = ?
                ''', (session_id,))
                
            logger.info(f"Session closed: {session_id}")
            
        except Exception as e:
            logger.error(f"Error closing session: {e}")
            
    @staticmethod
    def cleanup_old_audit_logs(days_to_keep: int = 365):
        """Clean up old audit logs to maintain performance."""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            with database.get_db_connection() as conn:
                cursor = conn.execute('''
                    DELETE FROM audit_log 
                    WHERE timestamp < ?
                ''', (cutoff_date.isoformat(),))
                
                deleted_count = cursor.rowcount
                logger.info(f"Cleaned up {deleted_count} old audit log entries")
                
                return deleted_count
                
        except Exception as e:
            logger.error(f"Error cleaning up audit logs: {e}")
            return 0
            
    @staticmethod
    def get_audit_statistics() -> Dict[str, Any]:
        """Get audit logging statistics."""
        try:
            with database.get_db_connection() as conn:
                # Total audit records
                total_records = conn.execute('SELECT COUNT(*) FROM audit_log').fetchone()[0]
                
                # Records by operation type
                operation_stats = conn.execute('''
                    SELECT operation_type, COUNT(*) as count 
                    FROM audit_log 
                    GROUP BY operation_type
                ''').fetchall()
                
                # Records by table
                table_stats = conn.execute('''
                    SELECT table_name, COUNT(*) as count 
                    FROM audit_log 
                    GROUP BY table_name 
                    ORDER BY count DESC 
                    LIMIT 10
                ''').fetchall()
                
                # Recent activity (last 24 hours)
                recent_activity = conn.execute('''
                    SELECT COUNT(*) 
                    FROM audit_log 
                    WHERE timestamp >= datetime('now', '-1 day')
                ''').fetchone()[0]
                
                # Active sessions
                active_sessions = conn.execute('''
                    SELECT COUNT(*) 
                    FROM user_sessions 
                    WHERE is_active = 1
                ''').fetchone()[0]
                
                return {
                    'total_records': total_records,
                    'operation_stats': dict(operation_stats),
                    'table_stats': dict(table_stats),
                    'recent_activity_24h': recent_activity,
                    'active_sessions': active_sessions
                }
                
        except Exception as e:
            logger.error(f"Error getting audit statistics: {e}")
            return {}

# Decorator for automatic audit logging
def audit_operation(table_name: str, operation_type: str):
    """
    Decorator to automatically log CRUD operations.
    
    Usage:
        @audit_operation('equipment', 'CREATE')
        def create_equipment(self, **kwargs):
            # Implementation
            return equipment_id
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                # Get user context (this would be set by the application)
                user_id = getattr(wrapper, '_current_user_id', 'system')
                session_id = getattr(wrapper, '_current_session_id', None)
                
                # For UPDATE and DELETE operations, get old values
                old_values = None
                if operation_type.upper() in ['UPDATE', 'DELETE'] and len(args) > 1:
                    # Assume first argument after self is the record ID
                    record_id = args[1]
                    # This would need to be implemented based on your model structure
                    # old_values = get_record_by_id(table_name, record_id)
                
                # Execute the original function
                result = func(*args, **kwargs)
                
                # Log the operation
                record_id = result if operation_type.upper() == 'CREATE' else (args[1] if len(args) > 1 else 'unknown')
                new_values = kwargs if operation_type.upper() in ['CREATE', 'UPDATE'] else None
                
                AuditLogger.log_operation(
                    table_name=table_name,
                    record_id=str(record_id),
                    operation_type=operation_type,
                    user_id=user_id,
                    old_values=old_values,
                    new_values=new_values,
                    session_id=session_id
                )
                
                return result
                
            except Exception as e:
                logger.error(f"Error in audit decorator: {e}")
                # Still execute the original function even if audit logging fails
                return func(*args, **kwargs)
                
        return wrapper
    return decorator

# Initialize audit logging when module is imported
try:
    AuditLogger.initialize_audit_tables()
except Exception as e:
    logger.error(f"Failed to initialize audit logging: {e}")
