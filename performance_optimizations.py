"""
Performance Optimizations for PROJECT-ALPHA Equipment Inventory Management System
Optimized for low-end systems and 1366x768 displays
"""

import logging
import time
import gc
import threading
from functools import wraps
from PyQt5.QtCore import QTimer, QThread, pyqtSignal
from PyQt5.QtWidgets import QApplication

logger = logging.getLogger('performance')

class PerformanceManager:
    """Manages application performance optimizations."""
    
    def __init__(self):
        self.cache = {}
        self.cache_timeout = 300  # 5 minutes
        self.last_gc_time = time.time()
        self.gc_interval = 60  # 1 minute
        
    def cache_result(self, key, result, timeout=None):
        """Cache a result with optional timeout."""
        if timeout is None:
            timeout = self.cache_timeout
        
        self.cache[key] = {
            'result': result,
            'timestamp': time.time(),
            'timeout': timeout
        }
    
    def get_cached_result(self, key):
        """Get a cached result if still valid."""
        if key not in self.cache:
            return None
        
        cached = self.cache[key]
        if time.time() - cached['timestamp'] > cached['timeout']:
            del self.cache[key]
            return None
        
        return cached['result']
    
    def clear_cache(self):
        """Clear all cached results."""
        self.cache.clear()
    
    def maybe_garbage_collect(self):
        """Perform garbage collection if enough time has passed."""
        current_time = time.time()
        if current_time - self.last_gc_time > self.gc_interval:
            gc.collect()
            self.last_gc_time = current_time
            logger.debug("Performed garbage collection")

# Global performance manager instance
performance_manager = PerformanceManager()

def cached_method(timeout=300):
    """Decorator to cache method results."""
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Create cache key from method name and arguments
            cache_key = f"{self.__class__.__name__}.{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # Try to get cached result
            cached_result = performance_manager.get_cached_result(cache_key)
            if cached_result is not None:
                logger.debug(f"Using cached result for {cache_key}")
                return cached_result
            
            # Execute method and cache result
            result = func(self, *args, **kwargs)
            performance_manager.cache_result(cache_key, result, timeout)
            logger.debug(f"Cached result for {cache_key}")
            
            return result
        return wrapper
    return decorator

def performance_monitor(func):
    """Decorator to monitor function performance."""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            if execution_time > 1.0:  # Log slow operations
                logger.warning(f"Slow operation: {func.__name__} took {execution_time:.2f}s")
            elif execution_time > 0.1:
                logger.debug(f"Operation: {func.__name__} took {execution_time:.3f}s")
            
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Failed operation: {func.__name__} failed after {execution_time:.3f}s: {e}")
            raise
        finally:
            # Maybe perform garbage collection
            performance_manager.maybe_garbage_collect()
    
    return wrapper

class AsyncDataLoader(QThread):
    """Asynchronous data loader to prevent UI freezing."""
    
    data_loaded = pyqtSignal(object)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, load_function, *args, **kwargs):
        super().__init__()
        self.load_function = load_function
        self.args = args
        self.kwargs = kwargs
    
    def run(self):
        """Run the data loading function in a separate thread."""
        try:
            result = self.load_function(*self.args, **self.kwargs)
            self.data_loaded.emit(result)
        except Exception as e:
            logger.error(f"Async data loading failed: {e}")
            self.error_occurred.emit(str(e))

class ChartOptimizer:
    """Optimizes chart rendering for low-resolution displays and performance."""
    
    @staticmethod
    def get_optimized_figure_size():
        """Get optimized figure size based on screen resolution."""
        from ui.window_utils import LowResolutionManager
        
        if LowResolutionManager.is_low_resolution():
            return (6.0, 4.0)  # Smaller figures for low-res
        else:
            return (8.0, 6.0)  # Standard size for higher-res
    
    @staticmethod
    def get_optimized_dpi():
        """Get optimized DPI for chart rendering."""
        from ui.window_utils import LowResolutionManager, DPIScaler
        
        screen_info = DPIScaler.get_screen_info()
        base_dpi = min(screen_info['dpi_x'], 100)  # Cap DPI for performance
        
        if LowResolutionManager.is_low_resolution():
            return min(base_dpi, 80)  # Lower DPI for low-res screens
        else:
            return base_dpi
    
    @staticmethod
    def optimize_chart_data(data, max_points=100):
        """Optimize chart data by reducing points if necessary."""
        if not data or len(data) <= max_points:
            return data
        
        # Simple data reduction - take every nth point
        step = len(data) // max_points
        return data[::step]

class UIOptimizer:
    """Optimizes UI performance for low-end systems."""
    
    @staticmethod
    def defer_heavy_operations(widget, operations, delay=100):
        """Defer heavy operations to prevent UI freezing."""
        def execute_operations():
            for operation in operations:
                try:
                    operation()
                    QApplication.processEvents()  # Keep UI responsive
                except Exception as e:
                    logger.error(f"Deferred operation failed: {e}")
        
        QTimer.singleShot(delay, execute_operations)
    
    @staticmethod
    def batch_table_updates(table, data, batch_size=50):
        """Update table in batches to maintain responsiveness."""
        def update_batch(start_index):
            end_index = min(start_index + batch_size, len(data))
            
            for i in range(start_index, end_index):
                # Update table row
                try:
                    table.update_row(i, data[i])
                except Exception as e:
                    logger.error(f"Failed to update table row {i}: {e}")
            
            # Schedule next batch if needed
            if end_index < len(data):
                QTimer.singleShot(10, lambda: update_batch(end_index))
        
        # Start batch updates
        if data:
            update_batch(0)
    
    @staticmethod
    def optimize_widget_for_low_res(widget):
        """Apply low-resolution optimizations to a widget."""
        from ui.window_utils import LowResolutionManager
        
        if not LowResolutionManager.is_low_resolution():
            return
        
        # Apply compact styling
        widget.setStyleSheet(widget.styleSheet() + """
            QWidget {
                font-size: 9px;
            }
            QPushButton {
                padding: 3px 6px;
                font-size: 9px;
                min-height: 20px;
            }
            QLabel {
                font-size: 9px;
            }
            QLineEdit, QComboBox, QSpinBox {
                font-size: 9px;
                padding: 2px;
                min-height: 22px;
            }
            QTableWidget {
                font-size: 8px;
            }
            QTableWidget::item {
                padding: 2px;
            }
        """)

class MemoryOptimizer:
    """Optimizes memory usage for the application."""
    
    @staticmethod
    def cleanup_matplotlib_figures():
        """Clean up matplotlib figures to free memory."""
        try:
            import matplotlib.pyplot as plt
            plt.close('all')
            gc.collect()
            logger.debug("Cleaned up matplotlib figures")
        except ImportError:
            pass
    
    @staticmethod
    def optimize_pandas_memory(df):
        """Optimize pandas DataFrame memory usage."""
        try:
            import pandas as pd
            
            # Convert object columns to category if they have few unique values
            for col in df.select_dtypes(include=['object']).columns:
                if df[col].nunique() / len(df) < 0.5:  # Less than 50% unique values
                    df[col] = df[col].astype('category')
            
            # Downcast numeric types
            for col in df.select_dtypes(include=['int']).columns:
                df[col] = pd.to_numeric(df[col], downcast='integer')
            
            for col in df.select_dtypes(include=['float']).columns:
                df[col] = pd.to_numeric(df[col], downcast='float')
            
            return df
        except ImportError:
            return df

class StartupOptimizer:
    """Optimizes application startup time."""
    
    @staticmethod
    def lazy_import_heavy_modules():
        """Lazy import heavy modules to speed up startup."""
        # Import heavy modules only when needed
        heavy_modules = [
            'matplotlib.pyplot',
            'pandas',
            'openpyxl',
            'reportlab'
        ]
        
        for module in heavy_modules:
            try:
                __import__(module)
                logger.debug(f"Pre-loaded {module}")
            except ImportError:
                logger.warning(f"Could not pre-load {module}")
    
    @staticmethod
    def defer_non_critical_initialization(widget):
        """Defer non-critical widget initialization."""
        def delayed_init():
            try:
                if hasattr(widget, 'load_data'):
                    widget.load_data()
                if hasattr(widget, 'setup_charts'):
                    widget.setup_charts()
            except Exception as e:
                logger.error(f"Delayed initialization failed: {e}")
        
        # Defer initialization by 500ms
        QTimer.singleShot(500, delayed_init)

# Convenience functions for easy use
def optimize_for_low_resolution(widget):
    """Apply all low-resolution optimizations to a widget."""
    UIOptimizer.optimize_widget_for_low_res(widget)

def create_optimized_chart():
    """Create an optimized matplotlib chart."""
    try:
        import matplotlib.pyplot as plt
        
        figsize = ChartOptimizer.get_optimized_figure_size()
        dpi = ChartOptimizer.get_optimized_dpi()
        
        fig = plt.figure(figsize=figsize, dpi=dpi)
        return fig
    except ImportError:
        return None

def load_data_async(load_function, callback, error_callback=None):
    """Load data asynchronously to prevent UI freezing."""
    loader = AsyncDataLoader(load_function)
    loader.data_loaded.connect(callback)
    if error_callback:
        loader.error_occurred.connect(error_callback)
    loader.start()
    return loader

# Performance monitoring context manager
class PerformanceContext:
    """Context manager for performance monitoring."""
    
    def __init__(self, operation_name):
        self.operation_name = operation_name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        logger.debug(f"Starting {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        execution_time = time.time() - self.start_time
        
        if exc_type is not None:
            logger.error(f"{self.operation_name} failed after {execution_time:.3f}s: {exc_val}")
        elif execution_time > 1.0:
            logger.warning(f"{self.operation_name} took {execution_time:.2f}s (slow)")
        else:
            logger.debug(f"{self.operation_name} completed in {execution_time:.3f}s")
        
        # Cleanup
        performance_manager.maybe_garbage_collect()
