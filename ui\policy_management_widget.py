"""
Policy Management Widget
Handles the UI for managing vehicle class policies
"""
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                           QPushButton, QTableWidget, QTableWidgetItem,
                           QHeaderView, QAbstractItemView, QMessageBox,
                           QComboBox, QLineEdit, QFormLayout, QGroupBox,
                           QSplitter, QFrame, QProgressDialog, QDialog)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor

from ui.policy_editor_dialog import PolicyEditorDialog
import policy_service
from policy_models import VehicleClassPolicy, PolicyCondition


class PolicyManagementWidget(QWidget):
    """Widget for managing vehicle class policies."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """Set up the policy management widget UI."""
        # Main layout
        main_layout = QVBoxLayout(self)
        
        # Title and description
        title_label = QLabel("Vehicle Class Policy Management")
        title_label.setStyleSheet("font-size: 16pt; font-weight: bold;")
        
        desc_label = QLabel("Define maintenance and discard policies for each vehicle class. "
                          "Policies apply to all equipment of the specified make and type.")
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(desc_label)
        
        # Search and filter controls
        filter_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search by Make and Type...")
        self.search_input.textChanged.connect(self.filter_policies)
        
        filter_layout.addWidget(QLabel("Filter:"))
        filter_layout.addWidget(self.search_input, 1)
        
        main_layout.addLayout(filter_layout)
        
        # Splitter for policies table and details
        splitter = QSplitter(Qt.Horizontal)
        
        # Policies table
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)
        
        self.policies_table = QTableWidget()
        self.policies_table.setColumnCount(3)
        self.policies_table.setHorizontalHeaderLabels(["Make and Type", "Conditions", "Last Modified"])
        self.policies_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.policies_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.policies_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.policies_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.policies_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.policies_table.setAlternatingRowColors(True)
        self.policies_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.policies_table.selectionModel().selectionChanged.connect(self.on_policy_selected)
        
        table_layout.addWidget(self.policies_table)
        
        # Table buttons
        table_buttons_layout = QHBoxLayout()
        
        self.add_button = QPushButton("Add Policy")
        self.edit_button = QPushButton("Edit Policy")
        self.delete_button = QPushButton("Delete Policy")
        
        self.add_button.clicked.connect(self.add_policy)
        self.edit_button.clicked.connect(self.edit_policy)
        self.delete_button.clicked.connect(self.delete_policy)
        
        # Disable buttons until selection
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        
        table_buttons_layout.addWidget(self.add_button)
        table_buttons_layout.addWidget(self.edit_button)
        table_buttons_layout.addWidget(self.delete_button)
        table_buttons_layout.addStretch()
        
        table_layout.addLayout(table_buttons_layout)
        
        # Policy details panel
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)
        
        details_title = QLabel("Policy Details")
        details_title.setStyleSheet("font-size: 14pt; font-weight: bold;")
        details_layout.addWidget(details_title)
        
        # Policy details form
        self.details_form = QFormLayout()
        self.details_make_type = QLabel("-")
        self.details_created = QLabel("-")
        self.details_modified = QLabel("-")
        self.details_created_by = QLabel("-")
        
        self.details_form.addRow("Make and Type:", self.details_make_type)
        self.details_form.addRow("Created:", self.details_created)
        self.details_form.addRow("Last Modified:", self.details_modified)
        self.details_form.addRow("Created By:", self.details_created_by)
        
        details_layout.addLayout(self.details_form)
        
        # Conditions group - only show discard criteria
        conditions_group = QGroupBox("Discard Criteria")
        conditions_layout = QVBoxLayout()
        
        # Only show discard conditions since other maintenance types were removed
        self.discard_details = QLabel("Not set")
        conditions_layout.addWidget(QLabel("Discard Criteria:"))
        conditions_layout.addWidget(self.discard_details)
        
        conditions_group.setLayout(conditions_layout)
        details_layout.addWidget(conditions_group)
        
        # Equipment count
        self.equipment_count_label = QLabel("0 equipment will use this policy")
        details_layout.addWidget(self.equipment_count_label)
        
        details_layout.addStretch()
        
        # Add widgets to splitter
        splitter.addWidget(table_widget)
        splitter.addWidget(details_widget)
        
        # Set default sizes
        splitter.setSizes([400, 300])
        
        main_layout.addWidget(splitter)
        
        # Bottom buttons
        bottom_buttons = QHBoxLayout()
        
        self.create_default_button = QPushButton("Create Default Policies")
        self.create_default_button.clicked.connect(self.create_default_policies)
        
        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.clicked.connect(self.load_data)
        
        bottom_buttons.addWidget(self.create_default_button)
        bottom_buttons.addStretch()
        bottom_buttons.addWidget(self.refresh_button)
        
        main_layout.addLayout(bottom_buttons)
    
    def load_data(self):
        """Load policies data into the table."""
        # Clear table
        self.policies_table.setRowCount(0)
        
        # Get policies
        policies = policy_service.get_all_policies()
        
        # Fill table
        for i, policy_data in enumerate(policies):
            policy = policy_data.get('policy')
            conditions = policy_data.get('conditions', [])
            
            self.policies_table.insertRow(i)
            
            # Make and Type
            make_type_item = QTableWidgetItem(policy.get('make_and_type'))
            self.policies_table.setItem(i, 0, make_type_item)
            
            # Conditions summary - only show discard since other maintenance types were removed
            condition_types = []
            for condition in conditions:
                condition_type = condition.get('condition_type')
                # Only process discard conditions since other tabs were removed
                if condition_type == PolicyCondition.DISCARD:
                    condition_types.append("Discard")
            
            condition_summary = ", ".join(condition_types) if condition_types else "None"
            condition_item = QTableWidgetItem(condition_summary)
            self.policies_table.setItem(i, 1, condition_item)
            
            # Modified date
            modified_date = policy.get('modified_date') or policy.get('created_date')
            modified_item = QTableWidgetItem(str(modified_date))
            self.policies_table.setItem(i, 2, modified_item)
            
            # Store policy ID
            make_type_item.setData(Qt.UserRole, policy.get('policy_id'))
        
        # Reset selection
        self.clear_details()
    
    def on_policy_selected(self):
        """Handle policy selection in the table."""
        selected_rows = self.policies_table.selectionModel().selectedRows()
        if not selected_rows:
            self.clear_details()
            return
        
        # Get policy ID from the first selected row
        row = selected_rows[0].row()
        policy_id = self.policies_table.item(row, 0).data(Qt.UserRole)
        
        # Enable edit and delete buttons
        self.edit_button.setEnabled(True)
        self.delete_button.setEnabled(True)
        
        # Load policy details
        policy_data = policy_service.get_policy_with_conditions(policy_id=policy_id)
        if not policy_data:
            self.clear_details()
            return
        
        policy = policy_data.get('policy')
        conditions = policy_data.get('conditions', [])
        
        # Update details panel
        self.details_make_type.setText(policy.get('make_and_type'))
        self.details_created.setText(str(policy.get('created_date')))
        self.details_modified.setText(str(policy.get('modified_date')))
        self.details_created_by.setText(policy.get('created_by') or "System")
        
        # Update conditions - only discard since other maintenance types were removed
        self.discard_details.setText(self.format_condition_text(PolicyCondition.DISCARD, conditions))
        
        # Get equipment count using this policy
        self.update_equipment_count(policy.get('make_and_type'))
    
    def format_condition_text(self, condition_type, conditions):
        """Format the condition text for display."""
        for condition in conditions:
            if condition.get('condition_type') == condition_type:
                years = condition.get('years_threshold')
                kms = condition.get('km_threshold')
                hours = condition.get('hours_threshold')
                logic = condition.get('logic_type')
                
                parts = []
                if years and float(years) > 0:
                    parts.append(f"{years} years")
                if kms and float(kms) > 0:
                    parts.append(f"{kms} kms")
                if hours and float(hours) > 0:
                    parts.append(f"{hours} hours")
                
                if not parts:
                    return "Not set"
                
                if logic == PolicyCondition.EARLIER:
                    return " or ".join(parts) + " (whichever is earlier)"
                elif logic == PolicyCondition.LATER:
                    return " and ".join(parts) + " (all must be met)"
                else:
                    return " or ".join(parts)
        
        return "Not set"
    
    def clear_details(self):
        """Clear the details panel."""
        self.details_make_type.setText("-")
        self.details_created.setText("-")
        self.details_modified.setText("-")
        self.details_created_by.setText("-")
        
        # Only clear discard details since other maintenance types were removed
        self.discard_details.setText("Not set")
        
        self.equipment_count_label.setText("0 equipment will use this policy")
        
        # Disable edit and delete buttons
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)
    
    def update_equipment_count(self, make_and_type):
        """Update the equipment count label."""
        try:
            import database

            # Count equipment with this make and type
            with database.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT COUNT(*) as count FROM equipment WHERE make_and_type = ?",
                    (make_and_type,)
                )
                result = cursor.fetchone()
                count = result.get('count', 0) if result else 0
            
            self.equipment_count_label.setText(f"{count} equipment will use this policy")
            
        except Exception as e:
            self.equipment_count_label.setText("Error counting equipment")
    
    def add_policy(self):
        """Add a new policy."""
        dialog = PolicyEditorDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_data()
    
    def edit_policy(self):
        """Edit the selected policy."""
        selected_rows = self.policies_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        row = selected_rows[0].row()
        policy_id = self.policies_table.item(row, 0).data(Qt.UserRole)
        
        dialog = PolicyEditorDialog(self, policy_id=policy_id)
        if dialog.exec_() == QDialog.Accepted:
            self.load_data()
    
    def delete_policy(self):
        """Delete the selected policy."""
        selected_rows = self.policies_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        row = selected_rows[0].row()
        policy_id = self.policies_table.item(row, 0).data(Qt.UserRole)
        make_and_type = self.policies_table.item(row, 0).text()
        
        # Confirm deletion
        reply = QMessageBox.question(
            self,
            "Confirm Delete",
            f"Are you sure you want to delete the policy for '{make_and_type}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if policy_service.delete_policy(policy_id):
                self.load_data()
                QMessageBox.information(self, "Success", "Policy deleted successfully.")
            else:
                QMessageBox.warning(self, "Error", "Failed to delete policy.")
    
    def create_default_policies(self):
        """Create default policies from existing discard criteria."""
        # Confirm action
        reply = QMessageBox.question(
            self,
            "Create Default Policies",
            "This will create default policies based on existing discard criteria. Continue?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply != QMessageBox.Yes:
            return
        
        # Show progress dialog
        progress = QProgressDialog("Creating default policies...", "Cancel", 0, 0, self)
        progress.setWindowModality(Qt.WindowModal)
        progress.setValue(0)
        progress.show()
        
        try:
            # Create default policies
            count = policy_service.create_default_policies()
            
            # Hide progress and show result
            progress.hide()
            
            if count > 0:
                QMessageBox.information(
                    self,
                    "Success",
                    f"Created {count} default policies."
                )
            else:
                QMessageBox.information(
                    self,
                    "Complete",
                    "No new default policies were created. Existing policies were not modified."
                )
            
            # Reload data
            self.load_data()
            
        except Exception as e:
            progress.hide()
            QMessageBox.critical(self, "Error", f"An error occurred: {str(e)}")
    
    def filter_policies(self):
        """Filter policies by search text."""
        search_text = self.search_input.text().lower()
        
        for row in range(self.policies_table.rowCount()):
            make_type = self.policies_table.item(row, 0).text().lower()
            
            if not search_text or search_text in make_type:
                self.policies_table.setRowHidden(row, False)
            else:
                self.policies_table.setRowHidden(row, True)
