#!/usr/bin/env python3
"""
Simple PyInstaller Build Script for PROJECT-ALPHA
No emoji characters to avoid Windows console encoding issues
"""

import os
import sys
import shutil
import subprocess
import platform
import logging
import time
from pathlib import Path
from datetime import datetime

# Simple logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('build_simple.log')
    ]
)

logger = logging.getLogger(__name__)

class SimplePyInstallerBuilder:
    """Simple PyInstaller builder for PROJECT-ALPHA"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.build_start_time = datetime.now()
        
        # Application metadata
        self.app_name = "InventoryTracker"
        self.app_version = "1.0.0"
        self.app_description = "PROJECT-ALPHA Equipment Inventory Management System"
        
        # Build configuration
        self.spec_file = self.project_root / "InventoryTracker.spec"
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        
        logger.info(f"Initializing PyInstaller builder for {self.app_description}")
        logger.info(f"Project root: {self.project_root}")
        logger.info(f"Platform: {platform.platform()}")
        logger.info(f"Python: {sys.version}")
        
    def check_dependencies(self) -> bool:
        """Check if all required dependencies are installed"""
        logger.info("Checking dependencies...")
        
        # Core dependencies
        required_modules = [
            'PyQt5', 'pandas', 'numpy', 'openpyxl', 'xlrd', 'matplotlib', 
            'reportlab', 'psutil', 'pint', 'fuzzywuzzy', 'dateutil', 
            'PIL', 'pyinstaller'
        ]
        
        missing_modules = []
        
        for module_name in required_modules:
            try:
                if module_name == 'dateutil':
                    import dateutil
                elif module_name == 'PIL':
                    import PIL
                else:
                    __import__(module_name)
                logger.info(f"Found: {module_name}")
            except ImportError:
                missing_modules.append(module_name)
                logger.error(f"Missing: {module_name}")
        
        if missing_modules:
            logger.error(f"Missing modules: {', '.join(missing_modules)}")
            logger.error("Install missing dependencies with: pip install -r requirements.txt")
            return False
        
        logger.info(f"All {len(required_modules)} dependencies are available")
        return True
    
    def check_pyinstaller(self) -> bool:
        """Verify PyInstaller installation"""
        logger.info("Checking PyInstaller...")
        
        try:
            result = subprocess.run([
                sys.executable, "-m", "PyInstaller", "--version"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                version = result.stdout.strip()
                logger.info(f"PyInstaller version: {version}")
                return True
            else:
                logger.error("PyInstaller not working properly")
                logger.error(f"Error: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error checking PyInstaller: {e}")
            return False
    
    def clean_previous_builds(self):
        """Clean previous build artifacts"""
        logger.info("Cleaning previous build artifacts...")
        
        # Directories to clean
        cleanup_dirs = [self.build_dir, self.dist_dir]
        
        cleaned_count = 0
        for dir_path in cleanup_dirs:
            if dir_path.exists():
                try:
                    shutil.rmtree(dir_path)
                    logger.info(f"Removed {dir_path.name}/")
                    cleaned_count += 1
                except Exception as e:
                    logger.warning(f"Could not remove {dir_path.name}/: {e}")
        
        # Clean Python cache files
        cache_files_cleaned = 0
        for cache_file in self.project_root.rglob('*.pyc'):
            try:
                cache_file.unlink()
                cache_files_cleaned += 1
            except Exception:
                pass
        
        logger.info(f"Cleanup completed: {cleaned_count} directories, {cache_files_cleaned} cache files")
    
    def validate_spec_file(self) -> bool:
        """Validate the PyInstaller spec file"""
        logger.info("Validating spec file...")
        
        if not self.spec_file.exists():
            logger.error(f"Spec file not found: {self.spec_file}")
            return False
        
        try:
            with open(self.spec_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for required elements
            required_elements = ['Analysis', 'PYZ', 'EXE', 'main.py']
            missing_elements = []
            
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                logger.error(f"Spec file missing elements: {missing_elements}")
                return False
            
            logger.info("Spec file validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Error validating spec file: {e}")
            return False
    
    def build_executable(self) -> bool:
        """Build the executable using PyInstaller"""
        logger.info("Starting PyInstaller build...")
        
        # Build command
        cmd = [
            sys.executable, "-m", "PyInstaller",
            str(self.spec_file),
            "--clean",
            "--noconfirm",
            "--log-level=INFO"
        ]
        
        logger.info(f"Running: {' '.join(cmd)}")
        
        try:
            # Start build process
            start_time = time.time()
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
                cwd=self.project_root
            )
            
            # Monitor progress
            warning_count = 0
            error_count = 0
            
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                
                if output:
                    line = output.strip()
                    if line:
                        # Categorize and display important messages
                        line_lower = line.lower()
                        if 'error' in line_lower or 'failed' in line_lower:
                            error_count += 1
                            print(f"ERROR: {line}")
                        elif 'warning' in line_lower:
                            warning_count += 1
                            print(f"WARNING: {line}")
                        elif any(keyword in line_lower for keyword in ['analyzing', 'building', 'exe']):
                            print(f"INFO: {line}")
                        elif 'successfully' in line_lower or 'completed' in line_lower:
                            print(f"SUCCESS: {line}")
            
            process.wait()
            build_time = time.time() - start_time
            
            # Check results
            if process.returncode == 0:
                logger.info(f"PyInstaller build completed successfully in {build_time:.1f}s")
                if warning_count > 0:
                    logger.warning(f"Build completed with {warning_count} warnings")
                return True
            else:
                logger.error(f"PyInstaller build failed (exit code: {process.returncode})")
                logger.error(f"Build time: {build_time:.1f}s")
                return False
                
        except Exception as e:
            logger.error(f"Build process error: {e}")
            return False
    
    def validate_executable(self) -> bool:
        """Validate the generated executable"""
        logger.info("Validating generated executable...")
        
        exe_path = self.dist_dir / f"{self.app_name}.exe"
        
        if not exe_path.exists():
            logger.error(f"Executable not found: {exe_path}")
            return False
        
        # Check file size
        file_size_mb = exe_path.stat().st_size / (1024 * 1024)
        logger.info(f"Executable size: {file_size_mb:.1f} MB")
        
        if file_size_mb < 30:
            logger.warning("Executable seems small - may be missing dependencies")
        elif file_size_mb > 300:
            logger.warning("Executable is large - consider optimization")
        
        logger.info("Executable validation completed")
        return True
    
    def create_distribution_package(self) -> bool:
        """Create a distribution package"""
        logger.info("Creating distribution package...")
        
        dist_package_dir = self.project_root / f"{self.app_name}_Distribution"
        
        try:
            # Remove old distribution
            if dist_package_dir.exists():
                shutil.rmtree(dist_package_dir)
            
            dist_package_dir.mkdir()
            
            # Copy executable
            exe_path = self.dist_dir / f"{self.app_name}.exe"
            if exe_path.exists():
                shutil.copy2(exe_path, dist_package_dir / f"{self.app_name}.exe")
                logger.info("Copied executable")
            
            # Create README
            readme_content = f"""# {self.app_description}

## Installation
1. Extract all files to a folder
2. Run {self.app_name}.exe
3. The application will create its database and configuration files automatically

## System Requirements
- Windows 10 or later
- 4GB RAM minimum (8GB recommended)
- 500MB free disk space

## Version Information
- Version: {self.app_version}
- Build Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- Platform: {platform.platform()}

## Support
For technical support, contact the development team.
"""
            
            with open(dist_package_dir / "README.txt", 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            # Copy license files
            for license_file in ['LICENSE', 'LICENSE.txt']:
                license_path = self.project_root / license_file
                if license_path.exists():
                    shutil.copy2(license_path, dist_package_dir)
            
            # Create launcher batch file
            launcher_content = f"""@echo off
title {self.app_description}
echo Starting {self.app_description}...
echo.
echo If you see this window, the application is starting.
echo The main window will appear shortly.
echo.
start "" "{self.app_name}.exe"
"""
            
            with open(dist_package_dir / f"Start_{self.app_name}.bat", 'w') as f:
                f.write(launcher_content)
            
            logger.info(f"Distribution package created: {dist_package_dir}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create distribution package: {e}")
            return False
    
    def run_complete_build(self) -> bool:
        """Execute the complete build process"""
        logger.info("Starting complete PyInstaller build process")
        logger.info("="*70)
        
        try:
            # Step 1: Dependencies
            if not self.check_dependencies():
                logger.error("Dependency check failed")
                return False
            
            # Step 2: PyInstaller
            if not self.check_pyinstaller():
                logger.error("PyInstaller check failed")
                return False
            
            # Step 3: Clean
            self.clean_previous_builds()
            
            # Step 4: Validate spec
            if not self.validate_spec_file():
                logger.error("Spec file validation failed")
                return False
            
            # Step 5: Build
            if not self.build_executable():
                logger.error("Executable build failed")
                return False
            
            # Step 6: Validate
            if not self.validate_executable():
                logger.error("Executable validation failed")
                return False
            
            # Step 7: Package
            if not self.create_distribution_package():
                logger.warning("Distribution package creation failed")
            
            logger.info("Build completed successfully!")
            return True
            
        except KeyboardInterrupt:
            logger.error("Build interrupted by user")
            return False
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False

def main():
    """Main entry point"""
    print("PROJECT-ALPHA PyInstaller Build System")
    print("="*70)
    
    try:
        builder = SimplePyInstallerBuilder()
        success = builder.run_complete_build()
        
        if success:
            print("\nBuild completed successfully!")
            print("Next steps:")
            print("   1. Test the executable in dist/")
            print("   2. Check the distribution package")
            print("   3. Test on a clean system")
            return 0
        else:
            print("\nBuild failed!")
            print("Check the build log for details")
            return 1
            
    except Exception as e:
        print(f"\nFatal error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 