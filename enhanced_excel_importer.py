"""
Enhanced Excel Importer for PROJECT-ALPHA
Fixes critical deployment issues with partial data imports
Ensures complete workbook processing across all system configurations
"""

import os
import logging
import pandas as pd
import gc
import psutil
from typing import Dict, List, Optional, Any

logger = logging.getLogger('enhanced_excel_importer')

class EnhancedExcelImporter:
    """Enhanced Excel importer that ensures complete workbook processing on all systems."""
    
    def __init__(self, file_path: str):
        """Initialize the enhanced Excel importer."""
        self.file_path = file_path
        self.stats = {
            'equipment': 0,
            'equipment_created': 0,
            'equipment_updated': 0,
            'fluids': 0,
            'maintenance': 0,
            'overhauls': 0,
            'batteries': 0,
            'tyre_maintenance': 0,
            'repairs': 0,
            'discard_criteria': 0,
            'conditioning': 0,
            'medium_resets': 0,
            'demand_forecast': 0,
            'tyre_forecast': 0,
            'battery_forecast': 0,
            'equipment_forecast': 0,
            'overhaul_forecast': 0,
            'conditioning_forecast': 0,
            'errors': [],
            'warnings': [],
            'sheets_processed': [],
            'sheets_skipped': [],
            'ba_numbers_updated': []
        }
    
    def get_system_memory_mb(self) -> float:
        """Get available system memory in MB."""
        try:
            return psutil.virtual_memory().available / (1024 * 1024)
        except:
            return 1024  # Default to 1GB if unable to detect
    
    def should_use_chunked_processing(self) -> bool:
        """Determine if chunked processing should be used based on file size and system memory."""
        try:
            file_size_mb = os.path.getsize(self.file_path) / (1024 * 1024)
            available_memory_mb = self.get_system_memory_mb()
            
            # Use chunked processing if:
            # 1. File is larger than 50MB, OR
            # 2. Available memory is less than 2GB, OR
            # 3. File size is more than 10% of available memory
            if (file_size_mb > 50 or 
                available_memory_mb < 2048 or 
                file_size_mb > (available_memory_mb * 0.1)):
                logger.info(f"Using chunked processing: file={file_size_mb:.1f}MB, memory={available_memory_mb:.1f}MB")
                return True
            
            logger.info(f"Using standard processing: file={file_size_mb:.1f}MB, memory={available_memory_mb:.1f}MB")
            return False
            
        except Exception as e:
            logger.warning(f"Error determining processing strategy: {e}, defaulting to chunked")
            return True
    
    def import_all_data(self) -> Dict[str, Any]:
        """Import all data from Excel file with enhanced compatibility."""
        try:
            logger.info(f"Starting enhanced Excel import from {self.file_path}")
            
            # Check if file exists and is readable
            if not os.path.exists(self.file_path):
                error_msg = f"Excel file not found: {self.file_path}"
                logger.error(error_msg)
                self.stats['errors'].append(error_msg)
                return self.stats
            
            # Determine processing strategy
            use_chunked = self.should_use_chunked_processing()
            
            if use_chunked:
                return self._import_with_chunked_processing()
            else:
                return self._import_with_standard_processing()
                
        except Exception as e:
            error_msg = f"Enhanced Excel import failed: {e}"
            logger.error(error_msg)
            self.stats['errors'].append(error_msg)
            return self.stats
    
    def _import_with_chunked_processing(self) -> Dict[str, Any]:
        """Import using memory-safe chunked processing."""
        try:
            from memory_safe_excel_importer import MemorySafeExcelImporter
            
            # Use smaller chunk size for low-memory systems
            available_memory_mb = self.get_system_memory_mb()
            if available_memory_mb < 1024:  # Less than 1GB
                chunk_size = 500
            elif available_memory_mb < 2048:  # Less than 2GB
                chunk_size = 1000
            else:
                chunk_size = 2000
            
            logger.info(f"Using chunked processing with chunk_size={chunk_size}")
            
            importer = MemorySafeExcelImporter(
                self.file_path, 
                chunk_size=chunk_size,
                max_memory_mb=min(512, available_memory_mb * 0.3)  # Use max 30% of available memory
            )
            
            result = importer.import_all_data()
            
            # Convert memory-safe importer stats to enhanced format
            self.stats.update({
                'equipment': result.get('equipment', 0),
                'fluids': result.get('fluids', 0),
                'maintenance': result.get('maintenance', 0),
                'overhauls': result.get('overhauls', 0),
                'batteries': result.get('batteries', 0),
                'tyre_maintenance': result.get('tyre_maintenance', 0),
                'repairs': result.get('repairs', 0),
                'discard_criteria': result.get('discard_criteria', 0),
                'conditioning': result.get('conditioning', 0),
                'errors': result.get('errors', []),
                'warnings': result.get('warnings', [])
            })
            
            return self.stats
            
        except Exception as e:
            error_msg = f"Chunked processing failed: {e}"
            logger.error(error_msg)
            self.stats['errors'].append(error_msg)
            return self.stats
    
    def _import_with_standard_processing(self) -> Dict[str, Any]:
        """Import using standard robust processing."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            
            logger.info("Using standard robust processing")
            
            importer = RobustExcelImporter()
            
            if not importer.initialize_staging():
                error_msg = "Failed to initialize staging database"
                logger.error(error_msg)
                self.stats['errors'].append(error_msg)
                return self.stats
            
            success, result = importer.process_excel_file(self.file_path)
            
            if not success:
                error_msg = f"Standard processing failed: {result}"
                logger.error(error_msg)
                self.stats['errors'].append(error_msg)
                return self.stats
            
            # Convert robust importer stats to enhanced format
            self.stats.update({
                'equipment': result.get('total_equipment', 0),
                'equipment_created': result.get('total_equipment', 0),  # Robust importer doesn't separate created/updated
                'equipment_updated': 0,  # Will be updated if we get this info
                'fluids': result.get('total_fluids', 0),
                'maintenance': result.get('total_maintenance', 0),
                'overhauls': result.get('total_overhauls', 0),
                'batteries': result.get('total_batteries', 0),
                'tyre_maintenance': result.get('total_conditioning', 0),
                'repairs': result.get('total_repairs', 0),
                'discard_criteria': result.get('total_discard_criteria', 0),
                'conditioning': result.get('total_conditioning', 0),
                'sheets_processed': result.get('sheets_processed', []),
                'overhaul_statuses_updated': result.get('overhaul_statuses_updated', 0)
            })
            
            return self.stats
            
        except Exception as e:
            error_msg = f"Standard processing failed: {e}"
            logger.error(error_msg)
            self.stats['errors'].append(error_msg)
            return self.stats
    
    def get_all_sheet_names(self) -> List[str]:
        """Get all sheet names from the Excel file."""
        try:
            with pd.ExcelFile(self.file_path) as excel_file:
                return excel_file.sheet_names
        except Exception as e:
            logger.error(f"Error reading sheet names: {e}")
            return []
    
    def validate_excel_file(self) -> bool:
        """Validate that the Excel file can be processed."""
        try:
            # Check file exists and is readable
            if not os.path.exists(self.file_path):
                return False
            
            # Try to open the file
            with pd.ExcelFile(self.file_path) as excel_file:
                sheet_names = excel_file.sheet_names
                if not sheet_names:
                    return False
                
                # Try to read at least one sheet
                for sheet_name in sheet_names:
                    try:
                        df = pd.read_excel(excel_file, sheet_name=sheet_name, nrows=1)
                        if not df.empty:
                            return True
                    except:
                        continue
                
                return False
                
        except Exception as e:
            logger.error(f"Excel file validation failed: {e}")
            return False


def import_excel_enhanced(file_path: str) -> Dict[str, Any]:
    """
    Enhanced Excel import function that ensures complete workbook processing.
    
    This function addresses deployment issues by:
    1. Detecting system capabilities and choosing appropriate processing strategy
    2. Ensuring all data types are processed completely
    3. Providing robust error handling and recovery
    4. Supporting both high-memory development and low-memory deployment systems
    
    Args:
        file_path (str): Path to the Excel file to import
        
    Returns:
        dict: Import statistics with counts of imported records by type
    """
    logger.info(f"Starting enhanced Excel import from {file_path}")
    
    try:
        # Validate file first
        importer = EnhancedExcelImporter(file_path)
        
        if not importer.validate_excel_file():
            error_msg = f"Excel file validation failed: {file_path}"
            logger.error(error_msg)
            return {
                'equipment': 0, 'fluids': 0, 'maintenance': 0, 'overhauls': 0,
                'batteries': 0, 'tyre_maintenance': 0, 'repairs': 0, 'discard_criteria': 0,
                'errors': [error_msg]
            }
        
        # Perform the import
        result = importer.import_all_data()
        
        # Log final statistics
        total_records = sum(result.get(key, 0) for key in [
            'equipment', 'fluids', 'maintenance', 'overhauls', 'batteries',
            'tyre_maintenance', 'repairs', 'discard_criteria', 'conditioning'
        ])
        
        logger.info(f"Enhanced Excel import completed: {total_records} total records imported")
        logger.info(f"Final stats: {result}")
        
        return result
        
    except Exception as e:
        error_msg = f"Enhanced Excel import error: {e}"
        logger.error(error_msg)
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        return {
            'equipment': 0, 'fluids': 0, 'maintenance': 0, 'overhauls': 0,
            'batteries': 0, 'tyre_maintenance': 0, 'repairs': 0, 'discard_criteria': 0,
            'errors': [error_msg]
        }


# Compatibility function for existing code
def import_from_excel_enhanced(file_path: str) -> Dict[str, Any]:
    """Compatibility wrapper for enhanced Excel import."""
    return import_excel_enhanced(file_path)
