"""
Excel Import Fixes for PROJECT-ALPHA
Addresses the partial import issue when deployed to other systems.
"""

import os
import logging
import pandas as pd
import psutil
import gc
from typing import Dict, List, Tuple, Optional, Iterator
from pathlib import Path

logger = logging.getLogger('excel_import_fixes')

class EnhancedMemorySafeExcelImporter:
    """
    Enhanced memory-safe Excel importer that fixes the partial import issue.
    Provides complete data type support and adaptive memory management.
    """
    
    def __init__(self, file_path: str, chunk_size: int = None, max_memory_mb: int = None):
        """
        Initialize the enhanced memory-safe Excel importer.
        
        Args:
            file_path: Path to the Excel file
            chunk_size: Number of rows to process at once (auto-calculated if None)
            max_memory_mb: Maximum memory usage in MB (auto-calculated if None)
        """
        self.file_path = file_path
        self.file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
        
        # Auto-calculate optimal settings based on system resources
        available_memory_mb = psutil.virtual_memory().available / (1024 * 1024)
        
        if chunk_size is None:
            self.chunk_size = self._calculate_optimal_chunk_size(available_memory_mb)
        else:
            self.chunk_size = chunk_size
            
        if max_memory_mb is None:
            self.max_memory_mb = min(available_memory_mb * 0.3, 1024)  # 30% of available or 1GB max
        else:
            self.max_memory_mb = max_memory_mb
            
        self.stats = {
            'equipment': 0,
            'fluids': 0,
            'maintenance': 0,
            'repairs': 0,
            'overhauls': 0,
            'tyre_maintenance': 0,
            'discard_criteria': 0,
            'batteries': 0,
            'medium_resets': 0,
            'conditioning': 0,
            'demand_forecast': 0,
            'tyre_forecast': 0,
            'battery_forecast': 0,
            'equipment_forecast': 0,
            'overhaul_forecast': 0,
            'conditioning_forecast': 0,
            'errors': [],
            'warnings': [],
            'sheets_processed': [],
            'sheets_skipped': []
        }
        
        logger.info(f"Enhanced importer initialized: chunk_size={self.chunk_size}, max_memory={self.max_memory_mb}MB")
        
    def _calculate_optimal_chunk_size(self, available_memory_mb: float) -> int:
        """Calculate optimal chunk size based on available memory and file size."""
        if available_memory_mb > 2048:  # >2GB available
            base_chunk = 5000
        elif available_memory_mb > 1024:  # >1GB available
            base_chunk = 2000
        elif available_memory_mb > 512:  # >512MB available
            base_chunk = 1000
        else:  # <512MB available
            base_chunk = 500
            
        # Adjust based on file size
        if self.file_size_mb > 100:
            return max(base_chunk // 2, 250)  # Smaller chunks for large files
        elif self.file_size_mb < 10:
            return min(base_chunk * 2, 10000)  # Larger chunks for small files
        else:
            return base_chunk
            
    def enhanced_detect_sheet_type(self, df: pd.DataFrame) -> str:
        """Enhanced sheet type detection with comprehensive keyword matching."""
        if df.empty:
            return 'unknown'
        
        # Convert all column names to lowercase for matching
        columns_text = ' '.join([str(col).lower() for col in df.columns])
        
        # Comprehensive detection rules with priority order
        detection_rules = [
            # Equipment (highest priority - most common)
            ('equipment', ['make', 'type', 'serial', 'ba_number', 'ba no', 'vintage', 'meterage', 'equipment', 'ser no']),
            
            # Fluids (high priority)
            ('fluids', ['fluid', 'oil', 'lubricant', 'capacity', 'grade', 'hydraulic', 'engine', 'transmission', 'coolant']),
            
            # Maintenance (high priority)
            ('maintenance', ['maintenance', 'service', 'due_date', 'last_service', 'tm-1', 'tm-2', 'technical maintenance']),
            
            # Overhauls (high priority)
            ('overhauls', ['overhaul', 'oh-i', 'oh-ii', 'oh1', 'oh2', 'major_service', 'major overhaul']),
            
            # Batteries
            ('batteries', ['battery', 'voltage', 'ampere', 'cell', 'charge', 'battery life', 'battery change']),
            
            # Tyres/Conditioning
            ('tyre_maintenance', ['tyre', 'tire', 'rotation', 'wheel', 'rubber', 'tyre life', 'tyre change']),
            ('conditioning', ['conditioning', 'refurbishment', 'restoration', 'reconditioning']),
            
            # Repairs
            ('repairs', ['repair', 'fix', 'breakdown', 'fault', 'defect', 'repair date', 'repair cost']),
            
            # Discard Criteria
            ('discard_criteria', ['discard', 'criteria', 'life_cycle', 'disposal', 'scrap', 'write off']),
            
            # Forecasts
            ('demand_forecast', ['demand', 'forecast', 'prediction', 'requirement', 'future need']),
            ('tyre_forecast', ['tyre forecast', 'tire forecast', 'tyre demand', 'tyre requirement']),
            ('battery_forecast', ['battery forecast', 'battery demand', 'battery requirement']),
            ('equipment_forecast', ['equipment forecast', 'equipment demand', 'equipment requirement']),
            ('overhaul_forecast', ['overhaul forecast', 'overhaul demand', 'overhaul schedule']),
            ('conditioning_forecast', ['conditioning forecast', 'conditioning demand', 'conditioning schedule'])
        ]
        
        # Check each detection rule in priority order
        for sheet_type, keywords in detection_rules:
            if any(keyword in columns_text for keyword in keywords):
                logger.info(f"Detected sheet type '{sheet_type}' based on keywords: {[k for k in keywords if k in columns_text]}")
                return sheet_type
        
        # Additional heuristic: check first few rows for data patterns
        if len(df) > 0:
            first_row_text = ' '.join([str(val).lower() for val in df.iloc[0].values if pd.notna(val)])
            
            # Check if first row contains equipment-like data
            if any(keyword in first_row_text for keyword in ['vehicle', 'generator', 'truck', 'equipment']):
                return 'equipment'
        
        logger.warning(f"Could not detect sheet type. Columns: {list(df.columns)}")
        return 'unknown'
        
    def read_excel_in_chunks_enhanced(self, sheet_name: str) -> Iterator[pd.DataFrame]:
        """Enhanced Excel reading with better header handling and error recovery."""
        try:
            logger.info(f"Reading sheet '{sheet_name}' in chunks of {self.chunk_size} rows")
            
            # First, try to read the entire sheet to get proper headers
            with pd.ExcelFile(self.file_path) as excel_file:
                # Try different header configurations to find the best one
                header_configs = [[0, 1], [0], [0, 1, 2]]
                best_df = None
                best_config = None
                
                for config in header_configs:
                    try:
                        sample_df = pd.read_excel(excel_file, sheet_name=sheet_name, header=config, nrows=10)
                        
                        # Flatten multi-level columns if needed
                        if isinstance(sample_df.columns, pd.MultiIndex):
                            sample_df.columns = [' -> '.join([str(c) for c in col if str(c) != 'nan']).strip(' -> ') 
                                               for col in sample_df.columns]
                        
                        # Check if this configuration gives us meaningful columns
                        meaningful_cols = sum(1 for col in sample_df.columns if str(col).strip() and str(col) != 'Unnamed')
                        
                        if best_df is None or meaningful_cols > len([c for c in best_df.columns if str(c).strip() and str(c) != 'Unnamed']):
                            best_df = sample_df
                            best_config = config
                            
                    except Exception as e:
                        logger.debug(f"Header config {config} failed: {e}")
                        continue
                
                if best_df is None:
                    logger.error(f"Could not read sheet '{sheet_name}' with any header configuration")
                    return
                
                # Store the best column names for consistent chunking
                self._column_names = best_df.columns.tolist()
                logger.info(f"Using header config {best_config} for sheet '{sheet_name}'. Columns: {self._column_names}")
                
                # Now read in chunks using the best configuration
                chunk_start = 0
                header_rows = len(best_config) if isinstance(best_config, list) else 1
                
                while True:
                    try:
                        # For first chunk, use the header configuration
                        if chunk_start == 0:
                            chunk_df = pd.read_excel(
                                excel_file,
                                sheet_name=sheet_name,
                                header=best_config,
                                nrows=self.chunk_size
                            )
                            
                            # Flatten multi-level columns if needed
                            if isinstance(chunk_df.columns, pd.MultiIndex):
                                chunk_df.columns = [' -> '.join([str(c) for c in col if str(c) != 'nan']).strip(' -> ') 
                                                   for col in chunk_df.columns]
                        else:
                            # For subsequent chunks, skip headers and use stored column names
                            chunk_df = pd.read_excel(
                                excel_file,
                                sheet_name=sheet_name,
                                skiprows=chunk_start + header_rows,
                                nrows=self.chunk_size,
                                header=None
                            )
                            
                            # Apply stored column names
                            if len(chunk_df.columns) == len(self._column_names):
                                chunk_df.columns = self._column_names
                            else:
                                logger.warning(f"Column count mismatch in chunk {chunk_start}: expected {len(self._column_names)}, got {len(chunk_df.columns)}")
                                # Pad or truncate columns as needed
                                if len(chunk_df.columns) < len(self._column_names):
                                    for i in range(len(chunk_df.columns), len(self._column_names)):
                                        chunk_df[self._column_names[i]] = None
                                else:
                                    chunk_df = chunk_df.iloc[:, :len(self._column_names)]
                                    chunk_df.columns = self._column_names
                        
                        if chunk_df.empty:
                            break
                        
                        # Remove completely empty rows
                        chunk_df = chunk_df.dropna(how='all')
                        
                        if chunk_df.empty:
                            chunk_start += self.chunk_size
                            continue
                        
                        yield chunk_df
                        
                        # Check memory usage and cleanup if needed
                        memory_usage = psutil.virtual_memory().percent
                        if memory_usage > 80:  # >80% memory usage
                            logger.warning(f"High memory usage: {memory_usage:.1f}%, forcing cleanup")
                            gc.collect()
                        
                        chunk_start += self.chunk_size
                        
                    except Exception as e:
                        logger.error(f"Error reading chunk starting at row {chunk_start}: {e}")
                        break
                        
        except Exception as e:
            logger.error(f"Error reading sheet '{sheet_name}': {e}")
            self.stats['errors'].append(f"Failed to read sheet '{sheet_name}': {e}")
            
    def process_chunk_by_type(self, chunk_df: pd.DataFrame, sheet_type: str) -> int:
        """Process a chunk based on its detected type with comprehensive support."""
        try:
            from robust_excel_importer_working import RobustExcelImporter
            
            # Create a temporary importer for this chunk
            temp_importer = RobustExcelImporter()
            if not temp_importer.initialize_staging():
                return 0
            
            count = 0
            
            # Process based on detected type with full support
            if sheet_type == 'equipment':
                count = temp_importer._extract_and_save_equipment(chunk_df, "equipment_chunk")
            elif sheet_type == 'fluids':
                count = temp_importer._extract_and_save_fluids(chunk_df, "fluids_chunk")
            elif sheet_type == 'maintenance':
                count = temp_importer._extract_and_save_maintenance(chunk_df, "maintenance_chunk")
            elif sheet_type == 'overhauls':
                count = temp_importer._extract_and_save_overhauls(chunk_df, "overhauls_chunk")
            elif sheet_type == 'batteries':
                count = temp_importer._extract_and_save_batteries(chunk_df, "batteries_chunk")
            elif sheet_type == 'tyre_maintenance':
                count = temp_importer._extract_and_save_conditioning(chunk_df, "tyres_chunk")
            elif sheet_type == 'conditioning':
                count = temp_importer._extract_and_save_conditioning(chunk_df, "conditioning_chunk")
            elif sheet_type == 'repairs':
                count = temp_importer._extract_and_save_repairs(chunk_df, "repairs_chunk")
            elif sheet_type == 'discard_criteria':
                count = temp_importer._extract_and_save_discard_criteria(chunk_df, "discard_chunk")
            else:
                # For unknown types, try to detect and process as equipment
                logger.warning(f"Unknown sheet type '{sheet_type}', attempting to process as equipment")
                count = temp_importer._extract_and_save_equipment(chunk_df, "unknown_chunk")
            
            # Clean up the temporary importer
            del temp_importer
            gc.collect()
            
            return count
            
        except Exception as e:
            logger.error(f"Error processing {sheet_type} chunk: {e}")
            self.stats['errors'].append(f"{sheet_type.title()} chunk processing failed: {e}")
            return 0
            
    def process_sheet_in_chunks_enhanced(self, sheet_name: str) -> int:
        """Enhanced sheet processing with complete data type support."""
        total_processed = 0
        sheet_type = None
        
        try:
            logger.info(f"Processing sheet '{sheet_name}' in chunks of {self.chunk_size} rows")
            
            chunk_count = 0
            for chunk_df in self.read_excel_in_chunks_enhanced(sheet_name):
                chunk_count += 1
                logger.debug(f"Processing chunk {chunk_count} of sheet '{sheet_name}' ({len(chunk_df)} rows)")
                
                # Detect sheet type from first chunk
                if sheet_type is None:
                    sheet_type = self.enhanced_detect_sheet_type(chunk_df)
                    logger.info(f"Detected sheet type: {sheet_type}")
                
                # Process chunk with enhanced type support
                chunk_processed = self.process_chunk_by_type(chunk_df, sheet_type)
                total_processed += chunk_processed
                
                # Update stats
                if sheet_type in self.stats:
                    self.stats[sheet_type] += chunk_processed
                
                # Force cleanup after each chunk
                del chunk_df
                gc.collect()
                
                logger.debug(f"Chunk {chunk_count} processed: {chunk_processed} records")
            
            if total_processed > 0:
                self.stats['sheets_processed'].append(sheet_name)
                logger.info(f"Sheet '{sheet_name}' processing completed: {total_processed} records ({sheet_type})")
            else:
                self.stats['sheets_skipped'].append(sheet_name)
                logger.warning(f"Sheet '{sheet_name}' processed but no records imported ({sheet_type})")
            
            return total_processed
            
        except Exception as e:
            logger.error(f"Error processing sheet '{sheet_name}': {e}")
            self.stats['errors'].append(f"Sheet '{sheet_name}' processing failed: {e}")
            self.stats['sheets_skipped'].append(sheet_name)
            return total_processed
            
    def import_all_data_enhanced(self) -> Dict:
        """Enhanced import with comprehensive validation and reporting."""
        try:
            logger.info(f"Starting enhanced memory-safe import of {self.file_path} ({self.file_size_mb:.1f}MB)")
            
            # Get list of sheets
            with pd.ExcelFile(self.file_path) as excel_file:
                sheet_names = excel_file.sheet_names
                logger.info(f"Found {len(sheet_names)} sheets: {sheet_names}")
            
            # Process each sheet with enhanced logic
            total_records = 0
            for sheet_name in sheet_names:
                try:
                    logger.info(f"Processing sheet: {sheet_name}")
                    sheet_records = self.process_sheet_in_chunks_enhanced(sheet_name)
                    total_records += sheet_records
                    
                    # Force cleanup between sheets
                    gc.collect()
                    
                except Exception as e:
                    logger.error(f"Error processing sheet '{sheet_name}': {e}")
                    self.stats['errors'].append(f"Sheet '{sheet_name}' failed: {e}")
                    self.stats['sheets_skipped'].append(sheet_name)
            
            # Final cleanup and validation
            gc.collect()
            
            # Add summary information
            self.stats['total_records'] = total_records
            self.stats['total_sheets'] = len(sheet_names)
            self.stats['sheets_processed_count'] = len(self.stats['sheets_processed'])
            self.stats['sheets_skipped_count'] = len(self.stats['sheets_skipped'])
            self.stats['success_rate'] = (self.stats['sheets_processed_count'] / len(sheet_names)) * 100 if sheet_names else 0
            
            logger.info(f"Enhanced Excel import completed. Total records: {total_records}")
            logger.info(f"Sheets processed: {self.stats['sheets_processed_count']}/{len(sheet_names)} ({self.stats['success_rate']:.1f}%)")
            
            if self.stats['sheets_skipped']:
                logger.warning(f"Sheets skipped: {self.stats['sheets_skipped']}")
            
            if self.stats['errors']:
                logger.error(f"Errors encountered: {len(self.stats['errors'])}")
                for error in self.stats['errors']:
                    logger.error(f"  - {error}")
            
            return self.stats
            
        except Exception as e:
            logger.error(f"Enhanced Excel import failed: {e}")
            self.stats['errors'].append(f"Import failed: {e}")
            return self.stats

def should_use_enhanced_chunked_processing(file_path: str) -> bool:
    """
    Determine if enhanced chunked processing should be used based on system resources and file size.
    More intelligent than the original 50MB threshold.
    """
    try:
        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
        available_memory_mb = psutil.virtual_memory().available / (1024 * 1024)
        
        # Use chunked processing if:
        # 1. File is large relative to available memory (>10% of available memory)
        # 2. Available memory is less than 1GB
        # 3. File is larger than 25MB (reduced from 50MB)
        
        memory_ratio_threshold = file_size_mb > (available_memory_mb * 0.1)
        low_memory_system = available_memory_mb < 1024
        large_file = file_size_mb > 25
        
        should_chunk = memory_ratio_threshold or low_memory_system or large_file
        
        logger.info(f"Chunking decision: file_size={file_size_mb:.1f}MB, available_memory={available_memory_mb:.1f}MB")
        logger.info(f"  memory_ratio_threshold={memory_ratio_threshold}, low_memory_system={low_memory_system}, large_file={large_file}")
        logger.info(f"  Decision: {'Use chunked processing' if should_chunk else 'Use standard processing'}")
        
        return should_chunk
        
    except Exception as e:
        logger.error(f"Error determining chunking strategy: {e}")
        # Default to chunked processing for safety
        return True

def enhanced_import_from_excel(file_path: str) -> Dict:
    """
    Enhanced Excel import function that fixes the partial import issue.
    Replaces the original import_from_excel function.
    """
    logger.info(f"Starting enhanced Excel import from {file_path}")
    
    try:
        # Check file exists and is readable
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Excel file not found: {file_path}")
        
        # Check file size
        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
        if file_size_mb > 500:  # Hard limit for military systems
            raise ValueError(f"Excel file too large ({file_size_mb:.1f}MB). Maximum supported size is 500MB.")
        
        # Determine processing strategy
        if should_use_enhanced_chunked_processing(file_path):
            logger.info("Using enhanced memory-safe chunked processing")
            importer = EnhancedMemorySafeExcelImporter(file_path)
            return importer.import_all_data_enhanced()
        else:
            logger.info("Using standard processing")
            from robust_excel_importer_working import RobustExcelImporter
            importer = RobustExcelImporter()
            
            if not importer.initialize_staging():
                logger.error("Failed to initialize staging database")
                return {
                    'equipment': 0, 'fluids': 0, 'maintenance': 0, 'overhauls': 0,
                    'batteries': 0, 'tyres': 0, 'repairs': 0, 'discard_criteria': 0,
                    'errors': ['Failed to initialize staging database']
                }
            
            success, stats = importer.process_excel_file(file_path)
            
            if not success:
                logger.error(f"Standard Excel import failed: {stats}")
                return {
                    'equipment': 0, 'fluids': 0, 'maintenance': 0, 'overhauls': 0,
                    'batteries': 0, 'tyres': 0, 'repairs': 0, 'discard_criteria': 0,
                    'errors': [f"Import failed: {stats}"]
                }
            
            return stats
            
    except Exception as e:
        logger.error(f"Enhanced Excel import failed: {e}")
        return {
            'equipment': 0, 'fluids': 0, 'maintenance': 0, 'overhauls': 0,
            'batteries': 0, 'tyres': 0, 'repairs': 0, 'discard_criteria': 0,
            'errors': [f"Import failed: {str(e)}"]
        }

def validate_import_completeness(file_path: str, import_stats: dict) -> dict:
    """
    Validate that all expected data was imported from the Excel file.
    Returns a comprehensive validation report.
    """
    validation_report = {
        'complete': True,
        'warnings': [],
        'missing_data_types': [],
        'sheet_analysis': {},
        'data_type_coverage': {},
        'recommendations': []
    }

    try:
        logger.info(f"Validating import completeness for {file_path}")

        # Analyze original file structure
        with pd.ExcelFile(file_path) as excel_file:
            total_sheets = len(excel_file.sheet_names)
            processed_sheets = 0

            for sheet_name in excel_file.sheet_names:
                try:
                    # Read a sample to detect sheet type
                    sample_df = pd.read_excel(excel_file, sheet_name=sheet_name, nrows=10)

                    # Use enhanced detection
                    importer = EnhancedMemorySafeExcelImporter(file_path)
                    detected_type = importer.enhanced_detect_sheet_type(sample_df)

                    # Check if this type was imported
                    imported_count = import_stats.get(detected_type, 0)
                    was_imported = imported_count > 0

                    validation_report['sheet_analysis'][sheet_name] = {
                        'detected_type': detected_type,
                        'imported': was_imported,
                        'imported_count': imported_count,
                        'sample_rows': len(sample_df)
                    }

                    if was_imported:
                        processed_sheets += 1

                    # Track data type coverage
                    if detected_type not in validation_report['data_type_coverage']:
                        validation_report['data_type_coverage'][detected_type] = {
                            'sheets_found': 0,
                            'sheets_imported': 0,
                            'total_records': 0
                        }

                    validation_report['data_type_coverage'][detected_type]['sheets_found'] += 1
                    if was_imported:
                        validation_report['data_type_coverage'][detected_type]['sheets_imported'] += 1
                        validation_report['data_type_coverage'][detected_type]['total_records'] += imported_count

                    # Check for missing critical data types
                    if detected_type != 'unknown' and not was_imported:
                        validation_report['missing_data_types'].append(detected_type)
                        validation_report['complete'] = False
                        validation_report['warnings'].append(f"Sheet '{sheet_name}' ({detected_type}) was not imported")

                except Exception as e:
                    logger.error(f"Error analyzing sheet '{sheet_name}': {e}")
                    validation_report['warnings'].append(f"Could not analyze sheet '{sheet_name}': {e}")

            # Calculate overall success metrics
            validation_report['success_rate'] = (processed_sheets / total_sheets) * 100 if total_sheets > 0 else 0
            validation_report['total_sheets'] = total_sheets
            validation_report['processed_sheets'] = processed_sheets

            # Generate recommendations
            if validation_report['missing_data_types']:
                validation_report['recommendations'].append("Some data types were not imported. Check file format and column headers.")

            if validation_report['success_rate'] < 100:
                validation_report['recommendations'].append("Not all sheets were processed successfully. Check for file corruption or unsupported formats.")

            if 'unknown' in validation_report['data_type_coverage'] and validation_report['data_type_coverage']['unknown']['sheets_found'] > 0:
                validation_report['recommendations'].append("Some sheets could not be classified. Consider updating column headers for better detection.")

            # Check for errors in import stats
            if 'errors' in import_stats and import_stats['errors']:
                validation_report['complete'] = False
                validation_report['warnings'].extend(import_stats['errors'])
                validation_report['recommendations'].append("Import errors occurred. Check log files for detailed error information.")

        logger.info(f"Validation completed: {validation_report['success_rate']:.1f}% success rate")
        return validation_report

    except Exception as e:
        logger.error(f"Error validating import completeness: {e}")
        validation_report['complete'] = False
        validation_report['warnings'].append(f"Validation failed: {e}")
        return validation_report

def create_import_summary_report(import_stats: dict, validation_report: dict = None) -> str:
    """Create a comprehensive import summary report for user feedback."""
    report = []
    report.append("=" * 60)
    report.append("EXCEL IMPORT SUMMARY REPORT")
    report.append("=" * 60)

    # Import status
    if validation_report:
        status = "COMPLETE" if validation_report['complete'] else "INCOMPLETE"
        report.append(f"Import Status: {status}")
        report.append(f"Success Rate: {validation_report.get('success_rate', 0):.1f}%")
    else:
        report.append("Import Status: COMPLETED (validation not performed)")

    report.append("")

    # Data types imported
    report.append("Data Types Imported:")
    data_types_found = False
    for data_type, count in import_stats.items():
        if isinstance(count, int) and count > 0 and data_type not in ['errors', 'warnings', 'total_records']:
            report.append(f"  ✓ {data_type.replace('_', ' ').title()}: {count:,} records")
            data_types_found = True

    if not data_types_found:
        report.append("  ⚠ No data was imported")

    # Missing data types
    if validation_report and validation_report.get('missing_data_types'):
        report.append("")
        report.append("Missing Data Types:")
        for missing_type in validation_report['missing_data_types']:
            report.append(f"  ✗ {missing_type.replace('_', ' ').title()}: Not imported")

    # Sheet analysis
    if validation_report and validation_report.get('sheet_analysis'):
        report.append("")
        report.append("Sheet Analysis:")
        for sheet_name, analysis in validation_report['sheet_analysis'].items():
            status_icon = "✓" if analysis['imported'] else "✗"
            report.append(f"  {status_icon} {sheet_name}: {analysis['detected_type']} ({analysis.get('imported_count', 0)} records)")

    # Warnings and errors
    warnings = []
    if 'warnings' in import_stats:
        warnings.extend(import_stats['warnings'])
    if validation_report and validation_report.get('warnings'):
        warnings.extend(validation_report['warnings'])

    if warnings:
        report.append("")
        report.append("Warnings:")
        for warning in warnings[:10]:  # Limit to first 10 warnings
            report.append(f"  ⚠ {warning}")
        if len(warnings) > 10:
            report.append(f"  ... and {len(warnings) - 10} more warnings")

    errors = import_stats.get('errors', [])
    if errors:
        report.append("")
        report.append("Errors:")
        for error in errors[:5]:  # Limit to first 5 errors
            report.append(f"  ❌ {error}")
        if len(errors) > 5:
            report.append(f"  ... and {len(errors) - 5} more errors")

    # Recommendations
    if validation_report and validation_report.get('recommendations'):
        report.append("")
        report.append("Recommendations:")
        for recommendation in validation_report['recommendations']:
            report.append(f"  💡 {recommendation}")

    # Summary statistics
    total_records = import_stats.get('total_records', sum(v for v in import_stats.values() if isinstance(v, int)))
    if total_records > 0:
        report.append("")
        report.append(f"Total Records Imported: {total_records:,}")

    report.append("")
    report.append("=" * 60)

    return "\n".join(report)
