#!/usr/bin/env python3
"""
PROJECT-ALPHA Equipment Inventory Management System
Comprehensive Build Script for PyInstaller Executable

This script handles the complete build process for creating a standalone
executable using PyInstaller with all necessary dependencies and optimizations.
"""

import os
import sys
import shutil
import subprocess
import platform
import logging
from pathlib import Path
from datetime import datetime
import tempfile

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('build_log.txt')
    ]
)
logger = logging.getLogger(__name__)

class ProjectBuilder:
    """Handles the complete build process for PROJECT-ALPHA."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.build_start_time = datetime.now()
        self.build_log = []
        
        # Application metadata
        self.app_name = "InventoryTracker"
        self.app_version = "1.0.0"
        self.app_description = "PROJECT-ALPHA Equipment Inventory Management System"
        
        logger.info(f"Initialized builder for {self.app_description}")
        logger.info(f"Project root: {self.project_root}")
        logger.info(f"Platform: {platform.platform()}")
        logger.info(f"Python: {sys.version}")
    
    def log(self, message, level="INFO"):
        """Log a message with timestamp."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        self.build_log.append(log_entry)
        if level == "ERROR":
            logger.error(message)
        elif level == "WARNING":
            logger.warning(message)
        else:
            logger.info(message)
    
    def check_dependencies(self):
        """Check if all required dependencies are installed."""
        self.log("Checking build dependencies...")
        
        required_modules = [
            'PyQt5', 'pandas', 'numpy', 'openpyxl', 'xlrd', 'matplotlib', 
            'reportlab', 'psutil', 'pint', 'fuzzywuzzy', 'Levenshtein',
            'dateutil', 'PIL', 'pyinstaller'
        ]
        
        missing_modules = []
        installed_modules = []
        
        for module in required_modules:
            try:
                if module == 'PIL':
                    __import__('PIL')
                else:
                    __import__(module)
                installed_modules.append(module)
                self.log(f"✓ {module} found")
            except ImportError:
                missing_modules.append(module)
                self.log(f"✗ {module} missing", "ERROR")
        
        if missing_modules:
            self.log(f"Missing required modules: {', '.join(missing_modules)}", "ERROR")
            self.log("Please install missing dependencies with:", "ERROR")
            self.log("pip install -r requirements.txt", "ERROR")
            return False
        
        self.log(f"All {len(installed_modules)} required dependencies found")
        return True
    
    def check_pyinstaller_version(self):
        """Check PyInstaller version and capabilities."""
        try:
            result = subprocess.run([sys.executable, "-m", "PyInstaller", "--version"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip()
                self.log(f"PyInstaller version: {version}")
                
                # Check if version is recent enough (5.0+)
                version_parts = version.split('.')
                if len(version_parts) >= 2:
                    major, minor = int(version_parts[0]), int(version_parts[1])
                    if major >= 5:
                        self.log("PyInstaller version is suitable")
                        return True
                    else:
                        self.log(f"PyInstaller version {version} may be too old (recommend 5.0+)", "WARNING")
                        return True  # Continue anyway
                return True
            else:
                self.log("PyInstaller not found or not working", "ERROR")
                return False
        except Exception as e:
            self.log(f"Error checking PyInstaller: {e}", "ERROR")
            return False
    
    def clean_previous_builds(self):
        """Clean up previous build artifacts."""
        self.log("Cleaning previous build artifacts...")
        
        cleanup_dirs = ['build', 'dist', '__pycache__']
        cleanup_files = ['*.pyc', '*.pyo']
        
        for dir_name in cleanup_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                try:
                    shutil.rmtree(dir_path)
                    self.log(f"Removed {dir_name}/")
                except Exception as e:
                    self.log(f"Warning: Could not remove {dir_name}/: {e}", "WARNING")
        
        # Clean Python cache files
        for cache_file in self.project_root.rglob('*.pyc'):
            try:
                cache_file.unlink()
            except:
                pass
        
        for cache_dir in self.project_root.rglob('__pycache__'):
            try:
                shutil.rmtree(cache_dir)
            except:
                pass
        
        self.log("Cleanup completed")
    
    def validate_spec_file(self):
        """Validate that the spec file exists and is properly formatted."""
        spec_file = self.project_root / "InventoryTracker.spec"
        
        if not spec_file.exists():
            self.log("InventoryTracker.spec file not found", "ERROR")
            return False
        
        try:
            with open(spec_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Basic validation
            required_elements = ['Analysis', 'PYZ', 'EXE', 'main.py']
            for element in required_elements:
                if element not in content:
                    self.log(f"Missing required element '{element}' in spec file", "ERROR")
                    return False
            
            self.log("Spec file validation passed")
            return True
            
        except Exception as e:
            self.log(f"Error validating spec file: {e}", "ERROR")
            return False
    
    def create_app_icon(self):
        """Create application icon if it doesn't exist."""
        icon_path = self.project_root / "resources" / "app_icon.ico"
        
        if icon_path.exists():
            self.log("Application icon found")
            return True
        
        self.log("Creating default application icon...")
        
        try:
            from PIL import Image, ImageDraw
            
            # Create a simple icon
            size = 256
            img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # Draw a simple military-style icon
            margin = 20
            # Background circle
            draw.ellipse([margin, margin, size-margin, size-margin], 
                        fill=(34, 139, 34, 255), outline=(0, 100, 0, 255), width=4)
            
            # Draw gear symbol
            center = size // 2
            gear_size = 60
            draw.ellipse([center-gear_size, center-gear_size, center+gear_size, center+gear_size], 
                        fill=(255, 255, 255, 255), outline=(0, 100, 0, 255), width=3)
            
            # Inner circle
            inner_size = 20
            draw.ellipse([center-inner_size, center-inner_size, center+inner_size, center+inner_size], 
                        fill=(34, 139, 34, 255))
            
            # Ensure resources directory exists
            icon_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save icon
            img.save(icon_path, format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
            self.log("Application icon created successfully")
            return True
            
        except ImportError:
            self.log("PIL not available, skipping icon creation", "WARNING")
            return True
        except Exception as e:
            self.log(f"Error creating icon: {e}", "WARNING")
            return True  # Non-critical error
    
    def build_executable(self):
        """Build the executable using PyInstaller."""
        self.log("Starting PyInstaller build...")
        
        # Build command
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "InventoryTracker.spec",
            "--clean",
            "--noconfirm",
            "--log-level=INFO"
        ]
        
        self.log(f"Running command: {' '.join(cmd)}")
        
        try:
            # Run PyInstaller with real-time output
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
                cwd=self.project_root
            )
            
            # Stream output in real time
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    line = output.strip()
                    if line:
                        print(f"PyInstaller: {line}")
                        # Log important messages
                        if any(keyword in line.lower() for keyword in ['error', 'warning', 'critical']):
                            self.log(f"PyInstaller: {line}")
            
            process.wait()
            
            if process.returncode == 0:
                self.log("PyInstaller build completed successfully")
                return True
            else:
                self.log(f"PyInstaller build failed with return code {process.returncode}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Error running PyInstaller: {e}", "ERROR")
            return False
    
    def validate_executable(self):
        """Validate that the executable was created and is functional."""
        exe_path = self.project_root / "dist" / f"{self.app_name}.exe"
        
        if not exe_path.exists():
            self.log("Executable file not found in dist/", "ERROR")
            return False
        
        # Check file size (should be substantial for this application)
        file_size_mb = exe_path.stat().st_size / (1024 * 1024)
        self.log(f"Executable size: {file_size_mb:.1f} MB")
        
        if file_size_mb < 50:  # Expect at least 50MB due to PyQt5 and dependencies
            self.log("Executable seems too small - may be missing dependencies", "WARNING")
        elif file_size_mb > 500:  # Warn if over 500MB
            self.log("Executable is quite large - consider optimizing", "WARNING")
        else:
            self.log("Executable size looks reasonable")
        
        # Try to run a quick test (just check if it starts)
        self.log("Testing executable startup...")
        try:
            # Run with --help flag to test basic functionality without GUI
            test_cmd = [str(exe_path), "--help"]
            result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=30)
            
            # For GUI apps, return code might be non-zero even on success
            # Just check that it doesn't crash immediately
            self.log("Executable basic test completed")
            return True
            
        except subprocess.TimeoutExpired:
            self.log("Executable test timed out (normal for GUI apps)", "WARNING")
            return True
        except Exception as e:
            self.log(f"Error testing executable: {e}", "WARNING")
            return True  # Don't fail build for test issues
    
    def create_distribution_package(self):
        """Create a complete distribution package."""
        self.log("Creating distribution package...")
        
        dist_dir = self.project_root / "PROJECT_ALPHA_Distribution"
        exe_path = self.project_root / "dist" / f"{self.app_name}.exe"
        
        # Remove old distribution
        if dist_dir.exists():
            shutil.rmtree(dist_dir)
        
        # Create distribution directory
        dist_dir.mkdir()
        
        # Copy executable
        if exe_path.exists():
            shutil.copy2(exe_path, dist_dir / f"{self.app_name}.exe")
            self.log("Executable copied to distribution")
        else:
            self.log("Executable not found for distribution", "ERROR")
            return False
        
        # Copy important files
        important_files = [
            "README.md",
            "LICENSE",
            "requirements.txt"
        ]
        
        for file_name in important_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                shutil.copy2(file_path, dist_dir / file_name)
                self.log(f"Copied {file_name}")
        
        # Create user documentation
        self.create_user_documentation(dist_dir)
        
        # Create launcher script
        self.create_launcher_script(dist_dir)
        
        self.log(f"Distribution package created: {dist_dir}")
        return True
    
    def create_user_documentation(self, dist_dir):
        """Create user-friendly documentation."""
        readme_content = f"""# {self.app_description}
Version {self.app_version}

## 🚀 Quick Start

1. **Double-click InventoryTracker.exe** to start the application
2. The application will automatically create its database on first run
3. Use the tabs to navigate between different functions:
   - **Dashboard**: Overview of equipment status and alerts
   - **Equipment**: Manage equipment inventory
   - **Fluids**: Track fluid requirements and maintenance
   - **Maintenance**: Schedule and track maintenance activities
   - **Overhaul**: Manage major overhauls and repairs
   - **Discard Criteria**: Equipment lifecycle management

## 📋 System Requirements

- **Windows 10 or later** (64-bit)
- **4GB RAM** minimum (8GB recommended)
- **200MB free disk space**
- **No additional software required** - all dependencies included

## 🔧 Features

### Core Functionality
- Complete equipment inventory management
- Automated maintenance scheduling
- Fluid requirement tracking
- Repair and overhaul management
- Excel import/export capabilities
- PDF report generation

### Advanced Features
- Real-time dashboard with charts
- Demand forecasting
- Equipment lifecycle tracking
- Low-resolution display support (1366x768)
- Offline operation (no internet required)

## 📊 Getting Started

### First Time Setup
1. Launch the application
2. The database will be created automatically
3. Import existing data using File > Import Excel
4. Begin adding equipment and setting up maintenance schedules

### Daily Usage
- Use the Dashboard for quick overview
- Add new equipment in the Equipment tab
- Schedule maintenance in the Maintenance tab
- Generate reports using the Export functions

## 🛠️ Troubleshooting

### Application Won't Start
- Check Windows Defender isn't blocking the executable
- Ensure you have administrator privileges if needed
- Try running as administrator (right-click > Run as administrator)

### Performance Issues
- Close other applications to free memory
- Ensure sufficient disk space
- Check that antivirus isn't scanning the executable

### Database Issues
- The database file (inventory.db) is created automatically
- Regular backups are recommended
- Contact support if database corruption occurs

## 📞 Support

For technical support or questions:
1. Check this documentation first
2. Review the build log (build_log.txt) for error details
3. Contact your IT support team
4. Include error messages and system information when reporting issues

## 📝 License

{self.app_description}
Built on {self.build_start_time.strftime('%Y-%m-%d')}

This software is for internal use only.

---

Enjoy using PROJECT-ALPHA! 🎖️
"""
        
        with open(dist_dir / "USER_GUIDE.txt", "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        self.log("User documentation created")
    
    def create_launcher_script(self, dist_dir):
        """Create a Windows batch launcher script."""
        launcher_content = f"""@echo off
title {self.app_description}
echo.
echo ====================================================
echo  {self.app_description}
echo  Version {self.app_version}
echo ====================================================
echo.
echo Starting application...
echo.

REM Check if executable exists
if not exist "InventoryTracker.exe" (
    echo ERROR: InventoryTracker.exe not found!
    echo Please ensure you are running this script from the correct directory.
    pause
    exit /b 1
)

REM Start the application
echo Launching {self.app_name}...
start "" "InventoryTracker.exe"

REM Wait a moment and check if it started
timeout /t 3 /nobreak >nul
echo.
echo Application should now be starting...
echo You can close this window.
echo.
timeout /t 2 /nobreak >nul
"""
        
        with open(dist_dir / "Start_InventoryTracker.bat", "w", encoding="utf-8") as f:
            f.write(launcher_content)
        
        self.log("Launcher script created")
    
    def save_build_report(self):
        """Save a comprehensive build report."""
        build_time = datetime.now() - self.build_start_time
        
        report_content = f"""
PROJECT-ALPHA Build Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Build Duration: {build_time}

=== BUILD SUMMARY ===
Application: {self.app_description}
Version: {self.app_version}
Platform: {platform.platform()}
Python Version: {sys.version}

=== BUILD LOG ===
"""
        
        for log_entry in self.build_log:
            report_content += log_entry + "\n"
        
        with open(self.project_root / "BUILD_REPORT.txt", "w", encoding="utf-8") as f:
            f.write(report_content)
        
        self.log("Build report saved")
    
    def run_complete_build(self):
        """Execute the complete build process."""
        self.log("="*60)
        self.log(f"Starting complete build for {self.app_description}")
        self.log("="*60)
        
        # Pre-build checks
        if not self.check_dependencies():
            self.log("Build failed: Missing dependencies", "ERROR")
            return False
        
        if not self.check_pyinstaller_version():
            self.log("Build failed: PyInstaller issues", "ERROR")
            return False
        
        if not self.validate_spec_file():
            self.log("Build failed: Invalid spec file", "ERROR")
            return False
        
        # Preparation
        self.clean_previous_builds()
        self.create_app_icon()
        
        # Build process
        if not self.build_executable():
            self.log("Build failed: PyInstaller build error", "ERROR")
            return False
        
        # Validation
        if not self.validate_executable():
            self.log("Build failed: Executable validation error", "ERROR")
            return False
        
        # Distribution
        if not self.create_distribution_package():
            self.log("Build failed: Distribution package error", "ERROR")
            return False
        
        # Finalization
        self.save_build_report()
        
        build_time = datetime.now() - self.build_start_time
        self.log("="*60)
        self.log(f"BUILD COMPLETED SUCCESSFULLY! 🎉")
        self.log(f"Build time: {build_time}")
        self.log(f"Distribution ready: PROJECT_ALPHA_Distribution/")
        self.log("="*60)
        
        return True

def main():
    """Main entry point for the build script."""
    print(f"""
╔══════════════════════════════════════════════════════════════╗
║                    PROJECT-ALPHA                             ║
║              Equipment Inventory Management                  ║
║                    Build System v1.0                        ║
╚══════════════════════════════════════════════════════════════╝

This script will create a standalone executable for Windows.

Features:
✓ Complete dependency bundling
✓ Professional executable with version info
✓ User documentation and launcher scripts
✓ Windows compatibility manifest
✓ Comprehensive error checking and validation

""")
    
    # Confirm build
    response = input("Do you want to proceed with the build? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Build cancelled.")
        return 1
    
    # Create and run builder
    builder = ProjectBuilder()
    success = builder.run_complete_build()
    
    if success:
        print(f"""
╔══════════════════════════════════════════════════════════════╗
║                   BUILD SUCCESSFUL! 🎉                      ║
╚══════════════════════════════════════════════════════════════╝

Your PROJECT-ALPHA executable is ready for deployment!

📁 Location: PROJECT_ALPHA_Distribution/
📋 Documentation: Check USER_GUIDE.txt
🚀 Run: Double-click InventoryTracker.exe or use Start_InventoryTracker.bat

Next Steps:
1. Test the executable on your development machine
2. Test on a clean Windows machine (no Python/dependencies)
3. Deploy to target systems
4. Distribute to end users

The executable includes ALL dependencies and requires no additional software.
""")
        return 0
    else:
        print(f"""
╔══════════════════════════════════════════════════════════════╗
║                    BUILD FAILED ❌                          ║
╚══════════════════════════════════════════════════════════════╝

Please check the build log for detailed error information:
- build_log.txt: Detailed build log
- BUILD_REPORT.txt: Comprehensive build report

Common issues:
1. Missing dependencies (run: pip install -r requirements.txt)
2. PyInstaller not installed (run: pip install pyinstaller)
3. Insufficient disk space
4. Antivirus blocking build process

""")
        return 1

if __name__ == "__main__":
    exit(main()) 