"""Database initialization and connection management with connection pooling."""
import os
import logging
import sqlite3
import threading
import queue
import time
from pathlib import Path
from contextlib import contextmanager

# Configure logger
logger = logging.getLogger('database')

# Dedicated DB debug log file
from config import DB_PATH
DB_DEBUG_LOG = DB_PATH.replace("inventory.db", "db_debug.log")
def db_debug_log(msg):
    # Only log errors and important events, not routine operations
    important_keywords = ['error', 'failed', 'corruption', 'migration', 'backup']
    if any(keyword in msg.lower() for keyword in important_keywords):
        with open(DB_DEBUG_LOG, "a") as f:
            f.write(f"[DB_DEBUG] {msg}\n")

# Global connection data
from config import DB_PATH

# Connection Pool Implementation
class DatabaseConnectionPool:
    """Thread-safe database connection pool for SQLite."""

    def __init__(self, max_connections=10, connection_timeout=30):
        self.max_connections = max_connections
        self.connection_timeout = connection_timeout
        self.pool = queue.Queue(maxsize=max_connections)
        self.active_connections = 0
        self.pool_lock = threading.Lock()
        self.schema_migrated = False
        self._initialize_pool()

    def _initialize_pool(self):
        """Initialize the connection pool with pre-created connections."""
        logger.info(f"Initializing database connection pool with {self.max_connections} connections")
        try:
            # Create initial connections
            for i in range(min(3, self.max_connections)):  # Start with 3 connections
                conn = self._create_connection()
                if conn:
                    self.pool.put(conn, block=False)
                    self.active_connections += 1
            logger.info(f"Connection pool initialized with {self.active_connections} connections")
        except Exception as e:
            logger.error(f"Failed to initialize connection pool: {e}")

    def _create_connection(self):
        """Create a new database connection with optimizations."""
        try:
            # Ensure parent directory exists
            db_dir = Path(DB_PATH).parent
            db_dir.mkdir(parents=True, exist_ok=True)

            conn = sqlite3.connect(DB_PATH, check_same_thread=False, timeout=30.0)
            conn.row_factory = dict_factory

            # Performance optimizations for SQLite
            conn.execute("PRAGMA foreign_keys = ON")
            conn.execute("PRAGMA journal_mode = WAL")
            conn.execute("PRAGMA synchronous = NORMAL")
            conn.execute("PRAGMA cache_size = -32000")  # 32MB cache per connection
            conn.execute("PRAGMA temp_store = MEMORY")
            conn.execute("PRAGMA mmap_size = 134217728")  # 128MB memory-mapped I/O
            conn.execute("PRAGMA busy_timeout = 30000")  # 30 second timeout

            # Run schema migration only once
            if not self.schema_migrated:
                with self.pool_lock:
                    if not self.schema_migrated:
                        migration_result = check_and_update_schema(conn)
                        if migration_result:
                            self.schema_migrated = True
                            logger.info("Database schema migration completed successfully")
                        else:
                            logger.error("Database schema migration failed")

            return conn

        except Exception as e:
            logger.error(f"Failed to create database connection: {e}")
            return None

    def get_connection(self):
        """Get a connection from the pool with timeout."""
        try:
            # Try to get existing connection from pool
            try:
                conn = self.pool.get(block=False)
                # Test connection is still valid
                conn.execute("SELECT 1")
                return conn
            except queue.Empty:
                # Pool is empty, create new connection if under limit
                with self.pool_lock:
                    if self.active_connections < self.max_connections:
                        conn = self._create_connection()
                        if conn:
                            self.active_connections += 1
                            return conn

                # Wait for connection to become available
                try:
                    conn = self.pool.get(timeout=self.connection_timeout)
                    # Test connection is still valid
                    conn.execute("SELECT 1")
                    return conn
                except queue.Empty:
                    raise sqlite3.OperationalError("Connection pool timeout - no connections available")

        except sqlite3.Error as e:
            logger.error(f"Database connection error: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting connection: {e}")
            raise sqlite3.OperationalError(f"Failed to get database connection: {e}")

    def return_connection(self, conn):
        """Return a connection to the pool."""
        if conn:
            try:
                # Test if connection is still valid
                conn.execute("SELECT 1")
                self.pool.put(conn, block=False)
            except (sqlite3.Error, queue.Full):
                # Connection is invalid or pool is full, close it
                try:
                    conn.close()
                except:
                    pass
                with self.pool_lock:
                    self.active_connections = max(0, self.active_connections - 1)

    def close_all(self):
        """Close all connections in the pool."""
        logger.info("Closing all database connections")
        with self.pool_lock:
            while not self.pool.empty():
                try:
                    conn = self.pool.get(block=False)
                    conn.close()
                except:
                    pass
            self.active_connections = 0

# Global connection pool instance
_connection_pool = None
_pool_lock = threading.Lock()

def get_connection_pool():
    """Get the global connection pool instance."""
    global _connection_pool
    if _connection_pool is None:
        with _pool_lock:
            if _connection_pool is None:
                _connection_pool = DatabaseConnectionPool()
    return _connection_pool

@contextmanager
def get_db_connection():
    """Context manager for database connections."""
    pool = get_connection_pool()
    conn = None
    try:
        conn = pool.get_connection()
        yield conn
    finally:
        if conn:
            pool.return_connection(conn)


def dict_factory(cursor, row):
    """Convert row results to dictionary for compatibility with existing code."""
    d = {}
    for idx, col in enumerate(cursor.description):
        d[col[0]] = row[idx]
    return d


import traceback

def handle_db_error(func_name, error):
    """Centralized database error handling with user-friendly messages."""
    if isinstance(error, sqlite3.IntegrityError):
        user_msg = "Data integrity constraint violated. Please check your input and try again."
        logger.error(f"{func_name}: {user_msg} - {str(error)}")
        return user_msg
    elif isinstance(error, sqlite3.OperationalError):
        user_msg = "Database operation failed. Please try again later."
        logger.error(f"{func_name}: {user_msg} - {str(error)}")
        return user_msg
    elif isinstance(error, sqlite3.DatabaseError):
        user_msg = "Database error occurred. Please contact support if this persists."
        logger.error(f"{func_name}: {user_msg} - {str(error)}")
        return user_msg
    else:
        user_msg = f"An unexpected error occurred: {str(error)}"
        logger.error(f"{func_name}: {user_msg}")
        return user_msg

def get_legacy_db_connection():
    """Create and return a new SQLite connection (caller must close). Legacy function for compatibility."""
    try:
        # Ensure parent directory exists
        db_dir = Path(DB_PATH).parent
        db_dir.mkdir(parents=True, exist_ok=True)

        db_debug_log(f"Opening SQLite connection to {DB_PATH}")
        conn = sqlite3.connect(DB_PATH, check_same_thread=False)
        conn.row_factory = dict_factory

        # Performance optimizations for SQLite
        conn.execute("PRAGMA foreign_keys = ON")
        conn.execute("PRAGMA journal_mode = WAL")  # Write-Ahead Logging for better concurrency
        conn.execute("PRAGMA synchronous = NORMAL")  # Faster writes, still safe
        conn.execute("PRAGMA cache_size = -64000")  # 64MB cache for better performance
        conn.execute("PRAGMA temp_store = MEMORY")  # Store temp tables in memory
        conn.execute("PRAGMA mmap_size = 268435456")  # 256MB memory-mapped I/O
        # Always check and update schema on every connection
        from database import check_and_update_schema
        migration_result = check_and_update_schema(conn)
        if not migration_result:
            logger.error("[DB MIGRATION] Database schema migration failed. Some features may not work correctly.")
            db_debug_log("[DB MIGRATION] Database schema migration failed. Some features may not work correctly.")
        return conn
    except sqlite3.DatabaseError as db_err:
        # Handle corruption: backup and recreate
        logger.error(f"Database file may be corrupted: {db_err}\n{traceback.format_exc()}")
        db_debug_log(f"Database file may be corrupted: {db_err}\n{traceback.format_exc()}")
        try:
            if os.path.exists(DB_PATH):
                corrupt_path = DB_PATH + ".corrupt.bak"
                os.rename(DB_PATH, corrupt_path)
                logger.error(f"Backed up corrupt DB to: {corrupt_path}")
                db_debug_log(f"Backed up corrupt DB to: {corrupt_path}")
            # Try to create new DB
            conn = sqlite3.connect(DB_PATH, check_same_thread=False)
            conn.row_factory = dict_factory

            # Performance optimizations for SQLite
            conn.execute("PRAGMA foreign_keys = ON")
            conn.execute("PRAGMA journal_mode = WAL")  # Write-Ahead Logging for better concurrency
            conn.execute("PRAGMA synchronous = NORMAL")  # Faster writes, still safe
            conn.execute("PRAGMA cache_size = -64000")  # 64MB cache for better performance
            conn.execute("PRAGMA temp_store = MEMORY")  # Store temp tables in memory
            conn.execute("PRAGMA mmap_size = 268435456")  # 256MB memory-mapped I/O
            from database import check_and_update_schema
            check_and_update_schema(conn)
            logger.info("Created new blank database after corruption.")
            db_debug_log("Created new blank database after corruption.")
            return conn
        except Exception as e2:
            logger.error(f"Failed to recover from DB corruption: {e2}\n{traceback.format_exc()}")
            db_debug_log(f"Failed to recover from DB corruption: {e2}\n{traceback.format_exc()}")
            return None
    except Exception as e:
        logger.error(f"Error creating SQLite connection: {e}\n{traceback.format_exc()}")
        db_debug_log(f"Error creating SQLite connection: {e}\n{traceback.format_exc()}")
        return None


def close_db_connection(connection):
    """Close the SQLite connection."""
    if connection:
        connection.close()
        logger.debug("Closed SQLite connection")
        db_debug_log("Closed SQLite connection")


def init_connection_pool():
    """Initialize the database connection (no pool needed for SQLite). Returns (success, first_run)."""
    logger.info("Initializing SQLite database")
    try:
        db_dir = Path(DB_PATH).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        first_run = not os.path.exists(DB_PATH)
        connection = get_legacy_db_connection()
        if connection:
            logger.info("SQLite database initialized successfully")
            close_db_connection(connection)
            return True, first_run
        else:
            logger.error("Failed to connect to SQLite database")
            return False, first_run
    except Exception as e:
        import traceback
        logger.error(f"Error initializing SQLite database: {e}\n{traceback.format_exc()}")
        return False, False


def check_and_update_schema(connection):
    """Check and update database schema to ensure all required columns exist."""
    import traceback
    try:
        cursor = connection.cursor()
        db_debug_log("Checking schema for equipment table...")

        # --- Robust, idempotent schema migration for ALL tables ---
        # Table: equipment (full migration) - Check if table exists first
        try:
            cursor.execute("PRAGMA table_info(equipment)")
            columns = cursor.fetchall()
            column_names = [col['name'] for col in columns]
            db_debug_log(f"Current equipment columns: {column_names}")

            equipment_required_columns = [
                ('serial_number', "ALTER TABLE equipment ADD COLUMN serial_number TEXT"),
                ('make_and_type', "ALTER TABLE equipment ADD COLUMN make_and_type TEXT"),
                ('units_held', "ALTER TABLE equipment ADD COLUMN units_held INTEGER DEFAULT 1"),
                ('vintage_years', "ALTER TABLE equipment ADD COLUMN vintage_years REAL DEFAULT 0"),
                ('meterage_kms', "ALTER TABLE equipment ADD COLUMN meterage_kms REAL DEFAULT 0"),
                ('meterage_description', "ALTER TABLE equipment ADD COLUMN meterage_description TEXT"),
                ('km_hrs_run_previous_month', "ALTER TABLE equipment ADD COLUMN km_hrs_run_previous_month REAL DEFAULT 0"),
                ('km_hrs_run_current_month', "ALTER TABLE equipment ADD COLUMN km_hrs_run_current_month REAL DEFAULT 0"),
                ('is_active', "ALTER TABLE equipment ADD COLUMN is_active INTEGER DEFAULT 1"),
                ('remarks', "ALTER TABLE equipment ADD COLUMN remarks TEXT"),
                ('section', "ALTER TABLE equipment ADD COLUMN section TEXT"),
                ('unit', "ALTER TABLE equipment ADD COLUMN unit TEXT"),
                ('location', "ALTER TABLE equipment ADD COLUMN location TEXT"),
                ('last_service_date', "ALTER TABLE equipment ADD COLUMN last_service_date TEXT"),
                ('next_service_date', "ALTER TABLE equipment ADD COLUMN next_service_date TEXT"),
                ('status', "ALTER TABLE equipment ADD COLUMN status TEXT"),
                ('ba_number', "ALTER TABLE equipment ADD COLUMN ba_number TEXT"),
                ('hours_run_total', "ALTER TABLE equipment ADD COLUMN hours_run_total REAL DEFAULT 0"),
                ('hours_run_previous_month', "ALTER TABLE equipment ADD COLUMN hours_run_previous_month REAL DEFAULT 0"),
                ('hours_run_current_month', "ALTER TABLE equipment ADD COLUMN hours_run_current_month REAL DEFAULT 0"),
                ('date_of_induction', "ALTER TABLE equipment ADD COLUMN date_of_induction TEXT"),
                ('date_of_commission', "ALTER TABLE equipment ADD COLUMN date_of_commission TEXT"),
                ('date_of_release', "ALTER TABLE equipment ADD COLUMN date_of_release TEXT"),
                ('equipment_status', "ALTER TABLE equipment ADD COLUMN equipment_status TEXT DEFAULT 'active'"),
                ('mileage', "ALTER TABLE equipment ADD COLUMN mileage TEXT")
            ]

            cursor.execute("PRAGMA table_info(equipment)")
            eq_columns = [col['name'] for col in cursor.fetchall()]
            for col_name, alter_sql in equipment_required_columns:
                if col_name not in eq_columns:
                    logger.info(f"Adding {col_name} column to equipment table")
                    db_debug_log(f"Adding {col_name} column to equipment table")
                    cursor.execute(alter_sql)
                    logger.info(f"{col_name} column added successfully")
                    db_debug_log(f"{col_name} column added successfully")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_equipment_ba_number ON equipment(ba_number)")
        except Exception as e:
            logger.warning(f"Could not check or migrate equipment table (table may not exist yet): {e}")
            db_debug_log(f"Could not check or migrate equipment table (table may not exist yet): {e}")

        # Table: maintenance
        try:
            # Check if maintenance table exists first
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='maintenance'")
            if cursor.fetchone():
                cursor.execute("PRAGMA table_info(maintenance)")
                maint_columns = [col['name'] for col in cursor.fetchall()]
                
                # Handle due_date to next_due_date column rename
                if 'due_date' in maint_columns and 'next_due_date' not in maint_columns:
                    logger.info("Renaming due_date column to next_due_date in maintenance table")
                    db_debug_log("Renaming due_date column to next_due_date in maintenance table")
                    # SQLite doesn't support RENAME COLUMN directly, so we need to recreate the table
                    cursor.execute("""
                        CREATE TABLE maintenance_new (
                            maintenance_id INTEGER PRIMARY KEY AUTOINCREMENT,
                            equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                            maintenance_type TEXT NOT NULL,
                            done_date TEXT,
                            next_due_date TEXT,
                            vintage_years REAL DEFAULT 0,
                            meterage_kms REAL DEFAULT 0,
                            completion_notes TEXT,
                            status TEXT DEFAULT 'scheduled',
                            completed_by TEXT,
                            actual_completion_date TEXT,
                            completion_meterage REAL,
                            maintenance_category TEXT DEFAULT 'TM-1'
                        )
                    """)
                    # Copy data from old table to new table
                    cursor.execute("""
                        INSERT INTO maintenance_new 
                        SELECT maintenance_id, equipment_id, maintenance_type, done_date, due_date as next_due_date,
                               vintage_years, meterage_kms, completion_notes, status, completed_by,
                               actual_completion_date, completion_meterage, maintenance_category
                        FROM maintenance
                    """)
                    # Drop old table and rename new table
                    cursor.execute("DROP TABLE maintenance")
                    cursor.execute("ALTER TABLE maintenance_new RENAME TO maintenance")
                    logger.info("Successfully renamed due_date to next_due_date")
                    db_debug_log("Successfully renamed due_date to next_due_date")
                
                # Now check for other missing columns
                cursor.execute("PRAGMA table_info(maintenance)")
                maint_columns = [col['name'] for col in cursor.fetchall()]
                
                maintenance_required_columns = [
                    ('completion_notes', "ALTER TABLE maintenance ADD COLUMN completion_notes TEXT"),
                    ('status', "ALTER TABLE maintenance ADD COLUMN status TEXT DEFAULT 'scheduled'"),
                    ('completed_by', "ALTER TABLE maintenance ADD COLUMN completed_by TEXT"),
                    ('actual_completion_date', "ALTER TABLE maintenance ADD COLUMN actual_completion_date TEXT"),
                    ('completion_meterage', "ALTER TABLE maintenance ADD COLUMN completion_meterage REAL"),
                    ('maintenance_category', "ALTER TABLE maintenance ADD COLUMN maintenance_category TEXT DEFAULT 'TM-1'"),
                    ('next_due_date', "ALTER TABLE maintenance ADD COLUMN next_due_date TEXT")
                ]
                
                for col_name, alter_sql in maintenance_required_columns:
                    if col_name not in maint_columns:
                        logger.info(f"Adding {col_name} column to maintenance table")
                        db_debug_log(f"Adding {col_name} column to maintenance table")
                        cursor.execute(alter_sql)
                        logger.info(f"{col_name} column added successfully")
                        db_debug_log(f"{col_name} column added successfully")
            else:
                logger.info("Maintenance table does not exist yet - skipping migration")
        except Exception as e:
            logger.warning(f"Could not check or migrate maintenance table (table may not exist yet): {e}")
            db_debug_log(f"Could not check or migrate maintenance table (table may not exist yet): {e}")

        # Table: repairs
        try:
            # Check if repairs table exists first
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='repairs'")
            if cursor.fetchone():
                repairs_required_columns = [
                    ('description', "ALTER TABLE repairs ADD COLUMN description TEXT"),
                    ('repair_date', "ALTER TABLE repairs ADD COLUMN repair_date TEXT")
                ]
                cursor.execute("PRAGMA table_info(repairs)")
                rep_columns = [col['name'] for col in cursor.fetchall()]
                for col_name, alter_sql in repairs_required_columns:
                    if col_name not in rep_columns:
                        logger.info(f"Adding {col_name} column to repairs table")
                        db_debug_log(f"Adding {col_name} column to repairs table")
                        cursor.execute(alter_sql)
                        logger.info(f"{col_name} column added successfully")
                        db_debug_log(f"{col_name} column added successfully")
            else:
                logger.info("Repairs table does not exist yet - skipping migration")
        except Exception as e:
            logger.warning(f"Could not check or migrate repairs table (table may not exist yet): {e}")
            db_debug_log(f"Could not check or migrate repairs table (table may not exist yet): {e}")

        # Table: fluids (full migration)
        try:
            # Check if fluids table exists first
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='fluids'")
            if cursor.fetchone():
                fluids_required_columns = [
                    ('equipment_id', "ALTER TABLE fluids ADD COLUMN equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE"),
                    ('fluid_type', "ALTER TABLE fluids ADD COLUMN fluid_type TEXT NOT NULL"),
                    ('sub_type', "ALTER TABLE fluids ADD COLUMN sub_type TEXT"),
                    ('accounting_unit', "ALTER TABLE fluids ADD COLUMN accounting_unit TEXT DEFAULT 'Ltr'"),
                    ('capacity_ltrs_kg', "ALTER TABLE fluids ADD COLUMN capacity_ltrs_kg REAL DEFAULT 0"),
                    ('addl_10_percent_top_up', "ALTER TABLE fluids ADD COLUMN addl_10_percent_top_up REAL DEFAULT 0"),
                    ('grade', "ALTER TABLE fluids ADD COLUMN grade TEXT"),
                    ('periodicity_km', "ALTER TABLE fluids ADD COLUMN periodicity_km INTEGER DEFAULT 0"),
                    ('periodicity_hrs', "ALTER TABLE fluids ADD COLUMN periodicity_hrs INTEGER DEFAULT 0"),
                    ('periodicity_months', "ALTER TABLE fluids ADD COLUMN periodicity_months INTEGER DEFAULT 0"),
                    ('last_serviced_date', "ALTER TABLE fluids ADD COLUMN last_serviced_date TEXT"),
                    ('last_serviced_meterage', "ALTER TABLE fluids ADD COLUMN last_serviced_meterage REAL DEFAULT 0"),
                    ('top_up_percent', "ALTER TABLE fluids ADD COLUMN top_up_percent REAL DEFAULT NULL"),
                    ('date_of_change', "ALTER TABLE fluids ADD COLUMN date_of_change TEXT")
                ]
                cursor.execute("PRAGMA table_info(fluids)")
                flu_columns = [col['name'] for col in cursor.fetchall()]
                for col_name, alter_sql in fluids_required_columns:
                    if col_name not in flu_columns:
                        logger.info(f"Adding {col_name} column to fluids table")
                        db_debug_log(f"Adding {col_name} column to fluids table")
                        cursor.execute(alter_sql)
                        logger.info(f"{col_name} column added successfully")
                        db_debug_log(f"{col_name} column added successfully")
            else:
                logger.info("Fluids table does not exist yet - skipping migration")
        except Exception as e:
            logger.warning(f"Could not check or migrate fluids table (table may not exist yet): {e}")
            db_debug_log(f"Could not check or migrate fluids table (table may not exist yet): {e}")

        # Table: overhauls (robust migration)
        try:
            cursor.execute("PRAGMA table_info(overhauls)")
            oh_columns = [col['name'] for col in cursor.fetchall()]
            overhauls_required_columns = [
                ('overhaul_type', "ALTER TABLE overhauls ADD COLUMN overhaul_type TEXT"),
                ('overhaul_date', "ALTER TABLE overhauls ADD COLUMN overhaul_date DATE"),
                ('done_date', "ALTER TABLE overhauls ADD COLUMN done_date DATE"),
                ('due_date', "ALTER TABLE overhauls ADD COLUMN due_date DATE"),
                ('status', "ALTER TABLE overhauls ADD COLUMN status TEXT"),
                ('meter_reading', "ALTER TABLE overhauls ADD COLUMN meter_reading REAL"),
                ('hours_reading', "ALTER TABLE overhauls ADD COLUMN hours_reading REAL"),
                ('notes', "ALTER TABLE overhauls ADD COLUMN notes TEXT"),
                ('description', "ALTER TABLE overhauls ADD COLUMN description TEXT"),
                ('created_date', "ALTER TABLE overhauls ADD COLUMN created_date TEXT"),
                ('updated_date', "ALTER TABLE overhauls ADD COLUMN updated_date TEXT"),
                ('completed_by', "ALTER TABLE overhauls ADD COLUMN completed_by TEXT"),
                ('completion_notes', "ALTER TABLE overhauls ADD COLUMN completion_notes TEXT")
            ]
            for col_name, alter_sql in overhauls_required_columns:
                if col_name not in oh_columns:
                    logger.info(f"Adding {col_name} column to overhauls table")
                    db_debug_log(f"Adding {col_name} column to overhauls table")
                    cursor.execute(alter_sql)
                    logger.info(f"{col_name} column added successfully")
                    db_debug_log(f"{col_name} column added successfully")
        except Exception as e:
            logger.warning(f"Could not check or migrate overhauls table: {e}")
            db_debug_log(f"Could not check or migrate overhauls table: {e}")

        # Table: discrepancies
        discrepancies_required_columns = [
            ('fluid_type', "ALTER TABLE discrepancies ADD COLUMN fluid_type TEXT"),
            ('sub_type', "ALTER TABLE discrepancies ADD COLUMN sub_type TEXT"),
            ('difference_ltrs_kg', "ALTER TABLE discrepancies ADD COLUMN difference_ltrs_kg REAL DEFAULT 0"),
            ('notes', "ALTER TABLE discrepancies ADD COLUMN notes TEXT"),
            ('reported_date', "ALTER TABLE discrepancies ADD COLUMN reported_date TEXT DEFAULT (date('now'))"),
            ('resolved', "ALTER TABLE discrepancies ADD COLUMN resolved INTEGER DEFAULT 0")
        ]
        try:
            cursor.execute("PRAGMA table_info(discrepancies)")
            disc_columns = [col['name'] for col in cursor.fetchall()]
            for col_name, alter_sql in discrepancies_required_columns:
                if col_name not in disc_columns:
                    logger.info(f"Adding {col_name} column to discrepancies table")
                    db_debug_log(f"Adding {col_name} column to discrepancies table")
                    cursor.execute(alter_sql)
                    logger.info(f"{col_name} column added successfully")
                    db_debug_log(f"{col_name} column added successfully")
        except Exception as e:
            logger.warning(f"Could not check or migrate discrepancies table: {e}")
            db_debug_log(f"Could not check or migrate discrepancies table: {e}")

        # Table: discard_criteria (equipment-level only, no components)
        try:
            cursor.execute("PRAGMA table_info(discard_criteria)")
            disc_crit_columns_info = cursor.fetchall()
            disc_crit_columns = [col['name'] for col in disc_crit_columns_info]
            
            # Check if component column exists and is NOT NULL
            component_col_info = next((col for col in disc_crit_columns_info if col['name'] == 'component'), None)
            if component_col_info and component_col_info['notnull'] == 1:
                logger.info("Migrating discard_criteria table to make component column nullable")
                db_debug_log("Migrating discard_criteria table to make component column nullable")
                
                # SQLite doesn't support ALTER COLUMN, so we need to recreate the table
                # 1. Create new table with nullable component
                cursor.execute("""
                    CREATE TABLE discard_criteria_new (
                        discard_criteria_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                        component TEXT,
                        criteria_years INTEGER DEFAULT 0,
                        criteria_kms INTEGER DEFAULT 0
                    )
                """)
                
                # 2. Copy data from old table to new table
                cursor.execute("""
                    INSERT INTO discard_criteria_new (discard_criteria_id, equipment_id, component, criteria_years, criteria_kms)
                    SELECT discard_criteria_id, equipment_id, component, criteria_years, criteria_kms
                    FROM discard_criteria
                """)
                
                # 3. Drop old table
                cursor.execute("DROP TABLE discard_criteria")
                
                # 4. Rename new table to old name
                cursor.execute("ALTER TABLE discard_criteria_new RENAME TO discard_criteria")
                
                logger.info("Successfully migrated discard_criteria table")
                db_debug_log("Successfully migrated discard_criteria table")
            
            # Add any missing columns
            discard_required_columns = [
                ('criteria_years', "ALTER TABLE discard_criteria ADD COLUMN criteria_years INTEGER DEFAULT 0"),
                ('criteria_kms', "ALTER TABLE discard_criteria ADD COLUMN criteria_kms INTEGER DEFAULT 0"),
                ('criteria_hours', "ALTER TABLE discard_criteria ADD COLUMN criteria_hours INTEGER DEFAULT 0"),
                ('created_date', "ALTER TABLE discard_criteria ADD COLUMN created_date TEXT"),
                ('created_by', "ALTER TABLE discard_criteria ADD COLUMN created_by TEXT"),
                ('modified_date', "ALTER TABLE discard_criteria ADD COLUMN modified_date TEXT"),
                ('modified_by', "ALTER TABLE discard_criteria ADD COLUMN modified_by TEXT"),
                ('discard_reason', "ALTER TABLE discard_criteria ADD COLUMN discard_reason TEXT"),
                ('discard_date', "ALTER TABLE discard_criteria ADD COLUMN discard_date TEXT"),
                ('notification_sent', "ALTER TABLE discard_criteria ADD COLUMN notification_sent INTEGER DEFAULT 0"),
                ('warning_threshold_reached', "ALTER TABLE discard_criteria ADD COLUMN warning_threshold_reached INTEGER DEFAULT 0")
            ]
            
            # Refresh column list after potential migration
            cursor.execute("PRAGMA table_info(discard_criteria)")
            disc_crit_columns = [col['name'] for col in cursor.fetchall()]
            
            for col_name, alter_sql in discard_required_columns:
                if col_name not in disc_crit_columns:
                    logger.info(f"Adding {col_name} column to discard_criteria table")
                    db_debug_log(f"Adding {col_name} column to discard_criteria table")
                    cursor.execute(alter_sql)
                    logger.info(f"{col_name} column added successfully")
                    db_debug_log(f"{col_name} column added successfully")
        except Exception as e:
            logger.warning(f"Could not check or migrate discard_criteria table: {e}")
            db_debug_log(f"Could not check or migrate discard_criteria table: {e}")

        # Table: tyre_maintenance
        tyre_required_columns = [
            ('tyre_rotation_kms', "ALTER TABLE tyre_maintenance ADD COLUMN tyre_rotation_kms INTEGER DEFAULT 0"),
            ('tyre_condition_kms', "ALTER TABLE tyre_maintenance ADD COLUMN tyre_condition_kms INTEGER DEFAULT 0"),
            ('tyre_condition_years', "ALTER TABLE tyre_maintenance ADD COLUMN tyre_condition_years INTEGER DEFAULT 0"),
            ('last_rotation_date', "ALTER TABLE tyre_maintenance ADD COLUMN last_rotation_date TEXT"),
            ('rotation_due_at', "ALTER TABLE tyre_maintenance ADD COLUMN rotation_due_at REAL"),
            ('date_of_change', "ALTER TABLE tyre_maintenance ADD COLUMN date_of_change TEXT"),
            ('quantity', "ALTER TABLE tyre_maintenance ADD COLUMN quantity INTEGER DEFAULT 0"),
            ('demand_date', "ALTER TABLE tyre_maintenance ADD COLUMN demand_date TEXT")
        ]
        try:
            cursor.execute("PRAGMA table_info(tyre_maintenance)")
            tyre_columns = [col['name'] for col in cursor.fetchall()]
            for col_name, alter_sql in tyre_required_columns:
                if col_name not in tyre_columns:
                    logger.info(f"Adding {col_name} column to tyre_maintenance table")
                    db_debug_log(f"Adding {col_name} column to tyre_maintenance table")
                    cursor.execute(alter_sql)
                    logger.info(f"{col_name} column added successfully")
                    db_debug_log(f"{col_name} column added successfully")
        except Exception as e:
            logger.warning(f"Could not check or migrate tyre_maintenance table: {e}")
            db_debug_log(f"Could not check or migrate tyre_maintenance table: {e}")

        # Table: demand_forecast
        demand_required_columns = [
            ('fluid_id', "ALTER TABLE demand_forecast ADD COLUMN fluid_id INTEGER REFERENCES fluids(fluid_id) ON DELETE CASCADE"),
            ('fiscal_year', "ALTER TABLE demand_forecast ADD COLUMN fiscal_year TEXT NOT NULL"),
            ('total_requirement', "ALTER TABLE demand_forecast ADD COLUMN total_requirement REAL DEFAULT 0"),
            ('remarks', "ALTER TABLE demand_forecast ADD COLUMN remarks TEXT")
        ]
        try:
            cursor.execute("PRAGMA table_info(demand_forecast)")
            demand_columns = [col['name'] for col in cursor.fetchall()]
            for col_name, alter_sql in demand_required_columns:
                if col_name not in demand_columns:
                    logger.info(f"Adding {col_name} column to demand_forecast table")
                    db_debug_log(f"Adding {col_name} column to demand_forecast table")
                    cursor.execute(alter_sql)
                    logger.info(f"{col_name} column added successfully")
                    db_debug_log(f"{col_name} column added successfully")
        except Exception as e:
            logger.warning(f"Could not check or migrate demand_forecast table: {e}")
            db_debug_log(f"Could not check or migrate demand_forecast table: {e}")

        # Table: conditioning (full migration)
        conditioning_required_columns = [
            ('conditioning_id', "ALTER TABLE conditioning ADD COLUMN conditioning_id INTEGER PRIMARY KEY AUTOINCREMENT"),
            ('equipment_id', "ALTER TABLE conditioning ADD COLUMN equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE"),
            ('fiscal_year', "ALTER TABLE conditioning ADD COLUMN fiscal_year TEXT NOT NULL"),
            ('conditioning_type', "ALTER TABLE conditioning ADD COLUMN conditioning_type TEXT NOT NULL"),
            ('total_requirement', "ALTER TABLE conditioning ADD COLUMN total_requirement REAL DEFAULT 0"),
            ('remarks', "ALTER TABLE conditioning ADD COLUMN remarks TEXT")
        ]
        try:
            cursor.execute("PRAGMA table_info(conditioning)")
            cond_columns = [col['name'] for col in cursor.fetchall()]
            for col_name, alter_sql in conditioning_required_columns:
                if col_name not in cond_columns:
                    logger.info(f"Adding {col_name} column to conditioning table")
                    db_debug_log(f"Adding {col_name} column to conditioning table")
                    cursor.execute(alter_sql)
                    logger.info(f"{col_name} column added successfully")
                    db_debug_log(f"{col_name} column added successfully")
        except Exception as e:
            logger.warning(f"Could not check or migrate conditioning table: {e}")
            db_debug_log(f"Could not check or migrate conditioning table: {e}")

        # Table: settings
        settings_required_columns = [
            ('settings_id', "ALTER TABLE settings ADD COLUMN settings_id INTEGER PRIMARY KEY AUTOINCREMENT"),
            ('key', "ALTER TABLE settings ADD COLUMN key TEXT UNIQUE NOT NULL"),
            ('value', "ALTER TABLE settings ADD COLUMN value TEXT"),
            ('description', "ALTER TABLE settings ADD COLUMN description TEXT"),
            ('created_at', "ALTER TABLE settings ADD COLUMN created_at TEXT DEFAULT (datetime('now'))"),
            ('updated_at', "ALTER TABLE settings ADD COLUMN updated_at TEXT DEFAULT (datetime('now'))")
        ]
        try:
            cursor.execute("PRAGMA table_info(settings)")
            settings_columns = [col['name'] for col in cursor.fetchall()]
            for col_name, alter_sql in settings_required_columns:
                if col_name not in settings_columns:
                    logger.info(f"Adding {col_name} column to settings table")
                    db_debug_log(f"Adding {col_name} column to settings table")
                    cursor.execute(alter_sql)
                    logger.info(f"{col_name} column added successfully")
                    db_debug_log(f"{col_name} column added successfully")
        except Exception as e:
            logger.warning(f"Could not check or migrate settings table: {e}")
            db_debug_log(f"Could not check or migrate settings table: {e}")

        # Table: maintenance_archives (critical migration for created_date column)
        maintenance_archives_required_columns = [
            ('archive_name', "ALTER TABLE maintenance_archives ADD COLUMN archive_name TEXT"),
            ('archive_type', "ALTER TABLE maintenance_archives ADD COLUMN archive_type TEXT"),
            ('period_start', "ALTER TABLE maintenance_archives ADD COLUMN period_start TEXT"),
            ('period_end', "ALTER TABLE maintenance_archives ADD COLUMN period_end TEXT"),
            ('created_date', "ALTER TABLE maintenance_archives ADD COLUMN created_date TEXT DEFAULT (datetime('now'))"),
            ('created_by', "ALTER TABLE maintenance_archives ADD COLUMN created_by TEXT"),
            ('record_count', "ALTER TABLE maintenance_archives ADD COLUMN record_count INTEGER DEFAULT 0"),
            ('archive_data', "ALTER TABLE maintenance_archives ADD COLUMN archive_data TEXT"),
            ('notes', "ALTER TABLE maintenance_archives ADD COLUMN notes TEXT")
        ]
        try:
            cursor.execute("PRAGMA table_info(maintenance_archives)")
            ma_columns = [col['name'] for col in cursor.fetchall()]
            for col_name, alter_sql in maintenance_archives_required_columns:
                if col_name not in ma_columns:
                    logger.info(f"Adding {col_name} column to maintenance_archives table")
                    db_debug_log(f"Adding {col_name} column to maintenance_archives table")
                    cursor.execute(alter_sql)
                    logger.info(f"{col_name} column added successfully")
                    db_debug_log(f"{col_name} column added successfully")
        except Exception as e:
            logger.warning(f"Could not check or migrate maintenance_archives table: {e}")
            db_debug_log(f"Could not check or migrate maintenance_archives table: {e}")

        # Table: battery (critical migration - recreate table with correct schema)
        try:
            cursor.execute("PRAGMA table_info(battery)")
            battery_columns_info = cursor.fetchall()
            battery_columns = [col['name'] for col in battery_columns_info]

            # Check if the table has the old schema (with battery_type column)
            has_old_schema = 'battery_type' in battery_columns
            has_new_schema = 'done_date' in battery_columns and 'custom_life_months' in battery_columns

            # If we have old schema columns (even if new ones exist), we need to migrate
            # because the old columns may have NOT NULL constraints that conflict with new usage
            if has_old_schema:
                db_debug_log("Migrating battery table from old/hybrid schema to new schema")

                # Backup existing data if any
                cursor.execute("SELECT COUNT(*) as count FROM battery")
                count_result = cursor.fetchone()
                record_count = count_result['count'] if count_result else 0

                if record_count > 0:
                    db_debug_log(f"Backing up {record_count} battery records before migration")
                    cursor.execute("CREATE TABLE battery_backup AS SELECT * FROM battery")

                # Drop old table and create new one with clean schema
                cursor.execute("DROP TABLE battery")
                cursor.execute("""
                    CREATE TABLE battery (
                        battery_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                        done_date TEXT,
                        custom_life_months INTEGER,
                        demand_date TEXT
                    )
                """)

                db_debug_log("Successfully migrated battery table to new schema")
            else:
                # Add demand_date column to existing table if it doesn't exist
                if 'demand_date' not in battery_columns:
                    logger.info("Adding demand_date column to battery table")
                    db_debug_log("Adding demand_date column to battery table")
                    cursor.execute("ALTER TABLE battery ADD COLUMN demand_date TEXT")
                    logger.info("demand_date column added successfully")
                    db_debug_log("demand_date column added successfully")
        except Exception as e:
            logger.warning(f"Could not check or migrate battery table: {e}")
            db_debug_log(f"Could not check or migrate battery table: {e}")

        # Table: battery_forecast - Add ampere_hours column
        try:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='battery_forecast'")
            if cursor.fetchone():
                cursor.execute("PRAGMA table_info(battery_forecast)")
                bf_columns = [col['name'] for col in cursor.fetchall()]
                battery_forecast_required_columns = [
                    ('ampere_hours', "ALTER TABLE battery_forecast ADD COLUMN ampere_hours INTEGER DEFAULT 0")
                ]
                for col_name, alter_sql in battery_forecast_required_columns:
                    if col_name not in bf_columns:
                        logger.info(f"Adding {col_name} column to battery_forecast table")
                        db_debug_log(f"Adding {col_name} column to battery_forecast table")
                        cursor.execute(alter_sql)
                        logger.info(f"{col_name} column added successfully")
                        db_debug_log(f"{col_name} column added successfully")
            else:
                logger.info("Battery forecast table does not exist yet - skipping migration")
        except Exception as e:
            logger.warning(f"Could not check or migrate battery_forecast table: {e}")
            db_debug_log(f"Could not check or migrate battery_forecast table: {e}")

        # Table: conditioning_history - Enhanced conditioning tracking
        try:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='conditioning_history'")
            if cursor.fetchone():
                # Table exists, check for missing columns and handle schema changes
                cursor.execute("PRAGMA table_info(conditioning_history)")
                ch_columns = [col['name'] for col in cursor.fetchall()]
                
                # Check if table has the new schema already
                has_new_schema = 'action_kms' in ch_columns and 'action_time' in ch_columns
                has_old_schema = 'meterage_at_action' in ch_columns or 'cost' in ch_columns or 'workshop' in ch_columns
                
                if has_old_schema and not has_new_schema:
                    # Need to migrate from old schema to new schema
                    logger.info("Migrating conditioning_history table to new simplified schema")
                    db_debug_log("Migrating conditioning_history table to new simplified schema")
                    
                    # Get existing data first
                    cursor.execute("SELECT * FROM conditioning_history")
                    existing_data = cursor.fetchall()
                    
                    # Drop old table
                    cursor.execute("DROP TABLE conditioning_history")
                    
                    # Create new table with updated schema
                    cursor.execute("""
                        CREATE TABLE conditioning_history (
                            history_id INTEGER PRIMARY KEY AUTOINCREMENT,
                            equipment_id INTEGER NOT NULL,
                            action_type TEXT NOT NULL,
                            action_date TEXT NOT NULL,
                            action_kms REAL DEFAULT 0.0,
                            action_time TEXT,
                            vintage_at_action REAL DEFAULT 0.0,
                            performed_by TEXT,
                            notes TEXT,
                            old_rotation_date TEXT,
                            new_rotation_date TEXT,
                            interval_reset BOOLEAN DEFAULT FALSE,
                            created_at TEXT NOT NULL DEFAULT (datetime('now')),
                            updated_at TEXT NOT NULL DEFAULT (datetime('now')),
                            FOREIGN KEY (equipment_id) REFERENCES equipment (equipment_id) ON DELETE CASCADE
                        )
                    """)
                    
                    # Migrate existing data if any
                    if existing_data:
                        logger.info(f"Migrating {len(existing_data)} existing records")
                        for row in existing_data:
                            try:
                                # Map old columns to new columns (adjust indices as needed)
                                cursor.execute("""
                                    INSERT INTO conditioning_history (
                                        equipment_id, action_type, action_date, action_kms, action_time,
                                        vintage_at_action, performed_by, notes, old_rotation_date,
                                        new_rotation_date, interval_reset, created_at, updated_at
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
                                """, (
                                    row[1] if len(row) > 1 else 1,  # equipment_id
                                    row[2] if len(row) > 2 else 'inspection',  # action_type
                                    row[3] if len(row) > 3 else None,  # action_date
                                    row[4] if len(row) > 4 else 0.0,  # action_kms (was meterage_at_action)
                                    (row[3] + ' 12:00:00') if len(row) > 3 and row[3] else None,  # action_time
                                    row[5] if len(row) > 5 else 0.0,  # vintage_at_action
                                    row[6] if len(row) > 6 else None,  # performed_by
                                    row[7] if len(row) > 7 else None,  # notes
                                    row[8] if len(row) > 8 else None,  # old_rotation_date
                                    row[9] if len(row) > 9 else None,  # new_rotation_date
                                    row[10] if len(row) > 10 else False,  # interval_reset
                                ))
                            except Exception as migrate_error:
                                logger.warning(f"Could not migrate record {row}: {migrate_error}")
                        logger.info("Data migration completed")
                    
                    logger.info("Successfully migrated conditioning_history to new schema")
                    db_debug_log("Successfully migrated conditioning_history to new schema")
                    
                elif not has_new_schema:
                    # Table exists but missing new columns - add them
                    conditioning_history_required_columns = [
                        ('action_kms', "ALTER TABLE conditioning_history ADD COLUMN action_kms REAL DEFAULT 0.0"),
                        ('action_time', "ALTER TABLE conditioning_history ADD COLUMN action_time TEXT"),
                        ('action_type', "ALTER TABLE conditioning_history ADD COLUMN action_type TEXT NOT NULL DEFAULT 'inspection'"),
                        ('action_date', "ALTER TABLE conditioning_history ADD COLUMN action_date TEXT NOT NULL DEFAULT (date('now'))"),
                        ('vintage_at_action', "ALTER TABLE conditioning_history ADD COLUMN vintage_at_action REAL DEFAULT 0.0"),
                        ('performed_by', "ALTER TABLE conditioning_history ADD COLUMN performed_by TEXT"),
                        ('notes', "ALTER TABLE conditioning_history ADD COLUMN notes TEXT"),
                        ('old_rotation_date', "ALTER TABLE conditioning_history ADD COLUMN old_rotation_date TEXT"),
                        ('new_rotation_date', "ALTER TABLE conditioning_history ADD COLUMN new_rotation_date TEXT"),
                        ('interval_reset', "ALTER TABLE conditioning_history ADD COLUMN interval_reset BOOLEAN DEFAULT FALSE"),
                        ('created_at', "ALTER TABLE conditioning_history ADD COLUMN created_at TEXT NOT NULL DEFAULT (datetime('now'))"),
                        ('updated_at', "ALTER TABLE conditioning_history ADD COLUMN updated_at TEXT NOT NULL DEFAULT (datetime('now'))")
                    ]
                    
                    for col_name, alter_sql in conditioning_history_required_columns:
                        if col_name not in ch_columns:
                            logger.info(f"Adding {col_name} column to conditioning_history table")
                            db_debug_log(f"Adding {col_name} column to conditioning_history table")
                            cursor.execute(alter_sql)
                            logger.info(f"{col_name} column added successfully")
                            db_debug_log(f"{col_name} column added successfully")
                else:
                    # Schema is already correct
                    logger.info("conditioning_history table schema is already up to date")
                    db_debug_log("conditioning_history table schema is already up to date")
            else:
                # Table doesn't exist, create it with new schema
                logger.info("Creating conditioning_history table with new schema")
                db_debug_log("Creating conditioning_history table with new schema")
                cursor.execute("""
                    CREATE TABLE conditioning_history (
                        history_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        equipment_id INTEGER NOT NULL,
                        action_type TEXT NOT NULL,
                        action_date TEXT NOT NULL,
                        action_kms REAL DEFAULT 0.0,
                        action_time TEXT,
                        vintage_at_action REAL DEFAULT 0.0,
                        performed_by TEXT,
                        notes TEXT,
                        old_rotation_date TEXT,
                        new_rotation_date TEXT,
                        interval_reset BOOLEAN DEFAULT FALSE,
                        created_at TEXT NOT NULL DEFAULT (datetime('now')),
                        updated_at TEXT NOT NULL DEFAULT (datetime('now')),
                        FOREIGN KEY (equipment_id) REFERENCES equipment (equipment_id) ON DELETE CASCADE
                    )
                """)
                logger.info("conditioning_history table created successfully with new schema")
                db_debug_log("conditioning_history table created successfully with new schema")
        except Exception as e:
            logger.warning(f"Could not check or migrate conditioning_history table: {e}")
            db_debug_log(f"Could not check or migrate conditioning_history table: {e}")

        # Table: battery_history - Add battery inspection history
        try:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='battery_history'")
            if cursor.fetchone():
                cursor.execute("PRAGMA table_info(battery_history)")
                bh_columns = [col['name'] for col in cursor.fetchall()]
                battery_history_required_columns = [
                    ('history_id', "ALTER TABLE battery_history ADD COLUMN history_id INTEGER PRIMARY KEY AUTOINCREMENT"),
                    ('equipment_id', "ALTER TABLE battery_history ADD COLUMN equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE"),
                    ('inspection_date', "ALTER TABLE battery_history ADD COLUMN inspection_date TEXT NOT NULL"),
                    ('inspection_time', "ALTER TABLE battery_history ADD COLUMN inspection_time TEXT"),
                    ('change_at_age', "ALTER TABLE battery_history ADD COLUMN change_at_age INTEGER"),
                    ('details', "ALTER TABLE battery_history ADD COLUMN details TEXT"),
                    ('source', "ALTER TABLE battery_history ADD COLUMN source TEXT DEFAULT 'Manual'"),
                    ('created_at', "ALTER TABLE battery_history ADD COLUMN created_at TEXT DEFAULT (datetime('now'))"),
                    ('updated_at', "ALTER TABLE battery_history ADD COLUMN updated_at TEXT DEFAULT (datetime('now'))")
                ]
                for col_name, alter_sql in battery_history_required_columns:
                    if col_name not in bh_columns:
                        logger.info(f"Adding {col_name} column to battery_history table")
                        db_debug_log(f"Adding {col_name} column to battery_history table")
                        cursor.execute(alter_sql)
                        logger.info(f"{col_name} column added successfully")
                        db_debug_log(f"{col_name} column added successfully")
            else:
                logger.info("Battery history table does not exist yet - skipping migration")
        except Exception as e:
            logger.warning(f"Could not check or migrate battery_history table: {e}")
            db_debug_log(f"Could not check or migrate battery_history table: {e}")

        connection.commit()
        return True
    except Exception as e:
        logger.error(f"Error updating schema: {e}\n{traceback.format_exc()}")
        db_debug_log(f"Error updating schema: {e}\n{traceback.format_exc()}")
        connection.rollback()
        return False

def init_db():
    """Initialize the database with the tables if they don't exist. Returns (success, first_run)"""
    import traceback
    first_run = False
    # Ensure directory exists
    db_dir = Path(DB_PATH).parent
    db_dir.mkdir(parents=True, exist_ok=True)
    # Check if DB file exists before
    if not os.path.exists(DB_PATH):
        first_run = True

    try:
        with get_db_connection() as connection:
            cursor = connection.cursor()
            # Equipment Table
            cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS equipment (
                equipment_id INTEGER PRIMARY KEY AUTOINCREMENT,
                serial_number TEXT,
                make_and_type TEXT NOT NULL,
                units_held INTEGER DEFAULT 1,
                vintage_years REAL DEFAULT 0,
                meterage_kms REAL DEFAULT 0,
                meterage_description TEXT,
                km_hrs_run_previous_month REAL DEFAULT 0,
                km_hrs_run_current_month REAL DEFAULT 0,
                is_active INTEGER DEFAULT 1,
                remarks TEXT,
                section TEXT,
                unit TEXT,
                location TEXT,
                last_service_date TEXT,
                next_service_date TEXT,
                status TEXT,
                ba_number TEXT,
                hours_run_total REAL DEFAULT 0,
                hours_run_previous_month REAL DEFAULT 0,
                hours_run_current_month REAL DEFAULT 0,
                srtr_ltr_hr REAL DEFAULT 0,
                rd_towing_ltr_100km REAL DEFAULT 0
            )
            """
            )
            cursor.execute(
                """
                CREATE INDEX IF NOT EXISTS idx_equipment_ba_number ON equipment(ba_number)
                """
            )
            # Fluids Table
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS fluids (
                    fluid_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                    fluid_type TEXT NOT NULL,
                    sub_type TEXT,
                    accounting_unit TEXT DEFAULT 'Ltr',
                    capacity_ltrs_kg REAL DEFAULT 0,
                    addl_10_percent_top_up REAL DEFAULT 0,
                    grade TEXT,
                    periodicity_km INTEGER DEFAULT 0,
                    periodicity_hrs INTEGER DEFAULT 0,
                    periodicity_months INTEGER DEFAULT 0,
                    last_serviced_date TEXT,
                    last_serviced_meterage REAL DEFAULT 0,
                    date_of_change TEXT
                )
            """)

            # Create maintenance table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS maintenance (
                    maintenance_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                    maintenance_type TEXT NOT NULL,
                    done_date TEXT,
                    next_due_date TEXT,
                    vintage_years REAL DEFAULT 0,
                    meterage_kms REAL DEFAULT 0,
                    completion_notes TEXT,
                    status TEXT DEFAULT 'scheduled',
                    completed_by TEXT,
                    actual_completion_date TEXT,
                    completion_meterage REAL,
                    maintenance_category TEXT DEFAULT 'TM-1'
                )
            """)

            # Create repairs table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS repairs (
                    repair_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                    repair_type TEXT NOT NULL,
                    description TEXT,
                    repair_date TEXT
                )
            """)

            # Create discrepancies table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS discrepancies (
                    discrepancy_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                    fluid_type TEXT NOT NULL,
                    sub_type TEXT,
                    difference_ltrs_kg REAL DEFAULT 0,
                    notes TEXT,
                    reported_date TEXT DEFAULT (date('now')),
                    resolved INTEGER DEFAULT 0
                )
            """)

            # Create discard_criteria table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS discard_criteria (
                    discard_criteria_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                    component TEXT NOT NULL,
                    criteria_years INTEGER DEFAULT 0,
                    criteria_kms INTEGER DEFAULT 0
                )
            """)

            # Create tyre_maintenance table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS tyre_maintenance (
                    tyre_maintenance_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                    tyre_rotation_kms INTEGER DEFAULT 0,
                    tyre_condition_kms INTEGER DEFAULT 0,
                    tyre_condition_years INTEGER DEFAULT 0,
                    last_rotation_date TEXT,
                    rotation_due_at REAL,
                    date_of_change TEXT,
                    quantity INTEGER DEFAULT 0,
                    demand_date TEXT
                )
            """)

            # Create demand_forecast table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS demand_forecast (
                    demand_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    fluid_id INTEGER REFERENCES fluids(fluid_id) ON DELETE CASCADE,
                    fiscal_year TEXT NOT NULL,
                    total_requirement REAL DEFAULT 0,
                    remarks TEXT
                )
            """)

            # Create tyre_forecast table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS tyre_forecast (
                    forecast_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                    fiscal_year TEXT NOT NULL,
                    tyre_type TEXT NOT NULL,
                    quantity_required INTEGER DEFAULT 0,
                    total_requirement REAL DEFAULT 0,
                    remarks TEXT,
                    created_at TEXT DEFAULT (datetime('now')),
                    updated_at TEXT DEFAULT (datetime('now'))
                )
            """)

            # Create battery_forecast table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS battery_forecast (
                    forecast_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                    fiscal_year TEXT NOT NULL,
                    battery_type TEXT NOT NULL,
                    voltage REAL DEFAULT 0,
                    ampere_hours INTEGER DEFAULT 0,
                    quantity_required INTEGER DEFAULT 0,
                    total_requirement REAL DEFAULT 0,
                    remarks TEXT,
                    created_at TEXT DEFAULT (datetime('now')),
                    updated_at TEXT DEFAULT (datetime('now'))
                )
            """)

            # Create equipment_forecast table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS equipment_forecast (
                    forecast_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                    fiscal_year TEXT NOT NULL,
                    equipment_type TEXT NOT NULL,
                    replacement_reason TEXT,
                    quantity_required INTEGER DEFAULT 1,
                    total_requirement REAL DEFAULT 0,
                    remarks TEXT,
                    created_at TEXT DEFAULT (datetime('now')),
                    updated_at TEXT DEFAULT (datetime('now'))
                )
            """)

            # Create conditioning table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS conditioning (
                    conditioning_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                    fiscal_year TEXT NOT NULL,
                    conditioning_type TEXT NOT NULL,
                    total_requirement REAL DEFAULT 0,
                    remarks TEXT
                )
            """)

            # Create medium_resets table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS medium_resets (
                    medium_reset_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                    reset_type TEXT NOT NULL,
                    done_date TEXT,
                    due_date TEXT,
                    mr1_due_date TEXT,
                    oh1_due_date TEXT,
                    mr2_due_date TEXT,
                    oh2_due_date TEXT,
                    discard_due_date TEXT,
                    status TEXT DEFAULT 'scheduled',
                    meter_reading INTEGER DEFAULT 0,
                    hours_reading INTEGER DEFAULT 0,
                    description TEXT,
                    notes TEXT
                )
            """)

            # Create overhauls table with comprehensive schema
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS overhauls (
                    overhaul_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                    overhaul_type TEXT NOT NULL CHECK (overhaul_type IN ('OH-I', 'OH-II')),
                    overhaul_date TEXT,  -- Legacy field for compatibility
                    done_date TEXT,
                    due_date TEXT,
                    status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'completed', 'overdue', 'warning', 'critical', 'discard', 'unknown')),
                    meter_reading REAL DEFAULT 0,
                    hours_reading REAL DEFAULT 0,
                    notes TEXT,
                    description TEXT,
                    created_date TEXT DEFAULT (datetime('now')),
                    updated_date TEXT DEFAULT (datetime('now')),
                    completed_by TEXT,
                    completion_notes TEXT
                )
            """)

            # Create battery table (updated schema to match models.py)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS battery (
                    battery_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                    done_date TEXT,
                    custom_life_months INTEGER,
                    demand_date TEXT
                )
            """)

            # Create battery_history table for tracking battery inspection history
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS battery_history (
                    history_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_id INTEGER REFERENCES equipment(equipment_id) ON DELETE CASCADE,
                    inspection_date TEXT NOT NULL,
                    inspection_time TEXT,
                    change_at_age INTEGER,
                    details TEXT,
                    source TEXT DEFAULT 'Manual',
                    created_at TEXT DEFAULT (datetime('now')),
                    updated_at TEXT DEFAULT (datetime('now'))
                )
            """)

            # Create maintenance_archives table (updated schema to match models.py)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS maintenance_archives (
                    archive_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    archive_name TEXT NOT NULL,
                    archive_type TEXT NOT NULL,
                    maintenance_category TEXT NOT NULL,
                    period_start TEXT NOT NULL,
                    period_end TEXT NOT NULL,
                    created_date TEXT DEFAULT (datetime('now')),
                    created_by TEXT,
                    record_count INTEGER DEFAULT 0,
                    archive_data TEXT,
                    notes TEXT
                )
            """)

            # Create settings table for application settings
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS settings (
                    settings_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE NOT NULL,
                    value TEXT,
                    description TEXT,
                    created_at TEXT DEFAULT (datetime('now')),
                    updated_at TEXT DEFAULT (datetime('now'))
                )
            """)

            # Create configuration table for app settings
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS app_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_key TEXT UNIQUE NOT NULL,
                    config_value TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Insert default configuration values if they don't exist
            cursor.execute('''
                INSERT OR IGNORE INTO app_config (config_key, config_value) 
                VALUES ('policy_docs_path', '')
            ''')

            # Commit changes
            connection.commit()
            logger.info("Database tables initialized successfully")

            # Check and update schema for any missing columns
            if not check_and_update_schema(connection):
                logger.error("Failed to update database schema")
                return False, first_run

            return True, first_run

    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False, first_run


def execute_query(query, params=(), fetchall=True):
    """Execute a SQL query and return results using connection pool.

    Args:
        query (str): The SQL query to execute.
        params (tuple): Parameters for the query.
        fetchall (bool): If True, fetch all rows; if False, fetch one row.

    Returns:
        Results of the query (list of dicts if fetchall=True, single dict if fetchall=False, rowcount for updates).
    """
    start_time = time.time()

    try:
        with get_db_connection() as connection:
            cursor = connection.cursor()

            # Log query for debugging (only for non-routine operations)
            if any(keyword in query.upper() for keyword in ['CREATE', 'ALTER', 'DROP', 'INSERT', 'UPDATE', 'DELETE']):
                logger.debug(f"Executing query: {query[:100]}...")
                db_debug_log(f"Executing query: {query} | Params: {params}")

            # Convert query for SQLite compatibility
            query = query.replace("%s", "?")

            cursor.execute(query, params)

            # Check if this is an INSERT/UPDATE/DELETE statement
            is_dml = query.strip().upper().startswith(('INSERT', 'UPDATE', 'DELETE'))
            has_returning = 'RETURNING' in query.upper()

            if is_dml:
                if has_returning:
                    row = cursor.fetchone()
                    result = dict(row) if row else None
                    connection.commit()
                    logger.debug("Transaction committed for DML RETURNING operation")
                    return result
                else:
                    affected = cursor.rowcount
                    connection.commit()
                    logger.debug("Transaction committed for DML operation")
                    return affected

            # For SELECT statements
            if fetchall:
                rows = cursor.fetchall()
                result = [dict(row) for row in rows]
            else:
                row = cursor.fetchone()
                result = dict(row) if row else None

            elapsed_time = time.time() - start_time
            if elapsed_time > 1.0:  # Only log slow queries
                logger.warning(f"Slow query completed in {elapsed_time:.3f} seconds. Rows returned: {len(result) if isinstance(result, list) else 1 if result else 0}")

            return result

    except sqlite3.Error as e:
        logger.error(f"Database error: {e}")
        logger.error(f"Query: {query}")
        logger.error(f"Params: {params}")
        return None

    except Exception as e:
        logger.error(f"Unexpected error executing query: {e}")
        logger.error(f"Query: {query}")
        logger.error(f"Params: {params}")
        return None


def execute_transaction(operations):
    """Execute multiple operations in a single transaction with rollback protection."""
    try:
        with get_db_connection() as connection:
            cursor = connection.cursor()
            results = []

            # Begin transaction
            cursor.execute("BEGIN TRANSACTION")

            try:
                for operation in operations:
                    query = operation.get('query')
                    params = operation.get('params', ())
                    fetchall = operation.get('fetchall', True)

                    # Convert query for SQLite compatibility
                    query = query.replace("%s", "?")
                    cursor.execute(query, params)

                    # Determine if this is a data modification query
                    is_dml = query.strip().upper().startswith(('INSERT', 'UPDATE', 'DELETE'))

                    if is_dml:
                        results.append(cursor.rowcount)
                    else:
                        if fetchall:
                            results.append(cursor.fetchall())
                        else:
                            results.append(cursor.fetchone())

                # Commit all operations
                connection.commit()
                logger.debug(f"Transaction completed successfully with {len(operations)} operations")
                return results

            except Exception as e:
                # Rollback on any error
                connection.rollback()
                logger.error(f"Transaction failed, rolled back: {e}")
                raise

    except sqlite3.Error as e:
        logger.error(f"Database transaction error: {e}")
        return None

    except Exception as e:
        logger.error(f"Unexpected transaction error: {e}")
        return None


def execute_insert(query, params=None):
    """Execute an insert query and return the inserted ID using connection pool."""
    if params is None:
        params = ()

    try:
        with get_db_connection() as connection:
            cursor = connection.cursor()
            logger.debug(f"Executing insert query: {query[:100]}...")

            # Convert query for SQLite compatibility
            query = query.replace("%s", "?")
            has_returning = 'RETURNING' in query.upper()
            cursor.execute(query, params)

            if has_returning:
                row = cursor.fetchone()
                inserted_id = dict(row).get('equipment_id') if row else None
                connection.commit()
                logger.debug("Insert committed (RETURNING)")
                return inserted_id
            else:
                connection.commit()
                cursor.execute("SELECT last_insert_rowid()")
                inserted_id = cursor.fetchone()["last_insert_rowid()"]
                logger.debug("Insert committed, id fetched via last_insert_rowid()")
                return inserted_id

    except sqlite3.Error as e:
        logger.error(f"Database insert error: {e}")
        logger.error(f"Query: {query}")
        logger.error(f"Params: {params}")
        return None

    except Exception as e:
        logger.error(f"Unexpected insert error: {e}")
        return None


def execute_update(query, params=None):
    """Execute an update query and return the number of affected rows using connection pool."""
    if params is None:
        params = ()

    try:
        with get_db_connection() as connection:
            cursor = connection.cursor()
            logger.debug(f"Executing update query: {query[:100]}...")

            # Convert query for SQLite compatibility
            query = query.replace("%s", "?")
            has_returning = 'RETURNING' in query.upper()
            cursor.execute(query, params)

            if has_returning:
                row = cursor.fetchone()
                result = dict(row) if row else None
                connection.commit()
                logger.debug("Update committed (RETURNING)")
                return result
            else:
                affected = cursor.rowcount
                connection.commit()
                logger.debug("Update committed")
                return affected

    except sqlite3.Error as e:
        logger.error(f"Database update error: {e}")
        logger.error(f"Query: {query}")
        logger.error(f"Params: {params}")
        return None

    except Exception as e:
        logger.error(f"Unexpected update error: {e}")
        return None


def cleanup_connection_pool():
    """Clean up the connection pool on application shutdown."""
    global _connection_pool
    if _connection_pool:
        _connection_pool.close_all()
        _connection_pool = None
        logger.info("Database connection pool cleaned up")

def get_config_value(config_key, default_value=None):
    """
    Get a configuration value from the database.
    
    Args:
        config_key (str): The configuration key
        default_value: Default value if key not found
        
    Returns:
        str: The configuration value or default
    """
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT config_value FROM app_config WHERE config_key = ?', (config_key,))
            result = cursor.fetchone()
            
            if result and result['config_value']:
                return result['config_value']
            return default_value
        
    except Exception as e:
        logger.error(f"Error getting config value {config_key}: {e}")
        return default_value

def set_config_value(config_key, config_value):
    """
    Set a configuration value in the database.
    
    Args:
        config_key (str): The configuration key
        config_value (str): The configuration value
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO app_config (config_key, config_value, updated_at) 
                VALUES (?, ?, CURRENT_TIMESTAMP)
            ''', (config_key, config_value))
            
            conn.commit()
            return True
        
    except Exception as e:
        logger.error(f"Error setting config value {config_key}: {e}")
        return False