"""
Enhanced Overhaul Tab with comprehensive CRUD functionality
Optimized for military deployment on 1366x768 displays.
"""

import logging
from datetime import datetime, date, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
    QDateEdit, QTextEdit, QCheckBox, QGroupBox, QSplitter, QFrame,
    QMessageBox, QTabWidget, QFileDialog, QProgressBar
)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from ui.responsive_crud_framework import ResponsiveCRUDWidget
from ui.crud_dialogs import ResponsiveFormDialog, BulkOperationDialog, HistoryViewDialog
from ui.window_utils import <PERSON><PERSON><PERSON><PERSON><PERSON>, FormManager, LayoutManager
from ui.custom_widgets import StatusLabel
from models import Overhaul, Equipment, OverhaulHistory
import utils
import database

logger = logging.getLogger('enhanced_overhaul_tab')

class EnhancedOverhaulTab(QWidget):
    """Enhanced overhaul tab with comprehensive CRUD functionality."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the main UI with OH-I and OH-II tabs."""
        main_layout = QVBoxLayout(self)
        LayoutManager.setup_responsive_layout(main_layout, margins=(5, 5, 5, 5), spacing=8)
        
        # Header with title and global actions
        header_layout = self.create_header_layout()
        main_layout.addLayout(header_layout)
        
        # Tab widget for OH-I and OH-II
        self.tab_widget = QTabWidget()
        
        # OH-I tab
        self.oh1_widget = OverhaulCRUDWidget("OH-I", parent=self)
        self.tab_widget.addTab(self.oh1_widget, "OH-I (First Overhaul)")
        
        # OH-II tab  
        self.oh2_widget = OverhaulCRUDWidget("OH-II", parent=self)
        self.tab_widget.addTab(self.oh2_widget, "OH-II (Second Overhaul)")
        
        main_layout.addWidget(self.tab_widget)
        
        # Connect signals
        self.oh1_widget.data_changed.connect(self.on_data_changed)
        self.oh2_widget.data_changed.connect(self.on_data_changed)
        
    def create_header_layout(self):
        """Create the header layout with global actions."""
        layout = QHBoxLayout()
        
        # Title
        title = QLabel("🔧 Equipment Overhaul Management")
        title.setFont(DPIScaler.create_scaled_font(16, bold=True))
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Global actions
        self.global_export_btn = QPushButton("📊 Export All")
        self.global_export_btn.clicked.connect(self.export_all_overhauls)
        layout.addWidget(self.global_export_btn)
        
        self.global_refresh_btn = QPushButton("🔄 Refresh All")
        self.global_refresh_btn.clicked.connect(self.refresh_all_tabs)
        layout.addWidget(self.global_refresh_btn)
        
        return layout
        
    def on_data_changed(self):
        """Handle data changes from sub-widgets."""
        # Refresh both tabs when data changes
        self.refresh_all_tabs()
        
    def refresh_all_tabs(self):
        """Refresh data in all tabs."""
        self.oh1_widget.refresh_data()
        self.oh2_widget.refresh_data()
        
    def export_all_overhauls(self):
        """Export all overhaul data to CSV."""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Export All Overhauls",
                f"all_overhauls_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )
            
            if file_path:
                # Export logic here
                QMessageBox.information(self, "Success", f"All overhauls exported to {file_path}")
                
        except Exception as e:
            logger.error(f"Error exporting overhauls: {e}")
            QMessageBox.critical(self, "Error", f"Failed to export overhauls: {str(e)}")

class OverhaulCRUDWidget(ResponsiveCRUDWidget):
    """CRUD widget for overhaul management with OH-I/OH-II specific functionality."""
    
    def __init__(self, overhaul_type, parent=None):
        self.overhaul_type = overhaul_type
        super().__init__(f"overhauls_{overhaul_type.lower()}", parent)
        
    def create_form_widget(self):
        """Create the overhaul-specific form widget."""
        widget = QWidget()
        layout = QFormLayout(widget)
        FormManager.setup_responsive_form_layout(layout)
        
        # Basic fields
        self.id_field = QLineEdit()
        self.id_field.setReadOnly(True)
        layout.addRow("Overhaul ID:", self.id_field)
        
        # Equipment selection
        self.equipment_field = QComboBox()
        self.equipment_field.setEditable(True)
        layout.addRow("Equipment:", self.equipment_field)
        
        # Overhaul type (read-only)
        self.type_field = QLineEdit(self.overhaul_type)
        self.type_field.setReadOnly(True)
        layout.addRow("Overhaul Type:", self.type_field)
        
        # Due date
        self.due_date_field = QDateEdit()
        self.due_date_field.setCalendarPopup(True)
        self.due_date_field.setDate(QDate.currentDate())
        layout.addRow("Due Date:", self.due_date_field)
        
        # Done date
        self.done_date_field = QDateEdit()
        self.done_date_field.setCalendarPopup(True)
        self.done_date_field.setSpecialValueText("Not completed")
        self.done_date_field.setDate(QDate(1900, 1, 1))
        layout.addRow("Done Date:", self.done_date_field)
        
        # Status
        self.status_field = QComboBox()
        self.status_field.addItems(["scheduled", "in_progress", "completed", "cancelled", "overdue"])
        layout.addRow("Status:", self.status_field)
        
        # Meterage at overhaul
        self.meterage_field = QDoubleSpinBox()
        self.meterage_field.setRange(0, 999999)
        self.meterage_field.setSuffix(" km")
        layout.addRow("Meterage (km):", self.meterage_field)
        
        # Cost
        self.cost_field = QDoubleSpinBox()
        self.cost_field.setRange(0, 9999999)
        self.cost_field.setPrefix("₹ ")
        layout.addRow("Cost:", self.cost_field)
        
        # Workshop/Vendor
        self.workshop_field = QLineEdit()
        layout.addRow("Workshop/Vendor:", self.workshop_field)
        
        # Notes
        self.notes_field = QTextEdit()
        self.notes_field.setMaximumHeight(DPIScaler.scale_size(100))
        layout.addRow("Notes:", self.notes_field)
        
        # Status display
        self.status_display = StatusLabel("Unknown", "unknown")
        layout.addRow("Calculated Status:", self.status_display)
        
        # Load equipment data
        self.load_equipment_data()
        
        return widget
        
    def load_equipment_data(self):
        """Load equipment data for the dropdown."""
        try:
            self.equipment_field.clear()
            equipment_list = Equipment.get_all()
            
            for equipment in equipment_list:
                if equipment.equipment_status != 'discarded':
                    display_text = f"{equipment.ba_number} - {equipment.make_and_type}"
                    self.equipment_field.addItem(display_text, equipment.equipment_id)
                    
        except Exception as e:
            logger.error(f"Error loading equipment data: {e}")
            
    def load_data(self):
        """Load overhaul data for this type."""
        try:
            # Get overhauls by type
            overhauls = Overhaul.get_by_type(self.overhaul_type)
            
            # Filter out discarded equipment
            filtered_overhauls = []
            for overhaul in overhauls:
                equipment = Equipment.get_by_id(overhaul.get('equipment_id'))
                if equipment and equipment.equipment_status != 'discarded':
                    filtered_overhauls.append(overhaul)
            
            # Prepare data for table
            headers = ["ID", "BA Number", "Make & Type", "Due Date", "Done Date", "Status", "Cost", "Workshop"]
            data = []
            
            for overhaul in filtered_overhauls:
                equipment = Equipment.get_by_id(overhaul.get('equipment_id'))
                if not equipment:
                    continue
                    
                # Calculate status
                status = self.calculate_overhaul_status(overhaul, equipment)
                
                row_data = {
                    "ID": overhaul.get('id'),
                    "BA Number": equipment.ba_number or "Not Assigned",
                    "Make & Type": equipment.make_and_type or "",
                    "Due Date": overhaul.get('due_date', ''),
                    "Done Date": overhaul.get('done_date', ''),
                    "Status": status.replace('_', ' ').title(),
                    "Cost": f"₹ {overhaul.get('cost', 0):,.2f}",
                    "Workshop": overhaul.get('workshop', '')
                }
                data.append(row_data)
            
            # Sort by BA Number for better grouping
            data.sort(key=lambda x: (x["BA Number"], x["Make & Type"]))
            
            # Set data in table
            self.data_table.set_data(headers, data, id_column="ID")
            
            # Hide ID column
            self.data_table.setColumnHidden(0, True)
            
        except Exception as e:
            logger.error(f"Error loading overhaul data: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load overhaul data: {str(e)}")
            
    def calculate_overhaul_status(self, overhaul, equipment):
        """Calculate overhaul status using the enhanced logic."""
        try:
            return utils.calculate_overhaul_status(
                self.overhaul_type,
                overhaul.get('due_date'),
                overhaul.get('done_date'),
                equipment.date_of_commission,
                None,  # oh1_done_date
                None,  # custom_intervals
                equipment.meterage_kms
            )
        except Exception as e:
            logger.error(f"Error calculating overhaul status: {e}")
            return "unknown"
            
    def populate_form(self, record_data):
        """Populate form with overhaul record data."""
        try:
            if not record_data:
                return
                
            # Get full overhaul data
            overhaul_id = record_data.get('ID')
            overhaul = Overhaul.get_by_id(overhaul_id)
            
            if not overhaul:
                return
                
            # Populate fields
            self.id_field.setText(str(overhaul.get('id', '')))
            
            # Set equipment
            equipment_id = overhaul.get('equipment_id')
            for i in range(self.equipment_field.count()):
                if self.equipment_field.itemData(i) == equipment_id:
                    self.equipment_field.setCurrentIndex(i)
                    break
                    
            # Set dates
            due_date = overhaul.get('due_date')
            if due_date:
                self.due_date_field.setDate(QDate.fromString(due_date, Qt.ISODate))
                
            done_date = overhaul.get('done_date')
            if done_date:
                self.done_date_field.setDate(QDate.fromString(done_date, Qt.ISODate))
            else:
                self.done_date_field.setDate(QDate(1900, 1, 1))
                
            # Set other fields
            status = overhaul.get('status', 'scheduled')
            status_index = self.status_field.findText(status)
            if status_index >= 0:
                self.status_field.setCurrentIndex(status_index)
                
            self.meterage_field.setValue(overhaul.get('meter_reading', 0))
            self.cost_field.setValue(overhaul.get('cost', 0))
            self.workshop_field.setText(overhaul.get('workshop', ''))
            self.notes_field.setPlainText(overhaul.get('notes', ''))
            
            # Update status display
            equipment = Equipment.get_by_id(equipment_id)
            if equipment:
                calculated_status = self.calculate_overhaul_status(overhaul, equipment)
                self.status_display.setStatus(calculated_status)
                
        except Exception as e:
            logger.error(f"Error populating form: {e}")
            
    def get_form_data(self):
        """Get form data as dictionary."""
        return {
            'id': self.id_field.text() or None,
            'equipment_id': self.equipment_field.currentData(),
            'overhaul_type': self.overhaul_type,
            'due_date': self.due_date_field.date().toString(Qt.ISODate),
            'done_date': self.done_date_field.date().toString(Qt.ISODate) if self.done_date_field.date() != QDate(1900, 1, 1) else None,
            'status': self.status_field.currentText(),
            'meter_reading': self.meterage_field.value(),
            'cost': self.cost_field.value(),
            'workshop': self.workshop_field.text(),
            'notes': self.notes_field.toPlainText()
        }
        
    def validate_form(self):
        """Validate form data."""
        self.validation_errors = []
        
        # Check equipment selection
        if not self.equipment_field.currentData():
            self.validation_errors.append("Please select equipment")
            
        # Check due date
        if self.due_date_field.date() < QDate.currentDate().addDays(-365):
            self.validation_errors.append("Due date cannot be more than 1 year in the past")
            
        # Check done date if set
        if (self.done_date_field.date() != QDate(1900, 1, 1) and 
            self.done_date_field.date() > QDate.currentDate()):
            self.validation_errors.append("Done date cannot be in the future")
            
        return len(self.validation_errors) == 0
        
    def save_record(self):
        """Save the overhaul record."""
        if not self.validate_form():
            QMessageBox.warning(self, "Validation Error", "\n".join(self.validation_errors))
            return
            
        try:
            form_data = self.get_form_data()
            
            if self.is_creating:
                # Create new overhaul
                overhaul = Overhaul(
                    equipment_id=form_data['equipment_id'],
                    overhaul_type=form_data['overhaul_type'],
                    due_date=form_data['due_date'],
                    done_date=form_data['done_date'],
                    status=form_data['status'],
                    meter_reading=form_data['meter_reading'],
                    cost=form_data['cost'],
                    workshop=form_data['workshop'],
                    notes=form_data['notes']
                )
                
                overhaul_id = overhaul.save()
                if overhaul_id:
                    # Log creation
                    OverhaulHistory.log_status_change(
                        overhaul_id,
                        None,
                        form_data['status'],
                        "user",
                        f"Overhaul created: {form_data['notes']}"
                    )
                    
                    QMessageBox.information(self, "Success", "Overhaul created successfully.")
                    self.data_changed.emit()
                    self.cancel_operation()
                else:
                    QMessageBox.warning(self, "Error", "Failed to create overhaul.")
                    
            elif self.is_editing:
                # Update existing overhaul
                overhaul_id = form_data['id']
                old_overhaul = Overhaul.get_by_id(overhaul_id)
                old_status = old_overhaul.get('status') if old_overhaul else None
                
                if Overhaul.update(overhaul_id, **{k: v for k, v in form_data.items() if k != 'id'}):
                    # Log status change if changed
                    if old_status != form_data['status']:
                        OverhaulHistory.log_status_change(
                            overhaul_id,
                            old_status,
                            form_data['status'],
                            "user",
                            f"Status updated: {form_data['notes']}"
                        )
                        
                    QMessageBox.information(self, "Success", "Overhaul updated successfully.")
                    self.data_changed.emit()
                    self.cancel_operation()
                else:
                    QMessageBox.warning(self, "Error", "Failed to update overhaul.")
                    
        except Exception as e:
            logger.error(f"Error saving overhaul: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save overhaul: {str(e)}")
            
    def delete_record(self):
        """Delete the selected overhaul record."""
        if not self.current_record:
            return
            
        reply = QMessageBox.question(
            self,
            "Confirm Delete",
            f"Are you sure you want to delete this {self.overhaul_type} overhaul record?\n\n"
            f"Equipment: {self.current_record.get('BA Number')} - {self.current_record.get('Make & Type')}\n"
            f"Due Date: {self.current_record.get('Due Date')}\n\n"
            f"This action cannot be undone.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                overhaul_id = self.current_record.get('ID')
                
                # Log deletion
                OverhaulHistory.log_status_change(
                    overhaul_id,
                    self.current_record.get('Status'),
                    'deleted',
                    "user",
                    "Overhaul record deleted"
                )
                
                if Overhaul.delete(overhaul_id):
                    QMessageBox.information(self, "Success", "Overhaul deleted successfully.")
                    self.data_changed.emit()
                    self.cancel_operation()
                else:
                    QMessageBox.warning(self, "Error", "Failed to delete overhaul.")
                    
            except Exception as e:
                logger.error(f"Error deleting overhaul: {e}")
                QMessageBox.critical(self, "Error", f"Failed to delete overhaul: {str(e)}")
                
    def view_history(self):
        """View overhaul history."""
        if not self.current_record:
            return
            
        overhaul_id = self.current_record.get('ID')
        dialog = HistoryViewDialog(overhaul_id, f"overhaul_{self.overhaul_type.lower()}", self)
        dialog.exec_()
        
    def bulk_create(self):
        """Bulk create overhauls."""
        dialog = BulkOperationDialog("create", f"overhaul_{self.overhaul_type.lower()}", self)
        if dialog.exec_() == QDialog.Accepted:
            self.data_changed.emit()
            
    def bulk_edit(self):
        """Bulk edit overhauls."""
        dialog = BulkOperationDialog("edit", f"overhaul_{self.overhaul_type.lower()}", self)
        if dialog.exec_() == QDialog.Accepted:
            self.data_changed.emit()
            
    def bulk_delete(self):
        """Bulk delete overhauls."""
        dialog = BulkOperationDialog("delete", f"overhaul_{self.overhaul_type.lower()}", self)
        if dialog.exec_() == QDialog.Accepted:
            self.data_changed.emit()

    def export_csv(self):
        """Export overhaul data to CSV."""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                f"Export {self.overhaul_type} Overhauls",
                f"{self.overhaul_type.lower()}_overhauls_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                # Export logic here
                QMessageBox.information(self, "Success", f"{self.overhaul_type} overhauls exported to {file_path}")

        except Exception as e:
            logger.error(f"Error exporting {self.overhaul_type} overhauls: {e}")
            QMessageBox.critical(self, "Error", f"Failed to export overhauls: {str(e)}")
