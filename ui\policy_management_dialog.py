"""
Policy Management Dashboard
Comprehensive interface for managing vehicle class policies and discard criteria.
"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTabWidget, QWidget, QFormLayout,
                            QComboBox, QLineEdit, QDoubleSpinBox, QCheckBox,
                            QGroupBox, QGridLayout, QMessageBox, QFrame,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QAbstractItemView, QSplitter, QTextEdit,
                            QProgressDialog, QSpinBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QColor

from policy_models import PolicyCondition
import policy_service
import models
import logging
from ui.policy_editor_dialog import PolicyEditorDialog
from discard_service import create_all_automatic_discard_criteria

# Check if policy module is available
POLICY_SUPPORT = True
try:
    import policy_service
    from policy_models import VehicleClassPolicy, PolicyCondition
except ImportError as e:
    POLICY_SUPPORT = False
    logging.warning(f"Policy module not available in PolicyManagementDialog. Error: {e}")

logger = logging.getLogger('policy_management_dialog')


class PolicyManagementDialog(QDialog):
    """
    Comprehensive Policy Management Dashboard.
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.policies_data = []
        self.equipment_stats = {}
        
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """Set up the policy management dashboard UI."""
        self.setWindowTitle("Policy Management Dashboard")
        
        # Setup responsive sizing
        from ui.window_utils import DialogManager
        DialogManager.setup_responsive_dialog(self, width_percent=0.8, height_percent=0.72, 
                                            min_width=800, min_height=600)
        
        # Main layout with proper margins
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)  # Add margins around entire dialog
        main_layout.setSpacing(10)  # Add spacing between main elements
        
        # Title and info section
        title_label = QLabel("POLICY MANAGEMENT DASHBOARD")
        title_label.setFont(QFont("Arial", 24, QFont.Bold))  # Increased font size from 18 to 24
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 24px;
                color: #2c3e50;
                margin: 10px 0px;
                padding: 8px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # Create splitter for main content
        content_splitter = QSplitter(Qt.Horizontal)
        
        # Left side - Policy operations and statistics
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(10, 10, 15, 10)  # Add margins: left, top, right, bottom
        left_layout.setSpacing(15)  # Add spacing between sections
        
        # Policy Statistics Section
        stats_group = QGroupBox("Policy Statistics")
        stats_layout = QVBoxLayout(stats_group)
        stats_layout.setContentsMargins(12, 12, 12, 12)  # Add internal margins
        stats_layout.setSpacing(8)  # Add spacing within stats section
        
        self.stats_label = QLabel("Loading statistics...")
        self.stats_label.setStyleSheet("""
            QLabel {
                font-family: monospace;
                padding: 15px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                line-height: 1.4;
            }
        """)
        stats_layout.addWidget(self.stats_label)
        
        left_layout.addWidget(stats_group)
        
        # Policy Operations Section
        operations_group = QGroupBox("Policy Operations")
        operations_layout = QVBoxLayout(operations_group)
        operations_layout.setContentsMargins(12, 12, 12, 12)  # Add internal margins
        operations_layout.setSpacing(10)  # Add spacing between button rows
        
        # Row 1: Policy Management
        mgmt_layout = QHBoxLayout()
        mgmt_layout.setSpacing(12)  # Add spacing between buttons
        
        self.create_default_policies_btn = QPushButton("Create Default Policies")
        self.create_default_policies_btn.clicked.connect(self.create_default_policies)
        self.create_default_policies_btn.setStyleSheet("""
            QPushButton {
                background-color: #5cb85c;
                color: white;
                border: 1px solid #4cae4c;
                padding: 10px 16px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 130px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #4cae4c;
                border: 2px solid #449d44;
            }
            QPushButton:pressed {
                background-color: #449d44;
            }
        """)
        mgmt_layout.addWidget(self.create_default_policies_btn)
        
        self.update_all_criteria_btn = QPushButton("Update All Criteria")
        self.update_all_criteria_btn.clicked.connect(self.update_all_criteria)
        self.update_all_criteria_btn.setStyleSheet("""
            QPushButton {
                background-color: #f0ad4e;
                color: black;
                border: 1px solid #eea236;
                padding: 10px 16px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 130px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #eea236;
                border: 2px solid #ec971f;
            }
            QPushButton:pressed {
                background-color: #ec971f;
            }
        """)
        mgmt_layout.addWidget(self.update_all_criteria_btn)
        
        operations_layout.addLayout(mgmt_layout)
        
        # Row 2: Utility Operations
        utility_layout = QHBoxLayout()
        utility_layout.setSpacing(12)  # Add spacing between buttons
        
        self.refresh_btn = QPushButton("Refresh Data")
        self.refresh_btn.clicked.connect(self.refresh_data)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: 1px solid #5a6268;
                padding: 10px 16px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 130px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #5a6268;
                border: 2px solid #545b62;
            }
            QPushButton:pressed {
                background-color: #545b62;
            }
        """)
        utility_layout.addWidget(self.refresh_btn)
        
        self.policy_docs_btn = QPushButton("Policy docs")
        self.policy_docs_btn.clicked.connect(self.open_policy_docs)
        self.policy_docs_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: 1px solid #8e44ad;
                padding: 10px 16px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 130px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
                border: 2px solid #7d3c98;
            }
            QPushButton:pressed {
                background-color: #7d3c98;
            }
        """)
        utility_layout.addWidget(self.policy_docs_btn)
        
        operations_layout.addLayout(utility_layout)
        
        left_layout.addWidget(operations_group)
        
        # Coverage Analysis Section
        coverage_group = QGroupBox("Coverage Analysis")
        coverage_layout = QVBoxLayout(coverage_group)
        coverage_layout.setContentsMargins(12, 12, 12, 12)  # Add internal margins
        coverage_layout.setSpacing(8)  # Add spacing within coverage section
        
        self.coverage_text = QTextEdit()
        self.coverage_text.setMaximumHeight(200)
        self.coverage_text.setReadOnly(True)
        self.coverage_text.setStyleSheet("""
            QTextEdit {
                font-family: monospace;
                font-size: 11px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
        coverage_layout.addWidget(self.coverage_text)
        
        left_layout.addWidget(coverage_group)
        left_layout.addStretch()
        
        # Right side - Policy overview table
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(15, 10, 10, 10)  # Add margins: left, top, right, bottom
        right_layout.setSpacing(12)  # Add spacing between table elements
        
        # Policy table section
        table_label = QLabel("Current Policies")
        table_label.setFont(QFont("Arial", 14, QFont.Bold))
        right_layout.addWidget(table_label)
        
        # Policy table
        self.policy_table = QTableWidget()
        self.policy_table.setColumnCount(7)
        self.policy_table.setHorizontalHeaderLabels([
            "ID", "Equipment Type", "Years", "KMs", "Hours", "Logic", "Equipment Count"
        ])
        self.policy_table.verticalHeader().setVisible(False)
        self.policy_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.policy_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.policy_table.setSortingEnabled(True)
        self.policy_table.setAlternatingRowColors(True)
        
        # Set DPI-aware column widths
        from ui.window_utils import DPIScaler
        header = self.policy_table.horizontalHeader()
        header.setStretchLastSection(False)
        
        # Apply DPI scaling to column widths
        base_widths = [50, 200, 60, 80, 60, 80, 100]  # ID, Equipment Type, Years, KMs, Hours, Logic, Equipment Count
        for col, base_width in enumerate(base_widths):
            scaled_width = DPIScaler.scale_size(base_width, min_size=40, max_size=300)
            header.resizeSection(col, scaled_width)
        
        # Apply DPI-aware font and styling
        table_font = DPIScaler.create_scaled_font(10)
        self.policy_table.setFont(table_font)
        
        # Set DPI-aware row height
        row_height = DPIScaler.scale_size(30, min_size=25, max_size=50)
        self.policy_table.verticalHeader().setDefaultSectionSize(row_height)
        
        # Double-click to edit
        self.policy_table.doubleClicked.connect(self.edit_selected_policy)
        
        right_layout.addWidget(self.policy_table)
        
        # Policy actions
        policy_actions_layout = QHBoxLayout()
        policy_actions_layout.setSpacing(12)  # Add spacing between action buttons
        policy_actions_layout.setContentsMargins(0, 8, 0, 0)  # Add top margin for separation
        
        self.edit_policy_btn = QPushButton("Edit Selected")
        self.edit_policy_btn.clicked.connect(self.edit_selected_policy)
        self.edit_policy_btn.setEnabled(False)
        self.edit_policy_btn.setStyleSheet("""
            QPushButton {
                background-color: #4a90e2;
                color: white;
                border: 1px solid #357abd;
                padding: 10px 16px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 130px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #357abd;
                border: 2px solid #2c5aa0;
            }
            QPushButton:pressed {
                background-color: #2c5aa0;
            }
            QPushButton:disabled {
                background-color: #f5f5f5;
                color: #999999;
                border: 1px solid #ddd;
            }
        """)
        policy_actions_layout.addWidget(self.edit_policy_btn)
        
        self.delete_policy_btn = QPushButton("Delete Selected")
        self.delete_policy_btn.clicked.connect(self.delete_selected_policy)
        self.delete_policy_btn.setEnabled(False)
        self.delete_policy_btn.setStyleSheet("""
            QPushButton {
                background-color: #d9534f;
                color: white;
                border: 1px solid #d43f3a;
                padding: 10px 16px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 130px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #d43f3a;
                border: 2px solid #c9302c;
            }
            QPushButton:pressed {
                background-color: #c9302c;
            }
            QPushButton:disabled {
                background-color: #f5f5f5;
                color: #999999;
                border: 1px solid #ddd;
            }
        """)
        policy_actions_layout.addWidget(self.delete_policy_btn)
        
        policy_actions_layout.addStretch()
        
        self.new_policy_btn = QPushButton("New Policy")
        self.new_policy_btn.clicked.connect(self.create_new_policy)
        self.new_policy_btn.setStyleSheet("""
            QPushButton {
                background-color: #5cb85c;
                color: white;
                border: 1px solid #4cae4c;
                padding: 10px 16px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 130px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #4cae4c;
                border: 2px solid #449d44;
            }
            QPushButton:pressed {
                background-color: #449d44;
            }
        """)
        policy_actions_layout.addWidget(self.new_policy_btn)
        
        right_layout.addLayout(policy_actions_layout)
        
        # Add widgets to splitter
        content_splitter.addWidget(left_widget)
        content_splitter.addWidget(right_widget)
        content_splitter.setSizes([400, 800])
        
        main_layout.addWidget(content_splitter)
        
        # Bottom buttons
        bottom_layout = QHBoxLayout()
        bottom_layout.setContentsMargins(0, 15, 0, 0)  # Add top margin for separation
        bottom_layout.setSpacing(10)  # Add spacing around close button
        bottom_layout.addStretch()
        
        self.close_btn = QPushButton("Close")
        self.close_btn.clicked.connect(self.accept)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: 1px solid #5a6268;
                padding: 10px 16px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #5a6268;
                border: 2px solid #545b62;
            }
            QPushButton:pressed {
                background-color: #545b62;
            }
        """)
        bottom_layout.addWidget(self.close_btn)
        
        # Connect table selection
        self.policy_table.selectionModel().selectionChanged.connect(self.on_policy_selection_changed)
    
    def get_button_style(self, style_type):
        """Get consistent button styling."""
        base_style = """
            QPushButton {
                padding: 10px 16px;
                border: 1px solid #ccc;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 130px;
                min-height: 35px;
            }
            QPushButton:hover {
                opacity: 0.9;
                border: 2px solid #333;
            }
            QPushButton:pressed {
                background-color: #e0e0e0;
            }
            QPushButton:disabled {
                background-color: #f5f5f5;
                color: #999999;
                border: 1px solid #ddd;
            }
        """
        
        colors = {
            "primary": "background-color: #4a90e2; color: white; border: 1px solid #357abd;",
            "success": "background-color: #5cb85c; color: white; border: 1px solid #4cae4c;",
            "warning": "background-color: #f0ad4e; color: black; border: 1px solid #eea236;",
            "danger": "background-color: #d9534f; color: white; border: 1px solid #d43f3a;",
            "info": "background-color: #5bc0de; color: black; border: 1px solid #46b8da;",
            "default": "background-color: #6c757d; color: white; border: 1px solid #5a6268;"
        }
        
        return base_style + colors.get(style_type, colors["default"])
    
    def load_data(self):
        """Load policy data and statistics."""
        if not POLICY_SUPPORT:
            self.stats_label.setText("Policy support is not available.")
            return
            
        try:
            # Load policies
            self.policies_data = policy_service.get_all_policies(force_refresh=True)
            
            # Load equipment statistics
            self.load_equipment_statistics()
            
            # Update UI
            self.update_policy_table()
            self.update_statistics_display()
            self.update_coverage_analysis()
            
        except Exception as e:
            logger.error(f"Error loading policy data: {e}")
            self.stats_label.setText(f"Error loading data: {str(e)}")
    
    def load_equipment_statistics(self):
        """Calculate equipment and policy statistics."""
        try:
            # Get all equipment
            all_equipment = models.Equipment.get_active()
            
            # Get all discard criteria
            all_criteria = models.DiscardCriteria.get_all()
            
            # Calculate statistics
            self.equipment_stats = {
                'total_equipment': len(all_equipment),
                'total_policies': len(self.policies_data),
                'equipment_with_policies': 0,
                'equipment_with_criteria': len(all_criteria),
                'equipment_without_criteria': 0,
                'equipment_types_with_policies': 0,
                'equipment_types_total': 0,
                'policy_coverage_percent': 0
            }
            
            # Count unique equipment types
            equipment_types = set()
            policy_types = set()
            
            for equipment in all_equipment:
                make_type = equipment.get('make_and_type')
                if make_type:
                    equipment_types.add(policy_service.normalize_make_type(make_type))
            
            for policy_data in self.policies_data:
                policy = policy_data.get('policy', {})
                make_type = policy.get('make_and_type')
                if make_type:
                    policy_types.add(policy_service.normalize_make_type(make_type))
            
            self.equipment_stats['equipment_types_total'] = len(equipment_types)
            self.equipment_stats['equipment_types_with_policies'] = len(policy_types)
            
            # Calculate equipment with policies
            equipment_with_policies = 0
            for equipment in all_equipment:
                make_type = equipment.get('make_and_type')
                if make_type and policy_service.get_policy_with_conditions(make_and_type=make_type):
                    equipment_with_policies += 1
            
            self.equipment_stats['equipment_with_policies'] = equipment_with_policies
            self.equipment_stats['equipment_without_criteria'] = (
                self.equipment_stats['total_equipment'] - self.equipment_stats['equipment_with_criteria']
            )
            
            # Calculate coverage percentage
            if self.equipment_stats['total_equipment'] > 0:
                self.equipment_stats['policy_coverage_percent'] = (
                    (equipment_with_policies / self.equipment_stats['total_equipment']) * 100
                )
            
        except Exception as e:
            logger.error(f"Error calculating equipment statistics: {e}")
    
    def update_policy_table(self):
        """Update the policy overview table."""
        self.policy_table.setRowCount(len(self.policies_data))
        
        for row, policy_data in enumerate(self.policies_data):
            policy = policy_data.get('policy', {})
            conditions = policy_data.get('conditions', [])
            
            # Find discard condition
            discard_condition = next(
                (c for c in conditions if c.get('condition_type') == PolicyCondition.DISCARD),
                {}
            )
            
            # Policy ID
            policy_id = policy.get('policy_id', '')
            id_item = QTableWidgetItem(str(policy_id))
            id_item.setData(Qt.UserRole, policy_data)
            self.policy_table.setItem(row, 0, id_item)
            
            # Equipment Type
            make_type = policy.get('make_and_type', '')
            self.policy_table.setItem(row, 1, QTableWidgetItem(make_type))
            
            # Years threshold
            years = discard_condition.get('years_threshold', 0)
            self.policy_table.setItem(row, 2, QTableWidgetItem(str(years) if years else '--'))
            
            # KMs threshold
            kms = discard_condition.get('km_threshold', 0)
            kms_text = f"{kms:,}" if kms else '--'
            self.policy_table.setItem(row, 3, QTableWidgetItem(kms_text))
            
            # Hours threshold
            hours = discard_condition.get('hours_threshold', 0)
            hours_text = f"{hours:,}" if hours else '--'
            self.policy_table.setItem(row, 4, QTableWidgetItem(hours_text))
            
            # Logic type
            logic = discard_condition.get('logic_type', 'EARLIER')
            logic_display = {
                'EARLIER': 'Any (Earlier)',
                'LATER': 'All (Later)',
                'EXACT': 'Exact'
            }.get(logic, logic)
            self.policy_table.setItem(row, 5, QTableWidgetItem(logic_display))
            
            # Equipment count (how many equipment match this policy)
            equipment_count = self.count_equipment_for_policy(make_type)
            self.policy_table.setItem(row, 6, QTableWidgetItem(str(equipment_count)))
        
        # Resize columns to content
        self.policy_table.resizeColumnsToContents()
    
    def count_equipment_for_policy(self, make_type):
        """Count how many equipment items match a policy."""
        try:
            all_equipment = models.Equipment.get_active()
            count = 0
            normalized_policy_type = policy_service.normalize_make_type(make_type)
            
            for equipment in all_equipment:
                equipment_type = equipment.get('make_and_type', '')
                if equipment_type:
                    normalized_equipment_type = policy_service.normalize_make_type(equipment_type)
                    if normalized_equipment_type == normalized_policy_type:
                        count += 1
            
            return count
        except Exception as e:
            logger.error(f"Error counting equipment for policy: {e}")
            return 0
    
    def update_statistics_display(self):
        """Update the statistics display."""
        stats = self.equipment_stats
        
        stats_text = f"""📊 POLICY STATISTICS

Total Equipment: {stats['total_equipment']:,}
Total Policies: {stats['total_policies']:,}

🎯 COVERAGE ANALYSIS
Equipment with Policies: {stats['equipment_with_policies']:,} ({stats['policy_coverage_percent']:.1f}%)
Equipment with Criteria: {stats['equipment_with_criteria']:,}
Equipment without Criteria: {stats['equipment_without_criteria']:,}

📋 EQUIPMENT TYPES
Types with Policies: {stats['equipment_types_with_policies']:,}
Total Equipment Types: {stats['equipment_types_total']:,}
Type Coverage: {(stats['equipment_types_with_policies']/max(stats['equipment_types_total'],1)*100):.1f}%"""
        
        self.stats_label.setText(stats_text)
    
    def update_coverage_analysis(self):
        """Update the coverage analysis text."""
        try:
            analysis_text = "🔍 COVERAGE ANALYSIS\n\n"
            
            # Equipment types without policies
            all_equipment = models.Equipment.get_active()
            equipment_types = set()
            policy_types = set()
            
            for equipment in all_equipment:
                make_type = equipment.get('make_and_type')
                if make_type:
                    equipment_types.add(policy_service.normalize_make_type(make_type))
            
            for policy_data in self.policies_data:
                policy = policy_data.get('policy', {})
                make_type = policy.get('make_and_type')
                if make_type:
                    policy_types.add(policy_service.normalize_make_type(make_type))
            
            missing_policies = equipment_types - policy_types
            
            if missing_policies:
                analysis_text += "❌ Equipment types WITHOUT policies:\n"
                for i, missing_type in enumerate(sorted(missing_policies)[:10]):
                    analysis_text += f"  • {missing_type}\n"
                if len(missing_policies) > 10:
                    analysis_text += f"  ... and {len(missing_policies) - 10} more\n"
            else:
                analysis_text += "✅ All equipment types have policies!\n"
            
            analysis_text += "\n"
            
            # Equipment meeting discard criteria
            if POLICY_SUPPORT:
                equipment_meeting_discard = 0
                for equipment in all_equipment:
                    try:
                        policy_status = policy_service.get_equipment_policy_status(equipment)
                        if policy_status and policy_status.get('meets_discard', False):
                            equipment_meeting_discard += 1
                    except:
                        continue
                
                analysis_text += f"⚠️ Equipment meeting discard criteria: {equipment_meeting_discard}\n"
            
            self.coverage_text.setPlainText(analysis_text)
            
        except Exception as e:
            logger.error(f"Error updating coverage analysis: {e}")
            self.coverage_text.setPlainText(f"Error analyzing coverage: {str(e)}")
    
    def on_policy_selection_changed(self):
        """Handle policy table selection changes."""
        selected_rows = self.policy_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0
        
        self.edit_policy_btn.setEnabled(has_selection)
        self.delete_policy_btn.setEnabled(has_selection)
    
    def refresh_data(self):
        """Refresh all data."""
        self.load_data()
    
    def open_policy_docs(self):
        """Open the policy documentation PDF file or folder."""
        import os
        import platform
        import subprocess
        from PyQt5.QtWidgets import QMessageBox
        from database import get_config_value
        
        try:
            # Get configured policy docs path from database
            policy_path = get_config_value('policy_docs_path', '')
            
            # Check if path is configured
            if not policy_path:
                reply = QMessageBox.question(
                    self,
                    "Policy Docs Path Not Set",
                    "Policy documents path has not been configured.\n\n"
                    "Would you like to set it now?\n"
                    "(Go to File → Import → Import Policy Docs Path...)",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
                if reply == QMessageBox.Yes:
                    QMessageBox.information(
                        self,
                        "Configure Path",
                        "Please go to:\nFile → Import → Import Policy Docs Path...\n\n"
                        "to configure the path to your policy documents."
                    )
                return
            
            # Check if the path exists
            if not os.path.exists(policy_path):
                reply = QMessageBox.question(
                    self,
                    "Path Not Found",
                    f"Policy documents path not found:\n{policy_path}\n\n"
                    "The file or folder may have been moved or deleted.\n"
                    "Would you like to reconfigure the path?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
                if reply == QMessageBox.Yes:
                    QMessageBox.information(
                        self,
                        "Reconfigure Path",
                        "Please go to:\nFile → Import → Import Policy Docs Path...\n\n"
                        "to update the path to your policy documents."
                    )
                return
            
            # Determine if the path is a file or folder and handle accordingly
            if os.path.isfile(policy_path):
                # Single PDF file - open directly
                self._open_pdf_file(policy_path)
            elif os.path.isdir(policy_path):
                # Folder containing PDFs - show selection dialog
                from ui.dialogs import PDFSelectionDialog
                
                pdf_dialog = PDFSelectionDialog(self, policy_path)
                if pdf_dialog.exec_() == QDialog.Accepted:
                    selected_pdf_path = pdf_dialog.get_selected_pdf_path()
                    if selected_pdf_path:
                        self._open_pdf_file(selected_pdf_path)
                # If dialog was cancelled, do nothing
            else:
                QMessageBox.warning(
                    self,
                    "Invalid Path",
                    f"The configured path is neither a file nor a folder:\n{policy_path}\n\n"
                    "Please reconfigure the policy documents path."
                )
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"An error occurred while opening policy documents:\n\n{str(e)}"
            )
    
    def _open_pdf_file(self, pdf_path):
        """Open a specific PDF file with the system's default application."""
        import os
        import platform
        import subprocess
        from PyQt5.QtWidgets import QMessageBox
        
        try:
            # Open the PDF file with the default system application
            system = platform.system()
            if system == "Windows":
                os.startfile(pdf_path)
            elif system == "Darwin":  # macOS
                subprocess.Popen(["open", pdf_path])
            else:  # Linux and other Unix-like systems
                subprocess.Popen(["xdg-open", pdf_path])
            
            # Show success message
            QMessageBox.information(
                self,
                "Policy Documentation",
                f"Policy document has been opened:\n{os.path.basename(pdf_path)}"
            )
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error Opening Document",
                f"Could not open the policy document:\n{pdf_path}\n\nError: {str(e)}"
            )
    
    def manage_policies(self):
        """Open the original policy editor dialog."""
        try:
            dialog = PolicyEditorDialog(parent=self)
            result = dialog.exec_()
            
            if result == QDialog.Accepted:
                # Refresh data after policy changes
                policy_service.clear_policy_cache()
                self.refresh_data()
                
        except Exception as e:
            logger.error(f"Error opening policy editor: {e}")
            QMessageBox.critical(self, "Error", f"Failed to open policy editor: {e}")
    
    def create_default_policies(self):
        """Create default policies based on configuration rules."""
        try:
            reply = QMessageBox.question(
                self,
                "Create Default Policies",
                "This will create policies for all equipment types based on configuration rules.\n\n"
                "Existing policies will be updated with new values.\n"
                "Do you want to continue?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # Show progress dialog
            progress = QProgressDialog("Creating default policies...", "Cancel", 0, 0, self)
            progress.setWindowTitle("Processing")
            progress.setWindowModality(Qt.WindowModal)
            progress.show()
            
            # Create policies from config
            if POLICY_SUPPORT:
                result = policy_service.create_default_policies_from_config()
                
                # Close progress dialog
                progress.close()
                
                # Show results
                if 'error' in result:
                    QMessageBox.critical(
                        self, "Error", 
                        f"Failed to create policies: {result['error']}"
                    )
                else:
                    message = f"Policy creation completed:\n\n"
                    message += f"Created: {result.get('created', 0)} new policies\n"
                    message += f"Updated: {result.get('updated', 0)} existing policies\n"
                    message += f"Skipped: {result.get('skipped', 0)} policies\n"
                    message += f"Total rules processed: {result.get('total_rules', 0)}"
                    
                    QMessageBox.information(self, "Success", message)
                    
                    # Refresh data to show new policies
                    policy_service.clear_policy_cache()
                    self.refresh_data()
            else:
                progress.close()
                QMessageBox.warning(
                    self, "Policy Support Disabled",
                    "Policy support is not available. Please check the policy module installation."
                )
                
        except Exception as e:
            if 'progress' in locals():
                progress.close()
            QMessageBox.critical(
                self, "Error", 
                f"Error creating default policies: {str(e)}"
            )
    
    def auto_create_all_criteria(self):
        """Create automatic discard criteria for all equipment based on make/type rules."""
        reply = QMessageBox.question(
            self,
            "Auto-Create Criteria",
            "This will create discard criteria for all equipment based on their make/type.\n\n"
            "Only equipment without existing criteria will be processed.\n"
            "Do you want to continue?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply != QMessageBox.Yes:
            return
        
        # Show progress dialog
        progress = QProgressDialog("Creating automatic discard criteria...", "Cancel", 0, 0, self)
        progress.setWindowTitle("Processing")
        progress.setWindowModality(Qt.WindowModal)
        progress.show()
        
        # Use timer to start processing after dialog is shown
        QTimer.singleShot(100, lambda: self._perform_auto_create(progress, force_update=False))
    
    def update_all_criteria(self):
        """Enhanced update all criteria with policy-aware logic."""
        reply = QMessageBox.question(
            self,
            "Update All Criteria", 
            "This will perform a POLICY-AWARE update of all discard criteria.\n\n"
            "⚠️ ENHANCED LOGIC:\n"
            "• Equipment with policies will be evaluated against policy thresholds\n"
            "• Equipment exceeding policy criteria will get discard entries\n"
            "• Equipment without policies will use configuration rules\n\n"
            "This may create many new criteria entries. Continue?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply != QMessageBox.Yes:
            return
        
        # Show progress dialog
        progress = QProgressDialog("Performing policy-aware criteria update...", "Cancel", 0, 0, self)
        progress.setWindowTitle("Processing")
        progress.setWindowModality(Qt.WindowModal)
        progress.show()
        
        # Use timer to start processing after dialog is shown  
        QTimer.singleShot(100, lambda: self._perform_policy_aware_update(progress))
    
    def _perform_auto_create(self, progress, force_update=False):
        """Perform the actual auto-creation process."""
        try:
            # Call the service function
            stats = create_all_automatic_discard_criteria(force_update=force_update)
            
            # Close progress dialog
            progress.close()
            
            # Show results
            action = "updated" if force_update else "created"
            message = f"Automatic discard criteria {action} successfully!\n\n"
            message += f"✅ Created: {stats.get('created', 0)}\n"
            message += f"🔄 Updated: {stats.get('updated', 0)}\n"  
            message += f"⏭️ Skipped: {stats.get('skipped', 0)}\n"
            if stats.get('errors', 0) > 0:
                message += f"❌ Errors: {stats.get('errors', 0)}\n"
            message += f"\nTotal Processed: {stats.get('total_processed', 0)}"
            
            QMessageBox.information(self, "Auto-Create Complete", message)
            
            # Reload data to show changes
            self.refresh_data()
            
        except Exception as e:
            progress.close()
            QMessageBox.critical(
                self,
                "Error",
                f"An error occurred during automatic criteria creation:\n{str(e)}"
            )
    
    def _perform_policy_aware_update(self, progress):
        """Perform the enhanced policy-aware criteria update."""
        try:
            # This is the main enhancement - policy-aware update logic
            all_equipment = models.Equipment.get_active()
            results = {
                'processed': 0,
                'policy_based': 0,
                'created': 0,
                'updated': 0,
                'skipped': 0,
                'errors': 0,
                'error_details': []
            }
            
            logger.info(f"Starting policy-aware update for {len(all_equipment)} equipment items")
            
            for equipment in all_equipment:
                try:
                    equipment_id = equipment.get('equipment_id')
                    make_type = equipment.get('make_and_type')
                    
                    if not equipment_id:
                        results['skipped'] += 1
                        results['error_details'].append(f"Equipment missing ID: {equipment}")
                        continue
                        
                    if not make_type:
                        results['skipped'] += 1
                        results['error_details'].append(f"Equipment {equipment_id} missing make_and_type")
                        continue
                    
                    logger.debug(f"Processing equipment {equipment_id}: {make_type}")
                    
                    # Check if equipment has applicable policy
                    policy_data = None
                    if POLICY_SUPPORT:
                        try:
                            policy_data = policy_service.get_policy_with_conditions(make_and_type=make_type)
                            logger.debug(f"Equipment {equipment_id} policy data: {bool(policy_data)}")
                        except Exception as pe:
                            logger.error(f"Error getting policy for {equipment_id}: {pe}")
                            results['error_details'].append(f"Policy error for {equipment_id}: {pe}")
                    
                    if policy_data and POLICY_SUPPORT:
                        # POLICY-BASED EVALUATION
                        try:
                            policy_status = policy_service.get_equipment_policy_status(equipment)
                            logger.debug(f"Equipment {equipment_id} policy status: {policy_status}")
                            
                            if policy_status and policy_status.get('meets_discard', False):
                                # Equipment meets policy discard criteria - create entry
                                discard_conditions = policy_status.get('conditions', {}).get(PolicyCondition.DISCARD, {})
                                
                                years_threshold = discard_conditions.get('years_threshold', 0)
                                km_threshold = discard_conditions.get('km_threshold', 0)
                                hours_threshold = discard_conditions.get('hours_threshold', 0)
                                
                                logger.info(f"Creating discard criteria for {equipment_id}: {years_threshold}y, {km_threshold}km, {hours_threshold}h")
                                
                                # Check if criteria already exists
                                existing_criteria = models.DiscardCriteria.get_by_equipment(equipment_id)
                                
                                if existing_criteria:
                                    # Update existing criteria with policy values
                                    criteria = models.DiscardCriteria(
                                        discard_criteria_id=existing_criteria[0]['discard_criteria_id'],
                                        equipment_id=equipment_id,
                                        criteria_years=years_threshold,
                                        criteria_kms=km_threshold,
                                        criteria_hours=hours_threshold
                                    )
                                    criteria.save()
                                    results['updated'] += 1
                                    logger.info(f"Updated criteria for equipment {equipment_id}")
                                else:
                                    # Create new criteria from policy
                                    criteria = models.DiscardCriteria(
                                        equipment_id=equipment_id,
                                        criteria_years=years_threshold,
                                        criteria_kms=km_threshold,
                                        criteria_hours=hours_threshold
                                    )
                                    criteria.save()
                                    results['created'] += 1
                                    logger.info(f"Created criteria for equipment {equipment_id}")
                                
                                results['policy_based'] += 1
                            else:
                                results['skipped'] += 1  # Has policy but doesn't meet criteria yet
                                logger.debug(f"Equipment {equipment_id} has policy but doesn't meet discard criteria")
                        except Exception as pe:
                            logger.error(f"Error evaluating policy status for {equipment_id}: {pe}")
                            results['errors'] += 1
                            results['error_details'].append(f"Policy evaluation error for {equipment_id}: {pe}")
                    else:
                        # NO POLICY - SKIP (removed config fallback)
                        results['skipped'] += 1
                        logger.debug(f"Equipment {equipment_id} has no policy - skipping (no fallback)")
                    
                    results['processed'] += 1
                    
                except Exception as e:
                    logger.error(f"Error processing equipment {equipment_id}: {e}")
                    results['errors'] += 1
                    results['error_details'].append(f"General error for {equipment_id}: {e}")
            
            # Close progress dialog
            progress.close()
            
            # Show detailed results
            message = f"POLICY-AWARE UPDATE COMPLETED!\n\n"
            message += f"📊 SUMMARY:\n"
            message += f"Equipment Processed: {results['processed']}\n"
            message += f"Criteria Created: {results['created']}\n"
            message += f"Criteria Updated: {results['updated']}\n"
            message += f"Skipped: {results['skipped']}\n"
            if results['errors'] > 0:
                message += f"Errors: {results['errors']}\n"
            message += f"\n🎯 BREAKDOWN:\n"
            message += f"Policy-Based Actions: {results['policy_based']}\n\n"
            
            if results['created'] > 0 or results['updated'] > 0:
                message += f"✅ Equipment meeting policy criteria now have discard entries!"
            else:
                message += f"⚠️ No discard criteria created/updated.\n"
                if results['errors'] > 0:
                    message += f"Check the application logs for error details.\n"
                    logger.error(f"Error details: {results['error_details'][:5]}")  # Log first 5 errors
                else:
                    message += f"This means no equipment currently meets discard thresholds."
            
            QMessageBox.information(self, "Policy-Aware Update Complete", message)
            
            # Reload data to show changes
            self.refresh_data()
            
        except Exception as e:
            progress.close()
            logger.error(f"Fatal error in policy-aware update: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"An error occurred during policy-aware update:\n{str(e)}"
            )
    
    def export_policy_report(self):
        """Export a comprehensive policy report."""
        QMessageBox.information(self, "Export Report", "Policy report export functionality will be implemented in a future update.")
    
    def edit_selected_policy(self):
        """Edit the selected policy."""
        selected_rows = self.policy_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        row = selected_rows[0].row()
        policy_data = self.policy_table.item(row, 0).data(Qt.UserRole)
        
        if policy_data:
            policy = policy_data.get('policy', {})
            policy_id = policy.get('policy_id')
            make_and_type = policy.get('make_and_type')
            
            try:
                dialog = PolicyEditorDialog(parent=self, policy_id=policy_id, make_and_type=make_and_type)
                result = dialog.exec_()
                
                if result == QDialog.Accepted:
                    # Refresh data after policy changes
                    policy_service.clear_policy_cache()
                    self.refresh_data()
                    
            except Exception as e:
                logger.error(f"Error editing policy: {e}")
                QMessageBox.critical(self, "Error", f"Failed to edit policy: {e}")
    
    def delete_selected_policy(self):
        """Delete the selected policy."""
        selected_rows = self.policy_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        row = selected_rows[0].row()
        policy_data = self.policy_table.item(row, 0).data(Qt.UserRole)
        
        if policy_data:
            policy = policy_data.get('policy', {})
            policy_id = policy.get('policy_id')
            make_and_type = policy.get('make_and_type', 'Unknown')
            
            reply = QMessageBox.question(
                self,
                "Confirm Policy Deletion",
                f"Are you sure you want to delete the policy for:\n\n{make_and_type}\n\n"
                f"This action cannot be undone.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                try:
                    success = policy_service.delete_policy(policy_id)
                    if success:
                        policy_service.clear_policy_cache()
                        self.refresh_data()
                        QMessageBox.information(self, "Success", f"Policy for {make_and_type} deleted successfully.")
                    else:
                        QMessageBox.critical(self, "Error", "Failed to delete policy.")
                except Exception as e:
                    logger.error(f"Error deleting policy: {e}")
                    QMessageBox.critical(self, "Error", f"Failed to delete policy: {e}")
    
    def create_new_policy(self):
        """Create a new policy."""
        try:
            dialog = PolicyEditorDialog(parent=self)
            result = dialog.exec_()
            
            if result == QDialog.Accepted:
                # Refresh data after policy creation
                policy_service.clear_policy_cache()
                self.refresh_data()
                
        except Exception as e:
            logger.error(f"Error creating new policy: {e}")
            QMessageBox.critical(self, "Error", f"Failed to create new policy: {e}") 