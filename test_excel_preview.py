#!/usr/bin/env python3
"""
Test script for Excel Import Preview and Conflict Resolution System

This script tests the new Excel import preview functionality:
1. Creates a test Excel file with sample data
2. Tests the ExcelDataAnalyzer
3. Tests the ImportPreviewDialog
4. Tests conflict detection and resolution

Run this script to verify the implementation works correctly.
"""

import sys
import os
import pandas as pd
import sqlite3
import tempfile
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import our modules
from ui.excel_import_preview_dialog import ExcelDataAnalyzer, ImportPreviewDialog, SimpleConfirmationDialog
import config
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('test_excel_preview')

class TestMainWindow(QMainWindow):
    """Test window for Excel preview functionality."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Excel Import Preview Test")
        self.setGeometry(100, 100, 400, 300)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout
        layout = QVBoxLayout(central_widget)
        
        # Test buttons
        self.create_test_file_btn = QPushButton("1. Create Test Excel File")
        self.create_test_file_btn.clicked.connect(self.create_test_excel_file)
        layout.addWidget(self.create_test_file_btn)
        
        self.test_analyzer_btn = QPushButton("2. Test Data Analyzer")
        self.test_analyzer_btn.clicked.connect(self.test_data_analyzer)
        self.test_analyzer_btn.setEnabled(False)
        layout.addWidget(self.test_analyzer_btn)
        
        self.test_preview_btn = QPushButton("3. Test Preview Dialog")
        self.test_preview_btn.clicked.connect(self.test_preview_dialog)
        self.test_preview_btn.setEnabled(False)
        layout.addWidget(self.test_preview_btn)
        
        self.test_conflicts_btn = QPushButton("4. Test Conflict Resolution")
        self.test_conflicts_btn.clicked.connect(self.test_conflict_resolution)
        self.test_conflicts_btn.setEnabled(False)
        layout.addWidget(self.test_conflicts_btn)

        self.test_bulk_actions_btn = QPushButton("5. Test Accept/Skip All")
        self.test_bulk_actions_btn.clicked.connect(self.test_bulk_actions)
        self.test_bulk_actions_btn.setEnabled(False)
        layout.addWidget(self.test_bulk_actions_btn)
        
        # Status
        self.test_file_path = None
        self.analysis_data = None
        
    def create_test_excel_file(self):
        """Create a test Excel file with sample equipment data."""
        try:
            # Create test data
            test_data = {
                'BA Number': ['BA001', 'BA002', 'BA003', 'BA004', 'BA005'],
                'Make and Type': ['Toyota Hilux', 'Ford Ranger', 'Isuzu D-Max', 'Mitsubishi Triton', 'Nissan Navara'],
                'Serial Number': ['TH001', 'FR002', 'ID003', 'MT004', 'NN005'],
                'Meterage (KM)': [15000, 25000, 8000, 32000, 12000],
                'Hours Run Total': [1200, 2100, 650, 2800, 980],
                'Vintage Years': [2020, 2019, 2021, 2018, 2020],
                'Units Held': [1, 1, 1, 1, 1],
                'Date of Commission': ['2020-01-15', '2019-06-20', '2021-03-10', '2018-11-05', '2020-08-12']
            }
            
            df = pd.DataFrame(test_data)
            
            # Create temporary Excel file
            temp_dir = tempfile.gettempdir()
            self.test_file_path = os.path.join(temp_dir, f"test_equipment_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
            
            # Write to Excel
            with pd.ExcelWriter(self.test_file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Equipment', index=False)
            
            logger.info(f"Test Excel file created: {self.test_file_path}")
            
            # Enable next test
            self.test_analyzer_btn.setEnabled(True)
            
            QMessageBox.information(
                self,
                "Test File Created",
                f"Test Excel file created successfully:\n{self.test_file_path}",
                QMessageBox.StandardButton.Ok
            )
            
        except Exception as e:
            logger.error(f"Error creating test file: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to create test file:\n{str(e)}",
                QMessageBox.StandardButton.Ok
            )
    
    def test_data_analyzer(self):
        """Test the ExcelDataAnalyzer."""
        if not self.test_file_path:
            QMessageBox.warning(self, "Warning", "Please create test file first.")
            return
        
        try:
            logger.info("Testing ExcelDataAnalyzer...")
            
            # Create analyzer
            analyzer = ExcelDataAnalyzer()
            
            # Analyze the test file
            self.analysis_data = analyzer.analyze_excel_file(self.test_file_path)
            
            if self.analysis_data.get('success', False):
                stats = self.analysis_data.get('statistics', {})
                message = f"""Analysis completed successfully!

Statistics:
• Total Sheets: {stats.get('total_sheets', 0)}
• Equipment Rows: {stats.get('total_equipment_rows', 0)}
• Valid BA Numbers: {stats.get('valid_ba_numbers', 0)}
• Invalid Rows: {stats.get('invalid_rows', 0)}
• Potential Conflicts: {stats.get('potential_conflicts', 0)}

Preview Data: {len(self.analysis_data.get('preview_data', []))} sheets analyzed
Conflicts: {len(self.analysis_data.get('conflicts', []))} conflicts detected
"""
                
                QMessageBox.information(self, "Analysis Results", message)
                
                # Enable next test
                self.test_preview_btn.setEnabled(True)
                if self.analysis_data.get('conflicts'):
                    self.test_conflicts_btn.setEnabled(True)
                    self.test_bulk_actions_btn.setEnabled(True)
                
            else:
                error = self.analysis_data.get('error', 'Unknown error')
                QMessageBox.critical(self, "Analysis Failed", f"Analysis failed:\n{error}")
                
        except Exception as e:
            logger.error(f"Error testing analyzer: {e}")
            QMessageBox.critical(self, "Error", f"Analyzer test failed:\n{str(e)}")
    
    def test_preview_dialog(self):
        """Test the ImportPreviewDialog."""
        if not self.analysis_data:
            QMessageBox.warning(self, "Warning", "Please run analyzer test first.")
            return
        
        try:
            logger.info("Testing ImportPreviewDialog...")
            
            # Check if there are conflicts
            conflicts = self.analysis_data.get('conflicts', [])
            
            if conflicts:
                # Show full preview dialog
                preview_dialog = ImportPreviewDialog(self.analysis_data, self)
                preview_dialog.import_confirmed.connect(self.on_import_confirmed)
                preview_dialog.import_cancelled.connect(self.on_import_cancelled)
                preview_dialog.exec_()
            else:
                # Show simple confirmation dialog
                confirmation_dialog = SimpleConfirmationDialog(self.analysis_data, self)
                result = confirmation_dialog.exec_()
                
                if result == confirmation_dialog.Accepted:
                    QMessageBox.information(self, "Import Confirmed", "Import would proceed (test mode)")
                else:
                    QMessageBox.information(self, "Import Cancelled", "Import was cancelled")
                    
        except Exception as e:
            logger.error(f"Error testing preview dialog: {e}")
            QMessageBox.critical(self, "Error", f"Preview dialog test failed:\n{str(e)}")
    
    def test_conflict_resolution(self):
        """Test conflict resolution by adding conflicting data to database."""
        if not self.analysis_data:
            QMessageBox.warning(self, "Warning", "Please run analyzer test first.")
            return
        
        try:
            logger.info("Testing conflict resolution...")
            
            # Add some test data to database to create conflicts
            self.create_test_conflicts()
            
            # Re-analyze to detect conflicts
            analyzer = ExcelDataAnalyzer()
            self.analysis_data = analyzer.analyze_excel_file(self.test_file_path)
            
            conflicts = self.analysis_data.get('conflicts', [])
            if conflicts:
                QMessageBox.information(
                    self,
                    "Conflicts Created",
                    f"Created {len(conflicts)} test conflicts.\nNow run the preview dialog test to see conflict resolution in action."
                )
            else:
                QMessageBox.information(self, "No Conflicts", "No conflicts were created. Database may be empty.")
                
        except Exception as e:
            logger.error(f"Error testing conflict resolution: {e}")
            QMessageBox.critical(self, "Error", f"Conflict resolution test failed:\n{str(e)}")
    
    def create_test_conflicts(self):
        """Create test conflicts by adding some equipment to the database."""
        try:
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            
            # Insert test equipment that will conflict
            test_equipment = [
                ('BA001', 'Toyota Hilux (Old)', 'TH001_OLD', 10000, 800, 2019, 1, '2019-01-15', 1, 'Test conflict data'),
                ('BA003', 'Isuzu D-Max (Old)', 'ID003_OLD', 5000, 400, 2020, 1, '2020-03-10', 1, 'Test conflict data')
            ]
            
            for equipment in test_equipment:
                cursor.execute('''
                    INSERT OR REPLACE INTO equipment 
                    (ba_number, make_and_type, serial_number, meterage_kms, hours_run_total, 
                     vintage_years, units_held, date_of_commission, is_active, remarks)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', equipment)
            
            conn.commit()
            conn.close()
            
            logger.info("Test conflict data added to database")
            
        except Exception as e:
            logger.error(f"Error creating test conflicts: {e}")
            raise
    
    def on_import_confirmed(self, analysis_data):
        """Handle import confirmation."""
        conflict_resolutions = analysis_data.get('conflict_resolutions', {})
        QMessageBox.information(
            self,
            "Import Confirmed",
            f"Import confirmed with {len(conflict_resolutions)} conflict resolutions (test mode)"
        )
    
    def test_bulk_actions(self):
        """Test Accept All and Skip All functionality."""
        if not self.analysis_data:
            QMessageBox.warning(self, "Warning", "Please run analyzer test first.")
            return

        try:
            logger.info("Testing bulk actions (Accept All / Skip All)...")

            # Ensure we have conflicts
            conflicts = self.analysis_data.get('conflicts', [])
            if not conflicts:
                # Create conflicts first
                self.create_test_conflicts()

                # Re-analyze to detect conflicts
                analyzer = ExcelDataAnalyzer()
                self.analysis_data = analyzer.analyze_excel_file(self.test_file_path)
                conflicts = self.analysis_data.get('conflicts', [])

            if conflicts:
                QMessageBox.information(
                    self,
                    "Bulk Actions Test",
                    f"Found {len(conflicts)} conflicts.\n\n"
                    "The preview dialog will now open.\n"
                    "Try using the 'Accept All Conflicts' and 'Skip All Conflicts' buttons "
                    "to test bulk resolution functionality."
                )

                # Show preview dialog with conflicts
                preview_dialog = ImportPreviewDialog(self.analysis_data, self)
                preview_dialog.import_confirmed.connect(self.on_import_confirmed)
                preview_dialog.import_cancelled.connect(self.on_import_cancelled)
                preview_dialog.exec_()
            else:
                QMessageBox.information(self, "No Conflicts", "No conflicts found for bulk action testing.")

        except Exception as e:
            logger.error(f"Error testing bulk actions: {e}")
            QMessageBox.critical(self, "Error", f"Bulk actions test failed:\n{str(e)}")

    def on_import_cancelled(self):
        """Handle import cancellation."""
        QMessageBox.information(self, "Import Cancelled", "Import was cancelled")


def main():
    """Main test function."""
    app = QApplication(sys.argv)
    
    # Check if database exists
    if not os.path.exists(config.DB_PATH):
        QMessageBox.critical(
            None,
            "Database Not Found",
            f"Database not found at: {config.DB_PATH}\n\nPlease ensure the application database exists before running tests.",
            QMessageBox.StandardButton.Ok
        )
        sys.exit(1)
    
    # Create and show test window
    window = TestMainWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
